{
	"[javascript]": {
		"editor.defaultFormatter": "dbaeumer.vscode-eslint",
		"editor.insertSpaces": true,
		"editor.tabSize": 2,
		"prettier.tabWidth": 2,
		"prettier.useTabs": false
	},
	"[json]": {
		"editor.defaultFormatter": "vscode.json-language-features"
	},
	"[jsonc]": {
		"editor.defaultFormatter": "vscode.json-language-features"
	},
	"[typescript]": {
		"editor.defaultFormatter": "dbaeumer.vscode-eslint",
		"editor.insertSpaces": true,
		"editor.tabSize": 2,
		"prettier.tabWidth": 2,
		"prettier.useTabs": false
	},
	"editor.codeActionsOnSave": {
		"source.fixAll": "explicit",
		"source.organizeImports": "explicit",
		// "source.fixAll.eslint": "explicit",
		// "source.fixAll.stylelint": "explicit",
		// "source.fixAll.tslint": "explicit",
		"source.removeUnusedImports": "explicit",
	},
	"editor.formatOnSave": true,
	"editor.formatOnType": true,
	"editor.insertSpaces": false,
	"i18n-ally.localesPaths": [
		"src/cross-cutting/domain/locales"
	],
}