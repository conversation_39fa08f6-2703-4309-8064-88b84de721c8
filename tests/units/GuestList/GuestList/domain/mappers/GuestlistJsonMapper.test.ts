import { entityStampsToJson } from '@/cross-cutting/domain/services/Stamps';
import { GuestListJsonMapper } from '@/guestLists/guestLists/domain/mappers/GuestListJsonMapper';
import { GuestListMother } from '@tests/stubs/guestList/GuestListMother';

describe(`${GuestListJsonMapper.name}`, () => {
  const guestList = GuestListMother.buildDefault();

  it('should serialize and deserialize a guestList', () => {
    const json = GuestListJsonMapper.toJson(guestList);

    expect(json.id).toBe(guestList.id);
    expect(json.referrerId).toBe(guestList.referrerId);
    expect(json.organizationId).toBe(guestList.organizationId);
    expect(json.eventId).toBe(guestList.eventId);
    expect(json.organizationAssignedId).toBe(guestList.organizationAssignedId);
    expect(json.signedUp).toBe(guestList.signedUp);
    expect(json.typeId).toBe(guestList.typeId);
    expect(json.type).toBe(guestList.type);
    expect(json.enter).toBe(guestList.enter);
    expect(json.state).toBe(guestList.state);
    expect(json.groups).toStrictEqual(guestList.groups);

    const stamps = entityStampsToJson(guestList);

    expect(json.createdAt).toBe(stamps.createdAt);
    expect(json.createdBy).toBe(stamps.createdBy);
    expect(json.updatedAt).toBe(stamps.updatedAt);
    expect(json.updatedBy).toBe(stamps.updatedBy);
    expect(json.removedAt).toBe(stamps.removedAt);
    expect(json.removedBy).toBe(stamps.removedBy);

    const entityOrError = GuestListJsonMapper.toEntity(json);

    expect(entityOrError.isRight()).toBeTruthy();
  });
});
