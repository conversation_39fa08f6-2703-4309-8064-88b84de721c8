import { entityStampsToJson } from '@/cross-cutting/domain/services/Stamps';
import { GuestListLimitsJsonMapper } from '@/guestLists/guestListLimits/domain/mappers/GuestListLimitsJsonMapper';
import { GuestListLimitMother } from '@tests/stubs/guestList/GuestListLimitMother';

describe(`${GuestListLimitsJsonMapper.name}`, () => {
  const guestList = GuestListLimitMother.buildDefault();

  it('should serialize and deserialize a guestList limit', () => {
    const json = GuestListLimitsJsonMapper.toJson(guestList);

    expect(json.id).toBe(guestList.id);
    expect(json.typeSlug).toBe(guestList.typeSlug);
    expect(json.organizationId).toBe(guestList.organizationId.fold(() => null, item => item));
    expect(json.eventId).toBe(guestList.eventId);
    expect(json.userId).toBe(guestList.userId.fold(() => null, item => item));
    expect(json.maximum).toBe(guestList.maximum);

    const stamps = entityStampsToJson(guestList);

    expect(json.createdAt).toBe(stamps.createdAt);
    expect(json.createdBy).toBe(stamps.createdBy);
    expect(json.updatedAt).toBe(stamps.updatedAt);
    expect(json.updatedBy).toBe(stamps.updatedBy);
    expect(json.removedAt).toBe(stamps.removedAt);
    expect(json.removedBy).toBe(stamps.removedBy);

    const entityOrError = GuestListLimitsJsonMapper.toEntity(json);

    expect(entityOrError.isRight()).toBeTruthy();
  });
});
