import { OrderTypes, type PaginationMetadata } from '@discocil/fv-criteria-converter-library/domain';
import {
  EMicrositeServices,
  FvDate,
  Maybe,
} from '@discocil/fv-domain-library/domain';

import { EventTrackingCalculateMetricsService } from '@/tracking/domain/services/EventTrackingCalculateMetricsService';
import { EEventTypes } from '@/tracking/domain/value-objects/EventType';
import { MotherCreator } from '@tests/stubs/MotherCreator';
import { EventTrackingMother } from '@tests/stubs/tracking/EventTrackingMother';

import type {
  EventTracking,
  EventTrackings,
  EventTrackingsPaginated,
  TrackingMetricsFvEventResponse,
} from '@/tracking/domain/entities/EventTracking';

const generateSessionId = (): string => MotherCreator.random().lorem.slug(10);

describe(`${EventTrackingCalculateMetricsService.name}`, () => {
  const service = new EventTrackingCalculateMetricsService();

  afterEach(async () => {
    jest.clearAllMocks();
  });

  const defaultPagination: PaginationMetadata = {
    page: 1,
    perPage: 100,
    total: 100,
    totalPages: 1,
    order: {
      type: OrderTypes.ASC,
      by: 'createdAt',
    },
    next: Maybe.none(),
    previous: Maybe.none(),
  };

  describe('execute', () => {
    it('should return empty metrics for empty input', () => {
      const paginatedTrackings: EventTrackingsPaginated[] = [];

      const trackingMetrics: TrackingMetricsFvEventResponse = service.execute(paginatedTrackings);

      expect(trackingMetrics.allEventTrackings.all[EEventTypes.InitiateCheckout].size).toBe(0);
      expect(trackingMetrics.allEventTrackings.all[EEventTypes.AddToCart].size).toBe(0);
      expect(trackingMetrics.allEventTrackings.all[EEventTypes.Purchase].size).toBe(0);
      expect(trackingMetrics.eventTrackingsForTheLast15Minutes.all[EEventTypes.InitiateCheckout].size).toBe(0);
      expect(trackingMetrics.eventTrackingsForTheLast15Minutes.all[EEventTypes.AddToCart].size).toBe(0);
      expect(trackingMetrics.eventTrackingsForTheLast15Minutes.all[EEventTypes.Purchase].size).toBe(0);
    });

    it('should process tracking with valid service type and session id', () => {
      const sessionId = generateSessionId();
      const trackings: EventTrackings = new Map();

      const eventTracking: EventTracking = EventTrackingMother.buildWithCustomData({
        name: EEventTypes.AddToCart,
        data: {
          serviceType: Maybe.some(EMicrositeServices.TICKETS),
          sessionId: Maybe.some(sessionId),
        },
      });

      trackings.set(eventTracking.id, eventTracking);

      const paginatedTrackings: EventTrackingsPaginated[] = [{
        trackings,
        pagination: defaultPagination,
      }];

      const trackingMetrics: TrackingMetricsFvEventResponse = service.execute(paginatedTrackings);

      expect(trackingMetrics.allEventTrackings[EMicrositeServices.TICKETS][EEventTypes.AddToCart].size).toBe(1);
      expect(trackingMetrics.allEventTrackings.all[EEventTypes.AddToCart].size).toBe(1);
      expect(trackingMetrics.allEventTrackings[EMicrositeServices.TICKETS][EEventTypes.AddToCart].has(sessionId)).toBe(true);
      expect(trackingMetrics.allEventTrackings.all[EEventTypes.AddToCart].has(sessionId)).toBe(true);
    });

    it('should skip tracking with empty service type', () => {
      const sessionId = generateSessionId();
      const trackings: EventTrackings = new Map();

      const eventTracking: EventTracking = EventTrackingMother.buildWithCustomData({
        name: EEventTypes.AddToCart,
        data: {
          serviceType: Maybe.none(),
          sessionId: Maybe.some(sessionId),
        },
      });

      trackings.set(eventTracking.id, eventTracking);

      const paginatedTrackings: EventTrackingsPaginated[] = [{
        trackings,
        pagination: defaultPagination,
      }];

      const trackingMetrics: TrackingMetricsFvEventResponse = service.execute(paginatedTrackings);

      expect(trackingMetrics.allEventTrackings[EMicrositeServices.TICKETS][EEventTypes.AddToCart].size).toBe(0);
      expect(trackingMetrics.allEventTrackings.all[EEventTypes.AddToCart].size).toBe(0);
    });

    it('should skip tracking with empty session id', () => {
      const trackings: EventTrackings = new Map();

      const eventTracking: EventTracking = EventTrackingMother.buildWithCustomData({
        name: EEventTypes.AddToCart,
        data: {
          serviceType: Maybe.some(EMicrositeServices.TICKETS),
          sessionId: Maybe.none(),
        },
      });

      trackings.set(eventTracking.id, eventTracking);

      const paginatedTrackings: EventTrackingsPaginated[] = [{
        trackings,
        pagination: defaultPagination,
      }];

      const trackingMetrics: TrackingMetricsFvEventResponse = service.execute(paginatedTrackings);

      expect(trackingMetrics.allEventTrackings[EMicrositeServices.TICKETS][EEventTypes.AddToCart].size).toBe(0);
      expect(trackingMetrics.allEventTrackings.all[EEventTypes.AddToCart].size).toBe(0);
    });

    it('should process multiple trackings with different event types', () => {
      const sessionId1 = generateSessionId();
      const sessionId2 = generateSessionId();
      const sessionId3 = generateSessionId();

      const trackings: EventTrackings = new Map();

      const addToCartTracking = EventTrackingMother.buildWithCustomData({
        name: EEventTypes.AddToCart,
        data: {
          serviceType: Maybe.some(EMicrositeServices.TICKETS),
          sessionId: Maybe.some(sessionId1),
        },
      });

      const initiateCheckoutTracking = EventTrackingMother.buildWithCustomData({
        name: EEventTypes.InitiateCheckout,
        data: {
          serviceType: Maybe.some(EMicrositeServices.GUESTLISTS),
          sessionId: Maybe.some(sessionId2),
        },
      });

      const purchaseTracking = EventTrackingMother.buildWithCustomData({
        name: EEventTypes.Purchase,
        data: {
          serviceType: Maybe.some(EMicrositeServices.RESERVATIONS),
          sessionId: Maybe.some(sessionId3),
        },
      });

      trackings.set(addToCartTracking.id, addToCartTracking);
      trackings.set(initiateCheckoutTracking.id, initiateCheckoutTracking);
      trackings.set(purchaseTracking.id, purchaseTracking);

      const paginatedTrackings: EventTrackingsPaginated[] = [{
        trackings,
        pagination: defaultPagination,
      }];

      const trackingMetrics: TrackingMetricsFvEventResponse = service.execute(paginatedTrackings);

      expect(trackingMetrics.allEventTrackings[EMicrositeServices.TICKETS][EEventTypes.AddToCart].size).toBe(1);
      expect(trackingMetrics.allEventTrackings[EMicrositeServices.GUESTLISTS][EEventTypes.InitiateCheckout].size).toBe(1);
      expect(trackingMetrics.allEventTrackings[EMicrositeServices.RESERVATIONS][EEventTypes.Purchase].size).toBe(1);

      expect(trackingMetrics.allEventTrackings.all[EEventTypes.AddToCart].size).toBe(1);
      expect(trackingMetrics.allEventTrackings.all[EEventTypes.InitiateCheckout].size).toBe(1);
      expect(trackingMetrics.allEventTrackings.all[EEventTypes.Purchase].size).toBe(1);
    });

    it('should deduplicate same session id across different trackings', () => {
      const sessionId = generateSessionId();
      const trackings: EventTrackings = new Map();

      const tracking1 = EventTrackingMother.buildWithCustomData({
        name: EEventTypes.AddToCart,
        data: {
          serviceType: Maybe.some(EMicrositeServices.TICKETS),
          sessionId: Maybe.some(sessionId),
        },
      });

      const tracking2 = EventTrackingMother.buildWithCustomData({
        name: EEventTypes.AddToCart,
        data: {
          serviceType: Maybe.some(EMicrositeServices.TICKETS),
          sessionId: Maybe.some(sessionId),
        },
      });

      trackings.set(tracking1.id, tracking1);
      trackings.set(tracking2.id, tracking2);

      const paginatedTrackings: EventTrackingsPaginated[] = [{
        trackings,
        pagination: defaultPagination,
      }];

      const trackingMetrics: TrackingMetricsFvEventResponse = service.execute(paginatedTrackings);

      expect(trackingMetrics.allEventTrackings[EMicrositeServices.TICKETS][EEventTypes.AddToCart].size).toBe(1);
      expect(trackingMetrics.allEventTrackings.all[EEventTypes.AddToCart].size).toBe(1);
      expect(trackingMetrics.allEventTrackings[EMicrositeServices.TICKETS][EEventTypes.AddToCart].has(sessionId)).toBe(true);
    });

    it('should separate metrics for last 15 minutes', () => {
      const sessionIdOld = generateSessionId();
      const sessionIdRecent = generateSessionId();

      const trackings: EventTrackings = new Map();

      const oldTracking = EventTrackingMother.buildWithCustomData({
        name: EEventTypes.AddToCart,
        date: FvDate.create().subtractMinutes(30).toPrimitive(),
        data: {
          serviceType: Maybe.some(EMicrositeServices.TICKETS),
          sessionId: Maybe.some(sessionIdOld),
        },
      });

      const recentTracking = EventTrackingMother.buildWithCustomData({
        name: EEventTypes.AddToCart,
        date: FvDate.create().subtractMinutes(5).toPrimitive(),
        data: {
          serviceType: Maybe.some(EMicrositeServices.TICKETS),
          sessionId: Maybe.some(sessionIdRecent),
        },
      });

      trackings.set(oldTracking.id, oldTracking);
      trackings.set(recentTracking.id, recentTracking);

      const paginatedTrackings: EventTrackingsPaginated[] = [{
        trackings,
        pagination: defaultPagination,
      }];

      const trackingMetrics: TrackingMetricsFvEventResponse = service.execute(paginatedTrackings);

      expect(trackingMetrics.allEventTrackings[EMicrositeServices.TICKETS][EEventTypes.AddToCart].size).toBe(2);
      expect(trackingMetrics.allEventTrackings.all[EEventTypes.AddToCart].size).toBe(2);

      expect(trackingMetrics.eventTrackingsForTheLast15Minutes[EMicrositeServices.TICKETS][EEventTypes.AddToCart].size).toBe(1);
      expect(trackingMetrics.eventTrackingsForTheLast15Minutes.all[EEventTypes.AddToCart].size).toBe(1);
      expect(trackingMetrics.eventTrackingsForTheLast15Minutes[EMicrositeServices.TICKETS][EEventTypes.AddToCart].has(sessionIdRecent)).toBe(true);
    });

    it('should process trackings from multiple paginated sets', () => {
      const sessionId1 = generateSessionId();
      const sessionId2 = generateSessionId();

      const trackings1: EventTrackings = new Map();
      const tracking1 = EventTrackingMother.buildWithCustomData({
        name: EEventTypes.AddToCart,
        data: {
          serviceType: Maybe.some(EMicrositeServices.TICKETS),
          sessionId: Maybe.some(sessionId1),
        },
      });

      trackings1.set(tracking1.id, tracking1);

      const trackings2: EventTrackings = new Map();
      const tracking2 = EventTrackingMother.buildWithCustomData({
        name: EEventTypes.Purchase,
        data: {
          serviceType: Maybe.some(EMicrositeServices.GUESTLISTS),
          sessionId: Maybe.some(sessionId2),
        },
      });

      trackings2.set(tracking2.id, tracking2);

      const paginatedTrackings: EventTrackingsPaginated[] = [
        { trackings: trackings1, pagination: defaultPagination },
        { trackings: trackings2, pagination: defaultPagination },
      ];

      const trackingMetrics: TrackingMetricsFvEventResponse = service.execute(paginatedTrackings);

      expect(trackingMetrics.allEventTrackings[EMicrositeServices.TICKETS][EEventTypes.AddToCart].size).toBe(1);
      expect(trackingMetrics.allEventTrackings[EMicrositeServices.GUESTLISTS][EEventTypes.Purchase].size).toBe(1);
      expect(trackingMetrics.allEventTrackings.all[EEventTypes.AddToCart].size).toBe(1);
      expect(trackingMetrics.allEventTrackings.all[EEventTypes.Purchase].size).toBe(1);
    });

    it('should process all supported microsite services', () => {
      const sessionIds = [
        generateSessionId(),
        generateSessionId(),
        generateSessionId(),
        generateSessionId(),
      ];

      const services = [
        EMicrositeServices.TICKETS,
        EMicrositeServices.GUESTLISTS,
        EMicrositeServices.RESERVATIONS,
        EMicrositeServices.PASSES,
      ];

      const trackings: EventTrackings = new Map();

      services.forEach((service, index) => {
        const tracking = EventTrackingMother.buildWithCustomData({
          name: EEventTypes.InitiateCheckout,
          data: {
            serviceType: Maybe.some(service),
            sessionId: Maybe.some(sessionIds[index]),
          },
        });

        trackings.set(tracking.id, tracking);
      });

      const paginatedTrackings: EventTrackingsPaginated[] = [{
        trackings,
        pagination: defaultPagination,
      }];

      const trackingMetrics: TrackingMetricsFvEventResponse = service.execute(paginatedTrackings);

      expect(trackingMetrics.allEventTrackings[EMicrositeServices.TICKETS][EEventTypes.InitiateCheckout].size).toBe(1);
      expect(trackingMetrics.allEventTrackings[EMicrositeServices.GUESTLISTS][EEventTypes.InitiateCheckout].size).toBe(1);
      expect(trackingMetrics.allEventTrackings[EMicrositeServices.RESERVATIONS][EEventTypes.InitiateCheckout].size).toBe(1);
      expect(trackingMetrics.allEventTrackings[EMicrositeServices.PASSES][EEventTypes.InitiateCheckout].size).toBe(1);
      expect(trackingMetrics.allEventTrackings.all[EEventTypes.InitiateCheckout].size).toBe(4);
    });

    it('should handle trackings at exact 15-minute threshold boundary', () => {
      const sessionId = generateSessionId();
      const trackings: EventTrackings = new Map();

      const oldBoundaryTracking = EventTrackingMother.buildWithCustomData({
        name: EEventTypes.Purchase,
        date: FvDate.create().subtractMinutes(16).toPrimitive(),
        data: {
          serviceType: Maybe.some(EMicrositeServices.PASSES),
          sessionId: Maybe.some(sessionId),
        },
      });

      trackings.set(oldBoundaryTracking.id, oldBoundaryTracking);

      const paginatedTrackings: EventTrackingsPaginated[] = [{
        trackings,
        pagination: defaultPagination,
      }];

      const trackingMetrics: TrackingMetricsFvEventResponse = service.execute(paginatedTrackings);

      expect(trackingMetrics.allEventTrackings[EMicrositeServices.PASSES][EEventTypes.Purchase].size).toBe(1);
      expect(trackingMetrics.allEventTrackings.all[EEventTypes.Purchase].size).toBe(1);

      expect(trackingMetrics.eventTrackingsForTheLast15Minutes[EMicrositeServices.PASSES][EEventTypes.Purchase].size).toBe(0);
      expect(trackingMetrics.eventTrackingsForTheLast15Minutes.all[EEventTypes.Purchase].size).toBe(0);
    });
  });

  describe('makeEventTrackingMetricsGroup', () => {
    it('should create correct structure with empty sets', () => {
      const metricsGroup = EventTrackingCalculateMetricsService.makeEventTrackingMetricsGroup();

      expect(metricsGroup[EMicrositeServices.TICKETS]).toBeDefined();
      expect(metricsGroup[EMicrositeServices.GUESTLISTS]).toBeDefined();
      expect(metricsGroup[EMicrositeServices.RESERVATIONS]).toBeDefined();
      expect(metricsGroup[EMicrositeServices.PASSES]).toBeDefined();
      expect(metricsGroup.all).toBeDefined();
      const services = [
        EMicrositeServices.TICKETS,
        EMicrositeServices.GUESTLISTS,
        EMicrositeServices.RESERVATIONS,
        EMicrositeServices.PASSES,
        'all' as const,
      ];

      services.forEach((service) => {
        expect(metricsGroup[service][EEventTypes.InitiateCheckout]).toBeInstanceOf(Set);
        expect(metricsGroup[service][EEventTypes.AddToCart]).toBeInstanceOf(Set);
        expect(metricsGroup[service][EEventTypes.Purchase]).toBeInstanceOf(Set);

        expect(metricsGroup[service][EEventTypes.InitiateCheckout].size).toBe(0);
        expect(metricsGroup[service][EEventTypes.AddToCart].size).toBe(0);
        expect(metricsGroup[service][EEventTypes.Purchase].size).toBe(0);
      });
    });

    it('should create independent set instances', () => {
      const metricsGroup1 = EventTrackingCalculateMetricsService.makeEventTrackingMetricsGroup();
      const metricsGroup2 = EventTrackingCalculateMetricsService.makeEventTrackingMetricsGroup();

      metricsGroup1[EMicrositeServices.TICKETS][EEventTypes.AddToCart].add(generateSessionId());

      expect(metricsGroup1[EMicrositeServices.TICKETS][EEventTypes.AddToCart].size).toBe(1);
      expect(metricsGroup2[EMicrositeServices.TICKETS][EEventTypes.AddToCart].size).toBe(0);
    });
  });
});
