import {
  EMicrositeServices,
  left,
  Maybe,
  NotFoundError,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { EventEntity } from '@/events/events/domain/entities/EventEntity';
import { TrackingFvEventUseCase } from '@/tracking/application/use-cases/TrackingFvEventUseCase';
import { EMicrositeContainerType } from '@/tracking/domain/contracts/EntityContracts';
import { EEventChannel, EEventTypes } from '@/tracking/domain/value-objects/EventType';
import { EventMother } from '@tests/stubs/event/EventMother';
import { MotherCreator } from '@tests/stubs/MotherCreator';

import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type { TrackingEventDto } from '@/tracking/domain/contracts/TrackingEventUseCaseContracts';
import type { TrackingRepository } from '@/tracking/domain/contracts/TrackingFvRepository';

describe(`${TrackingFvEventUseCase.name}`, () => {
  const eventRepository = mock<EventRepository>();
  const trackingMongoRepository = mock<TrackingRepository>();

  const useCase = new TrackingFvEventUseCase(
    trackingMongoRepository,
    eventRepository,
  );

  const event = EventMother.buildDefault();

  const trackingEventDto: TrackingEventDto = {
    id: UniqueEntityID.create().toPrimitive(),
    channel: EEventChannel.fv,
    name: EEventTypes.AddToCart,
    organizationId: event.id,
    eventId: Maybe.some(event.id),
    serviceType: Maybe.some(EMicrositeServices.TICKETS),
    containerType: Maybe.some(EMicrositeContainerType.WEB),
    externalId: Maybe.none(),
    urlPage: 'https://fv.com/event',
    sessionId: Maybe.none(),
    remoteAddress: '127.0.0.1',
    userAgent: MotherCreator.random().internet.userAgent(),
    user: Maybe.none(),
    fb: Maybe.none(),
    content: Maybe.none(),
    price: Maybe.none(),
    items: Maybe.none(),
    numItems: Maybe.none(),
    totalPrice: Maybe.none(),
    route: Maybe.none(),
  };

  it('should return success', async () => {
    eventRepository.find.mockResolvedValue(right(event));

    const useCaseResult = await useCase.execute(trackingEventDto);

    expect(useCaseResult.isLeft()).toBeFalsy();
    expect(useCaseResult.isRight()).toBeTruthy();

    const response = useCaseResult.value;

    expect(response).toBe(true);
  });

  it('should fail because eventId is empty', async () => {
    const failingTrackingEventDto: TrackingEventDto = {
      ...trackingEventDto,
      eventId: Maybe.none(),
    };

    eventRepository.find.mockResolvedValue(left(NotFoundError.build({
      context: TrackingFvEventUseCase.name,
      target: EventEntity.name,
    })));

    const useCaseResult = await useCase.execute(failingTrackingEventDto);

    expect(useCaseResult.isLeft()).toBeTruthy();

    const response = useCaseResult.value;

    expect(response).toBeInstanceOf(NotFoundError);
  });

  it('should fail because eventId has no matching event', async () => {
    const failingTrackingEventDto = {
      ...trackingEventDto,
      eventId: Maybe.some(UniqueEntityID.create().toPrimitive()),
    };

    eventRepository.find.mockResolvedValue(left(NotFoundError.build({
      context: TrackingFvEventUseCase.name,
      target: EventEntity.name,
    })));

    const useCaseResult = await useCase.execute(failingTrackingEventDto);

    expect(useCaseResult.isLeft()).toBeTruthy();

    const response = useCaseResult.value;

    expect(response).toBeInstanceOf(NotFoundError);
  });
});
