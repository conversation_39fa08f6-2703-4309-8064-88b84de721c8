import {
  EMicrositeServices,
  Maybe,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';


import { FetchAllPagesService } from '@/cross-cutting/infrastructure/services/FetchAllPagesService';
import { TrackingMetricsFvEventUseCase } from '@/tracking/application/use-cases/TrackingMetricsFvEventUseCase';
import { EMicrositeContainerType } from '@/tracking/domain/contracts/EntityContracts';
import { EventTrackingCalculateMetricsService } from '@/tracking/domain/services/EventTrackingCalculateMetricsService';
import { EEventChannel, EEventTypes } from '@/tracking/domain/value-objects/EventType';
import { EventMother } from '@tests/stubs/event/EventMother';
import { MotherCreator } from '@tests/stubs/MotherCreator';
import { EventTrackingMother } from '@tests/stubs/tracking/EventTrackingMother';

import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type { TrackingRepository } from '@/tracking/domain/contracts/TrackingFvRepository';
import type {
  EventTracking, EventTrackings, SearchPaginatedTrackingEvents, TrackingMetricsFvEventResponse,
} from '@/tracking/domain/entities/EventTracking';
import type { PaginationMetadata } from '@discocil/fv-criteria-converter-library/domain';

describe(`${TrackingMetricsFvEventUseCase.name}`, () => {
  const eventRepository = mock<EventRepository>();
  const trackingMongoRepository = mock<TrackingRepository>();

  const useCase = new TrackingMetricsFvEventUseCase(
    trackingMongoRepository,
    eventRepository,
    new FetchAllPagesService(),
    new EventTrackingCalculateMetricsService(),
  );

  const event = EventMother.buildDefault();

  const pagination: PaginationMetadata = {
    page: 1,
    perPage: 800,
    total: 800,
    totalPages: 1,
    order: {
      type: 'asc',
      by: 'createdAt',
    },
    next: Maybe.none(),
    previous: Maybe.none(),
  };

  it('should return metrics from eventA and not from eventB', async () => {
    eventRepository.find.mockResolvedValue(right(event));

    const channel = EEventChannel.fv;
    const name = EEventTypes.AddToCart;
    const serviceType = EMicrositeServices.TICKETS;

    const trackings: EventTrackings = new Map();

    let eventTracking: EventTracking = EventTrackingMother.buildWithCustomData({
      channel,
      name,
      organizationId: event.id,
      eventId: Maybe.some(event.id),
      data: {
        serviceType: Maybe.some(serviceType),
        containerType: Maybe.some(EMicrositeContainerType.WEB),
        sessionId: Maybe.some(MotherCreator.random().lorem.slug(5)),
      },
    });

    trackings.set(eventTracking.id, eventTracking);

    eventTracking = EventTrackingMother.buildWithCustomData({
      channel,
      name,
      organizationId: UniqueEntityID.create().toPrimitive(),
      eventId: Maybe.some(UniqueEntityID.create().toPrimitive()),
      data: {
        serviceType: Maybe.some(serviceType),
        containerType: Maybe.some(EMicrositeContainerType.WEB),
        sessionId: Maybe.some(MotherCreator.random().lorem.slug(5)),
      },
    });

    const searchPaginatedTrackings: SearchPaginatedTrackingEvents = {
      trackings,
      pagination,
    };

    trackingMongoRepository.search.mockResolvedValue(right(searchPaginatedTrackings));

    const useCaseResult = await useCase.execute({ eventId: event.id });

    expect(useCaseResult.isLeft()).toBeFalsy();
    expect(useCaseResult.isRight()).toBeTruthy();

    const response = useCaseResult.value as TrackingMetricsFvEventResponse;

    expect(response.allEventTrackings.all[name].size).toBe(1);
    expect(response.allEventTrackings[serviceType][name].size).toBe(1);
  });

  it('should return metrics', async () => {
    eventRepository.find.mockResolvedValue(right(event));

    const channel = EEventChannel.fv;

    const allServices = [
      EMicrositeServices.TICKETS,
      EMicrositeServices.GUESTLISTS,
      EMicrositeServices.RESERVATIONS,
      EMicrositeServices.PASSES,
    ];

    const allEventTypes = [
      EEventTypes.AddToCart,
      EEventTypes.InitiateCheckout,
      EEventTypes.Purchase,
    ];

    const trackings: EventTrackings = new Map();

    for (const _serviceType of allServices) {
      let eventTrackingQuantity = 3;

      for (const _eventType of allEventTypes) {
        for (let i = 0; i < eventTrackingQuantity; i++) {
          const defaultData = EventTrackingMother.buildDefault().data;

          const eventTracking: EventTracking = EventTrackingMother.buildWithCustomData({
            name: _eventType,
            organizationId: event.id,
            eventId: Maybe.some(event.id),
            channel,
            data: {
              ...defaultData,
              serviceType: Maybe.some(_serviceType),
              sessionId: Maybe.some(MotherCreator.random().lorem.slug(5)),
            },
          });

          trackings.set(eventTracking.id, eventTracking);
        }

        eventTrackingQuantity--;
      }
    }

    const searchPaginatedTrackings: SearchPaginatedTrackingEvents = {
      trackings,
      pagination,
    };

    trackingMongoRepository.search.mockResolvedValue(right(searchPaginatedTrackings));

    const useCaseResult = await useCase.execute({ eventId: event.id });

    expect(useCaseResult.isLeft()).toBeFalsy();
    expect(useCaseResult.isRight()).toBeTruthy();

    const response = useCaseResult.value as TrackingMetricsFvEventResponse;

    expect(response.allEventTrackings[EMicrositeServices.TICKETS][EEventTypes.AddToCart].size).toBe(3);
    expect(response.allEventTrackings[EMicrositeServices.TICKETS][EEventTypes.InitiateCheckout].size).toBe(2);
    expect(response.allEventTrackings[EMicrositeServices.TICKETS][EEventTypes.Purchase].size).toBe(1);

    expect(response.allEventTrackings[EMicrositeServices.GUESTLISTS][EEventTypes.AddToCart].size).toBe(3);
    expect(response.allEventTrackings[EMicrositeServices.GUESTLISTS][EEventTypes.InitiateCheckout].size).toBe(2);
    expect(response.allEventTrackings[EMicrositeServices.GUESTLISTS][EEventTypes.Purchase].size).toBe(1);

    expect(response.allEventTrackings[EMicrositeServices.RESERVATIONS][EEventTypes.AddToCart].size).toBe(3);
    expect(response.allEventTrackings[EMicrositeServices.RESERVATIONS][EEventTypes.InitiateCheckout].size).toBe(2);
    expect(response.allEventTrackings[EMicrositeServices.RESERVATIONS][EEventTypes.Purchase].size).toBe(1);

    expect(response.allEventTrackings[EMicrositeServices.PASSES][EEventTypes.AddToCart].size).toBe(3);
    expect(response.allEventTrackings[EMicrositeServices.PASSES][EEventTypes.InitiateCheckout].size).toBe(2);
    expect(response.allEventTrackings[EMicrositeServices.PASSES][EEventTypes.Purchase].size).toBe(1);

    const servicesCount = allServices.length;

    expect(response.allEventTrackings.all[EEventTypes.AddToCart].size).toBe(servicesCount * 3);
    expect(response.allEventTrackings.all[EEventTypes.InitiateCheckout].size).toBe(servicesCount * 2);
    expect(response.allEventTrackings.all[EEventTypes.Purchase].size).toBe(servicesCount);
  });
});
