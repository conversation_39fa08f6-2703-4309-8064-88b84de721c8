import { Organization } from '@/organizations/organizations/domain/entities/Organization';
import { OrganizationJsonMapper } from '@/organizations/organizations/domain/mappers/OrganizationJsonMapper';
import { OrganizationMother } from '@tests/stubs/organization/OrganizationMother';

describe(`${Organization.name}`, () => {
  const organization = OrganizationMother.buildDefault();

  it('should serialize and deserialize a organization', () => {
    const json = OrganizationJsonMapper.toJson(organization);

    expect(json.id).toBe(organization.id);
    expect(json.name).toBe(organization.name);
    expect(json.state).toBe(organization.state);
    expect(json.image).toBe(organization.image.fold(() => null, item => item));
    expect(json.currency).toBe(organization.currency);
    expect(json.countryCode).toBe(organization.countryCode);
    expect(json.paymentsProvider).toBe(organization.paymentsProvider);
    expect(json.merchantName).toBe(organization.merchantName);
    expect(json.feeRule).toBe(organization.feeRule);
    expect(json.slug).toBe(organization.slug.fold(() => null, item => item));
    expect(json.type).toBe(organization.type);
    expect(json.hosts).toEqual(organization.hosts);
    expect(json.plan).toBe(organization.plan);
    expect(json.ticketFields).toStrictEqual(organization.ticketFields.fold(() => null, item => item));
    expect(json.cover).toBe(organization.cover.fold(() => null, item => item));
    expect(json.coverImages).toStrictEqual(organization.coverImages.fold(() => null, (item) => {
      return {
        medium: item.medium.fold(() => null, item => item),
        mini: item.mini.fold(() => null, item => item),
        small: item.small.fold(() => null, item => item),
      };
    }));
    expect(json.gatewayAccountId).toBe(organization.gatewayAccountId.fold(() => null, item => item));
    expect(json.web).toBe(organization.web.fold(() => null, item => item));
    expect(json.facebook).toBe(organization.facebook.fold(() => null, item => item));
    expect(json.instagram).toBe(organization.instagram.fold(() => null, item => item));
    expect(json.menuUrl).toBe(organization.menuUrl.fold(() => null, item => item));
    expect(json.carousel).toEqual(organization.carousel);
    expect(json.floorImage).toBe(organization.floorImage.fold(() => null, item => item));
    expect(json.images).toStrictEqual(organization.images.fold(() => null, item => item));
    expect(json.covers).toStrictEqual(organization.covers);
    expect(json.active).toBe(organization.active);
    expect(json.usesDays).toBe(organization.usesDays);
    expect(json.ruleTemplateId).toBe(organization.ruleTemplateId.fold(() => null, item => item));
    expect(json.createdAt).toBe(organization.createdAt);
    expect(json.createdBy).toBe(organization.createdBy);
    expect(json.updatedAt).toBe(organization.updatedAt);
    expect(json.updatedBy).toBe(organization.updatedBy);
    expect(json.removedAt).toBe(organization.removedAt);
    expect(json.removedBy).toBe(organization.removedBy);

    const entityOrError = OrganizationJsonMapper.toEntity(json);

    expect(entityOrError.isRight()).toBeTruthy();
  });
});
