import {
  Either2,
  Fv<PERSON><PERSON><PERSON>,
  NotFoundError,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { DeletePaymentUseCase } from '@/payments/application/DeletePaymentUseCase';
import { PaymentMother } from '@tests/stubs/Payments/PaymentsMother';

import type { DeletePaymentDto } from '@/payments/domain/contracts/DeletePaymentDtoContract';
import type { PaymentRepository } from '@/payments/domain/contracts/PaymentRepository';

describe(`${DeletePaymentUseCase.name}`, () => {
  const paymentRepository = mock<PaymentRepository>();

  const useCase = new DeletePaymentUseCase(paymentRepository);

  const payment = PaymentMother.buildDefault();
  const deletePaymentDto: DeletePaymentDto = { paymentId: UniqueEntityID.build(payment.id) };

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should return NotFoundError when payment does not exist', async () => {
    const notFoundError = NotFoundError.build({
      context: 'PaymentMongoRepository',
      target: 'Payment',
    });

    paymentRepository.remove.mockResolvedValue(Either2.left(notFoundError));

    const useCaseResult = await useCase.execute(deletePaymentDto);

    expect(useCaseResult.isLeft()).toBeTruthy();
    expect(useCaseResult.get()).toBeInstanceOf(NotFoundError);
  });

  it('should successfully delete payment when it exists and return 1', async () => {
    const deletedCount = FvNumber.build(1);

    paymentRepository.remove.mockResolvedValue(Either2.right(deletedCount));

    const useCaseResult = await useCase.execute(deletePaymentDto);

    expect(useCaseResult.isRight()).toBeTruthy();

    const deleteResult = useCaseResult.get() as FvNumber;

    expect(deleteResult.toPrimitive()).toBe(1);
  });
});
