import { Maybe } from '@discocil/fv-domain-library';

import { entityStampsToJson } from '@/cross-cutting/domain/services/Stamps';
import { Payment } from '@/payments/domain/entities/Payment';
import { PaymentJsonMapper } from '@/payments/domain/mappers/PaymentJsonMapper';
import { PaymentMother } from '@tests/stubs/Payments/PaymentsMother';

describe(`${Payment.name}`, () => {
  const payment = PaymentMother.buildDefault();

  it('should serialize a payment', () => {
    const json = PaymentJsonMapper.toJson(payment);

    expect(json.id).toBe(payment.id);
    expect(json.organizationId).toBe(payment.organizationId);
    expect(json.eventId).toBe(payment.eventId.fold(() => null, item => item));
    expect(json.typeId).toBe(payment.typeId.fold(() => null, item => item));
    expect(json.userId).toBe(payment.userId.fold(() => null, item => item));
    expect(json.resourceType).toBe(payment.resourceType);
    expect(json.totalImportCts).toBe(payment.totalImportCts);
    expect(json.resourceIds).toEqual(payment.resourceIds);
    expect(json.feesTotal).toBe(payment.feesTotal);
    expect(json.discocilTotal).toBe(payment.discocilTotal);
    expect(json.totalPrice).toBe(payment.totalPrice);
    expect(json.totalImport).toBe(payment.totalImport);
    expect(json.paid).toBe(payment.getPaidInISO().fold(() => null, item => item));
    expect(json.nResources).toBe(payment.nResources);
    expect(json.expiresAt).toBe(payment.getExpiresAtInISO());
    expect(json.state).toBe(payment.state);
    expect(json.redirectUrl).toBe(payment.redirectUrl.fold(() => null, item => item));
    expect(json.errorUrl).toBe(payment.errorUrl.fold(() => null, item => item));
    expect(json.paymentsProvider).toBe(payment.paymentsProvider);
    expect(json.merchantName).toBe(payment.merchantName.fold(() => null, item => item));
    expect(json.currency).toBe(payment.currency);
    expect(json.paylinkId).toBe(payment.paylinkId.fold(() => null, item => item));
    expect(json.totalDiscount).toBe(payment.totalDiscount);
    expect(json.supplements).toEqual({
      number: payment.numberSupplements,
      price: payment.priceSupplements,
    });
    expect(json.warranty).toEqual({
      total: payment.totalWarranty,
      cost: payment.costWarranty,
    });
    expect(json.discount).toStrictEqual(payment.discount.fold(() => null, item => item));
    expect(json.sitting).toBe(payment.sitting.fold(() => null, item => item));
    expect(json.spotify).toBe(payment.spotify.fold(() => null, item => item));
    expect(json.isFourvenuesGateway).toBe(payment.isFourvenuesGateway);
    expect(json.gatewayAccountId).toBe(payment.gatewayAccountId.fold(() => null, item => item));
    expect(json.commissionRule).toBe(payment.commissionRule.fold(() => null, item => item));

    const stamps = entityStampsToJson(payment);

    expect(json.createdAt).toBe(stamps.createdAt);
    expect(json.createdBy).toBe(stamps.createdBy);
    expect(json.updatedAt).toBe(stamps.updatedAt);
    expect(json.updatedBy).toBe(stamps.updatedBy);
    expect(json.removedAt).toBe(stamps.removedAt);
    expect(json.removedBy).toBe(stamps.removedBy);
  });

  it('should deserialize a payment', () => {
    const json = PaymentJsonMapper.toJson(payment);
    const entityOrError = PaymentJsonMapper.toEntity(json);

    expect(entityOrError.isRight()).toBeTruthy();

    const builtPayment = entityOrError.value as Payment;

    expect(builtPayment.id).toBe(payment.id);
    expect(builtPayment.organizationId).toEqual(payment.organizationId);
    expect(builtPayment.eventId).toEqual(payment.eventId);
    expect(builtPayment.typeId).toEqual(payment.typeId);
    expect(builtPayment.userId).toEqual(payment.userId);
    expect(builtPayment.resourceType).toEqual(payment.resourceType);
    expect(builtPayment.totalImportCts).toEqual(payment.totalImportCts);
    expect(builtPayment.resourceIds).toEqual(payment.resourceIds);
    expect(builtPayment.feesTotal).toEqual(payment.feesTotal);
    expect(builtPayment.discocilTotal).toEqual(payment.discocilTotal);
    expect(builtPayment.totalPrice).toEqual(payment.totalPrice);
    expect(builtPayment.totalImport).toEqual(payment.totalImport);
    expect(builtPayment.getPaidInISO().get()).toEqual(payment.getPaidInISO().get());
    expect(builtPayment.nResources).toEqual(payment.nResources);
    expect(builtPayment.getExpiresAtInISO()).toEqual(payment.getExpiresAtInISO());
    expect(builtPayment.state).toEqual(payment.state);
    expect(builtPayment.redirectUrl.fold(() => null, item => item)).toEqual(payment.redirectUrl.fold(() => null, item => item));
    expect(builtPayment.errorUrl.fold(() => null, item => item)).toEqual(payment.errorUrl.fold(() => null, item => item));
    expect(builtPayment.paymentsProvider).toEqual(payment.paymentsProvider);
    expect(builtPayment.merchantName.fold(() => null, item => item)).toEqual(payment.merchantName.fold(() => null, item => item));
    expect(builtPayment.currency).toEqual(payment.currency);
    expect(builtPayment.discount.fold(() => null, item => item)).toStrictEqual(payment.discount.fold(() => null, item => item));
    expect(builtPayment.paylinkId.fold(() => null, item => item)).toEqual(payment.paylinkId.fold(() => null, item => item));
    expect(builtPayment.totalDiscount).toEqual(payment.totalDiscount);

    expect(builtPayment.numberSupplements).toEqual(payment.numberSupplements);
    expect(builtPayment.priceSupplements).toEqual(payment.priceSupplements);

    expect(builtPayment.totalWarranty).toEqual(payment.totalWarranty);
    expect(builtPayment.costWarranty).toEqual(payment.costWarranty);

    expect(builtPayment.sitting.fold(() => null, item => item)).toEqual(payment.sitting.fold(() => null, item => item));
    expect(builtPayment.spotify.fold(() => null, item => item)).toEqual(payment.spotify.fold(() => null, item => item));
    expect(builtPayment.isFourvenuesGateway).toEqual(payment.isFourvenuesGateway);
    expect(builtPayment.gatewayAccountId.fold(() => null, item => item)).toEqual(payment.gatewayAccountId.fold(() => null, item => item));
    expect(builtPayment.commissionRule.fold(() => null, item => item)).toEqual(payment.commissionRule.fold(() => null, item => item));

    expect(builtPayment.createdAt).toEqual(payment.createdAt);
    expect(builtPayment.createdBy).toEqual(payment.createdBy);
    expect(builtPayment.updatedAt).toEqual(payment.updatedAt);
    expect(builtPayment.updatedBy).toEqual(payment.updatedBy);
    expect(builtPayment.removedAt).toEqual(payment.removedAt);
    expect(builtPayment.removedBy).toEqual(payment.removedBy);

    expect(builtPayment.info).toStrictEqual(payment.info);
  });

  it('should serialize and deserialize a payment with empty values', () => {
    const paymentWithNulls = PaymentMother.createWithCustomData({
      eventId: Maybe.none(),
      typeId: Maybe.none(),
      userId: Maybe.none(),
      paid: Maybe.none(),
      redirectUrl: Maybe.none(),
      errorUrl: Maybe.none(),
      merchantName: Maybe.none(),
      paylinkId: Maybe.none(),
      discount: Maybe.none(),
      sitting: Maybe.none(),
      spotify: Maybe.none(),
      gatewayAccountId: Maybe.none(),
      commissionRule: Maybe.none(),
    });

    const json = PaymentJsonMapper.toJson(paymentWithNulls);

    expect(json.eventId).toBeNull();
    expect(json.typeId).toBeNull();
    expect(json.userId).toBeNull();
    expect(json.paid).toBeNull();
    expect(json.redirectUrl).toBeNull();
    expect(json.errorUrl).toBeNull();
    expect(json.merchantName).toBeNull();
    expect(json.paylinkId).toBeNull();
    expect(json.discount).toBeNull();
    expect(json.sitting).toBeNull();
    expect(json.spotify).toBeNull();
    expect(json.gatewayAccountId).toBeNull();
    expect(json.commissionRule).toBeNull();

    const entityOrError = PaymentJsonMapper.toEntity(json);

    expect(entityOrError.isRight()).toBeTruthy();

    const deserializedPayment = entityOrError.value as Payment;

    expect(deserializedPayment.eventId.isEmpty()).toBeTruthy();
    expect(deserializedPayment.typeId.isEmpty()).toBeTruthy();
    expect(deserializedPayment.userId.isEmpty()).toBeTruthy();
    expect(deserializedPayment.paid.isEmpty()).toBeTruthy();
    expect(deserializedPayment.redirectUrl.isEmpty()).toBeTruthy();
    expect(deserializedPayment.errorUrl.isEmpty()).toBeTruthy();
    expect(deserializedPayment.merchantName.isEmpty()).toBeTruthy();
    expect(deserializedPayment.paylinkId.isEmpty()).toBeTruthy();
    expect(deserializedPayment.discount.isEmpty()).toBeTruthy();
    expect(deserializedPayment.sitting.isEmpty()).toBeTruthy();
    expect(deserializedPayment.spotify.isEmpty()).toBeTruthy();
    expect(deserializedPayment.gatewayAccountId.isEmpty()).toBeTruthy();
    expect(deserializedPayment.commissionRule.isEmpty()).toBeTruthy();
  });
});
