import {
  EBookingCriteriaCompleteZone,
  FvDate,
  Maybe,
} from '@discocil/fv-domain-library/domain';

import { BookingZone } from '@/bookings/bookingZones/domain/entities/BookingZoneEntity';
import { BookingMother } from '@tests/stubs/booking/BookingMother';
import { AVAILABLE_HOUR_1, BookingZoneMother } from '@tests/stubs/booking/BookingZoneMother';

import type {
  BookingSpacePrimitives,
  BookingSpacesPrimitives,
  BookingZonePrimitives,
} from '@/bookings/bookingZones/domain/entities/BookingZoneEntity';

const BOOKING_TIME_THAT_FITS = AVAILABLE_HOUR_1.toPrimitive();
const BOOKING_TIME_OVERLAPPING_FROM_BEHIND = AVAILABLE_HOUR_1.subtractMinutes(30).toPrimitive();
const BOOKING_TIME_OVERLAPPING_FROM_FRONT = AVAILABLE_HOUR_1.addMinutes(30).toPrimitive();
const BOOKING_TIME_THAT_DOES_NOT_FIT = AVAILABLE_HOUR_1.addHours(2).toPrimitive();

function getSpaceIdsFromBookingZone(bookingZone: BookingZone): string[] {
  const spaceIds: string[] = [];

  for (const [id] of bookingZone.getSpacesWithoutCombinations()) {
    spaceIds.push(id);
  }

  return spaceIds;
}

function generateCustomZoneWithBookingTime1Hour(customProps: Partial<BookingZonePrimitives> = {}): BookingZone {
  return BookingZoneMother.buildWithCustomData({
    ...customProps,
    bookingTime: Maybe.some(FvDate.createFromSeconds(FvDate.IN_SECONDS.ONE_HOUR).toPrimitive()),
  });
}

describe(`${BookingZone.name}`, () => {
  describe('with bookingTime 1h', () => {
    it('should return isComplete: false (there are no bookings)', async () => {
      const bookingZone = generateCustomZoneWithBookingTime1Hour();

      expect(bookingZone.isComplete).toBe(false);
    });

    it('should return isComplete: false (there are no bookings) but all spaces have no rate', async () => {
      const spaces: BookingSpacesPrimitives = new Map<string, BookingSpacePrimitives>();

      const spaceA = BookingZoneMother.generateSpaceWithoutRate();
      const spaceB = BookingZoneMother.generateSpaceWithoutRate();

      spaces.set(spaceA.id, spaceA);
      spaces.set(spaceB.id, spaceB);

      const bookingZone = generateCustomZoneWithBookingTime1Hour({ spaces });

      expect(bookingZone.isComplete).toBe(false);
    });

    it('should return isComplete: false (the booking is unrelated)', async () => {
      const bookingZone = generateCustomZoneWithBookingTime1Hour();

      const spaceIds = getSpaceIdsFromBookingZone(bookingZone);

      const bookings = spaceIds.map(() => BookingMother.buildDefault());

      bookingZone.addBookings(bookings);

      expect(bookingZone.isComplete).toBe(false);
    });

    it('should return isComplete: false (the booking does not fit the available hour)', async () => {
      const bookingZone = generateCustomZoneWithBookingTime1Hour();

      const spaceIds = getSpaceIdsFromBookingZone(bookingZone);

      const bookings = spaceIds.map((_spaceId) => {
        return BookingMother.buildWithCustomData({
          spaceId: Maybe.some(_spaceId),
          time: Maybe.some(BOOKING_TIME_THAT_DOES_NOT_FIT),
        });
      });

      bookingZone.addBookings(bookings);

      expect(bookingZone.isComplete).toBe(false);
    });

    it('should return isComplete: true (a booking overlaps the hour from behind)', async () => {
      const bookingZone = generateCustomZoneWithBookingTime1Hour();

      const spaceIds = getSpaceIdsFromBookingZone(bookingZone);

      const bookings = spaceIds.map((_spaceId) => {
        return BookingMother.buildWithCustomData({
          spaceId: Maybe.some(_spaceId),
          time: Maybe.some(BOOKING_TIME_OVERLAPPING_FROM_BEHIND),
        });
      });

      bookingZone.addBookings(bookings);

      expect(bookingZone.isComplete).toBe(true);
    });

    it('should return isComplete: true (a booking overlaps the hour from front)', async () => {
      const bookingZone = generateCustomZoneWithBookingTime1Hour();

      const spaceIds = getSpaceIdsFromBookingZone(bookingZone);

      const bookings = spaceIds.map((_spaceId) => {
        return BookingMother.buildWithCustomData({
          spaceId: Maybe.some(_spaceId),
          time: Maybe.some(BOOKING_TIME_OVERLAPPING_FROM_FRONT),
        });
      });

      bookingZone.addBookings(bookings);

      expect(bookingZone.isComplete).toBe(true);
    });

    it('should return isComplete: false (spaceC is a booked combination relating spaceA + spaceB but in a different hour)', async () => {
      const spaces: BookingSpacesPrimitives = new Map<string, BookingSpacePrimitives>();

      const spaceA = BookingZoneMother.generateSpace();
      const spaceB = BookingZoneMother.generateSpace();
      const spaceC = BookingZoneMother.generateSpace([spaceA.id, spaceB.id]);

      spaces.set(spaceA.id, spaceA);
      spaces.set(spaceB.id, spaceB);
      spaces.set(spaceC.id, spaceC);

      const bookingZone = generateCustomZoneWithBookingTime1Hour({ spaces });

      const bookings = [];

      bookings.push(BookingMother.buildWithCustomData({
        spaceId: Maybe.some(spaceC.id),
        time: Maybe.some(BOOKING_TIME_THAT_DOES_NOT_FIT),
      }));

      bookingZone.addBookings(bookings);

      expect(bookingZone.isComplete).toBe(false);
    });

    it('should return isComplete: true (spaceC is a booked combination affecting spaceA + spaceB)', async () => {
      const spaces: BookingSpacesPrimitives = new Map<string, BookingSpacePrimitives>();

      const spaceA = BookingZoneMother.generateSpace();
      const spaceB = BookingZoneMother.generateSpace();
      const spaceC = BookingZoneMother.generateSpace([spaceA.id, spaceB.id]);

      spaces.set(spaceA.id, spaceA);
      spaces.set(spaceB.id, spaceB);
      spaces.set(spaceC.id, spaceC);

      const bookingZone = generateCustomZoneWithBookingTime1Hour({ spaces });

      const bookings = [];

      bookings.push(BookingMother.buildWithCustomData({
        spaceId: Maybe.some(spaceC.id),
        time: Maybe.some(BOOKING_TIME_THAT_FITS),
      }));

      bookingZone.addBookings(bookings);

      expect(bookingZone.isComplete).toBe(true);
    });

    it('should return isComplete: true', async () => {
      const bookingZone = generateCustomZoneWithBookingTime1Hour();

      const spaceIds = getSpaceIdsFromBookingZone(bookingZone);

      const bookings = spaceIds.map((_spaceId) => {
        return BookingMother.buildWithCustomData({
          spaceId: Maybe.some(_spaceId),
          time: Maybe.some(BOOKING_TIME_THAT_FITS),
        });
      });

      bookingZone.addBookings(bookings);

      expect(bookingZone.isComplete).toBe(true);
    });
  });


  describe('without bookingTime', () => {
    it('should return isComplete: false (there are no bookings)', async () => {
      const bookingZone = BookingZoneMother.buildDefault();

      expect(bookingZone.isComplete).toBe(false);
    });

    it('should return isComplete: false (the booking is unrelated)', async () => {
      const bookingZone = BookingZoneMother.buildDefault();

      const spaceIds = getSpaceIdsFromBookingZone(bookingZone);

      const bookings = spaceIds.map(() => BookingMother.buildDefault());

      bookingZone.addBookings(bookings);

      expect(bookingZone.isComplete).toBe(false);
    });

    it('should return isComplete: true (the booking occupies the only space)', async () => {
      const bookingZone = BookingZoneMother.buildDefault();

      const spaceIds = getSpaceIdsFromBookingZone(bookingZone);

      const bookings = spaceIds.map((_spaceId) => {
        return BookingMother.buildWithCustomData({ spaceId: Maybe.some(_spaceId) });
      });

      bookingZone.addBookings(bookings);

      expect(bookingZone.isComplete).toBe(true);
    });

    it('should return isComplete: true (the booking occupies the only space therefore isComplete should be true)', async () => {
      const bookingZone1 = BookingZoneMother.buildWithCustomData({ fieldLimit: EBookingCriteriaCompleteZone.SPACES });
      const bookingZone2 = BookingZoneMother.buildWithCustomData({ fieldLimit: EBookingCriteriaCompleteZone.TARGETED });
      const bookingZone3 = BookingZoneMother.buildWithCustomData({ fieldLimit: EBookingCriteriaCompleteZone.NEVER });

      const spaceIds1 = getSpaceIdsFromBookingZone(bookingZone1);
      const spaceIds2 = getSpaceIdsFromBookingZone(bookingZone2);
      const spaceIds3 = getSpaceIdsFromBookingZone(bookingZone3);

      const bookings1 = spaceIds1.map((_spaceId) => {
        return BookingMother.buildWithCustomData({ spaceId: Maybe.some(_spaceId) });
      });
      const bookings2 = spaceIds2.map((_spaceId) => {
        return BookingMother.buildWithCustomData({ spaceId: Maybe.some(_spaceId) });
      });
      const bookings3 = spaceIds3.map((_spaceId) => {
        return BookingMother.buildWithCustomData({ spaceId: Maybe.some(_spaceId) });
      });

      bookingZone1.addBookings(bookings1);
      bookingZone2.addBookings(bookings2);
      bookingZone3.addBookings(bookings3);

      expect(bookingZone1.isComplete).toBe(true);
      expect(bookingZone2.isComplete).toBe(true);
      expect(bookingZone3.isComplete).toBe(true);
    });
  });
});
