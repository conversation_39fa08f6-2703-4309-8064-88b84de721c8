import { FvDate } from '@discocil/fv-domain-library';

import { BookingSpaceAvailability } from '@/events/events/domain/entities/BookingSpaceAvailability';

describe('BookingSpaceAvailability', () => {
  const currentDate = FvDate.create();
  const futureDate = currentDate.addSeconds(120);
  const pastDate = currentDate.subtractSeconds(120);


  const validPrimitives = {
    id: 'availability-id-123',
    token: 'token-id-456',
    expiredAt: futureDate.toMilliseconds(),
    collaboratorId: 'collab-id-789',
    eventId: 'event-id-987',
  };

  it('should create a valid instance using .build()', () => {
    const resultBookingSpaceAvailability = BookingSpaceAvailability.build(validPrimitives);

    expect(resultBookingSpaceAvailability.isRight()).toBe(true);
    const instance = resultBookingSpaceAvailability.value as BookingSpaceAvailability;

    expect(instance.token).toBe(validPrimitives.token);
    expect(instance.expiredAt).toBe(validPrimitives.expiredAt);
    expect(instance.collaboratorId).toBe(validPrimitives.collaboratorId);
    expect(instance.eventId).toBe(validPrimitives.eventId);
  });

  it('should serialize correctly with toJson()', () => {
    const resultBookingSpaceAvailability = BookingSpaceAvailability.build(validPrimitives);
    const entity = resultBookingSpaceAvailability.value as BookingSpaceAvailability;

    expect(entity.toPrimitive()).toEqual(validPrimitives);
  });

  it('should return true when expired', () => {
    const expiredEntity = BookingSpaceAvailability.build({
      ...validPrimitives,
      expiredAt: pastDate.toMilliseconds(),
    }).value as BookingSpaceAvailability;

    expect(expiredEntity.isExpired()).toBe(true);
  });

  it('should return false when not expired', () => {
    const validEntity = BookingSpaceAvailability.build(validPrimitives).value as BookingSpaceAvailability;

    expect(validEntity.isExpired()).toBe(false);
  });
});
