import { EventConfiguration } from '@/events/eventConfigurations/domain/entities/EventConfiguration';
import { EventConfigurationJsonMapper } from '@/events/eventConfigurations/domain/mappers/EventConfigurationJsonMapper';
import { EventConfigurationMother } from '@tests/stubs/event/EventConfigurationMother';

describe(`${EventConfiguration.name}`, () => {
  const eventConfigurationOrError = EventConfigurationMother.buildDefault();

  it('should serialize and deserialize an event configuration', () => {
    expect(eventConfigurationOrError.isRight()).toBeTruthy();
    const eventConfiguration = eventConfigurationOrError.value as EventConfiguration;

    const json = EventConfigurationJsonMapper.toJson(eventConfiguration);

    expect(json.id).toBe(eventConfiguration.id);
    expect(json.eventId).toBe(eventConfiguration.eventId);
    expect(json.key).toBe(eventConfiguration.key);
    expect(json.value).toStrictEqual(eventConfiguration.value);
    expect(json.ticketCancellationTime).toBe(eventConfiguration.ticketCancellationTime);
    expect(json.createdAt).toBe(eventConfiguration.createdAt);
    expect(json.createdBy).toBe(eventConfiguration.createdBy);
    expect(json.updatedAt).toBe(eventConfiguration.updatedAt);
    expect(json.updatedBy).toBe(eventConfiguration.updatedBy);
    expect(json.removedAt).toBe(eventConfiguration.removedAt);
    expect(json.removedBy).toBe(eventConfiguration.removedBy);

    const entityOrError = EventConfigurationJsonMapper.toEntity(json);

    expect(entityOrError.isRight()).toBeTruthy();
  });
});
