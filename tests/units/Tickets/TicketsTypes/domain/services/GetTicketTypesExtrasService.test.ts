import {
  Collection,
  EMic<PERSON>iteChannel,
  left,
  Maybe,
  NotFoundError,
  right,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { QuantitySelector } from '@/tickets/ticketsTypes/domain/services/QuantitySelector';
import { Disponibility } from '@/tickets/ticketsTypes/domain/value-objects/Disponibility';
import { EventMother } from '@tests/stubs/event/EventMother';
import { OrganizationMother } from '@tests/stubs/organization/OrganizationMother';
import { TicketTypeMother } from '@tests/stubs/ticketType/TicketTypeMother';
import { AssignTicketTypeLimitsService } from '@/tickets/ticketsTypes/infrastructure/services/AssignTicketTypeLimitsService';
import { GetTicketTypesExtrasService } from '@/tickets/ticketsTypes/domain/services/GetTicketTypesExtrasService';
import { TicketTypeOptionMother } from '@tests/stubs/ticketType/TicketTypeOptionMother';

import type { IGetOptionsWithConfigService } from '@/tickets/ticketsTypes/domain/contracts/GetOptionsWithConfigContracts';
import type { Paylink } from '@/paylinks/domain/entities/Paylink';
import type { TicketTypeOptions } from '@/tickets/ticketsTypes/domain/entities/TicketTypeOption';
import type { AssignTicketTypeLimitsResponse, IAssignTicketTypeLimitsService } from '@/tickets/ticketsTypes/domain/contracts/AssignTicketTypeLimitsContracts';
import type { EventGroup } from '@/events/events/domain/entities/EventGroup';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type {
  DisponibilityTypeResponse,
  TicketTypesExtras,
} from '@/tickets/ticketsTypes/domain/contracts/TicketsTypesContracts';
import type {
  TicketTypes,
  TicketType,
} from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { GetTicketTypesExtrasServiceRequest } from '@/tickets/ticketsTypes/domain/contracts/GetTicketTypesExtrasContracts';

describe(`${GetTicketTypesExtrasService.name}`, () => {
  const assignTicketTypeLimitsService = mock<IAssignTicketTypeLimitsService>();
  const getOptionsWithConfigService = mock<IGetOptionsWithConfigService>();

  const organization = OrganizationMother.buildDefault();
  const event = EventMother.buildWithCustomData({ organizationId: organization.id });

  const ticketType1 = TicketTypeMother.createPublicPassingAllValidation();
  const ticketType2 = TicketTypeMother.createPublicPassingAllValidation();
  const ticketType3 = TicketTypeMother.createPublicPassingAllValidation();

  const ticketTypes: TicketTypes = new Map<IdPrimitive, TicketType>([
    [ticketType1.id, ticketType1],
    [ticketType2.id, ticketType2],
    [ticketType3.id, ticketType3],
  ]);

  const service = new GetTicketTypesExtrasService(getOptionsWithConfigService, assignTicketTypeLimitsService);

  const dto: GetTicketTypesExtrasServiceRequest = {
    event,
    ticketTypes,
    channel: {
      type: EMicrositeChannel.ORGANIZATION,
      channel: organization,
      organizations: new Map<IdPrimitive, Organization>(),
      eventGroups: new Map<IdPrimitive, EventGroup>(),
    },
    paylink: Maybe.none<Paylink>(),
  };


  const ticketTypeOptions = Collection.build([TicketTypeOptionMother.option(ticketType1.currency)]);

  const ticketTypesOptionsWithConfigResult = new Map<IdPrimitive, TicketTypeOptions>([
    [ticketType1.id, ticketTypeOptions],
    [ticketType2.id, ticketTypeOptions],
    [ticketType3.id, ticketTypeOptions],
  ]);

  const disponibilityByOptionId = new Map<string, DisponibilityTypeResponse>();

  disponibilityByOptionId.set(ticketTypeOptions.toArray()[0]!.id, {
    value: Disponibility.buildDefault().typeValue as number,
    isActive: true,
    isPercentageExceed: Maybe.none<boolean>(),
  });

  const ticketTypeLimits: AssignTicketTypeLimitsResponse = {
    areFewLeft: false,
    isSoldOut: false,
    maximum: 0,
    quantitySelector: QuantitySelector.build(),
    disponibility: {
      value: Disponibility.buildDefault().typeValue as number,
      isActive: true,
      isPercentageExceed: Maybe.none<boolean>(),
    },
    disponibilityByOptionId,
  };

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should return a Unexpected Error', async () => {
    getOptionsWithConfigService.execute.mockResolvedValue(right(ticketTypesOptionsWithConfigResult));

    const unexpectedError = UnexpectedError.build({ context: GetTicketTypesExtrasService.name });

    assignTicketTypeLimitsService.execute.mockResolvedValue(left(unexpectedError));

    const serviceResult = await service.execute(dto);

    expect(serviceResult.isLeft()).toBeTruthy();
    expect(serviceResult.isRight()).toBeFalsy();

    expect(serviceResult.value).toBeInstanceOf(UnexpectedError);

    const errorResponse = serviceResult.value as UnexpectedError;

    expect(errorResponse.message).toStrictEqual(unexpectedError.message);
    expect(errorResponse.cause).toStrictEqual(unexpectedError.cause);
  });

  it('should return not found error', async () => {
    getOptionsWithConfigService.execute.mockResolvedValue(right(ticketTypesOptionsWithConfigResult));

    const assignTicketTypeLimitsServiceError = NotFoundError.build({
      context: AssignTicketTypeLimitsService.name,
      target: AssignTicketTypeLimitsService.name,
    });

    assignTicketTypeLimitsService.execute.mockResolvedValue(left(assignTicketTypeLimitsServiceError));

    const serviceResult = await service.execute(dto);

    expect(serviceResult.isLeft()).toBeTruthy();
    expect(serviceResult.value).toBeInstanceOf(NotFoundError);
  });

  it('should return a map with the 3 ticket types', async () => {
    getOptionsWithConfigService.execute.mockResolvedValue(right(ticketTypesOptionsWithConfigResult));
    assignTicketTypeLimitsService.execute.mockResolvedValue(right(ticketTypeLimits));

    const serviceResult = await service.execute(dto);

    expect(serviceResult.isRight()).toBeTruthy();

    const response = serviceResult.value as TicketTypesExtras;

    expect(response).toBeInstanceOf(Map);
    expect(response.size).toBe(3);
  });
});
