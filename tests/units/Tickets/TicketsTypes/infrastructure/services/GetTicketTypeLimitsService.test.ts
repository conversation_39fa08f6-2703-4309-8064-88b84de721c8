import {
  E<PERSON><PERSON><PERSON>iteChannel,
  Maybe,
  UnexpectedError,
  left,
  right,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import { GetTicketTypeLimitsService } from '@/tickets/ticketsTypes/infrastructure/services/GetTicketTypeLimitsService';
import { TicketLimit } from '@/tickets/ticketLimits/domain/entities/TicketLimit';
import { Ticket } from '@/tickets/tickets/domain/entities/TicketEntity';
import { OrganizationMother } from '@tests/stubs/organization/OrganizationMother';
import { TicketLimitMother } from '@tests/stubs/ticket/TicketLimitMother';
import { TicketMother } from '@tests/stubs/ticket/TicketMother';
import { TicketOrganizationLimitMother } from '@tests/stubs/ticket/TicketOrganizationLimitMother';
import { TicketTypeMother } from '@tests/stubs/ticketType/TicketTypeMother';
import { UserMother } from '@tests/stubs/user/UserMother';

import type { TicketTypeLimitsRequest, TicketTypeLimitsResponse } from '@/tickets/ticketsTypes/domain/contracts/GetTicketTypeLimitsContracts';
import type { EventGroup } from '@/events/events/domain/entities/EventGroup';
import type { MicrositeChannel } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { TicketLimitRepository } from '@/tickets/ticketLimits/domain/contracts/TicketLimitRepository';
import type { TicketOrganizationLimitRepository } from '@/tickets/ticketOrganizationLimit/domain/contracts/TicketOrganizationLimitRepository';
import type { TicketRepository } from '@/tickets/tickets/domain/contracts/TicketRepository';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

describe(`${GetTicketTypeLimitsService.name}`, () => {
  const ticketRepository = mock<TicketRepository>();
  const ticketLimitRepository = mock<TicketLimitRepository>();
  const organizationRepository = mock<OrganizationRepository>();
  const ticketOrganizationLimitRepository = mock<TicketOrganizationLimitRepository>();
  const organization = OrganizationMother.buildDefault();
  const organizations = new Map([[organization.id, organization]]);
  const ticket = TicketMother.buildWithCustomData({ organizationId: organization.id });
  const tickets = new Map([[ticket.id, ticket]]);
  const ticketLimit = TicketLimitMother.buildWithCustomData({ organizationId: Maybe.fromValue(organization.id) });
  const user = UserMother.buildWithCustomData({ organizations: [organization.id] });
  const ticketsTotals = TicketMother.buildTotals();
  const ticketOrganizationLimit = TicketOrganizationLimitMother.buildDefault();

  const service = new GetTicketTypeLimitsService(
    ticketRepository,
    ticketLimitRepository,
    organizationRepository,
    ticketOrganizationLimitRepository,
  );

  const channel = (micrositeChannel: EMicrositeChannel): MicrositeChannel => {
    return {
      type: micrositeChannel,
      channel: micrositeChannel === EMicrositeChannel.ORGANIZATION ? organization : user,
      organizations: new Map<IdPrimitive, Organization>(),
      eventGroups: new Map<IdPrimitive, EventGroup>(),
    };
  };

  const dto = (micrositeChannel: EMicrositeChannel = EMicrositeChannel.ORGANIZATION): TicketTypeLimitsRequest => {
    return {
      ticketType: TicketTypeMother.createPublicActive(),
      channel: channel(micrositeChannel),
    };
  };

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('Should return unexpected error from TicketLimit', async () => {
    const mapperError = MapperError.build({ context: TicketLimit.name, target: TicketLimit.name });

    ticketLimitRepository.find.mockResolvedValue(left(mapperError));

    const serviceResult = await service.execute(dto());

    expect(serviceResult.isRight()).toBeFalsy();
    expect(serviceResult.value).toBeInstanceOf(MapperError);
  });

  it('Should return unexpected error from Ticket Get totals', async () => {
    const unexpectedError = UnexpectedError.build({ context: Ticket.name });

    ticketLimitRepository.find.mockResolvedValue(right(ticketLimit));
    ticketRepository.getTotals.mockResolvedValue(right(ticketsTotals));
    ticketRepository.search.mockResolvedValue(left(unexpectedError));
    ticketOrganizationLimitRepository.find.mockResolvedValue(right(ticketOrganizationLimit));
    organizationRepository.search.mockResolvedValue(right(organizations));

    const serviceResult = await service.execute(dto(EMicrositeChannel.REFERRER));

    expect(serviceResult.isRight()).toBeFalsy();
    expect(serviceResult.value).toBeInstanceOf(UnexpectedError);
  });

  it('Should return a correct answer with organization', async () => {
    ticketLimitRepository.find.mockResolvedValue(right(ticketLimit));
    ticketRepository.search.mockResolvedValue(right({ tickets }));
    ticketRepository.getTotals.mockResolvedValue(right(ticketsTotals));
    ticketLimitRepository.find.mockResolvedValue(right(ticketLimit));
    organizationRepository.search.mockResolvedValue(right(organizations));
    ticketOrganizationLimitRepository.find.mockResolvedValue(right(ticketOrganizationLimit));

    const serviceResult = await service.execute(dto());

    expect(serviceResult.isRight()).toBeTruthy();

    const ticketTypeLimits = serviceResult.value as TicketTypeLimitsResponse;

    expect(ticketTypeLimits).toHaveProperty('available');
    expect(ticketTypeLimits).toHaveProperty('totalSales');
    expect(ticketTypeLimits).toHaveProperty('maximum');
  });

  it('Should return a correct answer with referrer', async () => {
    ticketLimitRepository.find.mockResolvedValue(right(ticketLimit));
    ticketRepository.search.mockResolvedValue(right({ tickets }));
    ticketRepository.getTotals.mockResolvedValue(right(ticketsTotals));
    ticketLimitRepository.find.mockResolvedValue(right(ticketLimit));
    organizationRepository.search.mockResolvedValue(right(organizations));
    ticketOrganizationLimitRepository.find.mockResolvedValue(right(ticketOrganizationLimit));

    const serviceResult = await service.execute(dto(EMicrositeChannel.REFERRER));

    expect(serviceResult.isRight()).toBeTruthy();

    const ticketTypeLimits = serviceResult.value as TicketTypeLimitsResponse;

    expect(ticketTypeLimits).toHaveProperty('available');
    expect(ticketTypeLimits).toHaveProperty('totalSales');
    expect(ticketTypeLimits).toHaveProperty('maximum');
  });
});
