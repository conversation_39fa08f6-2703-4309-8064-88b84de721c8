import {
  E<PERSON><PERSON><PERSON>iteChannel,
  Maybe,
  NotFoundError,
  left,
  right,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { AssignTicketTypeLimitsService } from '@/tickets/ticketsTypes/infrastructure/services/AssignTicketTypeLimitsService';
import { GetTicketTypeLimitsService } from '@/tickets/ticketsTypes/infrastructure/services/GetTicketTypeLimitsService';
import { OrganizationMother } from '@tests/stubs/organization/OrganizationMother';
import { TicketTypeMother } from '@tests/stubs/ticketType/TicketTypeMother';
import { TicketTypeOptionMother } from '@tests/stubs/ticketType/TicketTypeOptionMother';

import type { Paylink } from '@/paylinks/domain/entities/Paylink';
import type { TicketTypeOption } from '@/tickets/ticketsTypes/domain/entities/TicketTypeOption';
import type { AssignTicketTypeLimitsRequest, AssignTicketTypeLimitsResponse } from '@/tickets/ticketsTypes/domain/contracts/AssignTicketTypeLimitsContracts';
import type { IGetTicketTypeLimitsService, TicketTypeLimitsResponse } from '@/tickets/ticketsTypes/domain/contracts/GetTicketTypeLimitsContracts';
import type { EventGroup } from '@/events/events/domain/entities/EventGroup';
import type { MicrositeChannel } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

describe(`${AssignTicketTypeLimitsService.name}`, () => {
  const getTicketTypeLimitsService = mock<IGetTicketTypeLimitsService>();
  const organization = OrganizationMother.buildDefault();
  const ticketType = TicketTypeMother.createLimitedActiveAndIncomplete();

  const channel: MicrositeChannel = {
    channel: organization,
    type: EMicrositeChannel.ORGANIZATION,
    organizations: new Map<IdPrimitive, Organization>(),
    eventGroups: new Map<IdPrimitive, EventGroup>(),
  };

  const options: TicketTypeOption[] = [TicketTypeOptionMother.option(ticketType.currency)];

  const service = new AssignTicketTypeLimitsService(getTicketTypeLimitsService);

  const dto: AssignTicketTypeLimitsRequest = {
    ticketType,
    channel,
    options,
    currentOption: Maybe.none<TicketTypeOption>(),
    paylink: Maybe.none<Paylink>(),
  };

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('Should return Not found error from get ticket type limit service', async () => {
    const notFoundError = NotFoundError.build({ context: GetTicketTypeLimitsService.name, target: GetTicketTypeLimitsService.name });

    getTicketTypeLimitsService.execute.mockResolvedValue(left(notFoundError));

    const serviceResult = await service.execute(dto);

    expect(serviceResult.isLeft()).toBeTruthy();
    expect(serviceResult.value).toBeInstanceOf(NotFoundError);

    const errorResponse = serviceResult.value as NotFoundError;

    expect(errorResponse.message).toStrictEqual(notFoundError.message);
    expect(errorResponse.cause).toStrictEqual(notFoundError.cause);
  });

  it('Should return  TicketTypeLimits', async () => {
    const response: TicketTypeLimitsResponse = TicketTypeMother.createTicketTypeLimits();

    getTicketTypeLimitsService.execute.mockResolvedValue(right(response));

    const serviceResult = await service.execute(dto);

    expect(serviceResult.isRight()).toBeTruthy();

    const assignTicketTypeLimits = serviceResult.value as AssignTicketTypeLimitsResponse;

    expect(assignTicketTypeLimits).toHaveProperty('areFewLeft');
    expect(assignTicketTypeLimits).toHaveProperty('isSoldOut');
    expect(assignTicketTypeLimits).toHaveProperty('maximum');
  });
});
