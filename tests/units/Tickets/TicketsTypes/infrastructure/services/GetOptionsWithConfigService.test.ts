import {
  EGGDD,
  FvObject,
  FvString,
  Maybe,
  UnexpectedError,
  left,
  right,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { TicketMongoRepository } from '@/tickets/tickets/infrastructure/database/repositories/TicketMongoRepository';
import { GetOptionsWithConfigService } from '@/tickets/ticketsTypes/infrastructure/services/GetOptionsWithConfigService';
import { EventMother } from '@tests/stubs/event/EventMother';
import { TicketTypeMother } from '@tests/stubs/ticketType/TicketTypeMother';

import type { TicketRepository } from '@/tickets/tickets/domain/contracts/TicketRepository';
import type { GetOptionsWithConfigRequest } from '@/tickets/ticketsTypes/domain/contracts/GetOptionsWithConfigContracts';
import type { TicketType } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type { TicketTypeOptions } from '@/tickets/ticketsTypes/domain/entities/TicketTypeOption';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

const isStringOrNull = (value: unknown): value is string | null => FvObject.isNull(value) || FvString.is(value);

describe(`${GetOptionsWithConfigService.name}`, () => {
  const ticketRepository = mock<TicketRepository>();
  const service = new GetOptionsWithConfigService(ticketRepository);

  const ticketTypes = new Map<IdPrimitive, TicketType>();

  const ticketType = TicketTypeMother.createPublicPassingAllValidation();

  ticketTypes.set(ticketType.id, ticketType);

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should return error from repository', async () => {
    const unexpectedError = UnexpectedError.build({ context: TicketMongoRepository.name });

    ticketRepository.numberOfTicketsSold.mockResolvedValue(left(unexpectedError));

    const request: GetOptionsWithConfigRequest = {
      ticketTypes,
      event: EventMother.buildDefault(),
    };

    const serviceResult = await service.execute(request);

    expect(serviceResult.isLeft()).toBeTruthy();
    expect(serviceResult.isRight()).toBeFalsy();

    expect(serviceResult.value).toBeInstanceOf(UnexpectedError);

    const errorResponse = serviceResult.value as UnexpectedError;

    expect(errorResponse.message).toStrictEqual(unexpectedError.message);
    expect(errorResponse.cause).toStrictEqual(unexpectedError.cause);
  });

  it('should return success', async () => {
    const request: GetOptionsWithConfigRequest = {
      ticketTypes,
      event: EventMother.buildDefault(),
    };

    const repositoryResponse = ticketType.options.map((option) => {
      return {
        id: option.id,
        totalSales: 25,
      };
    });

    ticketRepository.numberOfTicketsSold.mockResolvedValue(right(repositoryResponse));

    const serviceResult = await service.execute(request);

    expect(serviceResult.isLeft()).toBeFalsy();
    expect(serviceResult.isRight()).toBeTruthy();

    const ticketTypesOptionsWithConfig = serviceResult.value as Map<IdPrimitive, TicketTypeOptions>;

    const options = ticketTypesOptionsWithConfig.values().next().value as TicketTypeOptions;

    expect(Array.isArray(options.toArray())).toBeTruthy();

    const isEnumValue = (value: EGGDD): void => {
      expect(Object.values(EGGDD).includes(value)).toBe(true);
    };

    const optionValidator = expect.objectContaining({
      id: expect.any(String),
      price: expect.any(Number),
      ggddType: expect.any(String),
      ggddAmount: expect.any(Number),
      age: expect.any(Number),
      content: expect.any(Maybe),
      additionalInfo: expect.any(Maybe),
      to: expect.any(Date),
      max: expect.any(Number),
      image: expect.any(Maybe),
    });

    const configValidator = expect.objectContaining({
      availableAmount: expect.any(Number),
      isFuture: expect.any(Boolean),
      isCurrent: expect.any(Boolean),
      totalSold: expect.any(Number),
    });

    const validateStringOrNull = (value: string | null): void => {
      expect(isStringOrNull(value)).toBe(true);
    };

    options.toArray().forEach((option) => {
      expect(option).toEqual(optionValidator);
      expect(option.config.fold(() => null, value => value)).toEqual(configValidator);

      isEnumValue(option.ggddType);
      validateStringOrNull(option.content.fold(() => null, value => value));
      validateStringOrNull(option.additionalInfo.fold(() => null, value => value));
      validateStringOrNull(option.image.fold(() => null, value => value));
    });
  });
});
