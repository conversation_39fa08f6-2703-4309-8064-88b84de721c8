import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { CreateSubscriptionsUseCase } from '@/tickets/tickets/application/use-cases/CreateSubscriptionsUseCase';
import { SubscriptionMother } from '@tests/stubs/subscription/SubscriptionMother';
import { Subscription } from '@/subscription/domain/entities/SubscriptionEntity';
import { SubscriptionMongoRepository } from '@/subscription/infrastructure/database/repositories/SubscriptionMongoRepository';
import { SubscribeService } from '@/tickets/tickets/infrastructure/services/SubscribeService';

import type { SubscriptionRepository } from '@/subscription/domain/contracts/SubscriptionRepository';

describe(`${CreateSubscriptionsUseCase.name}`, () => {
  const subscriptionRepository = mock<SubscriptionRepository>();

  const subscribeService = new SubscribeService(subscriptionRepository);

  const useCase = new CreateSubscriptionsUseCase(subscribeService, subscriptionRepository);

  const subscription = SubscriptionMother.buildDefault();

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should return the Subscription when created if subscription is not found', async () => {
    const notFoundError = NotFoundError.build({
      context: SubscriptionMongoRepository.name,
      target: Subscription.name,
    });

    subscriptionRepository.find.mockResolvedValue(left(notFoundError));
    subscriptionRepository.save.mockResolvedValue(undefined);

    const useCaseResult = await useCase.execute({
      ip: subscription.ip,
      applicationId: subscription.applicationId,
      referrerId: subscription.referrerId,
      name: subscription.name,
      email: subscription.email,
      phone: subscription.phone,
      organizationId: subscription.organizationId,
    });

    expect(useCaseResult.isLeft()).toBeFalsy();
    expect(useCaseResult.isRight()).toBeTruthy();

    expect(useCaseResult.value).toBeInstanceOf(Map<string, Subscription>);

    const subscriptions = useCaseResult.value as Map<string, Subscription>;
    const suscriptionResult = subscriptions.values().next().value!;

    expect(suscriptionResult).toBeDefined();

    expect(suscriptionResult.ip.fold(() => null, item => item)).toBe(subscription.ip.fold(() => null, item => item));
    expect(suscriptionResult.applicationId).toBe(subscription.applicationId);
    expect(suscriptionResult.referrerId.fold(() => null, item => item)).toBe(subscription.referrerId.fold(() => null, item => item));
    expect(suscriptionResult.name.fold(() => null, item => item)).toBe(subscription.name.fold(() => null, item => item));
    expect(suscriptionResult.email).toBe(subscription.email);
    expect(suscriptionResult.phone.fold(() => null, item => item)).toBe(subscription.phone.fold(() => null, item => item));
    expect(suscriptionResult.organizationId).toBe(subscription.organizationId);
  });

  it('should return the Subscription when created if the subscription is found', async () => {
    subscriptionRepository.find.mockResolvedValue(right(subscription));
    subscriptionRepository.save.mockResolvedValue(undefined);

    const useCaseResult = await useCase.execute({
      ip: subscription.ip,
      applicationId: subscription.applicationId,
      referrerId: subscription.referrerId,
      name: subscription.name,
      email: subscription.email,
      phone: subscription.phone,
      organizationId: subscription.organizationId,
    });

    expect(useCaseResult.isLeft()).toBeFalsy();
    expect(useCaseResult.isRight()).toBeTruthy();

    expect(useCaseResult.value).toBeInstanceOf(Map<string, Subscription>);

    const subscriptions = useCaseResult.value as Map<string, Subscription>;
    const suscriptionResult = subscriptions.get(subscription.id)!;

    expect(suscriptionResult).toBeDefined();

    expect(suscriptionResult.ip.fold(() => null, item => item)).toBe(subscription.ip.fold(() => null, item => item));
    expect(suscriptionResult.applicationId).toBe(subscription.applicationId);
    expect(suscriptionResult.referrerId.fold(() => null, item => item)).toBe(subscription.referrerId.fold(() => null, item => item));
    expect(suscriptionResult.name.fold(() => null, item => item)).toBe(subscription.name.fold(() => null, item => item));
    expect(suscriptionResult.email).toBe(subscription.email);
    expect(suscriptionResult.phone.fold(() => null, item => item)).toBe(subscription.phone.fold(() => null, item => item));
    expect(suscriptionResult.organizationId).toBe(subscription.organizationId);
  });
});
