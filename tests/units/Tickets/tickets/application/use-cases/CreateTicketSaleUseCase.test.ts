import {
  NotFoundError,
  left,
  right,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { CreateTicketSaleUseCase } from '@/tickets/tickets/application/use-cases/CreateTicketSaleUseCase';
import { EventMother } from '@tests/stubs/event/EventMother';
import { TicketTypeMother } from '@tests/stubs/ticketType/TicketTypeMother';
import { EventMongoRepository } from '@/events/events/infrastructure/database/repositories/EventMongoRepository';
import { TicketMother } from '@tests/stubs/ticket/TicketMother';
import { TicketType } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import { TicketTypeMongoRepository } from '@/tickets/ticketsTypes/infrastructure/database/repositories/TicketTypeMongoRepository';
import { Sale } from '@/sale/domain/entities/Sale';
import { SaleMother } from '@tests/stubs/Sale/SaleMother';

import type { TicketTypeRepository } from '@/tickets/ticketsTypes/domain/contracts/TicketTypeRepository';
import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type { SaleRepository } from '@/sale/domain/contracts/SaleRepository';
import type { ISalesService } from '@/tickets/tickets/domain/contracts/PurchaseContracts';

describe(`${CreateTicketSaleUseCase.name}`, () => {
  const eventRepository = mock<EventRepository>();
  const ticketTypeRepository = mock<TicketTypeRepository>();
  const salesService = mock<ISalesService>();
  const saleRepository = mock<SaleRepository>();

  const useCase = new CreateTicketSaleUseCase(eventRepository, ticketTypeRepository, salesService, saleRepository);

  const event = EventMother.buildDefault();
  const ticketType = TicketTypeMother.createPublicPassingAllValidation();
  const ticket = TicketMother.createDefault();
  const sale = SaleMother.createDefault();

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should throw NotFoundError when event not found', async () => {
    const notFoundError = NotFoundError.build({
      context: EventMongoRepository.name,
      target: Event.name,
    });

    eventRepository.find.mockResolvedValue(left(notFoundError));
    ticketTypeRepository.find.mockResolvedValue(right(ticketType));

    const useCaseResult = await useCase.execute({ ticket });

    expect(useCaseResult.isLeft()).toBeTruthy();
    expect(useCaseResult.isRight()).toBeFalsy();

    expect(useCaseResult.value).toBeInstanceOf(NotFoundError);
  });

  it('should throw NotFoundError when ticket type not found', async () => {
    const notFoundError = NotFoundError.build({
      context: TicketTypeMongoRepository.name,
      target: TicketType.name,
    });

    eventRepository.find.mockResolvedValue(right(event));
    ticketTypeRepository.find.mockResolvedValue(left(notFoundError));

    const useCaseResult = await useCase.execute({ ticket });

    expect(useCaseResult.isLeft()).toBeTruthy();
    expect(useCaseResult.isRight()).toBeFalsy();

    expect(useCaseResult.value).toBeInstanceOf(NotFoundError);
  });

  it('should return the Sale when created', async () => {
    eventRepository.find.mockResolvedValue(right(event));
    ticketTypeRepository.find.mockResolvedValue(right(ticketType));
    salesService.execute.mockResolvedValue(right(new Map([[sale.id, sale]])));
    saleRepository.save.mockResolvedValue(undefined);

    const useCaseResult = await useCase.execute({ ticket });

    expect(useCaseResult.isLeft()).toBeFalsy();
    expect(useCaseResult.isRight()).toBeTruthy();

    expect(useCaseResult.value).toBeInstanceOf(Sale);
  });
});
