import {
  EDocumentType,
  PersonalDocument,
  PhoneNumber,
} from '@discocil/fv-domain-library/domain';

import { AssistantDataError } from '@/tickets/tickets/domain/errors/AssistantDataError';
import { CheckAssistantDataService } from '@/tickets/tickets/infrastructure/services/CheckAssistantDataService';
import { MotherCreator } from '@tests/stubs/MotherCreator';
import { CheckAssistantDataRequestMother } from '@tests/stubs/ticket/CheckAssistantDataRequestMother';

import type { Assistant } from '@/tickets/tickets/domain/entities/Assistant';
import type { FvError } from '@discocil/fv-domain-library/domain';

const testName = `${CheckAssistantDataService.name}`;

describe(`${CheckAssistantDataService.name}`, () => {
  const service = new CheckAssistantDataService();

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should return error when a phone number of one of the assistants is invalid', async () => {
    const badPhone = '123';
    const request = CheckAssistantDataRequestMother.makeDefault();

    const firstAssistant = request.assistants.toArray().at(0) as Assistant;

    firstAssistant.setPhone(badPhone);

    const serviceResult = await service.execute(request);
    const phoneResult = PhoneNumber.build(badPhone);

    expect(phoneResult.isLeft()).toBeTruthy();

    const assistantDataError = AssistantDataError.phone({
      context: testName,
      message: (phoneResult.value as FvError).message,
      target: 1,
    });
    const errorResponse = serviceResult.value as AssistantDataError;

    expect(serviceResult.isLeft()).toBeTruthy();
    expect(serviceResult.isRight()).toBeFalsy();

    expect(serviceResult.value).toBeInstanceOf(AssistantDataError);
    expect(errorResponse.message).toStrictEqual(assistantDataError.message);
    expect(errorResponse.cause).toStrictEqual(assistantDataError.cause);
  });

  it('should return error when the document number of one of the assistants is invalid', async () => {
    const badPersonalDocument = {
      type: EDocumentType.NIE,
      number: MotherCreator.random().number.int().toString(),
    };

    const request = CheckAssistantDataRequestMother.makeDefault();

    const lastAssistant = request.assistants.toArray().at(request.assistants.length() - 1) as Assistant;

    lastAssistant.setPersonalDocument(badPersonalDocument);

    const serviceResult = await service.execute(request);
    const personalDocumentResult = PersonalDocument.build(badPersonalDocument.type, badPersonalDocument.number);

    expect(serviceResult.isLeft()).toBeTruthy();
    expect(serviceResult.isRight()).toBeFalsy();

    const assistantDataError = AssistantDataError.documentNumber(
      {
        context: testName,
        message: (personalDocumentResult.value as FvError).message,
        target: request.assistants.length(),
      },
    );
    const errorResponse = serviceResult.value as AssistantDataError;

    expect(serviceResult.value).toBeInstanceOf(AssistantDataError);
    expect(errorResponse.message).toStrictEqual(assistantDataError.message);
    expect(errorResponse.cause).toStrictEqual(assistantDataError.cause);
  });

  it('should return success', async () => {
    const request = CheckAssistantDataRequestMother.makeDefault();
    const serviceResult = await service.execute(request);

    expect(serviceResult.isLeft()).toBeFalsy();
    expect(serviceResult.isRight()).toBeTruthy();

    expect(serviceResult.value).toStrictEqual(true);
  });
});
