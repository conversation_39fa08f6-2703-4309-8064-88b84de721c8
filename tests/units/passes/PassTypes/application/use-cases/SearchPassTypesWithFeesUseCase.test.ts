import {
  Maybe,
  UnexpectedError,
  UniqueEntityID,
  left,
  right,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { SearchPassTypesWithFeesUseCase } from '@/passes/passTypes/application/SearchPassTypesWithFeesUseCase';
import { PaginationMother } from '@tests/stubs/PaginationMother';
import { PassTypeMother } from '@tests/stubs/Pass/PassType/PassTypeMother';

import type { ShouldShowFeesUpfrontService } from '@/organizations/organizationConfigurations/domain/services/ShouldShowFeesUpfront';
import type { SearchPassTypesUseCase } from '@/passes/passTypes/application/SearchPassTypesUseCase';
import type { SearchPassTypesDto } from '@/passes/passTypes/domain/contracts/SearchPassTypesContract';
import type { SearchPassTypesType } from '@/passes/passTypes/domain/entities/PassType';
import type { ApplyFeesToPassTypes } from '@/passes/passTypes/domain/services/ApplyFeesToPassTypes';

describe(`${SearchPassTypesWithFeesUseCase.name}`, () => {
  const searchPassTypesUseCase = mock<SearchPassTypesUseCase>();
  const shouldShowFeesUpfrontService = mock<ShouldShowFeesUpfrontService>();
  const applyFeesToPassTypes = mock<ApplyFeesToPassTypes>();

  const useCase = new SearchPassTypesWithFeesUseCase(
    searchPassTypesUseCase,
    shouldShowFeesUpfrontService,
    applyFeesToPassTypes,
  );

  const organizationId = UniqueEntityID.create();
  const pagination = PaginationMother.buildMetadata();
  const passType = PassTypeMother.buildPublishedNotArchived({ organizationId: organizationId.toPrimitive() });
  const passTypes = new Map([[passType.id, passType]]);

  const searchRequest: SearchPassTypesDto = {
    organizationId: organizationId.toPrimitive(),
    eventId: Maybe.none(),
    pagination: PaginationMother.buildOption(),
  };

  const baseSearchResult = {
    passTypes,
    pagination,
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('when base search succeeds', () => {
    beforeEach(() => {
      searchPassTypesUseCase.execute.mockResolvedValue(right(baseSearchResult));
    });

    describe('when organization configuration indicates fees should NOT be shown upfront', () => {
      it('should return original result without applying fees', async () => {
        shouldShowFeesUpfrontService.execute.mockResolvedValue(false);

        const useCaseResult = await useCase.execute(searchRequest);

        expect(useCaseResult.isRight()).toBe(true);
        expect(useCaseResult.value).toEqual(baseSearchResult);
        expect(applyFeesToPassTypes.execute).not.toHaveBeenCalled();
      });
    });

    describe('when organization configuration indicates fees SHOULD be shown upfront', () => {
      beforeEach(() => {
        shouldShowFeesUpfrontService.execute.mockResolvedValue(true);
      });

      it('should apply fees to pass types and return updated result', async () => {
        const passTypesWithFees = new Map([[passType.id, passType]]);

        applyFeesToPassTypes.execute.mockReturnValue(right(passTypesWithFees));

        const useCaseResult = await useCase.execute(searchRequest);

        expect(useCaseResult.isRight()).toBe(true);
        const { passTypes } = useCaseResult.value as SearchPassTypesType;

        expect(passTypes).toEqual(passTypesWithFees);
      });

      it('should return error when ApplyFeesToPassTypes fails', async () => {
        const feeError = UnexpectedError.build({ context: 'ApplyFeesToPassTypes', target: 'execute' });

        applyFeesToPassTypes.execute.mockReturnValue(left(feeError));

        const useCaseResult = await useCase.execute(searchRequest);

        expect(useCaseResult.isLeft()).toBe(true);
        expect(useCaseResult.value).toBe(feeError);
      });
    });
  });
});
