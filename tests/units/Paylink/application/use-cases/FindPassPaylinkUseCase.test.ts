import {
  NotFoundError,
  left,
  right,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import { Organization } from '@/organizations/organizations/domain/entities/Organization';
import { FindPassPaylinkUseCase } from '@/paylinks/application/FindPassPaylinkUseCase';
import { Paylink } from '@/paylinks/domain/entities/Paylink';
import { PaylinkMapper } from '@/paylinks/infrastructure/database/mappers/PaylinkMapper';
import { PaylinkMongoRepository } from '@/paylinks/infrastructure/database/repositories/PaylinkMongoRepository';
import { OrganizationMother } from '@tests/stubs/organization/OrganizationMother';
import { PaylinkMother } from '@tests/stubs/paylinks/PaylinkMother';

import type { ResponseFindPassPaylinkSchemaDto } from '@/paylinks/domain/contracts/FindPassPaylinkResponse';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { PaylinkRepository } from '@/paylinks/domain/contracts/PaylinkRepository';

describe(`${FindPassPaylinkUseCase.name}`, () => {
  const paylinkRepository = mock<PaylinkRepository>();
  const organizationRepository = mock<OrganizationRepository>();

  const useCase = new FindPassPaylinkUseCase(paylinkRepository, organizationRepository);

  const organizationValid = OrganizationMother.buildDefault();
  const paylinkValid = PaylinkMother.buildWithCustomData({ organizationId: organizationValid.id });

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should throw NotFoundError when Paylink not found', async () => {
    const notFoundError = NotFoundError.build({ context: PaylinkMongoRepository.name, target: Paylink.name });

    paylinkRepository.find.mockResolvedValue(left(notFoundError));
    organizationRepository.find.mockResolvedValue(right(organizationValid));

    const useCaseResult = await useCase.execute({ activateCode: paylinkValid.id });

    expect(useCaseResult.isLeft()).toBeTruthy();
    expect(useCaseResult.isRight()).toBeFalsy();

    expect(useCaseResult.value).toBeInstanceOf(NotFoundError);
  });

  it('should throw MapperError when Paylink is not mapped', async () => {
    const mapperError = MapperError.build({
      context: PaylinkMapper.name,
      target: Paylink.name,
    });

    paylinkRepository.find.mockResolvedValue(left(mapperError));
    organizationRepository.find.mockResolvedValue(right(organizationValid));

    const useCaseResult = await useCase.execute({ activateCode: paylinkValid.id });

    expect(useCaseResult.isLeft()).toBeTruthy();
    expect(useCaseResult.isRight()).toBeFalsy();
  });

  it('should return the Paylink when found', async () => {
    paylinkRepository.find.mockResolvedValue(right(paylinkValid));
    organizationRepository.find.mockResolvedValue(right(organizationValid));

    const useCaseResult = await useCase.execute({ activateCode: paylinkValid.activateCode });

    expect(useCaseResult.isLeft()).toBeFalsy();
    expect(useCaseResult.isRight()).toBeTruthy();

    const { paylink, organization } = useCaseResult.value as ResponseFindPassPaylinkSchemaDto;

    expect(paylink).toBeInstanceOf(Paylink);
    expect(organization).toBeInstanceOf(Organization);
  });
});
