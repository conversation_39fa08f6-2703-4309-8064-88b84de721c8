import {
  Collection,
  EMicrositeChannel,
  EPaylinkStatus,
  Maybe,
  NotFoundError,
  left,
  right,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import { EventEntity } from '@/events/events/domain/entities/EventEntity';
import { LocationFinder } from '@/locations/domain/services/LocationFinder';
import { Organization } from '@/organizations/organizations/domain/entities/Organization';
import { FindTicketPaylinkUseCase } from '@/paylinks/application/FindTicketPaylinkUseCase';
import { Paylink } from '@/paylinks/domain/entities/Paylink';
import { PaylinkAlreadyUsed } from '@/paylinks/errors/PaylinkAlreadyUsed';
import { PaylinkMapper } from '@/paylinks/infrastructure/database/mappers/PaylinkMapper';
import { PaylinkMongoRepository } from '@/paylinks/infrastructure/database/repositories/PaylinkMongoRepository';
import { QuantitySelector } from '@/tickets/ticketsTypes/domain/services/QuantitySelector';
import { EventMother } from '@tests/stubs/event/EventMother';
import { OrganizationMother } from '@tests/stubs/organization/OrganizationMother';
import { OrganizationConfigurationMother } from '@tests/stubs/organizationConfiguration/OrganizationConfigurationMother';
import { PaylinkMother } from '@tests/stubs/paylinks/PaylinkMother';
import { LocationMother } from '@tests/stubs/ticket/LocationMother';
import { TicketTypeMother } from '@tests/stubs/ticketType/TicketTypeMother';
import { TicketTypeOptionMother } from '@tests/stubs/ticketType/TicketTypeOptionMother';
import { UserMother } from '@tests/stubs/user/UserMother';

import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type { EventGroup } from '@/events/events/domain/entities/EventGroup';
import type { LocationRepository } from '@/locations/domain/contracts/LocationRepository';
import type { IMicrositeChannelService } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { OrganizationConfigurationRepository } from '@/organizations/organizationConfigurations/domain/contracts/OrganizationConfigurationRepository';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { ResponseFindTicketPaylinkSchemaDto } from '@/paylinks/domain/contracts/FindTicketPaylinkResponse';
import type { PaylinkRepository } from '@/paylinks/domain/contracts/PaylinkRepository';
import type { DisponibilityTypeResponse, TicketTypeExtras } from '@/tickets/ticketsTypes/domain/contracts/TicketsTypesContracts';
import type { TicketTypeRepository } from '@/tickets/ticketsTypes/domain/contracts/TicketTypeRepository';
import type { TicketTypeOptions } from '@/tickets/ticketsTypes/domain/entities/TicketTypeOption';
import type { GetTicketTypesExtrasService } from '@/tickets/ticketsTypes/domain/services/GetTicketTypesExtrasService';
import type { UserRepository } from '@/user/domain/contracts/UserRepository';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

describe(`${FindTicketPaylinkUseCase.name}`, () => {
  const paylinkRepository = mock<PaylinkRepository>();
  const organizationRepository = mock<OrganizationRepository>();
  const eventRepository = mock<EventRepository>();
  const locationRepository = mock<LocationRepository>();
  const locationFinder = new LocationFinder(locationRepository);
  const organizationConfigurationRepository = mock<OrganizationConfigurationRepository>();
  const ticketTypeRepository = mock<TicketTypeRepository>();
  const micrositeChannelService = mock<IMicrositeChannelService>();
  const getTicketTypesExtrasService = mock<GetTicketTypesExtrasService>();
  const userRepository = mock<UserRepository>();

  const useCase = new FindTicketPaylinkUseCase(
    paylinkRepository,
    organizationRepository,
    eventRepository,
    locationFinder,
    organizationConfigurationRepository,
    ticketTypeRepository,
    micrositeChannelService,
    getTicketTypesExtrasService,
    userRepository,
  );

  const organizationValid = OrganizationMother.buildDefault();
  const eventValid = EventMother.buildDefault();
  const ticketType = TicketTypeMother.buildWithCustomData({ eventId: eventValid.id });
  const userValid = UserMother.buildWithCustomData({ organizations: [organizationValid.id] });

  const userWithoutUsername = UserMother.buildWithCustomData({ username: Maybe.none() });

  const paylinkPending = PaylinkMother.buildWithCustomData({
    organizationId: organizationValid.id,
    eventId: eventValid.id,
    rateId: ticketType.id,
    referrerId: userValid.id,
    status: EPaylinkStatus.PENDING,
  });

  const paylinkCompleted = PaylinkMother.buildWithCustomData({
    organizationId: organizationValid.id,
    eventId: eventValid.id,
    rateId: ticketType.id,
    referrerId: userValid.id,
    status: EPaylinkStatus.COMPLETED,
  });

  const location = LocationMother.buildDefault();
  const organizationConfiguration = OrganizationConfigurationMother.buildDefault();

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should throw NotFoundError when Paylink not found', async () => {
    const notFoundError = NotFoundError.build({ context: PaylinkMongoRepository.name, target: Paylink.name });

    paylinkRepository.find.mockResolvedValue(left(notFoundError));
    organizationRepository.find.mockResolvedValue(right(organizationValid));
    eventRepository.find.mockResolvedValue(right(eventValid));
    locationRepository.find.mockResolvedValue(right(location));

    const useCaseResult = await useCase.execute({ activateCode: paylinkPending.id });

    expect(useCaseResult.isLeft()).toBeTruthy();
    expect(useCaseResult.isRight()).toBeFalsy();

    expect(useCaseResult.value).toBeInstanceOf(NotFoundError);
  });

  it('should throw NotFoundError when Paylink referrerId has no username', async () => {
    const notFoundError = NotFoundError.build({ context: PaylinkMongoRepository.name, target: Paylink.name });

    paylinkRepository.find.mockResolvedValue(left(notFoundError));
    organizationRepository.find.mockResolvedValue(right(organizationValid));
    eventRepository.find.mockResolvedValue(right(eventValid));
    locationRepository.find.mockResolvedValue(right(location));
    userRepository.find.mockResolvedValue(right(userWithoutUsername));

    const useCaseResult = await useCase.execute({ activateCode: paylinkPending.id });

    expect(useCaseResult.isLeft()).toBeTruthy();
    expect(useCaseResult.isRight()).toBeFalsy();

    expect(useCaseResult.value).toBeInstanceOf(NotFoundError);
  });

  it('should throw MapperError when Paylink is not mapped', async () => {
    const mapperError = MapperError.build({ context: PaylinkMapper.name, target: Paylink.name });

    paylinkRepository.find.mockResolvedValue(left(mapperError));
    organizationRepository.find.mockResolvedValue(right(organizationValid));
    eventRepository.find.mockResolvedValue(right(eventValid));
    locationRepository.find.mockResolvedValue(right(location));

    const useCaseResult = await useCase.execute({ activateCode: paylinkPending.id });

    expect(useCaseResult.isLeft()).toBeTruthy();
    expect(useCaseResult.isRight()).toBeFalsy();
  });

  it('should throw NotFound when Paylink has status different than pending', async () => {
    paylinkRepository.find.mockResolvedValue(right(paylinkCompleted));
    organizationRepository.find.mockResolvedValue(right(organizationValid));
    eventRepository.find.mockResolvedValue(right(eventValid));
    locationRepository.find.mockResolvedValue(right(location));

    const useCaseResult = await useCase.execute({ activateCode: paylinkPending.id });

    expect(useCaseResult.isLeft()).toBeTruthy();
    expect(useCaseResult.isRight()).toBeFalsy();

    expect(useCaseResult.value).toBeInstanceOf(PaylinkAlreadyUsed);
  });

  it('should return the Paylink when found', async () => {
    paylinkRepository.find.mockResolvedValue(right(paylinkPending));
    organizationRepository.find.mockResolvedValue(right(organizationValid));
    eventRepository.find.mockResolvedValue(right(eventValid));
    locationRepository.find.mockResolvedValue(right(location));
    organizationConfigurationRepository.find.mockResolvedValue(right(organizationConfiguration));
    ticketTypeRepository.find.mockResolvedValue(right(ticketType));
    userRepository.find.mockResolvedValue(right(userValid));

    micrositeChannelService.execute.mockResolvedValue(right({
      type: EMicrositeChannel.ORGANIZATION,
      channel: organizationValid,
      organizations: new Map<IdPrimitive, Organization>(),
      eventGroups: new Map<IdPrimitive, EventGroup>(),
    }));

    const optionsPrices = Collection.build(TicketTypeOptionMother.getMany(5, ticketType.currency));

    const ticketTypesOptionsWithConfigResult = new Map<IdPrimitive, TicketTypeOptions>();

    ticketTypesOptionsWithConfigResult.set(ticketType.id, optionsPrices);

    const ticketTypesExtrasResult = new Map<IdPrimitive, TicketTypeExtras>();

    ticketTypesExtrasResult.set(ticketType.id, {
      areFewLeft: true,
      isSoldOut: true,
      maximum: 10,
      quantitySelector: QuantitySelector.build(),
      disponibility: {
        value: 10,
        isActive: false,
        isPercentageExceed: Maybe.none(),
      },
      disponibilityByOptionId: new Map<string, DisponibilityTypeResponse>(),
      to: Maybe.none(),
      currentOptionId: Maybe.fromValue(optionsPrices.toArray().at(-1)?.id),
    });

    getTicketTypesExtrasService.execute.mockResolvedValue(right(ticketTypesExtrasResult));

    const useCaseResult = await useCase.execute({ activateCode: paylinkPending.activateCode });

    expect(useCaseResult.isLeft()).toBeFalsy();
    expect(useCaseResult.isRight()).toBeTruthy();

    const {
      paylink, organization, event,
    } = useCaseResult.value as ResponseFindTicketPaylinkSchemaDto;

    expect(paylink).toBeInstanceOf(Paylink);
    expect(organization).toBeInstanceOf(Organization);
    expect(event).toBeInstanceOf(EventEntity);
  });
});
