import type { BookingSpaceAvailability } from '@/events/events/domain/entities/BookingSpaceAvailability';
import type { BookingSpaceAvailabilitySchemaType } from '@/events/events/infrastructure/database/schemas/BookingSpaceAvailabilitySchemaType';

export class BookingSpaceAvailabilitySchemaMapper {
  static execute(availability: BookingSpaceAvailability): BookingSpaceAvailabilitySchemaType {
    return {
      _id: availability.id,
      token: availability.token,
      expired_at: availability.expiredAt,
      collaborator_id: availability.collaboratorId,
      event_id: availability.eventId,
    };
  }
}
