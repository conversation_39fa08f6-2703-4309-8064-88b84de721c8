
import { execSync } from 'node:child_process';
import {
  existsSync, readdirSync,
  readFileSync,
} from 'node:fs';

import { TranslatorI18nService } from '@/cross-cutting/infrastructure/translator/TranslatorI18nService';
import config from '@config/index';
import { loggerMock } from '@tests/mocks';
import { startServer } from '@tests/mocks/Server';

import type { IServer } from '@tests/mocks/Server';

describe(`${TranslatorI18nService.name}`, () => {
  const { localesPath, supportedLocales } = config.localization;

  let httpServer: IServer;

  beforeAll(async () => {
    httpServer = await startServer();
  }, 10_000 * 10);

  afterAll(async () => {
    await httpServer.stop();
  });

  it('download phrase locales', async () => {
    execSync(`npm run locales`);

    expect(existsSync(localesPath)).toBeTruthy();

    supportedLocales.forEach((_locale) => {
      const localeFolderExists = existsSync(`${localesPath}/${_locale}`);

      expect(localeFolderExists).toBeTruthy();

      const files = readdirSync(`${localesPath}/${_locale}`);

      expect(files.length > 0).toBeTruthy();

      const firstFile = files[0];

      expect(firstFile).toBeTruthy();
      expect(firstFile?.endsWith('.json')).toBeTruthy();
    });
  });

  it('should translate a key', async () => {
    const translator = await TranslatorI18nService.initialize(loggerMock, config.localization);

    expect(translator).toBeInstanceOf(TranslatorI18nService);

    const namespace = 'common';
    const translationKey = 'nombre';

    for (const _locale of supportedLocales) {
      const translationFileRawJson = readFileSync(`${localesPath}/${_locale}/${namespace}.json`, 'utf-8');
      const translationFileParsedJson = JSON.parse(translationFileRawJson);

      const translatedKeyFromJson = translationFileParsedJson[translationKey];

      translator.setLanguage(_locale);

      const translatedKey = translator.translate(`${namespace}:${translationKey}`);

      expect(translatedKey).toEqual(translatedKeyFromJson);
    }
  });
});
