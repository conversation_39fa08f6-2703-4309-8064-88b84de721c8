import {
  Maybe,
  NotFoundError,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';
import supertest from 'supertest';
import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { FindEventUseCase } from '@/events/events/application/FindEventUseCase';
import { EventEntity } from '@/events/events/domain/entities/EventEntity';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { EventMongoRepository } from '@/events/events/infrastructure/database/repositories/EventMongoRepository';
import { ReservationDependencyIdentifier } from '@/reservations/shared/domain/dependencyIdentifier/ReservationDependencyIdentifier';
import { successResponseSchema } from '@app/http/@types/cli-api/events/find/successResponseSchema';
import { HTTP_CODES } from '@app/http/HttpCodes';
import config from '@config/index';
import { BillingAddressRepositoryTest } from '@tests/stubs/billingAddress/BillingAddressRepositoryTest';
import { EventRepositoryTest } from '@tests/stubs/event/EventRepositoryTest';
import { LocationRepositoryTest } from '@tests/stubs/locations/LocationRepositoryTest';
import { MicrositeMother } from '@tests/stubs/microsite/MicrositeMother';
import { MicrositeRepositoryTest } from '@tests/stubs/microsite/MicrositeRepositoryTest';
import { OrganizationRepositoryTest } from '@tests/stubs/organization/OrganizationRepositoryTest';
import { ReservationsConfigurationMother } from '@tests/stubs/reservations/ReservationsConfigurationMother';
import { ReservationsConfigurationRepositoryTest } from '@tests/stubs/reservations/ReservationsConfigurationRepositoryTest';
import { startServer, type IServer } from 'tests/mocks/Server';
import { BillingAddressMother } from 'tests/stubs/billingAddress/BillingAddressMother';
import { EventMother } from 'tests/stubs/event/EventMother';
import { OrganizationMother } from 'tests/stubs/organization/OrganizationMother';
import { LocationMother } from 'tests/stubs/ticket/LocationMother';

import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import type { Events } from '@/events/events/domain/entities/EventEntity';
import type { ReservationsConfigurationRepository } from '@/reservations/configuration/domain/contracts/ReservationsConfigurationRepository';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { ILocationRepositoryTest } from '@tests/stubs/locations/LocationRepositoryTest';
import type { RawServerBase } from 'fastify';

const name = 'FindEvent';

describe(`E2E | ${name}`, () => {
  const BASE_URL = '/api/events';

  let httpServer: IServer;
  let server: RawServerBase;

  let organizationRepository: OrganizationRepositoryTest;
  let billingAddressRepository: BillingAddressRepositoryTest;
  let locationRepository: ILocationRepositoryTest;
  let eventRepository: EventRepositoryTest;
  let micrositeRepository: MicrositeRepositoryTest;
  const reservationsRepository: ReservationsConfigurationRepositoryTest = new ReservationsConfigurationRepositoryTest();

  const organization = OrganizationMother.buildDefault();
  const organizationWithouthBillingAddress = OrganizationMother.buildDefault();
  const billingAddress = BillingAddressMother.buildWithCustomData({ organizationId: organization.id });
  const location = LocationMother.buildDefault();
  const microsite = MicrositeMother.buildWithCustomData({ organizationId: organization.id });

  const event = EventMother.buildWithCustomData({
    organizationId: organization.id,
    locationId: location.id,
    code: 'event_code',
  });

  const eventWithouthImages = EventMother.buildWithCustomData({
    organizationId: organization.id,
    locationId: location.id,
    images: Maybe.none(),
    code: 'event_code2',
  });

  const eventWithoutLocation = EventMother.buildWithCustomData({ organizationId: organization.id });

  const eventWithoutBillingAddress = EventMother.buildWithCustomData({
    organizationId: organizationWithouthBillingAddress.id,
    locationId: location.id,
  });

  const reservationsConfiguration = ReservationsConfigurationMother.createWithCustomData({ organizationId: organization.id });

  const events: Events = new Map<IdPrimitive, EventEntity>();

  events.set(event.id, event);
  events.set(eventWithouthImages.id, eventWithouthImages);
  events.set(eventWithoutBillingAddress.id, eventWithoutBillingAddress);
  events.set(eventWithoutLocation.id, eventWithoutLocation);

  beforeAll(async () => {
    httpServer = await startServer(() => {
      container.register<ReservationsConfigurationRepository>(
        ReservationDependencyIdentifier.ReservationsConfigurationRepository,
        { useValue: reservationsRepository },
      );
    });
    server = httpServer.getInstance();

    const databaseConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

    organizationRepository = new OrganizationRepositoryTest(databaseConnection);
    billingAddressRepository = new BillingAddressRepositoryTest(databaseConnection);
    locationRepository = new LocationRepositoryTest(databaseConnection);
    eventRepository = new EventRepositoryTest(databaseConnection);
    micrositeRepository = new MicrositeRepositoryTest(databaseConnection);

    await Promise.all([
      organizationRepository.save(organization),
      organizationRepository.save(organizationWithouthBillingAddress),
      billingAddressRepository.save(billingAddress),
      locationRepository.save(location),
      eventRepository.saveMany(events),
      micrositeRepository.save(microsite),
      reservationsRepository.save(reservationsConfiguration),
    ]);
  }, 10_000 * 10);

  afterAll(async () => {
    const ids = [...events.values()].map(event => UniqueEntityID.build(event.id));
    const criteria = EventCriteriaMother.idsToMatch(ids);

    await eventRepository.removeMany(criteria);
    await httpServer.stop();
  });

  it('Error: Entity Not Found', async () => {
    const eventId = 'event_id_not_found';

    const { status, body } = await supertest(server)
      .get(`${BASE_URL}/${eventId}`)
      .set('Authorization', `Fv ${config.apikey}`);

    const errorException = NotFoundError.build({ context: EventMongoRepository.name, target: EventEntity.name });

    expect(status).toBe(HTTP_CODES.NOT_FOUND_404);
    expect(body.message).toEqual(errorException.message);
  });

  it('Success: Find By ID', async () => {
    const eventId = event.id;

    const { status, body } = await supertest(server)
      .get(`${BASE_URL}/${eventId}`)
      .set('Authorization', `Fv ${config.apikey}`);

    expect(status).toBe(HTTP_CODES.OK_200);
    expect(body).toMatchSchema({ data: successResponseSchema });
  });

  it('Success: Find By Code', async () => {
    const eventCode = event.code;

    const { status, body } = await supertest(server)
      .get(`${BASE_URL}/${eventCode}`)
      .set('Authorization', `Fv ${config.apikey}`);

    expect(status).toBe(HTTP_CODES.OK_200);
    expect(body).toMatchSchema({ data: successResponseSchema });
  });

  it('Success: Find By ID withouth images', async () => {
    const eventId = eventWithouthImages.id;

    const { status, body } = await supertest(server)
      .get(`${BASE_URL}/${eventId}`)
      .set('Authorization', `Fv ${config.apikey}`);

    expect(status).toBe(HTTP_CODES.OK_200);
    expect(body).toMatchSchema({ data: successResponseSchema });
  });

  it('Success: Find By Code withouth images', async () => {
    const eventCode = eventWithouthImages.code;

    const { status, body } = await supertest(server)
      .get(`${BASE_URL}/${eventCode}`)
      .set('Authorization', `Fv ${config.apikey}`);

    expect(status).toBe(HTTP_CODES.OK_200);
    expect(body).toMatchSchema({ data: successResponseSchema });
  });

  it('Error: billing address not found', async () => {
    const { status, body } = await supertest(server)
      .get(`${BASE_URL}/${eventWithoutBillingAddress.id}`)
      .set('Authorization', `Fv ${config.apikey}`);

    const errorException = NotFoundError.build({ context: FindEventUseCase.name, target: EventEntity.name });

    expect(status).toBe(HTTP_CODES.NOT_FOUND_404);
    expect(body.message).toEqual(errorException.message);
  });
});
