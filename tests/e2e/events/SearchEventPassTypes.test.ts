import { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import supertest from 'supertest';
import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';
import { PassTypeCriteriaMother } from '@/passes/passTypes/domain/filters/PassTypeCriteriaMother';
import { HTTP_CODES } from '@app/http/HttpCodes';
import config from '@config/index';
import { startServer, type IServer } from '@tests/mocks/Server';
import { EventMother } from '@tests/stubs/event/EventMother';
import { EventRepositoryTest } from '@tests/stubs/event/EventRepositoryTest';
import { OrganizationMother } from '@tests/stubs/organization/OrganizationMother';
import { OrganizationRepositoryTest } from '@tests/stubs/organization/OrganizationRepositoryTest';
import { OrganizationConfigurationMother } from '@tests/stubs/organizationConfiguration/OrganizationConfigurationMother';
import { OrganizationConfigurationRepositoryTest } from '@tests/stubs/organizationConfiguration/OrganizationConfigurationRepositoryTest';
import { PassTypeEventMother } from '@tests/stubs/Pass/PassType/PassTypeEventMother';
import { PassTypeEventRepositoryTest, type IPassTypeEventRepositoryTest } from '@tests/stubs/Pass/PassType/PassTypeEventRepositoryTest';
import { PassTypeMother } from '@tests/stubs/Pass/PassType/PassTypeMother';
import { PassTypeRepositoryTest, type IPassTypeRepository } from '@tests/stubs/Pass/PassType/PassTypeRepositoryTest';
import { PriceRepositoryTest } from '@tests/stubs/Pass/Prices/PriceRepositoryTest';
import { PricesMother } from '@tests/stubs/Pass/Prices/PricesMother';
import { getMoneyBuilder } from '@tests/stubs/ticket/PurchaseTicketsHelpers';

import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import type { IPriceRepository } from '@tests/stubs/Pass/Prices/PriceRepositoryTest';
import type { RawServerBase } from 'fastify';

const name = 'SearchEventPassTypes';

describe(`E2E | ${name}`, () => {
  const BASE_URL = '/api/events/{id}/pass-types';

  let httpServer: IServer;
  let server: RawServerBase;

  let organizationRepository: OrganizationRepositoryTest;
  let eventRepository: EventRepositoryTest;
  let passTypeRepository: IPassTypeRepository;
  let passTypeEventRepository: IPassTypeEventRepositoryTest;
  let priceRepository: IPriceRepository;
  let organizationConfigurationRepository: OrganizationConfigurationRepositoryTest;

  const organization = OrganizationMother.buildDefault();
  const event = EventMother.buildWithCustomData({ organizationId: organization.id });
  const eventWithoutPassTypes = EventMother.buildWithCustomData({ organizationId: organization.id });

  const price1 = PricesMother.buildWithCustomData({ organizationId: organization.id });
  const price2 = PricesMother.buildWithCustomData({ organizationId: organization.id });

  const passType1 = PassTypeMother.buildWithCustomData({ organizationId: organization.id, priceIds: [price1.id] });
  const passType2 = PassTypeMother.buildWithCustomData({ organizationId: organization.id, priceIds: [price2.id] });

  const events = new Map([
    [event.id, event],
    [eventWithoutPassTypes.id, eventWithoutPassTypes],
  ]);

  const passTypes = new Map([
    [passType1.id, passType1],
    [passType2.id, passType2],
  ]);

  // Create connections between passTypes and event
  const passTypeEvent1 = PassTypeEventMother.buildConnection({
    organizationId: organization.id,
    eventId: event.id,
    passTypeId: passType1.id,
  });
  const passTypeEvent2 = PassTypeEventMother.buildConnection({
    organizationId: organization.id,
    eventId: event.id,
    passTypeId: passType2.id,
  });

  const passTypeEvents = [
    passTypeEvent1,
    passTypeEvent2,
  ];

  const prices = new Map([
    [price1.id, price1],
    [price2.id, price2],
  ]);

  beforeAll(async () => {
    httpServer = await startServer();

    server = httpServer.getInstance();

    const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

    organizationRepository = new OrganizationRepositoryTest(dbConnection);
    eventRepository = new EventRepositoryTest(dbConnection);
    passTypeRepository = new PassTypeRepositoryTest(dbConnection);
    passTypeEventRepository = new PassTypeEventRepositoryTest(dbConnection);
    priceRepository = new PriceRepositoryTest(dbConnection);
    organizationConfigurationRepository = new OrganizationConfigurationRepositoryTest(dbConnection);

    await Promise.all([
      organizationRepository.save(organization),
      eventRepository.saveMany(events),
      passTypeRepository.saveMany(passTypes),
      passTypeEventRepository.saveMany(passTypeEvents),
      priceRepository.saveMany(prices),
    ]);
  });

  afterAll(async () => {
    // Clean up test data
    const eventIds = [event.id, eventWithoutPassTypes.id].map(id => UniqueEntityID.build(id));
    const passTypeIds = [passType1.id, passType2.id].map(id => UniqueEntityID.build(id));
    const passTypeEventIds = [passTypeEvent1._id, passTypeEvent2._id].map(id => UniqueEntityID.build(id));

    const eventCriteria = EventCriteriaMother.idsToMatch(eventIds);
    const passTypeCriteria = PassTypeCriteriaMother.idsToMatch(passTypeIds);
    const passTypeEventCriteria = PassTypeCriteriaMother.idsToMatch(passTypeEventIds);

    const organizationCriteria = OrganizationCriteriaMother.idToMatch(UniqueEntityID.build(organization.id));

    await Promise.all([
      organizationRepository.remove(organizationCriteria),
      eventRepository.removeMany(eventCriteria),
      passTypeRepository.removeMany(passTypeCriteria),
      passTypeEventRepository.removeMany(passTypeEventCriteria),
    ]);

    await httpServer.stop();
  });

  it('should return pass types for event with organization slug', async () => {
    const url = BASE_URL.replace('{id}', event.id);
    const request = { slug: organization.slug.get() };

    const { body } = await supertest(server)
      .get(url)
      .query(request)
      .set('Authorization', `Fv ${config.apikey}`)
      .expect(HTTP_CODES.OK_200);

    expect(body.data).toBeInstanceOf(Array);
    expect(body.data.length).toBe(2);

    const passTypesIds = body.data.map((pt: unknown) => (pt as { id: string; })?.id);

    expect(passTypesIds).toContain(passType1.id);
    expect(passTypesIds).toContain(passType2.id);
  });

  it('should return pass types for event using event code', async () => {
    const url = BASE_URL.replace('{id}', event.code);
    const request = { slug: organization.slug.get() };

    const { body } = await supertest(server)
      .get(url)
      .query(request)
      .set('Authorization', `Fv ${config.apikey}`)
      .expect(HTTP_CODES.OK_200);

    expect(body.data).toBeInstanceOf(Array);
    expect(body.data.length).toBe(2);
  });

  it('should handle pagination correctly', async () => {
    const url = BASE_URL.replace('{id}', event.id);
    const request = {
      slug: organization.slug.get(),
      perPage: 1,
      page: 1,
    };

    const { body } = await supertest(server)
      .get(url)
      .query(request)
      .set('Authorization', `Fv ${config.apikey}`)
      .expect(HTTP_CODES.OK_200);

    expect(body.data).toHaveLength(1);
    expect(body.metadata).toHaveProperty('perPage', 1);
    expect(body.metadata).toHaveProperty('page', 1);
    expect(body.metadata).toHaveProperty('total', 2);
  });

  it('should return 404 for non-existent event', async () => {
    const nonExistentEventId = UniqueEntityID.create().toPrimitive();
    const url = BASE_URL.replace('{id}', nonExistentEventId);
    const request = { slug: organization.slug.get() };

    const { status } = await supertest(server)
      .get(url)
      .query(request)
      .set('Authorization', `Fv ${config.apikey}`);

    expect(status).toBe(HTTP_CODES.NOT_FOUND_404);
  });

  it('should return 404 for invalid organization slug', async () => {
    const url = BASE_URL.replace('{id}', event.id);
    const request = { slug: 'invalid-organization-slug' };

    const { status } = await supertest(server)
      .get(url)
      .query(request)
      .set('Authorization', `Fv ${config.apikey}`);

    expect(status).toBe(HTTP_CODES.NOT_FOUND_404);
  });

  it('should handle smsSale parameter', async () => {
    const url = BASE_URL.replace('{id}', event.id);
    const request = {
      slug: organization.slug.get(),
      smsSale: true,
    };

    const { body } = await supertest(server)
      .get(url)
      .query(request)
      .set('Authorization', `Fv ${config.apikey}`)
      .expect(HTTP_CODES.OK_200);

    expect(body.data).toHaveLength(2);
  });

  it('should handle organizationConfiguration areFeesShownUpfront field -> default false', async () => {
    const url = BASE_URL.replace('{id}', event.id);
    const request = { slug: organization.slug.get() };

    const { body } = await supertest(server)
      .get(url)
      .query(request)
      .set('Authorization', `Fv ${config.apikey}`)
      .expect(HTTP_CODES.OK_200);

    expect(body.data).toHaveLength(2);
    expect(body.data[0].price.price).toBe(prices.get(body.data[0].price.id)!.price);
    expect(body.data[1].price.price).toBe(prices.get(body.data[1].price.id)!.price);
  });

  it('should handle organizationConfiguration areFeesShownUpfront field -> true', async () => {
    const url = BASE_URL.replace('{id}', event.id);
    const request = { slug: organization.slug.get() };

    const organizationConfiguration = OrganizationConfigurationMother.buildWithCustomData({
      organizationId: organization.id,
      areFeesShownUpfront: true,
    });

    await organizationConfigurationRepository.save(organizationConfiguration);

    const buildMoney = getMoneyBuilder(price1.currency);

    const priceMoney1 = buildMoney(price1.price);
    const feesMoney1 = priceMoney1.percentage(price1.serviceFees.quantity);

    const priceWithFees1 = priceMoney1.add(feesMoney1).toDecimal();

    const priceMoney2 = buildMoney(price2.price);
    const feesMoney2 = priceMoney2.percentage(price2.serviceFees.quantity);

    const priceWithFees2 = priceMoney2.add(feesMoney2).toDecimal();

    const pricesWithFees = new Map([
      [price1.id, priceWithFees1],
      [price2.id, priceWithFees2],
    ]);

    const { body } = await supertest(server)
      .get(url)
      .query(request)
      .set('Authorization', `Fv ${config.apikey}`)
      .expect(HTTP_CODES.OK_200);

    expect(body.data).toHaveLength(2);
    expect(body.data[0].price.price).toBe(pricesWithFees.get(body.data[0].price.id)!);
    expect(body.data[1].price.price).toBe(pricesWithFees.get(body.data[1].price.id)!);
  });
});
