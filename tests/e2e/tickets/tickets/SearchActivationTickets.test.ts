import {
  Maybe, NotFoundError, UniqueEntityID,
} from '@discocil/fv-domain-library/domain';
import supertest from 'supertest';
import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { TicketDependencyIdentifier } from '@/tickets/tickets/domain/dependencyIdentifier/TicketDependencyIdentifier';
import { Ticket } from '@/tickets/tickets/domain/entities/TicketEntity';
import { TicketMongoRepository } from '@/tickets/tickets/infrastructure/database/repositories/TicketMongoRepository';
import { successResponseSchema } from '@app/http/@types/cli-api/tickets/searchActivation/successResponseSchema';
import { HTTP_CODES } from '@app/http/HttpCodes';
import config from '@config/index';
import { startServer, type IServer } from '@tests/mocks/Server';
import { OrganizationMother } from '@tests/stubs/organization/OrganizationMother';
import { OrganizationRepositoryTest } from '@tests/stubs/organization/OrganizationRepositoryTest';
import { TicketMother } from '@tests/stubs/ticket/TicketMother';

import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { TicketRepository } from '@/tickets/tickets/domain/contracts/TicketRepository';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { RawServerBase } from 'fastify';

const name = 'SearchActivationTickets';

describe(`E2E | ${name}`, () => {
  const BASE_URL = '/api/tickets/activation';
  let organizationRepository: OrganizationRepositoryTest;
  let organization: Organization;

  let ticketRepository: TicketRepository;
  const tickets = new Map<IdPrimitive, Ticket>();

  let ticket: Ticket;
  const groupTickets: Ticket[] = [];
  const purchaseId = UniqueEntityID.create();

  let httpServer: IServer;
  let server: RawServerBase;

  beforeAll(async () => {
    httpServer = await startServer();
    server = httpServer.getInstance();

    organization = OrganizationMother.buildDefault();
    organizationRepository = new OrganizationRepositoryTest(container.resolve(DependencyIdentifier.DatabaseConnection));

    ticket = TicketMother.buildWithCustomData({ organizationId: organization.id });
    groupTickets.push(
      TicketMother.buildWithCustomData({ organizationId: organization.id, purchaseId: Maybe.fromValue(purchaseId.value) }),
      TicketMother.buildWithCustomData({ organizationId: organization.id, purchaseId: Maybe.fromValue(purchaseId.value) }),
    );
    ticketRepository = container.resolve<TicketRepository>(TicketDependencyIdentifier.TicketRepository);

    tickets.set(ticket.id, ticket);
    tickets.set(groupTickets[0]!.id, groupTickets[0]!);
    tickets.set(groupTickets[1]!.id, groupTickets[1]!);

    await Promise.all([
      ticketRepository.saveMany(tickets),
      organizationRepository.save(organization),
    ]);
  }, 10_000 * 10);

  afterAll(async () => {
    await httpServer.stop();
  });

  it('Error: Entity Not Found', async () => {
    const ticketId = 'ticket_id_not_found';

    const { status, body } = await supertest(server)
      .get(`${BASE_URL}/${ticketId}`)
      .set('Authorization', `Fv ${config.apikey}`);

    const errorException = NotFoundError.build({ context: TicketMongoRepository.name, target: Ticket.name });

    expect(status).toBe(HTTP_CODES.NOT_FOUND_404);
    expect(body).toEqual({ message: errorException.message });
  });

  it('Success: Find By Ticket ID', async () => {
    const { status, body } = await supertest(server)
      .get(`${BASE_URL}/${ticket.id}`)
      .set('Authorization', `Fv ${config.apikey}`);

    expect(status).toBe(HTTP_CODES.OK_200);
    expect(body).toMatchSchema({ data: successResponseSchema });

    expect(body.data[0].id).toBe(ticket.id);
    expect(body.data[0].organization.id).toBe(organization.id);
    expect(body.data[0].eventId).toBe(ticket.eventId);
  });

  it('Success: Find By Purchase ID', async () => {
    const { status, body } = await supertest(server)
      .get(`${BASE_URL}/${purchaseId.value}`)
      .set('Authorization', `Fv ${config.apikey}`);

    expect(status).toBe(HTTP_CODES.OK_200);
    expect(body).toMatchSchema({ data: successResponseSchema });

    expect(body.data).toHaveLength(2);

    expect(body.data).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          id: groupTickets[0]!.id,
          purchaseId: groupTickets[0]!.purchaseId.get(),
          organization: expect.objectContaining({ id: organization.id }),
          eventId: groupTickets[0]!.eventId,
        }),
        expect.objectContaining({
          id: groupTickets[1]!.id,
          purchaseId: groupTickets[1]!.purchaseId.get(),
          organization: expect.objectContaining({ id: organization.id }),
          eventId: groupTickets[1]!.eventId,
        }),
      ]),
    );
  });
});
