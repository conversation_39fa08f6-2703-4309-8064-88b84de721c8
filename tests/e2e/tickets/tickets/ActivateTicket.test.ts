import {
  EDocumentType, ESaleTypes, ETicketStates, FvDate, Maybe, NotFoundError,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';
import supertest from 'supertest';
import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { Organization } from '@/organizations/organizations/domain/entities/Organization';
import { OrganizationMongoRepository } from '@/organizations/organizations/infrastructure/database/repositories/OrganizationMongoRepository';
import { ActivateTicketUseCase } from '@/tickets/tickets/application/use-cases/ActivateTicketUseCase';
import { TicketDependencyIdentifier } from '@/tickets/tickets/domain/dependencyIdentifier/TicketDependencyIdentifier';
import { Ticket } from '@/tickets/tickets/domain/entities/TicketEntity';
import { AssistantDataError } from '@/tickets/tickets/domain/errors/AssistantDataError';
import { TicketActivationError } from '@/tickets/tickets/domain/errors/TicketActivationError';
import { TicketCriteriaMother } from '@/tickets/tickets/domain/filters/TicketCriteriaMother';
import { TicketMongoRepository } from '@/tickets/tickets/infrastructure/database/repositories/TicketMongoRepository';
import { TicketType } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import { TicketTypeMongoRepository } from '@/tickets/ticketsTypes/infrastructure/database/repositories/TicketTypeMongoRepository';
import { HTTP_CODES } from '@app/http/HttpCodes';
import config from '@config/index';
import devCycleMock from '@tests/mocks/devCycle';
import { startServer, type IServer } from '@tests/mocks/Server';
import { MotherCreator } from '@tests/stubs/MotherCreator';
import { OrganizationMother } from '@tests/stubs/organization/OrganizationMother';
import { OrganizationRepositoryTest } from '@tests/stubs/organization/OrganizationRepositoryTest';
import { AssistantMother } from '@tests/stubs/ticket/AssistantMother';
import { TicketMother } from '@tests/stubs/ticket/TicketMother';
import { TicketTypeMother } from '@tests/stubs/ticketType/TicketTypeMother';
import { TicketTypeRepositoryTest, type ITicketTypeRepositoryTest } from '@tests/stubs/ticketType/TicketTypeRepositoryTest';

import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import type { TicketRepository } from '@/tickets/tickets/domain/contracts/TicketRepository';
import type { RawServerBase } from 'fastify';

const name = 'ActivateTicket';

describe(`E2E | ${name}`, () => {
  const BASE_URL = '/api/tickets';
  let organizationRepository: OrganizationRepositoryTest;
  const organization = OrganizationMother.buildDefault();

  let ticketTypeRepository: ITicketTypeRepositoryTest;
  const ticketType = TicketTypeMother.createPublicPassingAllValidationWithCustomData({
    organizationId: organization.id,
    fields: {
      phone: {
        type: 'text',
        required: true,
        label: 'string',
        slug: 'string',
      },
      personalDocumentType: {
        type: 'text',
        required: true,
        label: 'string',
        slug: 'string',
      },
    },
  });

  let ticketRepository: TicketRepository;

  // eslint-disable-next-line no-restricted-syntax
  const idx = MotherCreator.random().lorem.words(2);
  const purchaseId = UniqueEntityID.create().toPrimitive();

  const ticket = TicketMother.buildPendingActivation({
    organizationId: organization.id,
    typeId: ticketType.id,
    eventId: ticketType.eventId,
    purchaseId: Maybe.some(purchaseId),
  });

  const INVALID_PHONE = 'invalid phone format';
  const INVALID_DOCUMENT = 'invalid document format';

  const ticketsThatShouldFail = {
    organization_not_found: TicketMother.buildPendingActivation({
      organizationId: 'organization_not_found', typeId: ticketType.id, eventId: ticketType.eventId,
    }),
    ticket_type_not_found: TicketMother.buildPendingActivation({
      organizationId: organization.id, typeId: 'ticket_type_not_found', eventId: ticketType.eventId,
    }),
    already_activated: TicketMother.buildPendingActivation({
      organizationId: organization.id,
      typeId: ticketType.id,
      eventId: ticketType.eventId,
      state: ETicketStates.ACTIVATED,
    }),
    already_activated_2: TicketMother.buildPendingActivation({
      organizationId: organization.id,
      typeId: ticketType.id,
      eventId: ticketType.eventId,
      activatedAt: Maybe.some(FvDate.create().toPrimitive()),
    }),
    annulled: TicketMother.buildPendingActivation({
      organizationId: organization.id,
      typeId: ticketType.id,
      eventId: ticketType.eventId,
      state: ETicketStates.ANNULLED,
    }),
    sale_type_not_rrpp: TicketMother.buildPendingActivation({
      organizationId: organization.id,
      typeId: ticketType.id,
      eventId: ticketType.eventId,
      saleType: ESaleTypes.ONLINE,
    }),
  };

  const tickets = new Map<string, Ticket>();

  tickets.set(ticket.id, ticket);
  Object.values(ticketsThatShouldFail).forEach(ticketToFail => tickets.set(ticketToFail.id, ticketToFail));

  const assistantSuccessful = AssistantMother.createActivateAssistantRequest(ticket.id);
  const assistantPhoneError = AssistantMother.createActivateAssistantRequest(ticket.id, { phone: INVALID_PHONE });
  const assistantDocumentNumberError = AssistantMother.createActivateAssistantRequest(
    ticket.id,
    { personalDocument: { number: INVALID_DOCUMENT, type: EDocumentType.DNI } },
  );
  const remarketing = MotherCreator.random().datatype.boolean();

  let httpServer: IServer;
  let server: RawServerBase;

  beforeAll(async () => {
    httpServer = await startServer();
    server = httpServer.getInstance();

    ticketRepository = container.resolve<TicketRepository>(TicketDependencyIdentifier.TicketRepository);

    const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

    organizationRepository = new OrganizationRepositoryTest(dbConnection);
    ticketTypeRepository = new TicketTypeRepositoryTest(dbConnection);

    await Promise.all([
      ticketRepository.saveMany(tickets),
      ticketTypeRepository.save(ticketType),
      organizationRepository.save(organization),
    ]);
  }, 10_000 * 10);

  afterAll(async () => {
    await httpServer.stop();
  });

  beforeEach(() => {
    devCycleMock();
  });

  it('Error: Body validation fail', async () => {
    const { status } = await supertest(server)
      .patch(`${BASE_URL}/${ticket.id}`)
      .set('Authorization', `Fv ${config.apikey}`)
      .send({
        remarketing,
        idx: 'hola',
      });

    expect(status).toBe(HTTP_CODES.BAD_REQUEST_400);

    const { status: status2 } = await supertest(server)
      .patch(`${BASE_URL}/${ticket.id}`)
      .set('Authorization', `Fv ${config.apikey}`)
      .send({
        assistants: [{
          assistants: [assistantSuccessful],
          gender: 'pepito',
        }],
        remarketing,
        idx: 'hola',
      });

    expect(status2).toBe(HTTP_CODES.BAD_REQUEST_400);
  });

  it('Error: Ticket Not Found', async () => {
    const ticketId = 'ticket_id_not_found';

    const { status, body } = await supertest(server)
      .patch(`${BASE_URL}/${ticketId}`)
      .set('Authorization', `Fv ${config.apikey}`)
      .send({
        assistants: [assistantSuccessful],
        remarketing,
        idx,
      });

    const errorException = NotFoundError.build({
      context: TicketMongoRepository.name,
      target: Ticket.name,
    });

    expect(status).toBe(HTTP_CODES.NOT_FOUND_404);
    expect(body).toEqual({ message: errorException.message });
  });

  it('Error: Organization Not Found', async () => {
    const { status, body } = await supertest(server)
      .patch(`${BASE_URL}/${ticketsThatShouldFail.organization_not_found.id}`)
      .set('Authorization', `Fv ${config.apikey}`)
      .send({
        assistants: [assistantSuccessful],
        remarketing,
        idx,
      });

    const errorException = NotFoundError.build({
      context: OrganizationMongoRepository.name,
      target: Organization.name,
    });

    expect(status).toBe(HTTP_CODES.NOT_FOUND_404);
    expect(body).toEqual({ message: errorException.message });
  });

  it('Error: TicketType Not Found', async () => {
    const { status, body } = await supertest(server)
      .patch(`${BASE_URL}/${ticketsThatShouldFail.ticket_type_not_found.id}`)
      .set('Authorization', `Fv ${config.apikey}`)
      .send({
        assistants: [assistantSuccessful],
        remarketing,
        idx,
      });

    const errorException = NotFoundError.build({
      context: TicketTypeMongoRepository.name,
      target: TicketType.name,
    });

    expect(status).toBe(HTTP_CODES.NOT_FOUND_404);
    expect(body).toEqual({ message: errorException.message });
  });

  it('Error: Ticket Not Activable: already activated', async () => {
    const { status, body } = await supertest(server)
      .patch(`${BASE_URL}/${ticketsThatShouldFail.already_activated.id}`)
      .set('Authorization', `Fv ${config.apikey}`)
      .send({
        assistants: [assistantSuccessful],
        remarketing,
        idx,
      });

    const errorException = TicketActivationError.alreadyActive({ context: ActivateTicketUseCase.name });

    expect(status).toBe(HTTP_CODES.CONFLICT_409);
    expect(body).toEqual({ message: errorException.message });
  });

  it('Error: Ticket Not Activable: already activated 2', async () => {
    const { status, body } = await supertest(server)
      .patch(`${BASE_URL}/${ticketsThatShouldFail.already_activated_2.id}`)
      .set('Authorization', `Fv ${config.apikey}`)
      .send({
        assistants: [assistantSuccessful],
        remarketing,
        idx,
      });

    const errorException = TicketActivationError.alreadyActive({ context: ActivateTicketUseCase.name });

    expect(status).toBe(HTTP_CODES.CONFLICT_409);
    expect(body).toEqual({ message: errorException.message });
  });

  it('Error: Ticket Not Activable: annulled', async () => {
    const { status, body } = await supertest(server)
      .patch(`${BASE_URL}/${ticketsThatShouldFail.annulled.id}`)
      .set('Authorization', `Fv ${config.apikey}`)
      .send({
        assistants: [assistantSuccessful],
        remarketing,
        idx,
      });

    const errorException = TicketActivationError.isAnnulled({ context: ActivateTicketUseCase.name });

    expect(status).toBe(HTTP_CODES.CONFLICT_409);
    expect(body).toEqual({ message: errorException.message });
  });

  it('Error: Ticket Not Activable: sale type not rrpp', async () => {
    const { status, body } = await supertest(server)
      .patch(`${BASE_URL}/${ticketsThatShouldFail.sale_type_not_rrpp.id}`)
      .set('Authorization', `Fv ${config.apikey}`)
      .send({
        assistants: [assistantSuccessful],
        remarketing,
        idx,
      });

    const errorException = TicketActivationError.badSaleType({ context: ActivateTicketUseCase.name });

    expect(status).toBe(HTTP_CODES.CONFLICT_409);
    expect(body).toEqual({ message: errorException.message });
  });

  it('Error: Ticket Not Activable: phone error', async () => {
    const { status, body } = await supertest(server)
      .patch(`${BASE_URL}/${ticket.id}`)
      .set('Authorization', `Fv ${config.apikey}`)
      .send({
        assistants: [assistantPhoneError],
        remarketing,
        idx,
      });

    const errorException = AssistantDataError.phone({
      context: ActivateTicketUseCase.name, target: 1, message: `Invalid phone: ${assistantPhoneError.phone}`,
    });

    expect(status).toBe(HTTP_CODES.CONFLICT_409);
    expect(body).toEqual({ message: errorException.message });
  });

  it('Error: Ticket Not Activable: document number error', async () => {
    const { status, body } = await supertest(server)
      .patch(`${BASE_URL}/${ticket.id}`)
      .set('Authorization', `Fv ${config.apikey}`)
      .send({
        assistants: [assistantDocumentNumberError],
        remarketing,
        idx,
      });

    const errorException = AssistantDataError.documentNumber({
      context: ActivateTicketUseCase.name,
      message: `The document number ${assistantDocumentNumberError.personalDocument?.number} is invalid`,
      target: 1,
    });

    expect(status).toBe(HTTP_CODES.CONFLICT_409);
    expect(body).toEqual({ message: errorException.message });
  });

  it('Success: Single ticket activated', async () => {
    const { status, body } = await supertest(server)
      .patch(`${BASE_URL}/${ticket.id}`)
      .set('Authorization', `Fv ${config.apikey}`)
      .send({
        assistants: [assistantSuccessful],
        remarketing,
        idx,
      });

    expect(status).toBe(HTTP_CODES.NO_CONTENT_204);
    expect(body).toEqual({});

    const ticketCriteria = TicketCriteriaMother.idToMatch(UniqueEntityID.build(ticket.id));
    const ticketUpdatedOrError = await ticketRepository.find(ticketCriteria);

    expect(ticketUpdatedOrError.isRight()).toBeTruthy();

    const ticketUpdated = ticketUpdatedOrError.value as Ticket;

    const now = FvDate.create();
    const ONE_MINUTE_PAST = now.subtractMinutes(1);
    const ONE_MINUTE_FUTURE = now.addMinutes(1);

    const ticketUpdatedActivatedAt = FvDate.create(ticketUpdated.activatedAt.get());

    expect(ticketUpdated.id).toBe(ticket.id);
    expect(ticketUpdated.organizationId).toBe(ticket.organizationId);
    expect(ticketUpdated.eventId).toBe(ticket.eventId);
    expect(ticketUpdated.typeId).toBe(ticket.typeId);
    expect(ticketUpdated.state).toBe(ETicketStates.ACTIVATED);
    expect(ticketUpdated.paid).toBeTruthy();
    expect(ticketUpdatedActivatedAt.isGreaterThan(ONE_MINUTE_PAST)).toBeTruthy();
    expect(ticketUpdatedActivatedAt.isLessThan(ONE_MINUTE_FUTURE)).toBeTruthy();
    expect(ticketUpdated.idx.get()).toBe(idx);
    expect(ticketUpdated.remarketing).toBe(remarketing);
    expect(ticketUpdated.purchaseId.get()).toBe(purchaseId);

    expect(ticketUpdated.name.get()).toBe(assistantSuccessful.fullname);
    expect(ticketUpdated.dni.get()).toBe(assistantSuccessful.personalDocument?.number);
    expect(ticketUpdated.personalDocumentNumber.get()).toBe(assistantSuccessful.personalDocument?.number);
    expect(ticketUpdated.personalDocumentType.get()).toBe(assistantSuccessful.personalDocument?.type);
    expect(ticketUpdated.phone.get()).toBe(assistantSuccessful.phone);
    expect(ticketUpdated.email.get()).toBe(assistantSuccessful.email);
    expect(ticketUpdated.gender.get()).toBe(assistantSuccessful.gender);
    expect(ticketUpdated.birthDate.fold(() => undefined, birthDate => FvDate.create(birthDate).toSeconds())).toBe(assistantSuccessful.birthDate);
    expect(ticketUpdated.zipCode.get()).toBe(assistantSuccessful.zipCode);
    expect(ticketUpdated.country.get()).toBe(assistantSuccessful.country);
    expect(ticketUpdated.address.get()).toBe(assistantSuccessful.address);
    expect(ticketUpdated.answers).toStrictEqual(assistantSuccessful.answers);
  });

  it('Success: Group tickets activated', async () => {
    const { status, body } = await supertest(server)
      .patch(`${BASE_URL}/${purchaseId}`)
      .set('Authorization', `Fv ${config.apikey}`)
      .send({
        assistants: [assistantSuccessful],
        remarketing,
        idx,
      });

    expect(status).toBe(HTTP_CODES.NO_CONTENT_204);
    expect(body).toEqual({});

    const ticketCriteria = TicketCriteriaMother.idToMatch(UniqueEntityID.build(ticket.id));
    const ticketUpdatedOrError = await ticketRepository.find(ticketCriteria);

    expect(ticketUpdatedOrError.isRight()).toBeTruthy();

    const ticketUpdated = ticketUpdatedOrError.value as Ticket;

    const now = FvDate.create();
    const ONE_MINUTE_PAST = now.subtractMinutes(1);
    const ONE_MINUTE_FUTURE = now.addMinutes(1);

    const ticketUpdatedActivatedAt = FvDate.create(ticketUpdated.activatedAt.get());

    expect(ticketUpdated.id).toBe(ticket.id);
    expect(ticketUpdated.organizationId).toBe(ticket.organizationId);
    expect(ticketUpdated.eventId).toBe(ticket.eventId);
    expect(ticketUpdated.typeId).toBe(ticket.typeId);
    expect(ticketUpdated.state).toBe(ETicketStates.ACTIVATED);
    expect(ticketUpdated.paid).toBeTruthy();
    expect(ticketUpdatedActivatedAt.isGreaterThan(ONE_MINUTE_PAST)).toBeTruthy();
    expect(ticketUpdatedActivatedAt.isLessThan(ONE_MINUTE_FUTURE)).toBeTruthy();
    expect(ticketUpdated.idx.get()).toBe(idx);
    expect(ticketUpdated.remarketing).toBe(remarketing);
    expect(ticketUpdated.purchaseId.get()).toBe(purchaseId);

    expect(ticketUpdated.name.get()).toBe(assistantSuccessful.fullname);
    expect(ticketUpdated.dni.get()).toBe(assistantSuccessful.personalDocument?.number);
    expect(ticketUpdated.personalDocumentNumber.get()).toBe(assistantSuccessful.personalDocument?.number);
    expect(ticketUpdated.personalDocumentType.get()).toBe(assistantSuccessful.personalDocument?.type);
    expect(ticketUpdated.phone.get()).toBe(assistantSuccessful.phone);
    expect(ticketUpdated.email.get()).toBe(assistantSuccessful.email);
    expect(ticketUpdated.gender.get()).toBe(assistantSuccessful.gender);
    expect(ticketUpdated.birthDate.fold(() => undefined, birthDate => FvDate.create(birthDate).toSeconds())).toBe(assistantSuccessful.birthDate);
    expect(ticketUpdated.zipCode.get()).toBe(assistantSuccessful.zipCode);
    expect(ticketUpdated.country.get()).toBe(assistantSuccessful.country);
    expect(ticketUpdated.address.get()).toBe(assistantSuccessful.address);
    expect(ticketUpdated.answers).toStrictEqual(assistantSuccessful.answers);
  });
});
