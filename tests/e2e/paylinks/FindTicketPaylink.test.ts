import { EPaylinkStatus } from '@discocil/fv-domain-library';
import supertest from 'supertest';
import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { PaylinkDependencyIdentifier } from '@/paylinks/domain/dependencyIdentifier/PaylinkDependencyIdentifier';
import { findPaylinkSchema } from '@app/http/@types/cli-api/paylinks/tickets/find/schema';
import { HTTP_CODES } from '@app/http/HttpCodes';
import config from '@config/index';
import { startServer } from '@tests/mocks/Server';
import { EventMother } from '@tests/stubs/event/EventMother';
import { EventRepositoryTest } from '@tests/stubs/event/EventRepositoryTest';
import { OrganizationMother } from '@tests/stubs/organization/OrganizationMother';
import { OrganizationRepositoryTest } from '@tests/stubs/organization/OrganizationRepositoryTest';
import { PaylinkMother } from '@tests/stubs/paylinks/PaylinkMother';
import { TicketTypeMother } from '@tests/stubs/ticketType/TicketTypeMother';
import { TicketTypeRepositoryTest } from '@tests/stubs/ticketType/TicketTypeRepositoryTest';
import { UserMother } from '@tests/stubs/user/UserMother';
import { UserRepositoryTest } from '@tests/stubs/user/UserRepositoryTest';

import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import type { PaylinkRepository } from '@/paylinks/domain/contracts/PaylinkRepository';
import type { IServer } from '@tests/mocks/Server';
import type { RawServerBase } from 'fastify';

const name = 'FindTicketPaylink';

describe(`E2E | ${name}`, () => {
  let httpServer: IServer;
  let server: RawServerBase;

  const BASE_URL = '/api/paylinks';

  const organization = OrganizationMother.buildDefault();
  const event = EventMother.buildWithCustomData({ organizationId: organization.id });
  const ticketType = TicketTypeMother.buildWithCustomData({ organizationId: organization.id, eventId: event.id });
  const user = UserMother.buildWithCustomData({ organizations: [organization.id] });

  const paylinkPending = PaylinkMother.buildWithCustomData({
    organizationId: organization.id,
    eventId: event.id,
    rateId: ticketType.id,
    referrerId: user.id,
    status: EPaylinkStatus.PENDING,
  });

  const paylinkCompleted = PaylinkMother.buildWithCustomData({
    organizationId: organization.id,
    eventId: event.id,
    rateId: ticketType.id,
    referrerId: user.id,
    status: EPaylinkStatus.COMPLETED,
  });

  beforeAll(async () => {
    httpServer = await startServer();
    server = httpServer.getInstance();

    const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

    const organizationRepository = new OrganizationRepositoryTest(dbConnection);
    const eventRepository = new EventRepositoryTest(dbConnection);
    const ticketTypeRepository = new TicketTypeRepositoryTest(dbConnection);
    const paylinkRepository = container.resolve<PaylinkRepository>(PaylinkDependencyIdentifier.PaylinkRepository);
    const userRepository = new UserRepositoryTest(dbConnection);

    await Promise.all([
      organizationRepository.save(organization),
      eventRepository.save(event),
      ticketTypeRepository.save(ticketType),
      paylinkRepository.save(paylinkPending),
      paylinkRepository.save(paylinkCompleted),
      userRepository.save(user),
    ]);
  }, 10_000 * 10);

  afterAll(async () => {
    await httpServer.stop();
  });

  it('Get Ticket Paylink by Activation Code but is already used', async () => {
    const { status, body } = await supertest(server)
      .get(`${BASE_URL}/tickets/${paylinkCompleted.activateCode}`)
      .set('Authorization', `Fv ${config.apikey}`);

    expect(status).toBe(HTTP_CODES.NOT_FOUND_404);
    expect(body).toMatchSchema(findPaylinkSchema.response['4xx']);
  });

  it('Get Ticket Paylink by Activation Code', async () => {
    const { status, body } = await supertest(server)
      .get(`${BASE_URL}/tickets/${paylinkPending.activateCode}`)
      .set('Authorization', `Fv ${config.apikey}`);

    expect(status).toBe(HTTP_CODES.OK_200);
    expect(body.data).toMatchSchema(findPaylinkSchema.response['2xx']);
  });
});
