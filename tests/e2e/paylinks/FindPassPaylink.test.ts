import supertest from 'supertest';
import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { PaylinkDependencyIdentifier } from '@/paylinks/domain/dependencyIdentifier/PaylinkDependencyIdentifier';
import { findPaylinkSchema } from '@app/http/@types/cli-api/paylinks/passes/find/schema';
import { HTTP_CODES } from '@app/http/HttpCodes';
import config from '@config/index';
import { startServer } from '@tests/mocks/Server';
import { OrganizationMother } from '@tests/stubs/organization/OrganizationMother';
import { OrganizationRepositoryTest } from '@tests/stubs/organization/OrganizationRepositoryTest';
import { PaylinkMother } from '@tests/stubs/paylinks/PaylinkMother';

import type { PaylinkRepository } from '@/paylinks/domain/contracts/PaylinkRepository';
import type { IServer } from '@tests/mocks/Server';
import type { RawServerBase } from 'fastify';

const name = 'FindPassPaylink';

describe(`E2E | ${name}`, () => {
  let httpServer: IServer;
  let server: RawServerBase;

  const BASE_URL = '/api/paylinks';

  const organization = OrganizationMother.buildDefault();
  const paylink = PaylinkMother.buildWithCustomData({ organizationId: organization.id });

  let paylinkRepository: PaylinkRepository;
  let organizationRepository: OrganizationRepositoryTest;

  beforeAll(async () => {
    httpServer = await startServer();
    server = httpServer.getInstance();

    paylinkRepository = container.resolve<PaylinkRepository>(PaylinkDependencyIdentifier.PaylinkRepository);
    organizationRepository = new OrganizationRepositoryTest(container.resolve(DependencyIdentifier.DatabaseConnection));

    await Promise.all([paylinkRepository.save(paylink), organizationRepository.save(organization)]);
  }, 10_000 * 10);

  afterAll(async () => {
    await httpServer.stop();
  });

  it('Get Pass Paylink by Activation Code', async () => {
    const { status, body } = await supertest(server)
      .get(`${BASE_URL}/passes/${paylink.activateCode}`)
      .set('Authorization', `Fv ${config.apikey}`);

    expect(status).toBe(HTTP_CODES.OK_200);
    expect(body.data).toMatchSchema(findPaylinkSchema.response['2xx']);
  });
});
