import { MicrositeMongoRepository } from '@/microsite/infrastructure/database/repositories/MicrositeMongoRepository';
import { MicrositeSchemaMapper } from '@tests/stubs/microsite/MicrositeSchemaMapper';

import type { MicrositeRepository } from '@/microsite/domain/contracts/MicrositeRepository';
import type { Microsite } from '@/microsite/domain/entities/Microsite';

export interface IMicrositeRepositoryTest extends MicrositeRepository {
  save: (microsite: Microsite) => Promise<void>;
}

export class MicrositeRepositoryTest extends MicrositeMongoRepository implements IMicrositeRepositoryTest {
  async save(microsite: Microsite): Promise<void> {
    const toSave = MicrositeSchemaMapper.execute(microsite);

    const filter = { _id: microsite.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }
}
