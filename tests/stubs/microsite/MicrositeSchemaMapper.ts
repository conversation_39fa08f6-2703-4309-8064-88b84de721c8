import type { Microsite } from '@/microsite/domain/entities/Microsite';
import type { MicrositeSchemaType } from '@/microsite/infrastructure/database/Schemas/MicrositeSchemaType';

export class MicrositeSchemaMapper {
  static execute(microsite: Microsite): MicrositeSchemaType {
    return {
      _id: microsite.id,
      negocio_id: microsite.organizationId,
      theme_page: microsite.pageTheme.fold(() => undefined, item => item),
      thank_you_page: microsite.thankYouPage.fold(() => undefined, item => item),
      cancel_page: microsite.cancelPage.fold(() => undefined, item => item),
      google_property_id: microsite.propertyIdGoogle.fold(() => undefined, item => item),
      facebook_pixel_id: microsite.facebookPixelId.fold(() => undefined, item => item),
      facebook_access_token: microsite.facebookAccessToken.fold(() => undefined, item => item),
      services: [...microsite.services],
      created_at: microsite.createdAt,
      created_by: microsite.createdBy,
      updated_at: microsite.updatedAt,
      updated_by: microsite.updatedBy,
      removed_at: microsite.removedAt,
      removed_by: microsite.removedBy,
    };
  }
}
