import { UniqueEntityID } from '@discocil/fv-domain-library/domain';

import type { PassTypeEventSchemaType } from '@/passes/passTypes/infrastructure/database/Schemas/PassTypeEventSchemaType';

export class PassTypeEventMother {
  static primitivesDefault(): PassTypeEventSchemaType {
    return {
      _id: UniqueEntityID.create().toPrimitive(),
      negocio_id: UniqueEntityID.create().toPrimitive(),
      evento_id: UniqueEntityID.create().toPrimitive(),
      date: '',
      type_id: UniqueEntityID.create().toPrimitive(),
      condition_id: '',
      created_at: Date.now(),
      created_by: UniqueEntityID.create().toPrimitive(),
      updated_at: Date.now(),
      updated_by: UniqueEntityID.create().toPrimitive(),
      removed_at: 0,
      removed_by: '',
    };
  }

  static buildDefault(): PassTypeEventSchemaType {
    return this.primitivesDefault();
  }

  static buildWithCustomData(customData: Partial<PassTypeEventSchemaType> = {}): PassTypeEventSchemaType {
    return {
      ...this.primitivesDefault(),
      ...customData,
    };
  }

  static buildConnection(
    {
      organizationId,
      eventId,
      passTypeId,
    }: {
      organizationId: string;
      eventId: string;
      passTypeId: string;
    },
  ): PassTypeEventSchemaType {
    return this.buildWithCustomData({
      negocio_id: organizationId,
      evento_id: eventId,
      type_id: passTypeId,
    });
  }
}
