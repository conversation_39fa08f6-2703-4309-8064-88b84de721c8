import { Schema } from 'mongoose';

import { PassTypeEventMongoRepository } from '@/passes/passTypes/infrastructure/database/repositories/PassTypeEventMongoRepository';
import { passTypeEventSchema } from '@/passes/passTypes/infrastructure/database/Schemas/PassTypeEventSchema';

import type { PassTypeEventSchemaType } from '@/passes/passTypes/infrastructure/database/Schemas/PassTypeEventSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export interface IPassTypeEventRepositoryTest {
  save: (passTypeEvent: Partial<PassTypeEventSchemaType>) => Promise<void>;
  saveMany: (passTypeEvents: Array<Partial<PassTypeEventSchemaType>>) => Promise<void>;
  remove: (criteria: Criteria) => Promise<void>;
  removeMany: (criteria: Criteria) => Promise<void>;
}

export class PassTypeEventRepositoryTest extends PassTypeEventMongoRepository implements IPassTypeEventRepositoryTest {
  protected getSchema(): Schema {
    return new Schema(passTypeEventSchema);
  }

  async save(passTypeEvent: Partial<PassTypeEventSchemaType>): Promise<void> {
    const connection = await this.getConnection();
    const filter = { _id: passTypeEvent._id };
    const update = { $set: passTypeEvent };
    const options = { upsert: true };

    await connection.updateOne(filter, update, options);
  }

  async saveMany(passTypeEvents: Array<Partial<PassTypeEventSchemaType>>): Promise<void> {
    const connection = await this.getConnection();

    await connection.insertMany(passTypeEvents);
  }

  async remove(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteOne(filterQuery.filter);
  }

  async removeMany(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteMany(filterQuery.filter);
  }
}
