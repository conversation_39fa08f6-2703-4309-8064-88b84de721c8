import {
  ECurrency,
  EMicrositeServices,
  FvDate,
  Maybe,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { EMicrositeContainerType } from '@/tracking/domain/contracts/EntityContracts';
import { EventTracking } from '@/tracking/domain/entities/EventTracking';
import { EEventChannel, EEventTypes } from '@/tracking/domain/value-objects/EventType';

import { MotherCreator } from '../MotherCreator';

import type { EventTrackingCreatePrimitives, EventTrackingPrimitives } from '@/tracking/domain/entities/EventTracking';

export class EventTrackingMother {
  static primitivesDefault(): EventTrackingCreatePrimitives {
    return {
      name: EEventTypes.AddToCart,
      organizationId: UniqueEntityID.create().toPrimitive(),
      eventId: Maybe.some(UniqueEntityID.create().toPrimitive()),
      channel: EEventChannel.fv,
      data: {
        externalId: MotherCreator.random().lorem.slug(5),
        urlPage: 'https://fv.com/event',
        route: null,
        user: null,
        fb: null,
        price: {
          amount: 1,
          currency: ECurrency.EUR,
        },
        items: [],
        numItems: 0,
        totalPrice: {
          amount: 1,
          currency: ECurrency.EUR,
        },
        remoteAddress: '127.0.0.1',
        userAgent: MotherCreator.random().internet.userAgent(),
        serviceType: EMicrositeServices.TICKETS,
        containerType: EMicrositeContainerType.WEB,
      },
    };
  }

  static buildDefault(): EventTracking {
    const eventTrackingOrError = EventTracking.create(this.primitivesDefault());

    return eventTrackingOrError.value as EventTracking;
  }

  static buildWithCustomData(primitivesCustoms: Partial<EventTrackingPrimitives>): EventTracking {
    const primitives = {
      ...this.primitivesDefault(),
      id: UniqueEntityID.create().toPrimitive(),
      date: primitivesCustoms.date ?? FvDate.create().toPrimitive(),
      ...primitivesCustoms,
    };
    const entityResult = EventTracking.build(primitives);

    return entityResult.value as EventTracking;
  }
}
