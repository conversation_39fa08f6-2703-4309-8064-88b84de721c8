import { BlockSchemaMapper } from '@/blocks/infrastructure/database/mappers/BlockSchemaMapper';
import { BlockMongoRepository } from '@/blocks/infrastructure/database/repositories/BlockMongoRepository';

import type { BlockRepository } from '@/blocks/domain/contracts/BlockRepository';
import type { Block, Blocks } from '@/blocks/domain/entities/Block';
import type { BlockSchemaType } from '@/blocks/infrastructure/database/schemas/BlockSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export interface IBlockRepositoryTest extends BlockRepository {
  remove: (criteria: Criteria) => Promise<void>;
  removeMany: (criteria: Criteria) => Promise<void>;
  save: (block: Block) => Promise<void>;
  saveMany: (blocks: Blocks) => Promise<void>;
}

export class BlockRepositoryTest extends BlockMongoRepository implements IBlockRepositoryTest {
  async remove(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteOne(filterQuery.filter);
  }

  async removeMany(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteMany(filterQuery.filter);
  }

  async save(block: Block): Promise<void> {
    const toSave = BlockSchemaMapper.execute(block);

    const filter = { _id: block.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(blocks: Blocks): Promise<void> {
    const connection = await this.getConnection();
    const models: BlockSchemaType[] = [];

    blocks.forEach((block: Block) => models.push(BlockSchemaMapper.execute(block)));

    await connection.insertMany(models);
  }
}
