import { Maybe } from '@discocil/fv-domain-library/domain';

import { TicketTypeMother } from '../ticketType/TicketTypeMother';

import { AssistantMother } from './AssistantMother';

import type { CheckAssistantDataRequest } from '@/tickets/tickets/domain/contracts/PurchaseContracts';

export class CheckAssistantDataRequestMother {
  static makeDefault(): CheckAssistantDataRequest {
    return {
      ticketType: TicketTypeMother.createPublicPassingAllValidation(),
      assistants: AssistantMother.createAssistants(),
      organizationTicketFields: Maybe.some({
        phone: {
          type: 'text',
          required: true,
          label: 'string',
          slug: 'string',
        },
        personalDocumentType: {
          type: 'text',
          required: true,
          label: 'string',
          slug: 'string',
        },
      }),
    };
  }
}
