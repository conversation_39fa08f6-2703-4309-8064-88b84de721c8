
import {
  Collection,
  ECountryCode, EDocumentType, FvDate, FvNumber, Gender,
  Maybe,
  UniqueEntityID,
} from '@discocil/fv-domain-library';

import { Assistant } from '@/tickets/tickets/domain/entities/Assistant';

import { MotherCreator } from '../MotherCreator';
import { buildSupplement } from '../ticketType/TicketTypeMother';

import type {
  AssistantRequestPrimitives, Assistants, AssistantSupplement, PurchaseSupplement,
} from '@/tickets/tickets/domain/entities/Assistant';
import type { activateAssistant } from '@app/http/@types/cli-api/tickets/activate/bodySchema';
import type { Static } from '@sinclair/typebox';

export class AssistantMother {
  static createAssistantRequestPrimitives(customs?: Partial<AssistantRequestPrimitives>, supplementsQuantity = 1): AssistantRequestPrimitives {
    const supplements: PurchaseSupplement[] = Array.from({ length: supplementsQuantity }, () => {
      return {
        id: UniqueEntityID.create().toPrimitive(),
        purchaseQuantity: MotherCreator.random().number.int(),
      };
    });

    return {
      supplements,
      fullname: Maybe.none(),
      email: MotherCreator.random().internet.email(),
      emailConfirmation: Maybe.fromValue(MotherCreator.random().internet.email()),
      phone: Maybe.fromValue(`+34${MotherCreator.phone.es()}`),
      birthDate: Maybe.none(),
      gender: Maybe.none(),
      personalDocument: Maybe.some({
        type: EDocumentType.OTHER,
        number: MotherCreator.random().number.int().toString(),
      }),
      address: Maybe.none(),
      country: Maybe.none(),
      zipCode: Maybe.none(),
      answers: Array.from({ length: 5 }, () => {
        return {
          id: MotherCreator.random().lorem.words(10),
          answer: MotherCreator.random().lorem.words(10),
        };
      }),
      optionId: Maybe.none(),
      ...customs,
    };
  }

  static createAssistant(customs?: Partial<Assistant>): Assistant {
    const supplements = Collection.new<AssistantSupplement>('id');

    supplements.add({
      id: UniqueEntityID.create(),
      purchaseQuantity: FvNumber.build(MotherCreator.random().number.int()),
      ticketTypeSupplement: buildSupplement(),
    });

    const entity = Assistant.create({
      supplements,
      fullname: Maybe.none(),
      email: MotherCreator.random().internet.email(),
      emailConfirmation: Maybe.fromValue(MotherCreator.random().internet.email()),
      phone: Maybe.fromValue(`+34${MotherCreator.phone.es()}`),
      birthDate: Maybe.none(),
      gender: Maybe.none(),
      personalDocument: Maybe.some({
        type: EDocumentType.OTHER,
        number: MotherCreator.random().number.int().toString(),
      }),
      address: Maybe.none(),
      country: Maybe.none(),
      zipCode: Maybe.none(),
      answers: Array.from({ length: 5 }, () => {
        return {
          id: MotherCreator.random().lorem.words(10),
          answer: MotherCreator.random().lorem.words(10),
        };
      }),
      optionId: Maybe.none(),
      ticketTypeId: UniqueEntityID.create().toPrimitive(),
      ...customs,
    });

    return entity;
  }

  static createAssistants(length = 5): Assistants {
    const assistants = Collection.new<Assistant>('id');

    for (let i = 0; i < length; i++) {
      assistants.add(this.createAssistant());
    }

    return assistants;
  }

  static createActivateAssistantRequest(ticketId: string, customs?: Partial<Static<typeof activateAssistant>>): Static<typeof activateAssistant> {
    const email = MotherCreator.random().internet.email();

    return {
      ticketId,
      fullname: MotherCreator.random().lorem.words(10),
      email,
      phone: `+34${MotherCreator.phone.es()}`,
      zipCode: MotherCreator.random().location.zipCode(),
      gender: Gender.default().code,
      personalDocument: {
        number: MotherCreator.random().lorem.words(10),
        type: EDocumentType.OTHER,
      },
      birthDate: MotherCreator.random().datatype.boolean()
        ? FvDate.create(MotherCreator.random().date.birthdate()).toSeconds()
        : undefined,
      address: MotherCreator.random().location.streetAddress(),
      country: ECountryCode.ES,
      emailConfirmation: email,
      answers: [
        {
          id: MotherCreator.random().lorem.words(3),
          answer: MotherCreator.random().lorem.words(10),
        },
        {
          id: MotherCreator.random().lorem.words(3),
          answer: MotherCreator.random().lorem.words(10),
        },
      ],
      ...customs,
    };
  }
}
