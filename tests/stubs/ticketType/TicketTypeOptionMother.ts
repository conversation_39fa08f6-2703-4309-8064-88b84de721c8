import {
  EGGDD,
  FvDate,
  Maybe,
} from '@discocil/fv-domain-library/domain';

import {
  TicketTypeOption, type OptionConfig, type TicketTypeOptionPrimitives,
} from '@/tickets/ticketsTypes/domain/entities/TicketTypeOption';

import { MotherCreator } from '../MotherCreator';

import type { ECurrency } from '@discocil/fv-domain-library/domain';

export class TicketTypeOptionMother {
  static optionPrimitives(config?: Partial<OptionConfig>): TicketTypeOptionPrimitives {
    const option: TicketTypeOptionPrimitives = {
      id: MotherCreator.random().lorem.words(5),
      price: MotherCreator.random().number.int(500),
      name: Maybe.some(MotherCreator.random().lorem.words(8)),
      ggddType: EGGDD.PERCENTAGE,
      ggddAmount: MotherCreator.random().number.int(50),
      age: MotherCreator.random().number.int({ min: 10, max: 100 }),
      content: Maybe.some(MotherCreator.random().lorem.words(20)),
      additionalInfo: Maybe.some(MotherCreator.random().lorem.words(20)),
      to: FvDate.create(MotherCreator.random().date.future()).toPrimitive(),
      max: MotherCreator.random().number.int({ min: 1, max: 20 }),
      image: Maybe.some(MotherCreator.random().lorem.words(20)),
      config: Maybe.some({
        ...this.config(),
        ...config,
      }),
    };

    return option;
  }

  static option(currency: ECurrency, config?: Partial<OptionConfig>): TicketTypeOption {
    const option = this.optionPrimitives(config);

    const entityResult = TicketTypeOption.build(option, currency);

    return entityResult.value as TicketTypeOption;
  }

  private static config(): OptionConfig {
    return {
      availableAmount: MotherCreator.random().number.int({ min: 10, max: 20 }),
      isFuture: MotherCreator.random().datatype.boolean(),
      isCurrent: false,
      totalSold: 0,
    };
  }

  private static configNotAvailable(): OptionConfig {
    return {
      availableAmount: 0,
      isFuture: MotherCreator.random().datatype.boolean(),
      isCurrent: false,
      totalSold: 0,
    };
  }

  static getMany(length: number, currency: ECurrency, config?: Partial<OptionConfig>): TicketTypeOption[] {
    return Array.from({ length }, () => this.option(currency, config));
  }

  static getManyNotAvailable(length: number, currency: ECurrency): TicketTypeOption[] {
    return Array.from({ length }, () => this.option(currency, this.configNotAvailable()));
  }

  static getManyInPast(length: number, currency: ECurrency): TicketTypeOption[] {
    return Array.from({ length }, () => this.option(
      currency,
      {
        availableAmount: MotherCreator.random().number.int({ min: 10, max: 20 }),
        isFuture: false,
        isCurrent: false,
        totalSold: 0,
      },
    ));
  }
}
