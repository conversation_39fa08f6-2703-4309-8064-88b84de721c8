import { UniqueEntityID } from '@discocil/fv-domain-library/domain';

import { ReservationsConfiguration, type ReservationsConfigurationPrimitives } from '@/reservations/configuration/domain/dpos/ReservationsConfiguration';

import { MotherCreator } from '../MotherCreator';

export class ReservationsConfigurationMother {
  static primitivesDefault(): ReservationsConfigurationPrimitives {
    return {
      organizationId: UniqueEntityID.create().toPrimitive(),
      clientsCanMakeReservations: MotherCreator.random().datatype.boolean(),
      clientsCanSelectSpace: MotherCreator.random().datatype.boolean(),
    };
  }

  static createDefault(): ReservationsConfiguration {
    const primitives = this.primitivesDefault();

    const reservationsConfigurationValue = ReservationsConfiguration.build({ ...primitives });

    if (reservationsConfigurationValue.isLeft()) {
      throw new Error('Error creating reservation');
    }

    return reservationsConfigurationValue.value;
  }

  static createWithCustomData(partialPrimitives: Partial<ReservationsConfigurationPrimitives>): ReservationsConfiguration {
    const primitives = { ...this.primitivesDefault(), ...partialPrimitives };

    const reservationsConfigurationValue = ReservationsConfiguration.build({ ...primitives });

    if (reservationsConfigurationValue.isLeft()) {
      throw new Error('Error creating reservation');
    }

    return reservationsConfigurationValue.value;
  }
}
