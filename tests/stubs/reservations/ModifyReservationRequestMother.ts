import { Maybe, UniqueEntityID } from '@discocil/fv-domain-library/domain';

import { MotherCreator } from '../MotherCreator';

import { ReservationMother } from './ReservationMother';

import type { ModifyReservationRequest } from '@/reservations/reservations/domain/dtos/ModifyReservationRequest';

export class ModifyReservationRequestMother {
  static createDefault(): ModifyReservationRequest {
    const reservation = ReservationMother.createDefault();

    return {
      reservationId: UniqueEntityID.build(reservation.id),
      organizationId: UniqueEntityID.build(reservation.organizationId),
      observations: Maybe.fromValue(MotherCreator.random().lorem.words()),
      shouldAddSubscriber: true,
      isFullPayment: true,
      purchaseId: UniqueEntityID.create(),
      successUrl: MotherCreator.random().internet.url(),
      failureUrl: MotherCreator.random().internet.url(),
      ip: MotherCreator.random().internet.ip(),
      applicationId: UniqueEntityID.create().toPrimitive(),
      totalAmount: MotherCreator.random().number.int(),
      formFields: [{
        title: MotherCreator.random().lorem.words(),
        value: MotherCreator.random().lorem.words(),
      }],
    };
  }
}
