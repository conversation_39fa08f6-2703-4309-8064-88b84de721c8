
import {
  left, NotFoundError, right,
} from '@discocil/fv-domain-library';

import { ReservationsConfiguration, type ReservationsConfigurationEither } from '@/reservations/configuration/domain/dpos/ReservationsConfiguration';

import type { ReservationsConfigurationRepository } from '@/reservations/configuration/domain/contracts/ReservationsConfigurationRepository';

export interface IReservationConfigurationRepositoryTest {
  save: (reservationsConfiguration: ReservationsConfiguration) => Promise<void>;
}

export class ReservationsConfigurationRepositoryTest implements IReservationConfigurationRepositoryTest, ReservationsConfigurationRepository {
  private readonly configurations: ReservationsConfiguration[] = [];

  async save(reservationsConfiguration: ReservationsConfiguration): Promise<void> {
    this.configurations.push(reservationsConfiguration);
  }

  async find(organizationId: string): Promise<ReservationsConfigurationEither> {
    const configuration = this.configurations.find(configuration => configuration.organizationId === organizationId);

    if (!configuration) {
      return left(NotFoundError.build({ context: this.constructor.name, target: ReservationsConfiguration.name }));
    }

    return right(configuration);
  }
}
