import {
  ECurrency,
  EFeeType,
  EReservationState,
  Maybe,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { Reservation } from '@/reservations/reservations/domain/entities/Reservation';

import { MotherCreator } from '../MotherCreator';

import type { Rate } from '@/reservations/rates/domain/entities/Rate';
import type { ReservationPrimitives } from '@/reservations/reservations/domain/entities/Reservation';
import type { Money } from '@discocil/fv-domain-library/domain';

export class ReservationMother {
  static primitivesDefault(): ReservationPrimitives {
    return {
      id: UniqueEntityID.create().toPrimitive(),
      name: MotherCreator.random().lorem.words(1),
      organizationId: UniqueEntityID.create().toPrimitive(),
      eventId: UniqueEntityID.create().toPrimitive(),
      numberOfPeople: MotherCreator.random().number.int(),
      observations: Maybe.fromValue(MotherCreator.random().lorem.words(3)),
      referentId: Maybe.fromValue(UniqueEntityID.create().toPrimitive()),
      assignerId: Maybe.fromValue(UniqueEntityID.create().toPrimitive()),
      state: EReservationState.ACCEPTED,
      qrCode: MotherCreator.random().lorem.words(1),
      activationCode: MotherCreator.random().lorem.words(1),
      price: {
        amount: MotherCreator.random().number.float(),
        currency: ECurrency.EUR,
      },
      currency: ECurrency.EUR,
      allowCompletePayment: true,
      allowCustomDeposits: false,
      allowExternalPayments: false,
      rate: Maybe.none<Rate>(),
      deposit: Maybe.none<Money>(),
      coverCharge: Maybe.none<Money>(),
      payments: [],
      formFields: [],
      administrationFee: {
        type: EFeeType.FIXED,
        amount: MotherCreator.random().number.float(),
      },
      paymentUrl: Maybe.none<string>(),
      subscription: Maybe.fromValue({
        ip: MotherCreator.random().internet.ip(),
        applicationId: UniqueEntityID.create().toPrimitive(),
      }),
      requestBillingInfo: false,
    };
  }

  static createDefault(): Reservation {
    const primitives = this.primitivesDefault();

    const reservationValue = Reservation.create({
      ...primitives,
      shouldSubscribe: false,
    });

    if (reservationValue.isLeft()) {
      throw new Error('Error creating reservation');
    }

    return reservationValue.value;
  }

  static createWithCustomData(partialPrimitives: Partial<ReservationPrimitives>): Reservation {
    const primitives = { ...this.primitivesDefault(), ...partialPrimitives };

    const reservationValue = Reservation.create({
      ...primitives,
      shouldSubscribe: false,
    });

    if (reservationValue.isLeft()) {
      throw new Error('Error creating reservation');
    }

    return reservationValue.value;
  }
}
