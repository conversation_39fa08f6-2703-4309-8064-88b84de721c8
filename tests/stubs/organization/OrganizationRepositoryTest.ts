import { OrganizationSchemaMapper } from '@/organizations/organizations/infrastructure/database/mappers/OrganizationSchemaMapper';
import { OrganizationMongoRepository } from '@/organizations/organizations/infrastructure/database/repositories/OrganizationMongoRepository';

import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { Organization, Organizations } from '@/organizations/organizations/domain/entities/Organization';
import type { OrganizationSchemaType } from '@/organizations/organizations/infrastructure/database/schemas/OrganizationSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export interface IOrganizationRepositoryTest extends OrganizationRepository {
  remove: (criteria: Criteria) => Promise<void>;
  removeMany: (criteria: Criteria) => Promise<void>;
  save: (organization: Organization) => Promise<void>;
  saveMany: (organizations: Organizations) => Promise<void>;
}

export class OrganizationRepositoryTest extends OrganizationMongoRepository implements IOrganizationRepositoryTest {
  async remove(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteOne(filterQuery.filter);
  }

  async removeMany(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteMany(filterQuery.filter);
  }

  async save(organization: Organization): Promise<void> {
    const toSave = OrganizationSchemaMapper.execute(organization);

    const filter = { _id: organization.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const modelQuery = await this.getConnection();

    await modelQuery.updateOne(filter, update, options);
  }

  async saveMany(organizations: Organizations): Promise<void> {
    const connection = await this.getConnection();
    const models: OrganizationSchemaType[] = [];

    organizations.forEach((organization: Organization) => {
      models.push(OrganizationSchemaMapper.execute(organization));
    });

    await connection.insertMany(models);
  }
}
