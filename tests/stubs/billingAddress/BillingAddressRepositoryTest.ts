import { BillingAddressSchemaMapper } from '@/billingAddresses/infrastructure/database/mappers/BillingAddressSchemaMapper';
import { BillingAddressMongoRepository } from '@/billingAddresses/infrastructure/database/repositories/BillingAddressMongoRepository';

import type { BillingAddressRepository } from '@/billingAddresses/domain/contracts/BillingAddressRepository';
import type { BillingAddress, BillingAddresses } from '@/billingAddresses/domain/entities/BillingAddress';
import type { BillingAddressSchemaType } from '@/billingAddresses/infrastructure/database/schemas/BillingAddressSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export interface IBillingAddressRepositoryTest extends BillingAddressRepository {
  remove: (criteria: Criteria) => Promise<void>;
  removeMany: (criteria: Criteria) => Promise<void>;
  save: (billingAddress: BillingAddress) => Promise<void>;
  saveMany: (billingAddresses: BillingAddresses) => Promise<void>;
}

export class BillingAddressRepositoryTest extends BillingAddressMongoRepository implements IBillingAddressRepositoryTest {
  async remove(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteOne(filterQuery.filter);
  }

  async removeMany(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteMany(filterQuery.filter);
  }

  async save(billingAddress: BillingAddress): Promise<void> {
    const toSave = BillingAddressSchemaMapper.execute(billingAddress);

    const filter = { _id: billingAddress.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(billingAddresses: BillingAddresses): Promise<void> {
    const connection = await this.getConnection();
    const models: BillingAddressSchemaType[] = [];

    billingAddresses.forEach((billingAddress: BillingAddress) => models.push(BillingAddressSchemaMapper.execute(billingAddress)));

    await connection.insertMany(models);
  }
}
