import { OrganizationConfigurationSchemaMapper } from '@/organizations/organizationConfigurations/infrastructure/database/mappers/OrganizationConfigurationSchemaMapper';
import { OrganizationConfigurationMongoRepository } from '@/organizations/organizationConfigurations/infrastructure/database/repositories/OrganizationConfigurationMongoRepository';

import type { OrganizationConfigurationRepository } from '@/organizations/organizationConfigurations/domain/contracts/OrganizationConfigurationRepository';
import type { OrganizationConfiguration, OrganizationConfigurations } from '@/organizations/organizationConfigurations/domain/entities/OrganizationConfiguration';
import type { OrganizationConfigurationSchemaType } from '@/organizations/organizationConfigurations/infrastructure/database/schemas/OrganizationConfigurationSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export interface IOrganizationConfigurationRepositoryTest extends OrganizationConfigurationRepository {
  remove: (criteria: Criteria) => Promise<void>;
  removeMany: (criteria: Criteria) => Promise<void>;
  save: (organizationConfiguration: OrganizationConfiguration) => Promise<void>;
  saveMany: (organizationConfigurations: OrganizationConfigurations) => Promise<void>;
}

export class OrganizationConfigurationRepositoryTest
  extends OrganizationConfigurationMongoRepository
  implements IOrganizationConfigurationRepositoryTest {
  async remove(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteOne(filterQuery.filter);
  }

  async removeMany(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteMany(filterQuery.filter);
  }

  async save(organizationConfiguration: OrganizationConfiguration): Promise<void> {
    const toSave = OrganizationConfigurationSchemaMapper.execute(organizationConfiguration);

    const filter = { _id: organizationConfiguration.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const modelQuery = await this.getConnection();

    await modelQuery.updateOne(filter, update, options);
  }

  async saveMany(organizationConfigurations: OrganizationConfigurations): Promise<void> {
    const connection = await this.getConnection();
    const models: OrganizationConfigurationSchemaType[] = [];

    organizationConfigurations.forEach((organizationConfiguration: OrganizationConfiguration) => {
      models.push(OrganizationConfigurationSchemaMapper.execute(organizationConfiguration));
    });

    await connection.insertMany(models);
  }
}
