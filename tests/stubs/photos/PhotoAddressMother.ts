import { Maybe, UniqueEntityID } from '@discocil/fv-domain-library/domain';

import { Photo } from '@/photos/domain/entities/Photo';

import { MotherCreator } from '../MotherCreator';

import type { PhotoEither, PhotoPrimitives } from '@/photos/domain/entities/Photo';

export class PhotoMother {
  static readonly primitivesDefault = (): PhotoPrimitives => {
    return {
      id: UniqueEntityID.create().toPrimitive(),
      organizationId: UniqueEntityID.create().toPrimitive(),
      eventId: UniqueEntityID.create().toPrimitive(),
      oficial: MotherCreator.random().number.int({ min: 0, max: 1 }),
      size: MotherCreator.random().number.int({ min: 0, max: 1 }),
      url: MotherCreator.random().internet.url(),
      name: Maybe.fromValue(MotherCreator.random().lorem.text()),
      sizes: Maybe.some({
        medium: {
          isDefault: MotherCreator.random().datatype.boolean(),
          name: MotherCreator.random().image.avatar(),
          url: MotherCreator.random().image.avatar(),
        },
        small: {
          isDefault: MotherCreator.random().datatype.boolean(),
          name: MotherCreator.random().image.avatar(),
          url: MotherCreator.random().image.url(),
        },
      }),
      createdAt: MotherCreator.random().date.past().getTime(),
      createdBy: UniqueEntityID.create().toPrimitive(),
      updatedAt: MotherCreator.random().date.recent().getTime(),
      updatedBy: UniqueEntityID.create().toPrimitive(),
      removedAt: 0,
      removedBy: '',
    };
  };

  static buildDefaultEither(): PhotoEither {
    const photo = Photo.build(PhotoMother.primitivesDefault());

    return photo;
  }

  static buildEitherWithCustomData(customProps: Partial<PhotoPrimitives>): PhotoEither {
    const finalProps = { ...PhotoMother.primitivesDefault(), ...customProps };
    const photo = Photo.build(finalProps);

    return photo;
  }

  static buildDefault(): Photo {
    return this.buildDefaultEither().value as Photo;
  }

  static buildWithCustomData(customProps: Partial<PhotoPrimitives>): Photo {
    return this.buildEitherWithCustomData(customProps).value as Photo;
  }
}
