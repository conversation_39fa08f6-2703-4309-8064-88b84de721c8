import { PhotoSchemaMapper } from '@/photos/infrastructure/database/mappers/PhotoSchemaMapper';
import { PhotoMongoRepository } from '@/photos/infrastructure/database/repositories/PhotoMongoRepository';

import type { PhotoRepository } from '@/photos/domain/contracts/PhotoRepository';
import type { Photo, Photos } from '@/photos/domain/entities/Photo';
import type { PhotoSchemaType } from '@/photos/infrastructure/database/schemas/PhotoSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export interface IPhotoRepositoryTest extends PhotoRepository {
  remove: (criteria: Criteria) => Promise<void>;
  removeMany: (criteria: Criteria) => Promise<void>;
  save: (photo: Photo) => Promise<void>;
  saveMany: (photos: Photos) => Promise<void>;
}

export class PhotoRepositoryTest extends PhotoMongoRepository implements IPhotoRepositoryTest {
  async remove(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteOne(filterQuery.filter);
  }

  async removeMany(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteMany(filterQuery.filter);
  }

  async save(photo: Photo): Promise<void> {
    const toSave = PhotoSchemaMapper.execute(photo);

    const filter = { _id: photo.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(photos: Photos): Promise<void> {
    const connection = await this.getConnection();
    const models: Array<Partial<PhotoSchemaType>> = [];

    photos.forEach((photo: Photo) => models.push(PhotoSchemaMapper.execute(photo)));

    await connection.insertMany(models);
  }
}
