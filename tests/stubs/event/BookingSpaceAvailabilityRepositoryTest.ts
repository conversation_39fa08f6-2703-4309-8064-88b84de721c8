import { BookingSpaceAvailabilitySchemaMapper } from '@tests/events/events/infrastructure/database/mappers/BookingSpaceAvailabilitySchemaMapper';
import { BookingSpaceAvailabilityMongoRepository } from '@/events/events/infrastructure/database/repositories/BookingSpaceAvailabilityMongoRepository';

import type { BookingSpaceAvailabilityRepository } from '@/events/events/domain/contracts/bookingSpaceAvailability/BookingSpaceAvailabilityRepository';
import type { BookingSpaceAvailability } from '@/events/events/domain/entities/BookingSpaceAvailability';

export interface IBookingSpaceAvailabilityRepositoryTest extends BookingSpaceAvailabilityRepository {
  save: (availability: BookingSpaceAvailability) => Promise<void>;
}

export class BookingSpaceAvailabilityRepositoryTest
  extends BookingSpaceAvailabilityMongoRepository
  implements IBookingSpaceAvailabilityRepositoryTest {
  async save(availability: BookingSpaceAvailability): Promise<void> {
    const toSave = BookingSpaceAvailabilitySchemaMapper.execute(availability);
    const filter = { _id: availability.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }
}
