import { ArtistSchemaMapper } from '@/events/artists/infrastructure/database/mappers/ArtistSchemaMapper';
import { ArtistMongoRepository } from '@/events/artists/infrastructure/database/repositories/ArtistMongoRepository';

import type { ArtistRepository } from '@/events/artists/domain/contracts/ArtistRepository';
import type { Artist, Artists } from '@/events/artists/domain/entities/Artist';
import type { ArtistSchemaType } from '@/events/artists/infrastructure/database/schemas/ArtistSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export interface IArtistRepositoryTest extends ArtistRepository {
  remove: (criteria: Criteria) => Promise<void>;
  removeMany: (criteria: Criteria) => Promise<void>;
  save: (artist: Artist) => Promise<void>;
  saveMany: (artists: Artists) => Promise<void>;
}

export class ArtistRepositoryTest extends ArtistMongoRepository implements IArtistRepositoryTest {
  async remove(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteOne(filterQuery.filter);
  }

  async removeMany(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteMany(filterQuery.filter);
  }

  async save(artist: Artist): Promise<void> {
    const toSave = ArtistSchemaMapper.execute(artist);

    const filter = { _id: artist.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(artists: Artists): Promise<void> {
    const connection = await this.getConnection();
    const models: ArtistSchemaType[] = [];

    artists.forEach((artist: Artist) => models.push(ArtistSchemaMapper.execute(artist)));

    await connection.insertMany(models);
  }
}
