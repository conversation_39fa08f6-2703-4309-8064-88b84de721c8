import { EventConfigurationSchemaMapper } from '@/events/eventConfigurations/infrastructure/database/mappers/EventConfigurationSchemaMapper';
import { EventConfigurationMongoRepository } from '@/events/eventConfigurations/infrastructure/database/repositories/EventConfigurationMongoRepository';

import type { EventConfigurationRepository } from '@/events/eventConfigurations/domain/contracts/EventConfigurationRepository';
import type { EventConfiguration, EventConfigurations } from '@/events/eventConfigurations/domain/entities/EventConfiguration';
import type { EventConfigurationSchemaType } from '@/events/eventConfigurations/infrastructure/database/schemas/EventConfigurationSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export interface IEventConfigurationRepositoryTest extends EventConfigurationRepository {
  remove: (criteria: Criteria) => Promise<void>;
  removeMany: (criteria: Criteria) => Promise<void>;
  save: (eventconfiguration: EventConfiguration) => Promise<void>;
  saveMany: (eventconfigurations: EventConfigurations) => Promise<void>;
}

export class EventConfigurationRepositoryTest extends EventConfigurationMongoRepository implements IEventConfigurationRepositoryTest {
  async remove(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteOne(filterQuery.filter);
  }

  async removeMany(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteMany(filterQuery.filter);
  }

  async save(eventconfiguration: EventConfiguration): Promise<void> {
    const toSave = EventConfigurationSchemaMapper.execute(eventconfiguration);

    const filter = { _id: eventconfiguration.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(eventconfigurations: EventConfigurations): Promise<void> {
    const connection = await this.getConnection();
    const models: EventConfigurationSchemaType[] = [];

    eventconfigurations.forEach((Bloc: EventConfiguration) => models.push(EventConfigurationSchemaMapper.execute(Bloc)));

    await connection.insertMany(models);
  }
}
