workload:
  replicas:
    min: 3
    max: 9
  resources:
    requests:
      cpu: .5
      memory: 1Gi
    limits:
      cpu: 1
      memory: 2Gi

config:
  APP_ENV: staging
  NODE_OPTIONS: "--max-old-space-size=900"

ingresses:
  - name: default
    middlewares:
      - setBackHeaders@file
    className: traefik-proxy
    hosts:
      - name: cli-api-service-staging.fourvenues.com
        path: /
    tlsSecretName: certificate-fourvenues-com
