kind: NetworkPolicy
apiVersion: networking.k8s.io/v1

metadata:
  name: "{{ .Release.Name }}"
  namespace: "{{ .Release.Namespace }}"
  labels:
    app.kubernetes.io/instance: "{{ .Release.Name }}"

spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/instance: "{{ .Release.Name }}"

  policyTypes:
    - Ingress

  ingress:
    - from:
        - namespaceSelector:
            matchLabels:
              kubernetes.io/metadata.name: networking
        - podSelector:
            matchLabels:
              app.kubernetes.io/instance: traefik-proxy
      ports:
        - port: http
    - from:
        - namespaceSelector:
            matchLabels:
              kubernetes.io/metadata.name: "{{ .Release.Namespace }}"
      ports:
        - port: http
