{{- range $ii, $ingress := .Values.ingresses }}
---
kind: Ingress
apiVersion: networking.k8s.io/v1

metadata:
  name: "{{ $.Release.Name }}-{{ $ingress.name }}"
  namespace: "{{ $.Release.Namespace }}"
  labels:
    app.kubernetes.io/instance: "{{ $.Release.Name }}"
  annotations:
    traefik.ingress.kubernetes.io/router.middlewares: "{{ join "," $ingress.middlewares }}"

spec:
  {{ if $ingress.className -}}
  ingressClassName: "{{ $ingress.className }}"
  {{- end }}

  rules:
    {{- range $hi, $host := $ingress.hosts }}
    - host: "{{ $host.name }}"
      http:
        paths:
          - pathType: Prefix
            path: "{{ $host.path }}"
            backend:
              service:
                name: "{{ $.Release.Name }}"
                port:
                  name: http
    {{- end }}

  tls:
    - secretName: "{{ $ingress.tlsSecretName }}"
{{- end }}
