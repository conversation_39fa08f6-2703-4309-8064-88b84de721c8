kind: Deployment
apiVersion: apps/v1

metadata:
  name: "{{ .Release.Name }}"
  namespace: "{{ .Release.Namespace }}"
  labels:
    app.kubernetes.io/instance: "{{ .Release.Name }}"
  annotations:
    kubernetes.io/change-cause: "{{ .Values.workload.image }}"

spec:
  revisionHistoryLimit: {{ .Values.workload.revisionHistoryLimit }}

  strategy:
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1

  selector:
    matchLabels:
      app.kubernetes.io/instance: "{{ .Release.Name }}"

  template:
    metadata:
      labels:
        app.kubernetes.io/instance: "{{ .Release.Name }}"

    spec:
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: ScheduleAnyway
          labelSelector:
            matchLabels:
              app.kubernetes.io/instance: "{{ .Release.Name }}"

      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 1
              podAffinityTerm:
                topologyKey: kubernetes.io/hostname
                labelSelector:
                  matchExpressions:
                    - key: app.kubernetes.io/instance
                      operator: In
                      values:
                        - "{{ .Release.Name }}"

      {{ if .Values.workload.tolerations -}}
      tolerations: {{ toYaml .Values.workload.tolerations | nindent 8 }}
      {{- end }}

      containers:
        - name: node
          image: "{{ .Values.workload.image }}"
          envFrom:
            - configMapRef:
                name: "{{ .Release.Name }}"
            - secretRef:
                name: "{{ .Release.Name }}"
          ports:
            - name: http
              containerPort: 3750
          resources: {{ toYaml .Values.workload.resources | nindent 12 }}
          readinessProbe:
            httpGet:
              path: /readiness
              port: http
            initialDelaySeconds: 10
            periodSeconds: 3
            timeoutSeconds: 3
            failureThreshold: 5
          livenessProbe:
            httpGet:
              path: /liveness
              port: http
            initialDelaySeconds: 10
            periodSeconds: 5
            failureThreshold: 5
            timeoutSeconds: 4

      imagePullSecrets:
        - name: registry
