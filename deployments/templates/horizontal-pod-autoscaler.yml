kind: HorizontalPodAutoscaler
apiVersion: autoscaling/v1

metadata:
  name: "{{ .Release.Name }}"
  namespace: "{{ .Release.Namespace }}"
  labels:
    app.kubernetes.io/instance: "{{ .Release.Name }}"

spec:
  scaleTargetRef:
    kind: Deployment
    apiVersion: apps/v1
    name: "{{ .Release.Name }}"

  minReplicas: {{ .Values.workload.replicas.min }}
  maxReplicas: {{ .Values.workload.replicas.max }}

  targetCPUUtilizationPercentage: 75
