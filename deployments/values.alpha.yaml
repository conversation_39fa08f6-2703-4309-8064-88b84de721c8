workload:
  replicas:
    min: 2
    max: 6
  resources:
    requests:
      cpu: .5
      memory: 1Gi
    limits:
      cpu: 1
      memory: 2Gi
  tolerations:
    - key: cloud.google.com/gke-spot
      operator: Equal
      value: "true"
      effect: "NoSchedule"

config:
  APP_ENV: alpha
  NODE_OPTIONS: "--max-old-space-size=900"

ingresses:
  - name: default
    middlewares:
      - setBackHeaders@file
    className: traefik-proxy
    hosts:
      - name: cli-api-service-alpha.fourvenues.com
        path: /
    tlsSecretName: certificate-fourvenues-com
