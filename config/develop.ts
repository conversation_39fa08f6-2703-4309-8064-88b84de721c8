import { FIVE_MINUTES_IN_SECONDS } from './Configuration';
import { Enviroments } from './enviroments';

import type { Configuration } from './Configuration';

export const configuration: Partial<Configuration> = {
  apikey: '2BMcY7$BSQ^ghQiQt@evtmq58o9XUvCi',
  disableRequestLogging: false,
  env: Enviroments.DEVELOP,
  cache: {
    enabled: true,
    endpoint: {
      enabled: true,
      ttl: FIVE_MINUTES_IN_SECONDS,
    },
    database: {
      enabled: true,
      ttl: FIVE_MINUTES_IN_SECONDS,
    },
  },
  logLevel: 'debug',
};
