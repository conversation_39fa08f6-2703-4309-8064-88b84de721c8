import z from 'zod';

import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';

import { Enviroments } from './enviroments';

export type NotEmptyArray<T> = [T, ...T[]];

type FvApp = {
  readonly applicationId: string;
  readonly url?: string;
};

type FvApps = {
  readonly clients: FvApp;
  readonly professionals: FvApp;
  readonly microsites: FvApp;
};

type FvSecureService = {
  readonly apiKey: string;
  readonly url: string;
};

type FvService = Pick<FvSecureService, 'url'>;

type FvServices = {
  readonly api: FvService;
  readonly connector: FvService;
  readonly payment: FvService;
  readonly reservations: FvSecureService;
  readonly tickets: FvSecureService;
};

type CacheConfigItem = {
  enabled: boolean;
  ttl: number;
};

export const FIVE_MINUTES_IN_SECONDS = 300;
export const FIVE_SECOND_IN_MS = 5_000;
export const TEN_SECONDS = 10;
export const TEN_SECOND_IN_MS = 10_000;
export const THIRTY_SECOND_IN_MS = 30_000;

export type LogLevel = 'info' | 'warn' | 'debug' | 'log' | 'error' | 'silent';

export const developerEnvs: Enviroments[] = [Enviroments.DEVELOP, Enviroments.TEST];

const DBNameEnum = z.enum(Object.values(EDBNames) as NotEmptyArray<EDBNames>);
const EnviromentEnum = z.enum(Object.values(Enviroments) as NotEmptyArray<Enviroments>);

const EnvSchema = z.object({
  APP_ENV: EnviromentEnum.default(Enviroments.PRODUCTION),
  APP_KEY: z.coerce.string(),
  FV_SERVICES_RESERVATIONS_API_KEY: z.coerce.string(),
  FV_SERVICES_TICKETS_API_KEY: z.coerce.string().default(''),
  HTTP_PORT: z.coerce.number().positive().default(3750),
  MONGODB_HOST: z.coerce.string().default(''),
  MONGODB_NAME: DBNameEnum.default(EDBNames.API),
  MONGODB_PASSWORD: z.coerce.string().default(''),
  MONGODB_PORT: z.coerce.number().positive().default(27027),
  MONGODB_URL: z.url().default('mongodb://host.docker.internal:27027?directConnection=true'),
  MONGODB_USER: z.coerce.string().default(''),
  PHRASE_KEY: z.coerce.string(),
  RABBIT_ENV: z.coerce.string(),
  RABBIT_HOST: z.coerce.string(),
  RABBIT_PASSWORD: z.coerce.string(),
  RABBIT_PORT: z.coerce.number().positive().default(5672),
  RABBIT_URL: z.url().default('amqp://guest:<EMAIL>:5672/dev'),
  RABBIT_USER: z.coerce.string(),
  REDIS_HOST: z.coerce.string(),
  REDIS_PORT: z.coerce.number().positive().default(6379),
  SECRET_AUTH: z.coerce.string(),
});

export const env = EnvSchema.parse(process.env);

export type DBName = z.infer<typeof DBNameEnum>;

export type Configuration = {
  readonly apiDocumentation: boolean;
  readonly apikey: string;
  readonly aws: {
    readonly cloudFront: {
      readonly url: string;
      readonly id: string;
    };
    readonly s3: {
      readonly name: string;
      readonly accessKey: string;
      readonly secretKey: string;
      readonly region: string;
    };
  };
  readonly cache: {
    readonly enabled: boolean;
    readonly endpoint: CacheConfigItem;
    readonly database: CacheConfigItem;
  };
  readonly circuitBreaker: {
    readonly timeout: number;
    readonly errorThresholdPercentage: number;
    readonly resetTimeout: number;
  };
  readonly devCycle: {
    readonly apiKey: string;
    readonly userId: string;
  };
  readonly disableRequestLogging: boolean;
  readonly domain: string;
  readonly env: Enviroments;
  readonly fv: {
    readonly apps: FvApps;
    readonly services: FvServices;
  };
  readonly healthCheckPath: string;
  readonly imagePassesLocation: string;
  readonly keepAliveTimeout: number;
  readonly logLevel: LogLevel;
  readonly language: string;
  readonly logger: {
    readonly token: string;
    readonly transport: string;
  };
  readonly maintenance: {
    readonly enabled: boolean;
  };
  readonly mongo: {
    port: number;
    url: string;
    readonly useCache: boolean;
    readonly options: {
      readonly autoIndex: boolean;
      readonly autoReconnect?: boolean;
      readonly authSource: string;
      readonly dbName: string;
      readonly maxPoolSize: number;
      readonly pass?: string;
      readonly reconnectInterval?: number;
      readonly sanitizeFilter: boolean;
      readonly user?: string;
      readonly waitQueueTimeoutMS: number;
    };
  };
  readonly origin: string | string[];
  readonly port: number;
  readonly rabbit: {
    readonly user: string;
    readonly password: string;
    host: string;
    port: number;
    readonly env: string;
    url: string;
  };
  readonly redis: {
    readonly enabled: boolean;
    host: string;
    port: number;
    readonly database: number;
    readonly ttl: number;
  };
  readonly seatsIO: {
    readonly apiKey: string;
  };
  readonly secretAuth: string;
  readonly serviceName: string;
  readonly shutdownDelay: number;
  readonly winston: {
    readonly projectId: string;
    readonly keyFilename: string;
    readonly labels: {
      readonly module: string;
    };
    readonly prefix: string;
  };
  readonly localization: {
    readonly localesPath: string;
    readonly fallbackLocale: string;
    readonly defaultLocale: string;
    readonly supportedLocales: string[];
    readonly namespaces: string[];
    readonly phraseApiUrl: string;
    readonly phraseProjectId: string;
    readonly phraseApiKey: string;
  };
};
