import { FIVE_MINUTES_IN_SECONDS } from './Configuration';
import { Enviroments } from './enviroments';

import type { Configuration } from './Configuration';

export const configuration: Partial<Configuration> = {
  env: Enviroments.TEST,
  cache: {
    enabled: false,
    endpoint: {
      enabled: false,
      ttl: FIVE_MINUTES_IN_SECONDS,
    },
    database: {
      enabled: false,
      ttl: FIVE_MINUTES_IN_SECONDS,
    },
  },
  logLevel: 'silent',
};
