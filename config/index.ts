import { DEFAULT_LENGUAGE } from '@discocil/fv-domain-library/domain';


import { configuration as alphaConfiguration } from './alpha';
import {
  developerEnvs,
  env,
  FIVE_MINUTES_IN_SECONDS,
  FIVE_SECOND_IN_MS,
  TEN_SECOND_IN_MS,
  TEN_SECONDS,
  THIRTY_SECOND_IN_MS,
} from './Configuration';
import { configuration as developConfiguration } from './develop';
import { AplicationIds, Enviroments } from './enviroments';
import { configuration as lab1Configuration } from './lab1';
import { configuration as productionConfiguration } from './production';
import { configuration as testConfiguration } from './testing';

import type { Configuration } from './Configuration';

export type NotEmptyArray<T> = [T, ...T[]];

export const isDeveloperEnv = (env: Enviroments): boolean => developerEnvs.includes(env);
export const isLocal = (): boolean => config.env === Enviroments.DEVELOP;

const defaultConfig: Configuration = {
  apiDocumentation: true,
  apikey: '2BMcY7$BSQ^ghQiQt@evtmq58o9XUvCi',
  aws: {
    cloudFront: {
      url: 'https://d229raf0omws8p.cloudfront.net',
      id: 'ENQPT84251O3C',
    },
    s3: {
      name: 'discocil-misc-alpha',
      accessKey: '********************',
      secretKey: 'q134Td23H/VWtUxK7VJWYHpyvC63R2HAgeThg9/q',
      region: 'us-east-1',
    },
  },
  cache: {
    enabled: true,
    endpoint: {
      enabled: true,
      ttl: TEN_SECONDS,
    },
    database: {
      enabled: true,
      ttl: TEN_SECONDS,
    },
  },
  circuitBreaker: {
    timeout: TEN_SECOND_IN_MS,
    errorThresholdPercentage: 50,
    resetTimeout: THIRTY_SECOND_IN_MS,
  },
  devCycle: {
    apiKey: 'dvc_server_81dc6d28_432e_471f_b28e_2cb6b6a2176f_0bac9b3',
    userId: '@fourvenues.com',
  },
  disableRequestLogging: true,
  domain: '0.0.0.0',
  env: Enviroments.PRODUCTION,
  fv: {
    apps: {
      clients: {
        applicationId: AplicationIds.CLIENTS,
        url: 'http://localhost:4075',
      },
      professionals: { applicationId: AplicationIds.PROFESSIONALS },
      microsites: {
        applicationId: AplicationIds.CLIENTS,
        url: 'http://localhost:4076',
      },
    },
    services: {
      api: { url: 'http://localhost:3071' },
      connector: { url: 'http://localhost:3076' },
      payment: { url: 'http://localhost:4074' },
      reservations: {
        apiKey: '3oD9GE8IzYO6KICAIiyUcI4eqiMuQKmiW20YWua664SQQwiw8M6QiUCwUs0IEmoi4u28QYEUSkCa8OIwOqwwGqyiYGGGW62U2MG8',
        url: 'http://host.docker.internal:3087/api',
      },
      tickets: {
        apiKey: 'B6ttixPCUaYecIqaiWEgOeqmuw64eEcWqk8iuy6uwwG',
        url: 'http://host.docker.internal:3083/api',
      },
    },
  },
  healthCheckPath: '/',
  imagePassesLocation: 'image_passes/test',
  keepAliveTimeout: 30_000,
  language: DEFAULT_LENGUAGE,
  logger: {
    token: 'b76cac153d6a404cb5a54b76aafd5b9b',
    transport: '@discocil/pino-rollbar-transport',
  },
  logLevel: 'info',
  maintenance: { enabled: false },
  mongo: {
    url: env.MONGODB_URL,
    port: env.MONGODB_PORT,
    useCache: true,
    options: {
      autoIndex: true,
      authSource: 'admin',
      dbName: env.MONGODB_NAME,
      maxPoolSize: 10,
      // minPoolSize: 5,
      user: env.MONGODB_USER,
      pass: env.MONGODB_PASSWORD,
      sanitizeFilter: true,
      waitQueueTimeoutMS: FIVE_SECOND_IN_MS,
    },
  },
  origin: '*',
  port: env.HTTP_PORT,
  rabbit: {
    user: 'guest',
    password: 'guest',
    host: 'host.docker.internal',
    port: 5672,
    env: 'dev',
    url: 'amqp://guest:<EMAIL>:5672/dev',
  },
  redis: {
    enabled: true,
    host: 'host.docker.internal',
    port: 6379,
    ttl: FIVE_MINUTES_IN_SECONDS,
    database: 7,
  },
  seatsIO: { apiKey: 'b572fc72-1d1c-4001-8dde-82779c8d336d' },
  secretAuth: env.SECRET_AUTH,
  serviceName: 'cli-api-service',
  shutdownDelay: 5_000,
  winston: {
    projectId: 'discocil-cloud',
    keyFilename: 'assets/discocil-cloud-02947b625b0c.json',
    labels: { module: 'cli-api' },
    prefix: 'cli-api-',
  },
  localization: {
    localesPath: 'src/cross-cutting/domain/locales',
    defaultLocale: 'en',
    fallbackLocale: 'es',
    supportedLocales: ['es', 'en', 'fr', 'it', 'nl', 'ca', 'pt'],
    namespaces: [
      'common',
    ],
    phraseApiUrl: 'https://api.phrase.com/v2/projects/',
    phraseProjectId: 'adfcbcff78823027d3367d4af5e2eca7',
    phraseApiKey: env.PHRASE_KEY,
  },
};

const environmentsConfigurations: Record<Enviroments, Partial<Configuration>> = {
  [Enviroments.ALPHA]: alphaConfiguration,
  [Enviroments.DEVELOP]: developConfiguration,
  [Enviroments.LAB1]: lab1Configuration,
  [Enviroments.PRODUCTION]: productionConfiguration,
  [Enviroments.TEST]: testConfiguration,
};

const configurationSelected = environmentsConfigurations[env.APP_ENV];

const config: Configuration = {
  ...defaultConfig,
  ...configurationSelected,
};

export default config;
