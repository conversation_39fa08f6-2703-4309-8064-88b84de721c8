import { env, FIVE_MINUTES_IN_SECONDS } from './Configuration';
import { Enviroments } from './enviroments';

import type { Configuration } from './Configuration';

export const configuration: Partial<Configuration> = {
  apikey: env.APP_KEY,
  env: Enviroments.LAB1,
  logLevel: 'error',
  origin: [
    'https://cli-lab1.fourvenues.com',
  ],
  rabbit: {
    user: env.RABBIT_USER,
    password: env.RABBIT_PASSWORD,
    host: env.RABBIT_HOST,
    port: env.RABBIT_PORT,
    env: env.RABBIT_ENV,
    url: env.RABBIT_URL,
  },
  redis: {
    enabled: true,
    host: env.REDIS_HOST,
    port: env.REDIS_PORT,
    ttl: FIVE_MINUTES_IN_SECONDS,
    database: 7,
  },
};
