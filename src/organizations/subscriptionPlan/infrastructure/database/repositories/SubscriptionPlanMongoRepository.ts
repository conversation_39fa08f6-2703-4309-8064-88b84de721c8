import { left, NotFoundError } from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { SubscriptionPlan } from '@/organizations/subscriptionPlan/domain/entities/SubscriptionPlan';

import { SubscriptionPlanMapper } from '../../mappers/SubscriptionPlanMapper';
import { SubscriptionPlanSchemaMapper } from '../../mappers/SubscriptionPlanSchemaMapper';
import { subscriptionPlanSchema } from '../../Schemas/SubscriptionPlanSchema';

import type { SubscriptionPlanRepository } from '@/organizations/subscriptionPlan/domain/contracts/SubscriptionPlanRepository';
import type { SubscriptionPlanEither, SubscriptionPlanKeys } from '@/organizations/subscriptionPlan/domain/entities/SubscriptionPlan';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { SubscriptionPlanSchemaType } from '../../Schemas/SubscriptionPlanType';

type PropertiesMapper = Partial<Record<SubscriptionPlanKeys, keyof SubscriptionPlanSchemaType>>;

export class SubscriptionPlanMongoRepository extends MongoRepository implements SubscriptionPlanRepository {
  protected getSchema(): Schema {
    return new Schema(subscriptionPlanSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'planes_suscripciones';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<SubscriptionPlanEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<SubscriptionPlanSchemaType>();

    return queryResponse
      ? SubscriptionPlanMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: SubscriptionPlan.name }));
  }

  async save(subscriptionPlan: SubscriptionPlan): Promise<void> {
    const subscriptionPlanToSave = SubscriptionPlanSchemaMapper.execute(subscriptionPlan);

    const filter = { _id: subscriptionPlan.id };
    const update = { $set: subscriptionPlanToSave };
    const options = { upsert: true };

    const modelQuery = await this.getConnection();

    await modelQuery.updateOne(filter, update, options);
  }
}
