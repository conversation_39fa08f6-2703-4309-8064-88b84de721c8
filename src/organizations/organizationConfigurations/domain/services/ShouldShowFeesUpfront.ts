import { OrganizationConfigurationCriteriaMother } from '../filters/OrganizationConfigurationCriteriaMother';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import type { OrganizationConfigurationRepository } from '../contracts/OrganizationConfigurationRepository';

export class ShouldShowFeesUpfrontService {
  constructor(
    private readonly organizationConfigurationRepository: OrganizationConfigurationRepository,
  ) {}

  async execute(organizationId: UniqueEntityID): Promise<boolean> {
    const organizationConfigurationCriteria = OrganizationConfigurationCriteriaMother.organizationToMatch(organizationId);

    const organizationConfigurationResult = await this.organizationConfigurationRepository.find(organizationConfigurationCriteria);

    const shouldShowFeesUpfront = organizationConfigurationResult.isRight()
      ? organizationConfigurationResult.value.areFeesShownUpfront
      : false;

    return shouldShowFeesUpfront;
  }
}
