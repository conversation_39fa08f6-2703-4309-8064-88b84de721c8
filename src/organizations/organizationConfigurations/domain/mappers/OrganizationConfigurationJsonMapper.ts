import { Maybe } from '@discocil/fv-domain-library/domain';

import { OrganizationConfiguration } from '../entities/OrganizationConfiguration';

import type { Nullable } from '@discocil/fv-domain-library/domain';
import type { OrganizationConfigurationEither, OrganizationConfigurationPrimitives } from '../entities/OrganizationConfiguration';

export type OrganizationConfigurationJsonPrimitives = Omit<OrganizationConfigurationPrimitives,
  'termsConditions'
  | 'parentalAuthorization'
> & Nullable<{
  readonly termsConditions: string;
  readonly parentalAuthorization: string;
}>;

export class OrganizationConfigurationJsonMapper {
  static toEntity(primitives: OrganizationConfigurationJsonPrimitives): OrganizationConfigurationEither {
    return OrganizationConfiguration.build({
      ...primitives,
      termsConditions: Maybe.fromValue(primitives.termsConditions),
      parentalAuthorization: Maybe.fromValue(primitives.parentalAuthorization),
    });
  }

  static toJson(organizationConfiguration: OrganizationConfiguration): OrganizationConfigurationJsonPrimitives {
    return {
      id: organizationConfiguration.id,
      organizationId: organizationConfiguration.organizationId,
      termsConditions: organizationConfiguration.termsConditions.fold(() => null, item => item),
      agesPerDays: organizationConfiguration.agesPerDays,
      age: organizationConfiguration.age,
      parentalAuthorization: organizationConfiguration.parentalAuthorization.fold(() => null, item => item),
      qualityRequired: organizationConfiguration.qualityRequired,
      pastReturns: organizationConfiguration.pastReturns,
      receptionReturns: organizationConfiguration.receptionReturns,
      rrppEntriesCanBeCanceled: organizationConfiguration.rrppEntriesCanBeCanceled,
      receptionShowTotals: organizationConfiguration.receptionShowTotals,
      singenReception: organizationConfiguration.singenReception,
      hasEmailReconfirmation: organizationConfiguration.hasEmailReconfirmation,
      areFeesShownUpfront: organizationConfiguration.areFeesShownUpfront,
      reservedRandom: organizationConfiguration.reservedRandom,
      reservedChangeStatusNotify: organizationConfiguration.reservedChangeStatusNotify,
      reservedDaysCli: organizationConfiguration.reservedDaysCli,
      reservedDaysPro: organizationConfiguration.reservedDaysPro,
      reservedReconfirmMethod: organizationConfiguration.reservedReconfirmMethod,
      reservedReconfirmTime: organizationConfiguration.reservedReconfirmTime,
      reservedRRPPCanCancel: organizationConfiguration.reservedRRPPCanCancel,
      reservedSeeSpaces: organizationConfiguration.reservedSeeSpaces,
      useQuotas: organizationConfiguration.useQuotas,
      visibleCli: organizationConfiguration.visibleCli,
      whatsappOnlineTickets: organizationConfiguration.whatsappOnlineTickets,
      bookingExtraPeoplePricing: organizationConfiguration.bookingExtraPeoplePricing,
      ticketCancellationTime: organizationConfiguration.ticketCancellationTime,
      createdAt: organizationConfiguration.createdAt,
      createdBy: organizationConfiguration.createdBy,
      updatedAt: organizationConfiguration.updatedAt,
      updatedBy: organizationConfiguration.updatedBy,
      removedAt: organizationConfiguration.removedAt,
      removedBy: organizationConfiguration.removedBy,
    };
  }
}
