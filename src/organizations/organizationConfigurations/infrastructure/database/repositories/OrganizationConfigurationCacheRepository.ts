import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { OrganizationConfigurationJsonMapper } from '@/organizations/organizationConfigurations/domain/mappers/OrganizationConfigurationJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { OrganizationConfigurationRepository } from '@/organizations/organizationConfigurations/domain/contracts/OrganizationConfigurationRepository';
import type {
  OrganizationConfiguration,
  OrganizationConfigurationEither,
  OrganizationConfigurations,
  OrganizationConfigurationsEither,
} from '@/organizations/organizationConfigurations/domain/entities/OrganizationConfiguration';
import type { OrganizationConfigurationJsonPrimitives } from '@/organizations/organizationConfigurations/domain/mappers/OrganizationConfigurationJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class OrganizationConfigurationCacheRepository extends CacheRepository implements OrganizationConfigurationRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: OrganizationConfigurationRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<OrganizationConfigurationEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: OrganizationConfigurationJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = OrganizationConfigurationJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        OrganizationConfigurationJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async search(criteria: Criteria): Promise<OrganizationConfigurationsEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: OrganizationConfigurationJsonPrimitives[] = JSON.parse(cacheHit);
      const organizationConfigurations: OrganizationConfigurations = new Map<IdPrimitive, OrganizationConfiguration>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = OrganizationConfigurationJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        organizationConfigurations.set(_primitive.id, entityOrError.value);
      }

      return right(organizationConfigurations);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const organizationConfigurations = repositoryResult.value;
      const organizationConfigurationsLength = organizationConfigurations.size;
      let organizationConfigurationsCacheIndex = 0;
      const jsonOrganizationConfigurationss = new Array<OrganizationConfigurationJsonPrimitives>(organizationConfigurationsLength);

      if (organizationConfigurationsLength > 0) {
        for (const _entity of organizationConfigurations.values()) {
          jsonOrganizationConfigurationss[organizationConfigurationsCacheIndex] = OrganizationConfigurationJsonMapper.toJson(_entity);

          organizationConfigurationsCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonOrganizationConfigurationss, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}
