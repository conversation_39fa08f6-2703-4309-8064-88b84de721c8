import { Maybe } from '@discocil/fv-domain-library/domain';

import { Organization } from '../entities/Organization';

import type { FormFields } from '@/cross-cutting/domain/contracts/FormFields';
import type { Images, ImagesExternalPrimitives } from '@/cross-cutting/domain/contracts/Images';
import type { Nullable } from '@discocil/fv-domain-library/domain';
import type { OrganizationEither, OrganizationPrimitives } from '../entities/Organization';

export type OrganizationJsonPrimitives = Omit<
  OrganizationPrimitives,
  'image'
  | 'slug'
  | 'ticketFields'
  | 'cover'
  | 'coverImages'
  | 'gatewayAccountId'
  | 'web'
  | 'facebook'
  | 'instagram'
  | 'menuUrl'
  | 'floorImage'
  | 'images'
  | 'ruleTemplateId'
  | 'description'
> & Nullable<{
  readonly image: string;
  readonly slug: string;
  readonly ticketFields: FormFields;
  readonly cover: string;
  readonly coverImages: ImagesExternalPrimitives;
  readonly gatewayAccountId: string;
  readonly web: string;
  readonly facebook: string;
  readonly instagram: string;
  readonly menuUrl: string;
  readonly floorImage: string;
  readonly images: Images;
  readonly ruleTemplateId: string;
  readonly description: string;
}>;

export class OrganizationJsonMapper {
  static toEntity(primitives: OrganizationJsonPrimitives): OrganizationEither {
    return Organization.build({
      ...primitives,
      image: Maybe.fromValue(primitives.image),
      slug: Maybe.fromValue(primitives.slug),
      ticketFields: Maybe.fromValue(primitives.ticketFields),
      cover: Maybe.fromValue(primitives.cover),
      coverImages: this.makeCoverImages(primitives.coverImages),
      gatewayAccountId: Maybe.fromValue(primitives.gatewayAccountId),
      web: Maybe.fromValue(primitives.web),
      facebook: Maybe.fromValue(primitives.facebook),
      instagram: Maybe.fromValue(primitives.instagram),
      menuUrl: Maybe.fromValue(primitives.menuUrl),
      floorImage: Maybe.fromValue(primitives.floorImage),
      images: Maybe.fromValue(primitives.images),
      ruleTemplateId: Maybe.fromValue(primitives.ruleTemplateId),
      description: Maybe.fromValue(primitives.description),
    });
  }

  static toJson(data: Organization): OrganizationJsonPrimitives {
    return {
      id: data.id,
      name: data.name,
      state: data.state,
      image: data.image.fold(() => null, item => item),
      currency: data.currency,
      countryCode: data.countryCode,
      paymentsProvider: data.paymentsProvider,
      merchantName: data.merchantName,
      feeRule: data.feeRule,
      slug: data.slug.fold(() => null, item => item),
      type: data.type,
      hosts: data.hosts,
      plan: data.plan,
      ticketFields: data.ticketFields.fold(() => null, item => item),
      cover: data.cover.fold(() => null, item => item),
      coverImages: data.coverImages.fold(() => null, (item) => {
        return {
          medium: item.medium.fold(() => null, item => item),
          mini: item.mini.fold(() => null, item => item),
          small: item.small.fold(() => null, item => item),
        };
      }),
      gatewayAccountId: data.gatewayAccountId.fold(() => null, item => item),
      web: data.web.fold(() => null, item => item),
      facebook: data.facebook.fold(() => null, item => item),
      instagram: data.instagram.fold(() => null, item => item),
      menuUrl: data.menuUrl.fold(() => null, item => item),
      carousel: data.carousel,
      floorImage: data.floorImage.fold(() => null, item => item),
      images: data.images.fold(() => null, item => item),
      covers: data.covers,
      active: data.active,
      usesDays: data.usesDays,
      ruleTemplateId: data.ruleTemplateId.fold(() => null, item => item),
      description: data.description.fold(() => null, item => item),
      createdAt: data.createdAt,
      createdBy: data.createdBy,
      updatedAt: data.updatedAt,
      updatedBy: data.updatedBy,
      removedAt: data.removedAt,
      removedBy: data.removedBy,
    };
  }

  private static makeCoverImages(coverIncoming: ImagesExternalPrimitives | null | undefined): Maybe<Images> {
    if (coverIncoming === null || coverIncoming === undefined) {
      return Maybe.none<Images>();
    }

    return Maybe.some({
      medium: Maybe.fromValue(coverIncoming.medium),
      mini: Maybe.fromValue(coverIncoming.mini),
      small: Maybe.fromValue(coverIncoming.small),
    });
  }
}
