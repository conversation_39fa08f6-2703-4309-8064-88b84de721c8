import {
  Collection, left, right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';

import { FeeMapper } from '../mappers/FeeMapper';
import { feeSchema } from '../schemas/FeeSchema';

import type { FeeRepository } from '@/fees/domain/contracts/FeeRepository';
import type {
  Fe<PERSON>, FeeKeys, FeesEither,
} from '@/fees/domain/entities/Fee';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { FeeSchemaType } from '../schemas/FeeSchemaType';

type PropertiesMapper = Partial<Record<FeeKeys, keyof FeeSchemaType>>;

export class FeeMongoRepository extends MongoRepository implements FeeRepository {
  protected getSchema(): Schema {
    return new Schema(feeSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'fees';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'organization_id',
      applyTo: 'apply_to',
      feesToApply: 'fees_to_apply',
      removedAt: 'removed_at',
      state: 'state',
    };
  }

  async search(criteria: Criteria): Promise<FeesEither> {
    const response = Collection.new<Fee>('id');

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection.find<FeeSchemaType>(filterQuery.filter);

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const feeResult = FeeMapper.execute(model);

      if (feeResult.isLeft()) {
        return left(feeResult.value);
      }

      const fee = feeResult.value;

      response.add(fee);
    }

    return right(response);
  }
}
