import { Maybe } from '@discocil/fv-domain-library/domain';

import { Subscription } from '../entities/SubscriptionEntity';

import type { Nullable } from '@discocil/fv-domain-library/domain';
import type { SubscriptionEither, SubscriptionPrimitives } from '../entities/SubscriptionEntity';

export type SubscriptionJsonPrimitives = Omit<SubscriptionPrimitives,
  'ip'
  | 'referrerId'
  | 'name'
  | 'phone'
> & Nullable<{
  readonly ip: string;
  readonly referrerId: string;
  readonly name: string;
  readonly phone: string;
}>;

export class SubscriptionJsonMapper {
  static toEntity(primitives: SubscriptionJsonPrimitives): SubscriptionEither {
    return Subscription.build({
      ...primitives,
      ip: Maybe.fromValue(primitives.ip),
      referrerId: Maybe.fromValue(primitives.referrerId),
      name: Maybe.fromValue(primitives.name),
      phone: Maybe.fromValue(primitives.phone),
    });
  }

  static toJson(subscription: Subscription): SubscriptionJsonPrimitives {
    return {
      id: subscription.id,
      applicationId: subscription.applicationId,
      organizationId: subscription.organizationId,
      ip: subscription.ip.fold(() => null, item => item),
      referrerId: subscription.referrerId.fold(() => null, item => item),
      name: subscription.name.fold(() => null, item => item),
      email: subscription.email,
      phone: subscription.phone.fold(() => null, item => item),
      imported: subscription.imported,
      createdAt: subscription.createdAt,
      createdBy: subscription.createdBy,
      updatedAt: subscription.updatedAt,
      updatedBy: subscription.updatedBy,
      removedAt: subscription.removedAt,
      removedBy: subscription.removedBy,
    };
  }
}
