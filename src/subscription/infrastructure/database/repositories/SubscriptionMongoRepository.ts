import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { Subscription } from '@/subscription/domain/entities/SubscriptionEntity';

import { SubscriptionMapper } from '../mappers/SubscriptionMapper';
import { SubscriptionSchemaMapper } from '../mappers/SubscriptionSchemaMapper';
import { subscriptionSchema } from '../schemas/SubscriptionSchema';

import type { SubscriptionRepository } from '@/subscription/domain/contracts/SubscriptionRepository';
import type {
  SubscriptionEither,
  SubscriptionKeys,
  Subscriptions,
  SubscriptionsEither,
} from '@/subscription/domain/entities/SubscriptionEntity';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { SubscriptionSchemaType } from '../schemas/SubscriptionSchemaTypes';

type PropertiesMapper = Partial<Record<SubscriptionKeys, keyof SubscriptionSchemaType>>;

export class SubscriptionMongoRepository extends MongoRepository implements SubscriptionRepository {
  protected getSchema(): Schema {
    return new Schema(subscriptionSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'suscripciones';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      phone: 'telefono',
      email: 'email',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<SubscriptionEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<SubscriptionSchemaType>();

    return queryResponse
      ? SubscriptionMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: Subscription.name }));
  }

  async save(subscription: Subscription): Promise<void> {
    const toSave = SubscriptionSchemaMapper.execute(subscription);

    const filter = { _id: subscription.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(subscriptions: Subscriptions): Promise<void> {
    const promises = [...subscriptions.values()].map(async (subscription: Subscription) => this.save(subscription));

    await Promise.all(promises);
  }

  async search(criteria: Criteria): Promise<SubscriptionsEither> {
    const response: Subscriptions = new Map<IdPrimitive, Subscription>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<SubscriptionSchemaType[]>();

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const subscriptionOrError = SubscriptionMapper.execute(model);

      if (subscriptionOrError.isLeft()) {
        return left(subscriptionOrError.value);
      }

      const subscription = subscriptionOrError.value;

      response.set(subscription.id, subscription);
    }

    return right(response);
  }
}
