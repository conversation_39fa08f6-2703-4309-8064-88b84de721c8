import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { SubscriptionJsonMapper } from '@/subscription/domain/mappers/SubscriptionJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { SubscriptionRepository } from '@/subscription/domain/contracts/SubscriptionRepository';
import type {
  Subscription,
  SubscriptionEither,
  Subscriptions,
  SubscriptionsEither,
} from '@/subscription/domain/entities/SubscriptionEntity';
import type { SubscriptionJsonPrimitives } from '@/subscription/domain/mappers/SubscriptionJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class SubscriptionCacheRepository extends CacheRepository implements SubscriptionRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: SubscriptionRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<SubscriptionEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: SubscriptionJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = SubscriptionJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        SubscriptionJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async save(subscription: Subscription): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.save(subscription);
  }

  async saveMany(subscriptions: Subscriptions): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.saveMany(subscriptions);
  }

  async search(criteria: Criteria): Promise<SubscriptionsEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: SubscriptionJsonPrimitives[] = JSON.parse(cacheHit);
      const subscriptions: Subscriptions = new Map<IdPrimitive, Subscription>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = SubscriptionJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        subscriptions.set(_primitive.id, entityOrError.value);
      }

      return right(subscriptions);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const subscriptions = repositoryResult.value;
      const subscriptionLength = subscriptions.size;
      let subscriptionCacheIndex = 0;
      const jsonSubscriptions = new Array<SubscriptionJsonPrimitives>(subscriptionLength);

      if (subscriptionLength > 0) {
        for (const _subscription of subscriptions.values()) {
          jsonSubscriptions[subscriptionCacheIndex] = SubscriptionJsonMapper.toJson(_subscription);

          subscriptionCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonSubscriptions, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}
