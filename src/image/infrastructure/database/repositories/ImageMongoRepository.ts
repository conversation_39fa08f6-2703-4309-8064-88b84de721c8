import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';

import { imageMongoSchema } from '../Schemas/ImageSchema';
import { ImageSchemaMapper } from '../mappers/ImageSchemaMapper';

import type { ImageRepository } from '@/image/domain/contracts/ImageRepository';
import type { Image } from '@/image/domain/entities/Image';

export class ImageMongoRepository extends MongoRepository implements ImageRepository {
  protected getSchema(): Schema {
    return new Schema(imageMongoSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'image_uploads';
  }

  async save(uploadImage: Image): Promise<void> {
    const toSave = ImageSchemaMapper.execute(uploadImage);

    const connection = await this.getConnection();

    const filter = { _id: uploadImage.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    await connection.updateOne(filter, update, options);
  }
}
