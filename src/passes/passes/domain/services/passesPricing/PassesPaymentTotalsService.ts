import { left, right } from '@discocil/fv-domain-library/domain';
import { GetPaymentTotalsService } from '@discocil/fv-pricing-library/ticketing';

import { PricingLibraryError } from '@/cross-cutting/domain/errors/PricingLibraryError';

import type { Either } from '@discocil/fv-domain-library/domain';
import type { PaymentTotalsRequest, PaymentTotalsResponse } from '@discocil/fv-pricing-library/ticketing';

type PassesPaymentTotalsServiceResponse = Either<PricingLibraryError, PaymentTotalsResponse>;

export class PassesPaymentTotalsService {
  static execute(input: PaymentTotalsRequest): PassesPaymentTotalsServiceResponse {
    try {
      const passesPaymentTotals = GetPaymentTotalsService.execute(input);

      return right(passesPaymentTotals);
    } catch (error: unknown) {
      return left(PricingLibraryError.build({
        context: this.constructor.name,
        data: { input, error },
      }));
    }
  }
}
