import { left, right } from '@discocil/fv-domain-library/domain';
import {
  GetTicketsPricingService, type TicketsPricingRequest, type TicketsPricingResponse,
} from '@discocil/fv-pricing-library/ticketing';

import { PricingLibraryError } from '@/cross-cutting/domain/errors/PricingLibraryError';

import type { Either } from '@discocil/fv-domain-library/domain';

type PassesPricingServiceResponse = Either<PricingLibraryError, TicketsPricingResponse>;

export class PassesPricingService {
  static execute(input: TicketsPricingRequest): PassesPricingServiceResponse {
    try {
      const passesPricing = GetTicketsPricingService.execute(input);

      return right(passesPricing);
    } catch (error: unknown) {
      return left(PricingLibraryError.build({
        context: this.constructor.name,
        data: { input, error },
      }));
    }
  }
}
