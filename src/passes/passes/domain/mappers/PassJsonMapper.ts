import { FvDate, Maybe } from '@discocil/fv-domain-library/domain';

import { Pass } from '../entities/Pass';

import type { TPersonalDocument } from '@/cross-cutting/domain/contracts/CommonContracts';
import type {
  DatePrimitive,
  ECountryCode,
  EGender,
  IdPrimitive,
  Nullable,
} from '@discocil/fv-domain-library/domain';
import type {
  CustomerCustomField,
  CustomerPrimitives,
  PassEither,
  PassPrimitives,
} from '../entities/Pass';

type CustomerJson = Readonly<Pick<CustomerPrimitives, 'fullname' | 'email'> & Nullable<{
  readonly image: string;
  readonly phone: string;
  readonly gender: EGender;
  readonly personalDocumentNumber: TPersonalDocument;
  readonly address: string;
  readonly birthday: string;
  readonly country: ECountryCode;
  readonly postalCode: string;
  readonly customFields: CustomerCustomField[];
}>>;

export type PassJsonPrimitives = Omit<PassPrimitives,
  'organizationAssignedId'
  | 'referrerId'
  | 'typeId'
  | 'typeName'
  | 'typeColor'
  | 'priceId'
  | 'paymentId'
  | 'sourceUrl'
  | 'purchaseDate'
  | 'customer'
> & Nullable<{
  readonly organizationAssignedId: IdPrimitive;
  readonly referrerId: IdPrimitive;
  readonly typeId: IdPrimitive;
  readonly typeName: string;
  readonly typeColor: string;
  readonly priceId: IdPrimitive;
  readonly paymentId: IdPrimitive;
  readonly sourceUrl: string;
}> & {
  readonly purchaseDate: string;
  readonly customer: CustomerJson;
};

export class PassJsonMapper {
  static toEntity(primitives: PassJsonPrimitives): PassEither {
    const isoToDate = (value: string): FvDate => FvDate.createFromISO(value).value as FvDate;

    return Pass.build({
      ...primitives,
      organizationAssignedId: Maybe.fromValue(primitives.organizationAssignedId),
      referrerId: Maybe.fromValue(primitives.referrerId),
      typeId: Maybe.fromValue(primitives.typeId),
      typeName: Maybe.fromValue(primitives.typeName),
      typeColor: Maybe.fromValue(primitives.typeColor),
      priceId: Maybe.fromValue(primitives.priceId),
      paymentId: Maybe.fromValue(primitives.paymentId),
      sourceUrl: Maybe.fromValue(primitives.sourceUrl),
      purchaseDate: isoToDate(primitives.purchaseDate).toPrimitive(),
      customer: {
        fullname: primitives.customer.fullname,
        email: primitives.customer.email,
        image: Maybe.fromValue(primitives.customer.image),
        phone: Maybe.fromValue(primitives.customer.phone),
        gender: Maybe.fromValue(primitives.customer.gender),
        personalDocumentNumber: Maybe.fromValue(primitives.customer.personalDocumentNumber),
        address: Maybe.fromValue(primitives.customer.address),
        birthday: primitives.customer.birthday
          ? Maybe.fromValue(isoToDate(primitives.customer.birthday).toPrimitive())
          : Maybe.none<DatePrimitive>(),
        country: Maybe.fromValue(primitives.customer.country),
        postalCode: Maybe.fromValue(primitives.customer.postalCode),
        customFields: primitives.customer.customFields ? Maybe.fromValue(primitives.customer.customFields) : Maybe.none(),
      },
    });
  }

  static toJson(pass: Pass): PassJsonPrimitives {
    return {
      id: pass.id,
      organizationId: pass.organizationId,
      organizationAssignedId: pass.organizationAssignedId.fold(() => null, item => item),
      applicationId: pass.applicationId,
      referrerId: pass.referrerId.fold(() => null, item => item),
      customer: {
        fullname: pass.customer.fullname,
        email: pass.customer.email,
        image: pass.customer.image.fold(() => null, item => item),
        phone: pass.customer.phone.fold(() => null, item => item),
        gender: pass.customer.gender.fold(() => null, item => item),
        personalDocumentNumber: pass.customer.personalDocumentNumber.fold(() => null, item => item),
        address: pass.customer.address.fold(() => null, item => item),
        birthday: pass.customer.birthday.fold(() => null, item => FvDate.create(item).toISO()),
        country: pass.customer.country.fold(() => null, item => item),
        postalCode: pass.customer.postalCode.fold(() => null, item => item),
        customFields: pass.customer.customFields.fold(() => null, item => item),
      },
      typeId: pass.typeId.fold(() => null, item => item),
      typeName: pass.typeName.fold(() => null, item => item),
      typeColor: pass.typeColor.fold(() => null, item => item),
      priceId: pass.priceId.fold(() => null, item => item),
      purchaseDate: pass.getPurchaseDateInISO(),
      purchasePrice: pass.purchasePrice,
      serviceFees: pass.serviceFees,
      revenue: pass.revenue,
      paymentId: pass.paymentId.fold(() => null, item => item),
      state: pass.state,
      idx: pass.idx,
      language: pass.language,
      device: pass.device,
      archived: pass.archived,
      remarketing: pass.remarketing,
      sourceUrl: pass.sourceUrl.fold(() => null, item => item),
      code: pass.code,
      saleType: pass.saleType,
      currency: pass.currency,
      discocilTotal: pass.discocilTotal,
      createdAt: pass.createdAt,
      createdBy: pass.createdBy,
      updatedAt: pass.updatedAt,
      updatedBy: pass.updatedBy,
      removedAt: pass.removedAt,
      removedBy: pass.removedBy,
    };
  }
}
