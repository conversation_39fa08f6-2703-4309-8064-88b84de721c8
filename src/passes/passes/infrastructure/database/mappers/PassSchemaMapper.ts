import { FvDate } from '@discocil/fv-domain-library/domain';

import type { Pass } from '@/passes/passes/domain/entities/Pass';
import type { PassOriginalSchemaType } from '../schemas/PassSchemaType';

export class PassSchemaMapper {
  static execute(pass: Pass): PassOriginalSchemaType {
    return {
      _id: pass.id,
      aplicacion_id: pass.applicationId,
      negocio_id: pass.organizationId,
      negocio_assigned_id: pass.organizationAssignedId.fold(() => undefined, item => item),
      customer_id: undefined,
      referrer_id: pass.referrerId.fold(() => undefined, item => item),
      customer_fullname: pass.customer.fullname,
      customer_image: pass.customer.image.fold(() => undefined, item => item),
      customer_email: pass.customer.email ?? undefined,
      customer_phone: pass.customer.phone.fold(() => undefined, item => item),
      customer_gender: pass.customer.gender.fold(() => undefined, item => item),
      customer_personal_document_number: pass.customer.personalDocumentNumber.fold(() => undefined, item => item.number ?? undefined),
      customer_personal_document_type: pass.customer.personalDocumentNumber.fold(() => undefined, item => item.type ?? undefined),
      customer_address: pass.customer.address.fold(() => undefined, item => item),
      customer_country: pass.customer.country.fold(() => undefined, item => item),
      customer_postal_code: pass.customer.postalCode.fold(() => undefined, item => item),
      customer_custom_fields: pass.customer.customFields.fold(() => undefined, item => item),
      customer_birth_date: pass.customer.birthday.fold(() => undefined, item => FvDate.create(item).toSeconds()),
      sourceUrl: pass.sourceUrl.fold(() => undefined, item => item),
      type_id: pass.typeId.fold(() => undefined, item => item),
      type_name: pass.typeName.fold(() => undefined, item => item),
      type_color: pass.typeColor.fold(() => undefined, item => item),
      code: pass.code,
      price_id: pass.priceId.fold(() => undefined, item => item),
      purchase_date: FvDate.create(pass.purchaseDate).toSeconds(),
      purchase_price: pass.purchasePrice,
      service_fees: pass.serviceFees,
      revenue: pass.revenue,
      payment_id: pass.paymentId.fold(() => undefined, item => item),
      state: pass.state,
      idx: pass.idx,
      language: pass.language ?? undefined,
      device: pass.device.device ?? undefined,
      os: pass.device.os ?? undefined,
      remarketing: pass.remarketing,
      archived: false,
      browser: pass.device.browser ?? undefined,
      sale_type: pass.saleType,
      discocil_total: pass.discocilTotal,
      created_at: pass.createdAt,
      created_by: pass.createdBy,
      updated_at: pass.updatedAt,
      updated_by: pass.updatedBy,
      removed_at: pass.removedAt,
      removed_by: pass.removedBy,
    };
  }
}
