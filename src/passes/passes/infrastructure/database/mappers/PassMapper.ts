import {
  ECurrency,
  EPassState,
  EPassTypeSaleTypes,
  FvDate,
  Maybe,
} from '@discocil/fv-domain-library/domain';
import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import { Pass } from '@/passes/passes/domain/entities/Pass';
import config from '@config/index';

import { passValidationSchema } from '../schemas/PassSchemaValidation';

import type { TPersonalDocument } from '@/cross-cutting/domain/contracts/CommonContracts';
import type { PassEither, PassPrimitives } from '@/passes/passes/domain/entities/Pass';
import type { DatePrimitive } from '@discocil/fv-domain-library/domain';
import type Ajv from 'ajv';
import type { PassSchemaType } from '../schemas/PassSchemaType';

export class PassMapper {
  static execute(data: PassSchemaType): PassEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(passValidationSchema);

    if (!validate(data) || validate.errors) {
      MapperError.logger({
        context: PassMapper.name,
        data,
        target: validate.errors,
      });

      return PassSoftMapper.execute(data);
    }

    return this.buildEntity(data);
  }

  static buildEntity(data: PassSchemaType): PassEither {
    const currency = data.organization?.currency ?? ECurrency.EUR;

    return Pass.build({
      id: data._id,
      applicationId: data.aplicacion_id ?? config.fv.apps.professionals.applicationId,
      organizationId: data.negocio_id,
      organizationAssignedId: Maybe.fromValue(data.negocio_assigned_id),
      referrerId: Maybe.fromValue(data.referrer_id),
      code: data.code,
      sourceUrl: Maybe.fromValue(data.sourceUrl),
      customer: this.makeCustomer(data),
      saleType: data.sale_type ?? EPassTypeSaleTypes.ONLINE,
      typeId: Maybe.fromValue(data.type_id),
      typeName: Maybe.fromValue(data.type_name),
      typeColor: Maybe.fromValue(data.type_color),
      priceId: Maybe.fromValue(data.price_id),
      purchaseDate: this.makePurchaseDate(data),
      purchasePrice: data.purchase_price ?? 0,
      serviceFees: data.service_fees ?? 0,
      revenue: data.revenue ?? 0,
      paymentId: Maybe.fromValue(data.payment_id),
      state: data.state,
      idx: data.idx,
      language: data.language,
      device: {
        browser: data.browser ?? null,
        device: data.device ?? null,
        os: data.os ?? null,
      },
      archived: data.archived ?? false,
      remarketing: data.remarketing,
      currency,
      discocilTotal: data.discocil_total,
      createdAt: data.created_at,
      createdBy: data.created_by,
      updatedAt: data.updated_at,
      updatedBy: data.updated_by,
      removedAt: data.removed_at,
      removedBy: data.removed_by,
    });
  }

  protected static makePersonalDocument(data: PassSchemaType): Maybe<TPersonalDocument> {
    const personalDocumentNumber = data.customer_personal_document_number;
    const personalDocumentType = data.customer_personal_document_type;

    if (!!personalDocumentNumber && !!personalDocumentType) {
      return Maybe.some({
        number: personalDocumentNumber,
        type: personalDocumentType,
      });
    }

    return Maybe.none<TPersonalDocument>();
  }

  protected static makeCustomer(data: PassSchemaType): PassPrimitives['customer'] {
    return {
      fullname: data.customer_fullname,
      image: Maybe.fromValue(data.customer_image),
      email: data.customer_email,
      phone: Maybe.fromValue(data.customer_phone),
      gender: Maybe.fromValue(data.customer_gender),
      personalDocumentNumber: this.makePersonalDocument(data),
      address: Maybe.fromValue(data.customer_address),
      birthday: this.makeBirthday(data),
      country: Maybe.fromValue(data.customer_country),
      postalCode: Maybe.fromValue(data.customer_postal_code),
      customFields: this.makeCustomerCustomFields(data),
    };
  }

  protected static makeCustomerCustomFields(data: PassSchemaType): PassPrimitives['customer']['customFields'] {
    const customerCustomFields = data.customer_custom_fields;

    if (!customerCustomFields || !Array.isArray(customerCustomFields) || customerCustomFields.length === 0) {
      return Maybe.none();
    }

    const customFields = customerCustomFields.map(customField => ({
      question: customField.question,
      answer: customField.answer,
    }));

    return Maybe.some(customFields);
  }

  protected static makeBirthday(data: PassSchemaType): Maybe<DatePrimitive> {
    let birthday = Maybe.none<DatePrimitive>();

    if (data.customer_birth_date) {
      const birthdayResult = FvDate.createFromSeconds(data.customer_birth_date);

      birthday = Maybe.some(birthdayResult.value);
    }

    return birthday;
  }

  protected static makePurchaseDate(data: PassSchemaType): PassPrimitives['purchaseDate'] {
    const purchaseDate = FvDate.createFromSeconds(data.purchase_date);

    return purchaseDate.value;
  }
}

export class PassSoftMapper extends PassMapper {
  static execute(data: PassSchemaType): PassEither {
    return this.buildEntity({
      ...data,
      _id: data._id,
      code: data.code ?? null,
      sale_type: data.sale_type ?? EPassTypeSaleTypes.ONLINE,
      purchase_price: data.purchase_price ?? 0,
      service_fees: data.service_fees ?? 0,
      revenue: data.revenue ?? 0,
      state: data.state ?? EPassState.PENDING_PAYMENT,
      idx: data.idx ?? null,
      language: data.language ?? null,
    });
  }
}
