import { left, NotFoundError } from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { Pass } from '@/passes/passes/domain/entities/Pass';

import { PassMapper } from '../mappers/PassMapper';
import { PassSchemaMapper } from '../mappers/PassSchemaMapper';
import { passSchema } from '../schemas/PassSchema';

import type { PassRepository } from '@/passes/passes/domain/contracts/PassRepository';
import type {
  PassEither,
  Passes,
  PassKeys,
} from '@/passes/passes/domain/entities/Pass';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { PassSchemaType } from '../schemas/PassSchemaType';

type PropertiesMapper = Partial<Record<PassKeys, keyof PassSchemaType>>;

export class PassMongoRepository extends MongoRepository implements PassRepository {
  protected getSchema(): Schema {
    return new Schema(passSchema);
  }

  protected getDBName(): string {
    return EDBNames.PASSES;
  }

  protected getModel(): string {
    return 'passes';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      code: 'code',
      removedAt: 'removed_at',
    };
  }

  async save(pass: Pass): Promise<void> {
    const toSave = PassSchemaMapper.execute(pass);

    const filter = { _id: pass.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const modelQuery = await this.getConnection();

    await modelQuery.updateOne(filter, update, options);
  }

  async saveMany(passes: Passes): Promise<void> {
    const toSave: Array<Partial<PassSchemaType>> = [];

    passes.forEach((pass: Pass) => toSave.push(PassSchemaMapper.execute(pass)));

    const modelQuery = await this.getConnection();

    await modelQuery.insertMany(toSave);
  }

  async find(criteria: Criteria): Promise<PassEither> {
    criteria.setPage(1).setPerPage(1);

    const queryResponse = (await this.customQueryFinder<PassSchemaType[]>(criteria)).shift();

    return queryResponse ? PassMapper.execute(queryResponse) : left(NotFoundError.build({ context: this.constructor.name, target: Pass.name }));
  }

  private async customQueryFinder<T>(criteria: Criteria): Promise<T> {
    const connection = await this.getConnection();
    const pipeline = this.criteriaConverter.toPipeline(criteria);

    return connection.aggregate([
      ...pipeline,
      {
        $lookup: {
          from: 'eventos',
          let: { eventId: '$evento_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$eventId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'event',
        },
      },
      {
        $lookup: {
          from: 'negocios',
          let: { organizationId: '$negocio_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$organizationId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'organization',
        },
      },
      {
        $project: {
          main: '$$ROOT',
          eventObj: { $arrayElemAt: ['$event', 0] },
          organizationObj: { $arrayElemAt: ['$organization', 0] },
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              '$main',
              {
                event: { currency: { $ifNull: ['$eventObj.currency', null] } },
                organization: { currency: { $ifNull: ['$organizationObj.currency', null] } },
              },
            ],
          },
        },
      },
    ]) as unknown as T;
  }
}
