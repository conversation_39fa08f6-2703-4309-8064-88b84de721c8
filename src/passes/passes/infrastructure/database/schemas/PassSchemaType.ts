import type {
  ECountryCode,
  ECurrency,
  EDocumentType,
  EGender,
  EPassState,
  EPassTypeSaleTypes,
} from '@discocil/fv-domain-library/domain';

export type PassSchemaType = {
  _id: string;
  aplicacion_id: string;
  referrer_id?: string;
  negocio_id: string;
  negocio_assigned_id?: string;
  customer_id?: string | null;
  customer_fullname: string;
  customer_image?: string;
  code: string;
  customer_email: string;
  customer_phone?: string;
  customer_gender?: EGender;
  customer_personal_document_number?: string;
  customer_personal_document_type?: EDocumentType;
  customer_address?: string;
  customer_country?: ECountryCode;
  customer_postal_code?: string;
  type_id?: string;
  type_name?: string;
  type_color?: string;
  price_id?: string;
  purchase_date: number;
  purchase_price: number;
  service_fees: number;
  revenue: number;
  payment_id?: string;
  state: EPassState;
  idx: string;
  language: string;
  device?: string;
  archived: boolean;
  os?: string;
  browser?: string;
  customer_custom_fields?: {
    question: string;
    answer: string;
  }[];
  customer_birth_date?: number;
  sourceUrl?: string;
  remarketing: boolean;
  sale_type: EPassTypeSaleTypes;
  organization: {
    currency: ECurrency;
  };
  discocil_total: number;
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
  removed_at: number;
  removed_by: string;
};

export type PassOriginalSchemaType = Omit<PassSchemaType, 'organization'>;
