import type { JSONSchemaType } from 'ajv';
import type { PassOriginalSchemaType } from './PassSchemaType';

export const passValidationSchema: JSONSchemaType<PassOriginalSchemaType> = {
  title: 'Pass Type JSON Schema',
  required: ['negocio_id'],
  type: 'object',
  properties: {
    _id: { type: 'string' },
    aplicacion_id: { type: 'string' },
    referrer_id: { type: 'string', nullable: true },
    negocio_id: { type: 'string' },
    negocio_assigned_id: { type: 'string', nullable: true },
    customer_id: { type: 'string', nullable: true },
    customer_fullname: { type: 'string' },
    customer_image: { type: 'string', nullable: true },
    code: { type: 'string' },
    customer_email: { type: 'string' },
    customer_phone: { type: 'string', nullable: true },
    customer_gender: { type: 'string', nullable: true },
    customer_personal_document_number: { type: 'string', nullable: true },
    customer_personal_document_type: { type: 'string', nullable: true },
    customer_address: { type: 'string', nullable: true },
    customer_postal_code: { type: 'string', nullable: true },
    customer_custom_fields: {
      type: 'array',
      nullable: true,
      items: {
        type: 'object',
        properties: {
          question: { type: 'string' },
          answer: { type: 'string' },
        },
        required: ['question', 'answer'],
      },
    },
    customer_birth_date: { type: 'number', nullable: true },
    customer_country: { type: 'string', nullable: true },
    type_id: { type: 'string', nullable: true },
    type_name: { type: 'string', nullable: true },
    type_color: { type: 'string', nullable: true },
    price_id: { type: 'string', nullable: true },
    purchase_date: { type: 'number' },
    purchase_price: { type: 'number' },
    service_fees: { type: 'number' },
    revenue: { type: 'number' },
    payment_id: { type: 'string', nullable: true },
    state: { type: 'string' },
    idx: { type: 'string' },
    language: { type: 'string' },
    device: { type: 'string', nullable: true },
    archived: { type: 'boolean' },
    os: { type: 'string', nullable: true },
    remarketing: { type: 'boolean' },
    sourceUrl: { type: 'string', nullable: true },
    browser: { type: 'string', nullable: true },
    sale_type: { type: 'string' },
    discocil_total: { type: 'number' },
    created_at: { type: 'number' },
    created_by: { type: 'string' },
    updated_at: { type: 'number' },
    updated_by: { type: 'string' },
    removed_at: { type: 'number' },
    removed_by: { type: 'string' },
  },
};
