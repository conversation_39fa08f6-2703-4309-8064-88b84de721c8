import {
  ECountryCode,
  EPassState,
  EPassTypeSaleTypes,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import config from '@config/index';

const customerCustomFieldSchema = new Schema({
  question: { type: String, required: true },
  answer: { type: String, required: true },
}, { _id: false });

export const passSchema = {
  _id: {
    type: String,
    required: true,
  },
  aplicacion_id: {
    type: String,
    required: true,
    default: config.fv.apps.professionals.applicationId,
    index: true,
  },
  negocio_id: {
    type: String,
    required: true,
    index: true,
  },
  negocio_assigned_id: {
    type: String,
    default: '',
  },
  code: {
    type: String,
    required: true,
    index: true,
  },
  referrer_id: {
    type: String,
    default: '',
    index: true,
  },

  referrer_fullname: { type: String },
  customer_id: {
    type: String,
    index: true,
  },
  customer_fullname: { type: String },
  customer_image: { type: String },
  customer_email: { type: String },
  customer_phone: { type: String },
  customer_gender: { type: String },
  customer_personal_document_number: { type: String },
  customer_personal_document_type: { type: String },
  customer_address: { type: String },
  customer_country: {
    type: String,
    enum: Object.values(ECountryCode),
  },
  customer_postal_code: { type: String },
  customer_custom_fields: { type: [customerCustomFieldSchema] },
  customer_birth_date: { type: Number },
  type_id: {
    type: String,
    default: '',
    index: true,
  },
  type_name: {
    type: String,
    default: '',
  },
  type_color: {
    type: String,
    default: '',
  },
  price_id: {
    type: String,
    default: '',
    index: true,
  },
  purchase_date: {
    type: Number,
    default: 0,
  },
  purchase_price: {
    type: Number,
    default: 0,
  },
  service_fees: {
    type: Number,
    default: 0,
  },
  revenue: {
    type: Number,
    default: 0,
  },
  refunded: {
    type: Number,
    default: 0,
  },
  refunded_at: {
    type: Number,
    default: 0,
  },
  refunds: {
    type: Array,
    default: [],
  },
  last_time_used: {
    type: Number,
    default: 0,
  },
  usage_times: {
    type: Number,
    default: 0,
  },
  usage_revenue: {
    type: Number,
    default: 0,
  },
  payment_id: {
    type: String,
    default: '',
    index: true,
  },
  state: {
    type: String,
    default: EPassState.PENDING_PAYMENT,
    enum: Object.values(EPassState),
    index: true,
  },
  archived: {
    type: Boolean,
    default: false,
  },
  idx: {
    type: String,
    index: true,
  },
  sourceUrl: { type: String },
  language: { type: String },
  browser: { type: String },
  device: { type: String },
  os: { type: String },
  student_association: { type: String },
  university: { type: String },
  remarketing: { type: Boolean },
  sale_type: {
    type: String,
    required: true,
    default: EPassTypeSaleTypes.ONLINE,
    enum: Object.values(EPassTypeSaleTypes),
    index: true,
  },
  discocil_total: { type: Number },
  created_at: { type: Number },
  created_by: { type: String },
  updated_at: { type: Number },
  updated_by: { type: String },
  removed_at: {
    type: Number,
    index: true,
    default: 0,
  },
  removed_by: { type: String },
};
