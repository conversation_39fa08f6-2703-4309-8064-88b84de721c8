
import type { IExternalMessageBrokerClient } from '@/cross-cutting/domain/messageBroker/ExternalMessageBrokerClientContracts';
import type { DomainEvent } from '@discocil/fv-domain-library/domain';
import type { IPassEmitter } from '../../domain/contracts/PassEmitter';
import type { Pass } from '../../domain/entities/Pass';

export class PassEmitter implements IPassEmitter {
  constructor(private readonly externalMessageBroker: IExternalMessageBrokerClient) {}

  async processPassClientRegistration(pass: Pass): Promise<void> {
    void this.externalMessageBroker.sendToGlobalQueue({
      eventName: 'pass_client_registration',
      message: {
        passTypeId: pass.typeId.fold(() => null, typeId => typeId),
        organizationId: pass.organizationId,
        customerEmail: pass.customer.email,
        referrerId: pass.referrerId.fold(() => null, referrerId => referrerId),
      },
    });
  }

  async publish(domainEvent: DomainEvent): Promise<void> {
    void this.externalMessageBroker.publish(domainEvent);
  }
}
