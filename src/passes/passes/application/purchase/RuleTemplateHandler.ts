import { AbstractHand<PERSON> } from '@discocil/fv-domain-library/application';
import { container } from 'tsyringe';

import { OrganizationDependencyIdentifier } from '@/organizations/organizations/domain/dependencyIdentifier/OrganizationDependencyIdentifier';
import { RuleTemplateFinder } from '@/organizations/organizations/domain/services/RuleTemplateFinder';

import type { RuleTemplateRepository } from '@/organizations/organizations/domain/contracts/RuleTemplateRepository';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';

type Request = {
  readonly organization: Organization;
};

export class RuleTemplateHandler extends AbstractHandler {
  private static instance: RuleTemplateHandler;

  static build(): RuleTemplateHandler {
    if (!RuleTemplateHandler.instance) {
      RuleTemplateHandler.instance = new RuleTemplateHandler();
    }

    return RuleTemplateHandler.instance;
  }

  async handle<T>(request: Request): Promise<T> {
    const { organization } = request;

    const ruleTemplateRepository = container.resolve<RuleTemplateRepository>(OrganizationDependencyIdentifier.RuleTemplateRepository);
    const ruleTemplateFinder = new RuleTemplateFinder(ruleTemplateRepository);
    const ruleTemplate = await ruleTemplateFinder.execute(organization);

    return super.handle({
      ...request,
      ruleTemplate,
    });
  }
}
