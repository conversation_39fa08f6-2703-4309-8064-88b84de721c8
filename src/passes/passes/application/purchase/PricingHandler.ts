import {
  EFeeType,
  FvNumber,
  left,
  NotFoundError,
} from '@discocil/fv-domain-library';
import { AbstractHandler } from '@discocil/fv-domain-library/application';
import { EValueType } from '@discocil/fv-pricing-library/ticketing';

import { PassesPaymentTotalsService } from '../../domain/services/passesPricing/PassesPaymentTotalsService';
import { PassesPricingService } from '../../domain/services/passesPricing/PassesPricingService';

import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { RuleTemplate } from '@/organizations/organizations/domain/entities/RuleTemplate';
import type { Prices } from '@/passes/prices/domain/entities/Price';
import type {
  AdminFeeConfigPrimitives,
  InputTicketPrimitives,
  PaymentGatewayFeeConfigPrimitives,
} from '@discocil/fv-pricing-library/ticketing';
import type { SetPassDTO } from '../../domain/contracts/PurchasePassResponse';

type Request = {
  readonly prices: Prices;
  readonly passesDto: SetPassDTO[];
  readonly ruleTemplate: RuleTemplate;
  readonly organization: Organization;
};

export class PricingHandler extends AbstractHandler {
  private static instance: PricingHandler;

  static build(): PricingHandler {
    if (!PricingHandler.instance) {
      PricingHandler.instance = new PricingHandler();
    }

    return PricingHandler.instance;
  }

  async handle<T>(request: Request): Promise<T> {
    const {
      prices, passesDto, ruleTemplate, organization,
    } = request;

    const currency = organization.currency;

    const passesPaymentGatewayTransactionFeeRule = ruleTemplate.getGGDDPasses();

    const paymentGatewayTransactionFeeConfig: PaymentGatewayFeeConfigPrimitives = {
      fixedAmount: passesPaymentGatewayTransactionFeeRule.fixed,
      percentageDecimalValue: FvNumber.build(passesPaymentGatewayTransactionFeeRule.percentage).toPercentage(),
      maximumAmount: passesPaymentGatewayTransactionFeeRule.maximum,
      minimumAmount: passesPaymentGatewayTransactionFeeRule.minimum,
    };

    const inputPasses: InputTicketPrimitives[] = [];

    for (const pass of passesDto) {
      const price = prices.get(pass.priceId);

      if (!price) {
        return super.finish(left(NotFoundError.build({
          context: this.constructor.name,
          data: { pass, prices },
        })));
      }

      const adminFeeConfig: AdminFeeConfigPrimitives = {
        type: price.serviceFees.type === EFeeType.FIXED ? EValueType.FIXED : EValueType.PERCENTAGE,
        value: price.serviceFees.quantity,
      };

      const inputPass: InputTicketPrimitives = {
        id: pass.customer.id,
        ticketTypeId: pass.passTypeId,
        optionId: pass.priceId,
        unitPrice: price.price,
        addOns: [],
        isWarrantySelected: false,
        adminFeeConfig,
      };

      inputPasses.push(inputPass);
    }

    const passesPricingOrError = PassesPricingService.execute({
      tickets: inputPasses,
      currency,
      paymentGatewayTransactionFeeConfig,
    });

    if (passesPricingOrError.isLeft()) {
      return super.finish(passesPricingOrError);
    }

    const passesPricing = passesPricingOrError.value;

    const paymentTotalsOrError = PassesPaymentTotalsService.execute({
      tickets: passesPricing,
      currency,
    });

    if (paymentTotalsOrError.isLeft()) {
      return super.finish(paymentTotalsOrError);
    }

    const paymentTotals = paymentTotalsOrError.value;

    return super.handle({
      ...request,
      passesPricing,
      paymentTotals,
    });
  }
}
