import { Maybe } from '@discocil/fv-domain-library/domain';

import { Price } from '../entities/Price';

import type { Nullable } from '@discocil/fv-domain-library/domain';
import type { PriceEither, PricePrimitives } from '../entities/Price';

export type PriceJsonPrimitives = Omit<PricePrimitives,
  'description'
  | 'additionalInfo'
> & Nullable<{
  readonly description: string;
  readonly additionalInfo: string;
}>;

export class PriceJsonMapper {
  static toEntity(primitives: PriceJsonPrimitives): PriceEither {
    return Price.build({
      ...primitives,
      description: Maybe.fromValue(primitives.description),
      additionalInfo: Maybe.fromValue(primitives.additionalInfo),
    });
  }

  static toJson(price: Price): PriceJsonPrimitives {
    return {
      id: price.id,
      organizationId: price.organizationId,
      description: price.description.fold(() => null, item => item),
      price: price.price,
      currency: price.currency,
      serviceFees: price.serviceFees,
      maximumNumberPass: price.maximumNumberPass,
      additionalInfo: price.additionalInfo.fold(() => null, item => item),
      createdAt: price.createdAt,
      createdBy: price.createdBy,
      updatedAt: price.updatedAt,
      updatedBy: price.updatedBy,
      removedAt: price.removedAt,
      removedBy: price.removedBy,
    };
  }
}
