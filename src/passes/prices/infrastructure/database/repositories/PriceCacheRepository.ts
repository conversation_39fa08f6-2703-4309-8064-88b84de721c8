import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { PriceJsonMapper } from '@/passes/prices/domain/mappers/PriceJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { PriceRepository } from '@/passes/prices/domain/contracts/PriceRepository';
import type {
  Price,
  PriceEither,
  Prices,
  PricesEither,
} from '@/passes/prices/domain/entities/Price';
import type { PriceJsonPrimitives } from '@/passes/prices/domain/mappers/PriceJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class PriceCacheRepository extends CacheRepository implements PriceRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: PriceRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<PriceEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: PriceJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = PriceJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        PriceJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async search(criteria: Criteria): Promise<PricesEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: PriceJsonPrimitives[] = JSON.parse(cacheHit);
      const prices: Prices = new Map<IdPrimitive, Price>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = PriceJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        prices.set(_primitive.id, entityOrError.value);
      }

      return right(prices);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const prices = repositoryResult.value;
      const eventLength = prices.size;
      let pricesCacheIndex = 0;
      const jsonPrices = new Array<PriceJsonPrimitives>(eventLength);

      if (eventLength > 0) {
        for (const _event of prices.values()) {
          jsonPrices[pricesCacheIndex] = PriceJsonMapper.toJson(_event);

          pricesCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonPrices, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}
