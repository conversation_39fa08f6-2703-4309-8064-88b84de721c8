import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { Price } from '@/passes/prices/domain/entities/Price';

import { PriceMapper } from '../mappers/PriceMapper';
import { PriceSchema } from '../schemas/PriceSchema';

import type { PriceRepository } from '@/passes/prices/domain/contracts/PriceRepository';
import type {
  PriceEither,
  PriceKeys,
  PricesEither,
} from '@/passes/prices/domain/entities/Price';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { Schema } from 'mongoose';
import type { PriceSchemaType } from '../schemas/PriceSchemaType';

type PropertiesMapper = Partial<Record<PriceKeys, keyof PriceSchemaType>>;

export class PriceMongoRepository extends MongoRepository implements PriceRepository {
  protected getSchema(): Schema {
    return PriceSchema;
  }

  protected getDBName(): string {
    return EDBNames.PASSES;
  }

  protected getModel(): string {
    return 'prices';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<PriceEither> {
    criteria.setPage(1).setPerPage(1);

    const queryResponse = (await this.customQueryFinder<PriceSchemaType[]>(criteria)).shift();

    return queryResponse ? PriceMapper.execute(queryResponse) : left(NotFoundError.build({ context: this.constructor.name, target: Price.name }));
  }

  async search(criteria: Criteria): Promise<PricesEither> {
    const prices = new Map<IdPrimitive, Price>();

    const queryResponse = await this.customQueryFinder<PriceSchemaType[]>(criteria);

    if (queryResponse.length === 0) {
      return right(prices);
    }

    for (const model of queryResponse) {
      const priceResult = PriceMapper.execute(model);

      if (priceResult.isLeft()) {
        return left(priceResult.value);
      }

      const price = priceResult.value;

      prices.set(price.id, price);
    }

    return right(prices);
  }

  private async customQueryFinder<T>(criteria: Criteria): Promise<T> {
    const connection = await this.getConnection();
    const pipeline = this.criteriaConverter.toPipeline(criteria);

    return connection.aggregate([
      ...pipeline,
      {
        $lookup: {
          from: 'negocios',
          let: { organizationId: '$negocio_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$organizationId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'organization',
        },
      },
      {
        $project: {
          main: '$$ROOT',
          organizationObj: { $arrayElemAt: ['$organization', 0] },
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              '$main',
              { organization: { currency: { $ifNull: ['$organizationObj.currency', null] } } },
            ],
          },
        },
      },
    ]) as unknown as T;
  }
}
