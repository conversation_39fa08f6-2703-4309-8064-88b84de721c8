import {
  FvDate,
  left,
  Maybe,
} from '@discocil/fv-domain-library/domain';

import { PassType } from '../entities/PassType';

import type { FieldOptions } from '@/cross-cutting/domain/contracts/FormFields';
import type { Nullable } from '@discocil/fv-domain-library/domain';
import type {
  PassTypeEither,
  PassTypeFieldName,
  PassTypeFormFields,
  PassTypePrimitives,
} from '../entities/PassType';

type PassTypeFormFieldsJson = Record<PassTypeFieldName, FieldOptions | null>;

type PassTypePaylinkJson = {
  readonly hasExpirationTime: boolean;
  readonly expirationTime: string | null;
};

type PassTypeMessageJson = {
  readonly manualStop: string | null;
  readonly autoEnd: string;
  readonly autoLimit: string;
};

export type PassTypeJsonPrimitives = Omit<PassTypePrimitives,
  'startDate'
  | 'endDate'
  | 'designId'
  | 'defaultConditionId'
  | 'fields'
  | 'message'
  | 'description'
  | 'iosPasskitId'
  | 'paylink'
> & Nullable<{
  readonly designId: string;
  readonly defaultConditionId: string;
  readonly fields: PassTypeFormFieldsJson;
  readonly description: string;
  readonly iosPasskitId: string;
}> & {
  readonly startDate: string;
  readonly endDate: string;
  readonly message: PassTypeMessageJson;
  readonly paylink: PassTypePaylinkJson;
};

export class PassTypeJsonMapper {
  static toEntity(primitives: PassTypeJsonPrimitives): PassTypeEither {
    const startDateOrError = FvDate.createFromISO(primitives.startDate);
    const endDateOrError = FvDate.createFromISO(primitives.endDate);

    if (startDateOrError.isLeft()) {
      return left(startDateOrError.value);
    }

    if (endDateOrError.isLeft()) {
      return left(endDateOrError.value);
    }

    return PassType.build({
      ...primitives,
      startDate: startDateOrError.value.toPrimitive(),
      endDate: endDateOrError.value.toPrimitive(),
      designId: Maybe.fromValue(primitives.designId),
      defaultConditionId: Maybe.fromValue(primitives.defaultConditionId),
      description: Maybe.fromValue(primitives.description),
      iosPasskitId: Maybe.fromValue(primitives.iosPasskitId),
      message: {
        ...primitives.message,
        manualStop: Maybe.fromValue(primitives.message.manualStop),
      },
      paylink: {
        ...primitives.paylink,
        expirationTime: primitives.paylink.expirationTime
          ? Maybe.some((FvDate.createFromISO(primitives.paylink.expirationTime).value as FvDate).toSeconds())
          : Maybe.none<number>(),
      },
      fields: primitives.fields
        ? Maybe.fromValue(this.makeFields(primitives.fields))
        : Maybe.none<PassTypeFormFields>(),
    });
  }

  private static makeFields(fields: PassTypeFormFieldsJson): PassTypeFormFields {
    return {
      fullname: Maybe.fromValue(fields.fullname),
      email: Maybe.fromValue(fields.email),
      phone: Maybe.fromValue(fields.phone),
      birthDate: Maybe.fromValue(fields.birthDate),
      postalCode: Maybe.fromValue(fields.postalCode),
      gender: Maybe.fromValue(fields.gender),
      address: Maybe.fromValue(fields.address),
      country: Maybe.fromValue(fields.country),
      personalDocumentNumber: Maybe.fromValue(fields.personalDocumentNumber),
      image: Maybe.fromValue(fields.image),
      customerCustomFields: Maybe.fromValue(fields.customerCustomFields),
    };
  }

  static toJson(passType: PassType): PassTypeJsonPrimitives {
    return {
      id: passType.id,
      organizationId: passType.organizationId,
      name: passType.name,
      color: passType.color,
      startDate: passType.getStartDateIsISO(),
      endDate: passType.getEndDateIsISO(),
      startCountdown: passType.startCountdown,
      tags: passType.tags,
      designId: passType.designId.fold(() => null, item => item),
      defaultConditionId: passType.defaultConditionId.fold(() => null, item => item),
      published: passType.published,
      archived: passType.archived,
      fields: passType.fields.fold(
        () => null,
        (item) => {
          return {
            fullname: item.fullname.fold(() => null, item => item),
            email: item.email.fold(() => null, item => item),
            phone: item.phone.fold(() => null, item => item),
            birthDate: item.birthDate.fold(() => null, item => item),
            postalCode: item.postalCode.fold(() => null, item => item),
            gender: item.gender.fold(() => null, item => item),
            address: item.address.fold(() => null, item => item),
            country: item.country.fold(() => null, item => item),
            personalDocumentNumber: item.personalDocumentNumber.fold(() => null, item => item),
            image: item.image.fold(() => null, item => item),
            customerCustomFields: item.customerCustomFields.fold(() => null, item => item),
          };
        },
      ),
      message: {
        manualStop: passType.message.manualStop.fold(() => null, item => item),
        autoEnd: passType.message.autoEnd,
        autoLimit: passType.message.autoLimit,
      },
      description: passType.description.fold(() => null, item => item),
      saleTypes: passType.saleTypes,
      totals: {
        limit: passType.totals.limit,
        sold: passType.totals.sold,
      },
      priceIds: passType.priceIds,
      eventIds: passType.eventIds,
      iosPasskitId: passType.iosPasskitId.fold(() => null, item => item),
      paylink: {
        hasExpirationTime: passType.paylink.hasExpirationTime,
        expirationTime: passType.paylink.expirationTime.fold(() => null, item => FvDate.createFromSeconds(item).toISO()),
      },
      createdAt: passType.createdAt,
      createdBy: passType.createdBy,
      updatedAt: passType.updatedAt,
      updatedBy: passType.updatedBy,
      removedAt: passType.removedAt,
      removedBy: passType.removedBy,
    };
  }
}
