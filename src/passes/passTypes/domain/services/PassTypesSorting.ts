import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { PassType, PassTypes } from '../entities/PassType';

export class PassTypesSorting {
  static execute(passTypes: PassTypes): PassTypes {
    const sortedPassTypes: PassTypes = new Map<IdPrimitive, PassType>();

    Array.from(passTypes.values()).toSorted((aPassType, bPassType) => {
      const pricePassTypeA = aPassType.getPrice().fold(() => 0, price => price.price);
      const pricePassTypeB = bPassType.getPrice().fold(() => 0, price => price.price);

      return pricePassTypeA - pricePassTypeB
        || aPassType.name.localeCompare(bPassType.name);
    }).forEach(passType => sortedPassTypes.set(passType.id, passType));

    return sortedPassTypes;
  }
}
