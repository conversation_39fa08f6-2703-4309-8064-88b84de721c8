import {
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { PassTypeCriteriaMother } from '../filters/PassTypeCriteriaMother';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  Either,
  InvalidArgumentError,
  NotFoundError,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';
import type { PassTypeRepository } from '../contracts/PassTypeRepository';
import type { FilterPassTypesWithoutFutureEvents } from './FilterPassTypesWithoutFutureEvents';

type OrganizationHasPassTypesInput = {
  readonly organizationId: UniqueEntityID;
};

type OrganizationHasPassTypesOutput = Either<InvalidArgumentError | MapperError | NotFoundError, boolean>;

export class OrganizationHasPassTypes {
  constructor(
    private readonly passTypeRepository: PassTypeRepository,
    private readonly filterPassTypesWithoutFutureEvents: FilterPassTypesWithoutFutureEvents,
  ) {}

  async execute(input: OrganizationHasPassTypesInput): Promise<OrganizationHasPassTypesOutput> {
    const { organizationId } = input;

    const criteria = PassTypeCriteriaMother.organizationToMatch(organizationId);

    const passTypesResult = await this.passTypeRepository.search(criteria);

    if (passTypesResult.isLeft()) {
      return left(passTypesResult.value);
    }

    const passTypes = passTypesResult.value.passTypes;

    const passTypesFilteredResult = await this.filterPassTypesWithoutFutureEvents.execute({ passTypes });

    if (passTypesFilteredResult.isLeft()) {
      return left(passTypesFilteredResult.value);
    }

    const organizationHasPassTypes = passTypesFilteredResult.value.size > 0;

    return right(organizationHasPassTypes);
  }
}
