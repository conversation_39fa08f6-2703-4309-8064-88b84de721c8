import { left, right } from '@discocil/fv-domain-library/domain';
import { GetOptionPricingService } from '@discocil/fv-pricing-library/ticketing';

import { PricingLibraryError } from '@/cross-cutting/domain/errors/PricingLibraryError';

import type { Either } from '@discocil/fv-domain-library/domain';
import type { OptionPricingRequest, OptionPricingResponse } from '@discocil/fv-pricing-library/ticketing';

type PassTypePricingServiceResponse = Either<PricingLibraryError, OptionPricingResponse>;

export class PassTypePricingService {
  static execute(input: OptionPricingRequest): PassTypePricingServiceResponse {
    try {
      const passTypePricing = GetOptionPricingService.execute(input);

      return right(passTypePricing);
    } catch (error: unknown) {
      return left(PricingLibraryError.build({
        context: this.constructor.name,
        data: { input, error },
      }));
    }
  }
}
