import {
  left,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type { PassType, PassTypes } from '@/passes/passTypes/domain/entities/PassType';
import type {
  Either,
  IdPrimitive,
  NotFoundError,
} from '@discocil/fv-domain-library/domain';

type FilterPassTypesWithoutFutureEventsInput = {
  readonly passTypes: PassTypes;
};

type FilterPassTypesWithoutFutureEventsOutput = Either<MapperError | NotFoundError, PassTypes>;

export class FilterPassTypesWithoutFutureEvents {
  constructor(private readonly eventRepository: EventRepository) {}

  async execute(input: FilterPassTypesWithoutFutureEventsInput): Promise<FilterPassTypesWithoutFutureEventsOutput> {
    const { passTypes } = input;

    const allEventIds = passTypes.values().reduce((acc, passType) => acc.concat(passType.eventIds), [] as IdPrimitive[]);

    const allEventIdsUnique = Array.from(new Set(allEventIds));

    const futureEventsResult = await this.eventRepository.search(
      EventCriteriaMother.presentOrFutureEventsByIds(allEventIdsUnique.map(id => UniqueEntityID.build(id))),
    );

    if (futureEventsResult.isLeft()) {
      return left(futureEventsResult.value);
    }

    const futureEvents = futureEventsResult.value.events;

    const passTypesWithFutureEvents: PassTypes = new Map<IdPrimitive, PassType>();

    passTypes.forEach((passType) => {
      const hasFutureEvent = passType.eventIds.some(eventId => futureEvents.has(eventId));

      if (hasFutureEvent) {
        passTypesWithFutureEvents.set(passType.id, passType);
      }
    });

    return right(passTypesWithFutureEvents);
  }
}
