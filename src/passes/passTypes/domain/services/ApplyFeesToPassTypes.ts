import {
  EFeeType,
  left,
  right,
} from '@discocil/fv-domain-library/domain';
import { EValueType } from '@discocil/fv-pricing-library/ticketing';

import { PassTypePricingService } from './PassTypePricingService';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { PricingLibraryError } from '@/cross-cutting/domain/errors/PricingLibraryError';
import type { Fees } from '@/fees/domain/entities/Fee';
import type { PassTypes } from '@/passes/passTypes/domain/entities/PassType';
import type {
  Either,
  InvalidArgumentError,
  MoneyError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import type { AdminFeeConfigPrimitives } from '@discocil/fv-pricing-library/ticketing';

type ApplyFeesToPassTypesInput = {
  readonly customerCustomFees: Fees;
  readonly passTypes: PassTypes;
};

type ApplyFeesToPassTypesOutput = Either<
  InvalidArgumentError | NotFoundError | MapperError | UnexpectedError | MoneyError | PricingLibraryError,
  PassTypes>;

export class ApplyFeesToPassTypes {
  execute(input: ApplyFeesToPassTypesInput): ApplyFeesToPassTypesOutput {
    const { customerCustomFees, passTypes } = input;

    const customerCustomFeesPrimitives = customerCustomFees.toArray().map(item => item.toPrimitives());

    for (const passType of passTypes.values()) {
      const priceOrEmpty = passType.getPrice();

      if (priceOrEmpty.isEmpty()) {
        continue;
      }

      const price = priceOrEmpty.get();

      const adminFeeConfig: AdminFeeConfigPrimitives = {
        type: price.serviceFees.type === EFeeType.PERCENTAGE ? EValueType.PERCENTAGE : EValueType.FIXED,
        value: price.serviceFees.quantity,
      };

      const passTypePricingOrError = PassTypePricingService.execute({
        price: price.price,
        currency: price.currency,
        customerCustomFees: customerCustomFeesPrimitives,
        adminFeeConfig,
      });

      if (passTypePricingOrError.isLeft()) {
        return left(passTypePricingOrError.value);
      }

      price.setPrice(passTypePricingOrError.value.totalAmount);
    }

    return right(passTypes);
  }
}
