import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { ErrorMethodRequest } from '@/cross-cutting/domain/errors/ErrorRequest';

export class PriceNotFoundError extends FvError {
  static readonly defaultCause = EErrorKeys.ENTITY_NOT_FOUND;

  static build(request: ErrorMethodRequest): PriceNotFoundError {
    const exceptionMessage = 'Price not found';

    const {
      context, error, data, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
