import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { ESaleTypes } from '@discocil/fv-domain-library/domain';
import type { PassTypeKeys } from '../entities/PassType';

class FilterField extends FilterFieldBase<PassTypeKeys> {}

export class SaleTypesFilter {
  private static readonly field: PassTypeKeys = 'saleTypes';

  static buildEqual(saleType: ESaleTypes): Filter {
    const saleTypeField = new FilterField(this.field);
    const saleTypeOperator = FilterOperator.equal();
    const saleTypeValue = FilterValue.build(saleType);

    return new Filter(saleTypeField, saleTypeOperator, saleTypeValue);
  }
}
