import {
  And<PERSON><PERSON>er, Filter, OrFilter,
} from '@discocil/fv-criteria-converter-library/domain';
import { Maybe } from '@discocil/fv-domain-library';

import { EventIdFilter } from '@/cross-cutting/domain/filters/EventIdFilter';

import type { PassTypeRepository } from '@/passes/passTypes/domain/contracts/PassTypeRepository';
import type {
  PassTypeEither,
  PassTypesSearchEither,
} from '@/passes/passTypes/domain/entities/PassType';
import type { CompositeFilter, Criteria } from '@discocil/fv-criteria-converter-library/domain';

export class PassTypeFacadeMongoRepository implements PassTypeRepository {
  constructor(
    private readonly passTypeMongoRepository: PassTypeRepository,
    private readonly passTypeEventMongoRepository: PassTypeRepository,
  ) {}

  async find(criteria: Criteria): Promise<PassTypeEither> {
    return this.passTypeMongoRepository.find(criteria);
  }

  async search(criteria: Criteria): Promise<PassTypesSearchEither> {
    const eventIdFilter = this.findFilterRecursively(criteria.filters.filters, EventIdFilter.field);

    if (eventIdFilter.isDefined()) {
      return this.passTypeEventMongoRepository.search(criteria);
    }

    return this.passTypeMongoRepository.search(criteria);
  }

  private findFilterRecursively(filters: CompositeFilter[], targetField: string): Maybe<Filter> {
    for (const filter of filters) {
      if (filter instanceof Filter && filter.field.value === targetField) {
        return Maybe.fromValue(filter);
      }

      if (filter instanceof AndFilter || filter instanceof OrFilter) {
        const filterResult = this.findFilterRecursively(filter.filters, targetField);

        if (filterResult.isDefined()) {
          return filterResult;
        }
      }
    }

    return Maybe.none();
  }
}
