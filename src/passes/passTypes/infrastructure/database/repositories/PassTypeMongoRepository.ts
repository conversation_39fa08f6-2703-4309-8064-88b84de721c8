import { Paginator } from '@discocil/fv-criteria-converter-library/domain';
import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { PassType } from '@/passes/passTypes/domain/entities/PassType';

import { PassTypeMapper } from '../mappers/PassTypeMapper';
import { PassTypeSchema } from '../Schemas/PassTypeSchema';

import type { PassTypeRepository } from '@/passes/passTypes/domain/contracts/PassTypeRepository';
import type {
  PassTypeEither,
  PassTypeKeys,
  PassTypes,
  PassTypesSearchEither,
} from '@/passes/passTypes/domain/entities/PassType';
import type { Criteria, RequiredCriteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { Schema } from 'mongoose';
import type { PassTypeSchemaType } from '../Schemas/PassTypeSchemaType';

type PropertiesMapper = Partial<Record<PassTypeKeys, keyof PassTypeSchemaType>>;

export class PassTypeMongoRepository extends MongoRepository implements PassTypeRepository {
  protected getSchema(): Schema {
    return PassTypeSchema;
  }

  protected getDBName(): string {
    return EDBNames.PASSES;
  }

  protected getModel(): string {
    return 'types';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      published: 'published',
      archived: 'archived',
      removedAt: 'removed_at',
      saleTypes: 'sale_types',
    };
  }

  async find(criteria: Criteria): Promise<PassTypeEither> {
    criteria.setPage(1).setPerPage(1);

    const queryResponse = (await this.customQueryFinder<PassTypeSchemaType[]>(criteria)).shift();

    return queryResponse
      ? PassTypeMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: PassType.name }));
  }

  async search(criteria: Criteria): Promise<PassTypesSearchEither> {
    const passTypes: PassTypes = new Map<IdPrimitive, PassType>();

    const queryResponse = await this.customQueryFinder<PassTypeSchemaType[]>(criteria);

    if (queryResponse.length === 0) {
      return right({ passTypes });
    }

    for (const model of queryResponse) {
      const passTypesResult = PassTypeMapper.execute(model);

      if (passTypesResult.isLeft()) {
        return left(passTypesResult.value);
      }

      const passType = passTypesResult.value;

      passTypes.set(passType.id, passType);
    }

    if (!criteria.pagination) {
      return right({ passTypes });
    }

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);
    const total = await connection.countDocuments(filterQuery.filter);
    const requiredCriteria = criteria as RequiredCriteria;

    return right({
      passTypes,
      pagination: Paginator.execute(
        total,
        requiredCriteria,
        queryResponse.length,
      ),
    });
  }

  private async customQueryFinder<T>(criteria: Criteria): Promise<T> {
    const connection = await this.getConnection();
    const pipeline = this.criteriaConverter.toPipeline(criteria);

    return connection.aggregate([
      ...pipeline,
      {
        $lookup: {
          from: 'types_eventdate',
          let: { passTypeId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [
                    '$type_id',
                    '$$passTypeId',
                  ],
                },
                evento_id: { $ne: null },
                removed_at: 0,
              },
            },
          ],
          as: 'event_types',
        },
      },
      {
        $addFields: {
          event_ids: {
            $map: {
              input: '$event_types',
              as: 'event_type',
              in: '$$event_type.evento_id',
            },
          },
        },
      },
      { $project: { event_types: 0 } },
    ]) as unknown as T;
  }
}
