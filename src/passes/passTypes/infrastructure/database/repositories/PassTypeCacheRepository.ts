import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { PaginationMetadataMapper } from '@/cross-cutting/infrastructure/services/paginationMetadataMapper';
import { PassTypeJsonMapper } from '@/passes/passTypes/domain/mappers/PassTypeJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { PaginationMetadataPrimitives } from '@/cross-cutting/infrastructure/services/paginationMetadataMapper';
import type { PassTypeRepository } from '@/passes/passTypes/domain/contracts/PassTypeRepository';
import type {
  PassType,
  PassTypeEither,
  PassTypes,
  PassTypesSearchEither,
} from '@/passes/passTypes/domain/entities/PassType';
import type { PassTypeJsonPrimitives } from '@/passes/passTypes/domain/mappers/PassTypeJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class PassTypeCacheRepository extends CacheRepository implements PassTypeRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: PassTypeRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<PassTypeEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: PassTypeJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = PassTypeJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        PassTypeJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async search(criteria: Criteria): Promise<PassTypesSearchEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: { passTypes: PassTypeJsonPrimitives[]; pagination: PaginationMetadataPrimitives; } = JSON.parse(cacheHit);
      const passTypes: PassTypes = new Map<IdPrimitive, PassType>();

      for (const _primitive of jsonPrimitives.passTypes) {
        const entityOrError = PassTypeJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        passTypes.set(_primitive.id, entityOrError.value);
      }

      return right({
        passTypes,
        pagination: PaginationMetadataMapper.fromPrimitives(jsonPrimitives.pagination),
      });
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const { passTypes, pagination } = repositoryResult.value;
      const passTypesLength = passTypes.size;
      let passTypesCacheIndex = 0;
      const jsonPassTypes = new Array<PassTypeJsonPrimitives>(passTypesLength);

      if (passTypesLength > 0) {
        for (const _event of passTypes.values()) {
          jsonPassTypes[passTypesCacheIndex] = PassTypeJsonMapper.toJson(_event);

          passTypesCacheIndex++;
        }
      }

      const resultForCache = {
        passTypes: jsonPassTypes,
        pagination: PaginationMetadataMapper.toPrimitives(pagination),
      };

      await this.cacheHandler.set(cacheKey, resultForCache, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}
