import {
  Criteria,
  Filter,
  Filters,
  Order,
  Paginator,
} from '@discocil/fv-criteria-converter-library/domain';
import { MongoCriteriaConverter } from '@discocil/fv-criteria-converter-library/infrastructure';
import {
  InvalidArgumentError,
  left,
  Maybe,
  right,
} from '@discocil/fv-domain-library/domain';

import { EventIdFilter } from '@/cross-cutting/domain/filters/EventIdFilter';
import { OrganizationIdFilter } from '@/cross-cutting/domain/filters/OrganizationIdFilter';
import { RemovedAtFilter } from '@/cross-cutting/domain/filters/RemovedAtFilter';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';

import { PassTypeMapper } from '../mappers/PassTypeMapper';
import { PassTypeEventSchema } from '../Schemas/PassTypeEventSchema';

import { PassTypeMongoRepository } from './PassTypeMongoRepository';

import type { PassTypeRepository } from '@/passes/passTypes/domain/contracts/PassTypeRepository';
import type {
  PassType,
  PassTypeKeys,
  PassTypes,
  PassTypesSearchEither,
} from '@/passes/passTypes/domain/entities/PassType';
import type {
  CompositeFilter,
  Pagination,
  RequiredCriteria,
} from '@discocil/fv-criteria-converter-library/domain';
import type { Either, IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { Schema } from 'mongoose';
import type { PassTypeEventSchemaType } from '../Schemas/PassTypeEventSchemaType';
import type { PassTypeSchemaType } from '../Schemas/PassTypeSchemaType';

type PropertiesMapperPassTypeEvent = Record<string, keyof PassTypeEventSchemaType>;
type PropertiesMapperPassType = Partial<Record<PassTypeKeys, keyof PassTypeSchemaType>>;

export class PassTypeEventMongoRepository extends PassTypeMongoRepository implements PassTypeRepository {
  protected getSchema(): Schema {
    return PassTypeEventSchema;
  }

  protected getDBName(): string {
    return EDBNames.PASSES;
  }

  protected getModel(): string {
    return 'types_eventdate';
  }

  protected propertiesMapper(): PropertiesMapperPassTypeEvent {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      eventId: 'evento_id',
      removedAt: 'removed_at',
    };
  }

  protected propertiesMapperPassType(): PropertiesMapperPassType {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      published: 'published',
      archived: 'archived',
      removedAt: 'removed_at',
      saleTypes: 'sale_types',
    };
  }

  async search(criteria: Criteria): Promise<PassTypesSearchEither> {
    const passTypes: PassTypes = new Map<IdPrimitive, PassType>();

    const passTypeEventCriteria = this.getPassTypeEventCriteria(criteria);

    if (passTypeEventCriteria.isLeft()) {
      return left(passTypeEventCriteria.value);
    }

    const passTypeCriteria = this.getPassTypeCriteria(criteria);

    if (passTypeCriteria.isLeft()) {
      return left(passTypeCriteria.value);
    }

    const queryResponse = await this.customPassTypeEventQueryFinder<PassTypeSchemaType[]>({
      passTypeEventCriteria: passTypeEventCriteria.value,
      passTypeCriteria: passTypeCriteria.value,
    });

    if (queryResponse.length === 0) {
      return right({ passTypes });
    }

    for (const model of queryResponse) {
      const passTypesResult = PassTypeMapper.execute(model);

      if (passTypesResult.isLeft()) {
        return left(passTypesResult.value);
      }

      const passType = passTypesResult.value;

      passTypes.set(passType.id, passType);
    }

    if (!criteria.pagination) {
      return right({ passTypes });
    }

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(passTypeEventCriteria.value);
    const total = await connection.countDocuments(filterQuery.filter);
    const requiredCriteria = criteria as RequiredCriteria;

    return right({
      passTypes,
      pagination: Paginator.execute(total, requiredCriteria, queryResponse.length),
    });
  }

  private async customPassTypeEventQueryFinder<T>(criterias: { passTypeEventCriteria: Criteria; passTypeCriteria: Criteria; }): Promise<T> {
    const { passTypeEventCriteria, passTypeCriteria } = criterias;

    const connection = await this.getConnection();

    const passTypeEventPipeline = this.criteriaConverter.toPipeline(passTypeEventCriteria);

    const passTypePipeline = new MongoCriteriaConverter(this.propertiesMapperPassType()).toPipeline(passTypeCriteria);

    const aggResult = await connection.aggregate([
      ...passTypeEventPipeline,
      {
        $lookup: {
          from: 'types',
          localField: 'type_id',
          foreignField: '_id',
          as: 'type',
        },
      },
      { $unwind: '$type' },
      { $replaceRoot: { newRoot: '$type' } },
      ...passTypePipeline,
      {
        $lookup: {
          from: 'types_eventdate',
          let: { passTypeId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [
                    '$type_id',
                    '$$passTypeId',
                  ],
                },
                evento_id: { $ne: null },
                removed_at: 0,
              },
            },
          ],
          as: 'types',
        },
      },
      {
        $addFields: {
          event_ids: {
            $map: {
              input: '$types',
              as: 'type',
              in: '$$type.evento_id',
            },
          },
        },
      },
      { $project: { types: 0 } },
    ]) as unknown as T;

    return aggResult;
  }

  private getPassTypeCriteria(criteria: Criteria): Either<InvalidArgumentError, Criteria> {
    const targetFields = [EventIdFilter.field];
    const filters = this.findFilters(criteria.filters.filters, targetFields, true);

    if (filters.isEmpty()) {
      return left(InvalidArgumentError.build({
        context: this.constructor.name,
        data: { criteria },
      }));
    }

    return right(this.buildCriteria(filters.get(), criteria.order));
  }

  private getPassTypeEventCriteria(criteria: Criteria): Either<InvalidArgumentError, Criteria> {
    const targetFields = [EventIdFilter.field, OrganizationIdFilter.field, RemovedAtFilter.field];
    const filters = this.findFilters(criteria.filters.filters, targetFields, false);

    if (filters.isEmpty()) {
      return left(InvalidArgumentError.build({
        context: this.constructor.name,
        data: { criteria },
      }));
    }

    return right(this.buildCriteria(filters.get(), Order.byDefault(), criteria.pagination));
  }

  private findFilters(filters: CompositeFilter[], targetFields: string[], shouldExclude: boolean): Maybe<Filter[]> {
    const targetFilters: Filter[] = [];
    const targetFiltersCount = shouldExclude ? filters.length - targetFields.length : targetFields.length;

    for (const filter of filters) {
      if (filter instanceof Filter && targetFields.includes(filter.field.value) !== shouldExclude) {
        targetFilters.push(filter);

        if (targetFilters.length === targetFiltersCount) {
          return Maybe.fromValue(targetFilters);
        }
      }
    }

    return Maybe.none();
  }

  private buildCriteria(filterArray: Filter[], order?: Order, pagination?: Pagination): Criteria {
    const filters = Filters.build();

    for (const filter of filterArray) {
      filters.add(filter);
    }

    return Criteria.build(filters, order, pagination);
  }
}
