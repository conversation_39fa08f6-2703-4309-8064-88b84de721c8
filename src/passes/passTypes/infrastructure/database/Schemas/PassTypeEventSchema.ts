import { Schema } from 'mongoose';

export const passTypeEventSchema = {
  _id: { type: String },
  negocio_id: {
    type: String,
    required: true,
    index: true,
  },
  evento_id: {
    type: String,
    index: true,
  },
  date: {
    type: String,
    default: '',
  },
  type_id: {
    type: String,
    index: true,
  },
  condition_id: {
    type: String,
    index: true,
  },
  created_at: { type: Number },
  created_by: { type: String },
  updated_at: { type: Number },
  updated_by: { type: String },
  removed_at: {
    type: Number,
    index: true,
  },
  removed_by: { type: String },
};

export const PassTypeEventSchema = new Schema(passTypeEventSchema);
