import {
  Collection,
  UniqueEntityID,
  contextualizeError,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { Fee } from '@/fees/domain/entities/Fee';
import { ShouldShowFeesUpfrontService } from '@/organizations/organizationConfigurations/domain/services/ShouldShowFeesUpfront';

import { ApplyFeesToPassTypes } from '../domain/services/ApplyFeesToPassTypes';

import { SearchPassTypesUseCase } from './SearchPassTypesUseCase';

import type { UseCase } from '@discocil/fv-domain-library/application';
import type { SearchPassTypesDto } from '../domain/contracts/SearchPassTypesContract';
import type { PassTypesSearchEither } from '../domain/entities/PassType';

export class SearchPassTypesWithFeesUseCase implements UseCase<SearchPassTypesDto, Promise<PassTypesSearchEither>> {
  constructor(
    private readonly searchPassTypesUseCase: SearchPassTypesUseCase,
    private readonly shouldShowFeesUpfrontService: ShouldShowFeesUpfrontService,
    private readonly applyFeesToPassTypes: ApplyFeesToPassTypes,
  ) {}

  @contextualizeError()
  async execute(request: SearchPassTypesDto): Promise<PassTypesSearchEither> {
    const searchPassTypesResult = await this.searchPassTypesUseCase.execute(request);

    if (searchPassTypesResult.isLeft()) {
      return left(searchPassTypesResult.value);
    }

    const { passTypes, pagination } = searchPassTypesResult.value;

    const organizationId = UniqueEntityID.build(request.organizationId);

    const shouldShowFeesUpfront = await this.shouldShowFeesUpfrontService.execute(organizationId);

    if (!shouldShowFeesUpfront) {
      return right({
        passTypes,
        pagination,
      });
    }

    const applyFeesResult = this.applyFeesToPassTypes.execute({
      customerCustomFees: Collection.new<Fee>(),
      passTypes,
    });

    if (applyFeesResult.isLeft()) {
      return left(applyFeesResult.value);
    }

    const passTypesWithFees = applyFeesResult.value;

    return right({
      passTypes: passTypesWithFees,
      pagination,
    });
  }
}
