import {
  left, right, UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';
import { UserCriteriaMother } from '@/user/domain/filters/UserCriteriaMother';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { UserRepository } from '@/user/domain/contracts/UserRepository';
import type { User } from '@/user/domain/entities/User';
import type { Either, NotFoundError } from '@discocil/fv-domain-library/domain';

export class GetOrganizationSlugsFromUser {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly organizationRepository: OrganizationRepository,
  ) {}

  async execute(slug: string): Promise<Either<NotFoundError | MapperError, Set<string>>> {
    const organizationSlugs = new Set<string>();

    const userCriteria = UserCriteriaMother.userNameMatch(slug);
    const userResultOrError = await this.userRepository.find(userCriteria);

    if (userResultOrError.isLeft()) {
      return left(userResultOrError.value);
    }

    const user = userResultOrError.value;
    const userOrganizationSlugs = await this.getOrganizationSlugsForUser(user);

    if (userOrganizationSlugs.size > 0) {
      organizationSlugs.clear();

      for (const organizationSlug of userOrganizationSlugs) {
        organizationSlugs.add(organizationSlug);
      }
    }

    return right(organizationSlugs);
  }

  private async getOrganizationSlugsForUser(user: User): Promise<Set<string>> {
    const organizationSlugs = new Set<string>();

    const userOrganizations = user.organizations ?? [];
    const userOrganizationIds = userOrganizations.map(id => UniqueEntityID.build(id));

    const organizationCriteria = OrganizationCriteriaMother.idsToMatch(userOrganizationIds);

    const organizationsResult = await this.organizationRepository.search(organizationCriteria);

    if (organizationsResult.isLeft()) {
      return organizationSlugs;
    }

    const organizations = organizationsResult.value;

    for (const _organization of organizations.values()) {
      if (_organization.slug.isDefined()) {
        organizationSlugs.add(_organization.slug.get());
      }
    }

    return organizationSlugs;
  }
}
