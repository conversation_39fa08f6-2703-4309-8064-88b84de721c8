import {
  contextualizeError,
  left,
  Money,
  right,
} from '@discocil/fv-domain-library/domain';

import { IGetOptionsService } from '@/tickets/tickets/domain/contracts/GetOptionsContracts';

import type {
  GetTicketTypePricingEither, GetTicketTypePricingRequest, IGetTicketTypePricingService, TicketTypePricingsMoney,
} from '@/tickets/ticketsTypes/domain/contracts/PricingTicketContract';

export class GetTicketTypePricingService implements IGetTicketTypePricingService {
  constructor(private readonly getOptionsService: IGetOptionsService) {}

  @contextualizeError()
  async execute(dto: GetTicketTypePricingRequest): Promise<GetTicketTypePricingEither> {
    const { ticketType } = dto;

    const optionsResult = await this.getOptionsService.execute(dto);

    if (optionsResult.isLeft()) {
      return left(optionsResult.value);
    }

    const options = optionsResult.value;

    const ids = new Set<string>();
    const prices: TicketTypePricingsMoney = new Map<string, Money>();

    for (const option of options) {
      const priceOrError = Money.build({ amount: option.price, currency: ticketType.currency });

      if (priceOrError.isLeft()) {
        return left(priceOrError.value);
      }

      const price = priceOrError.value;

      ids.add(option.id);
      prices.set(option.id, price);
    }

    const samePrice = new Set([...prices.keys()]).size === 1;

    return right({
      ids,
      prices,
      samePrice,
      options,
    });
  }
}
