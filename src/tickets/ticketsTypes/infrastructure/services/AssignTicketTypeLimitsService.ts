import {
  FvNumber,
  left,
  right,
  contextualizeError,
} from '@discocil/fv-domain-library/domain';

import { TicketLimit } from '@/tickets/ticketLimits/domain/entities/TicketLimit';
import { getAreFewLeftDisponibilityTypeStrategy } from '@/tickets/tickets/domain/services/areFewLeftDisponibilityStrategy/AreFewLeftDisponibilityTypeStrategy';
import { DisponibilityContext } from '@/tickets/tickets/domain/services/areFewLeftDisponibilityStrategy/DisponibilityContext';
import { QuantitySelector } from '@/tickets/ticketsTypes/domain/services/QuantitySelector';

import type {
  AssignTicketTypeLimitsEither,
  AssignTicketTypeLimitsRequest,
  IAssignTicketTypeLimitsService,
} from '@/tickets/ticketsTypes/domain/contracts/AssignTicketTypeLimitsContracts';
import type { IGetTicketTypeLimitsService } from '@/tickets/ticketsTypes/domain/contracts/GetTicketTypeLimitsContracts';
import type { DisponibilityTypeResponse } from '@/tickets/ticketsTypes/domain/contracts/TicketsTypesContracts';

export class AssignTicketTypeLimitsService implements IAssignTicketTypeLimitsService {
  constructor(private readonly getTicketTypeLimitsService: IGetTicketTypeLimitsService) { }

  @contextualizeError()
  async execute(dto: AssignTicketTypeLimitsRequest): Promise<AssignTicketTypeLimitsEither> {
    const {
      ticketType,
      options,
      currentOption,
      paylink,
    } = dto;

    const totals = options.reduce(
      (totals, option) => {
        if (option.config.isEmpty()) {
          return totals;
        }

        if (option.shouldSumTheAvailableToTotal()) {
          totals.availableTotalOptions += option.getAvailableAmount();
        }

        totals.maximumTotalOptions += option.max;
        totals.totalSales += option.getTotalSold();

        return totals;
      },
      {
        availableTotalOptions: 0, maximumTotalOptions: 0, totalSales: 0,
      },
    );

    const { availableTotalOptions, maximumTotalOptions } = totals;

    let availableAllowed = availableTotalOptions;
    let maximumAllowed = maximumTotalOptions;
    let totalSales = totals.totalSales;
    let isSoldOut = availableTotalOptions === 0;

    const maximumsAllowed = new Set<number>();

    maximumsAllowed.add(TicketLimit.maxSimultaneous);
    maximumsAllowed.add(ticketType.maxClients);

    if (ticketType.isLimited()) {
      const limitResult = await this.getTicketTypeLimitsService.execute(dto);

      if (limitResult.isLeft()) {
        return left(limitResult.value);
      }

      const limit = limitResult.value;

      availableAllowed = limit.available;
      maximumAllowed = limit.maximum;
      totalSales = FvNumber.max(totalSales, limit.totalSales).toPrimitive();
      isSoldOut = isSoldOut || !limit.available;

      maximumsAllowed.add(availableTotalOptions);
    }

    maximumsAllowed.add(availableAllowed);

    const maximum = maximumAllowed;

    const disponibilityStrategy = getAreFewLeftDisponibilityTypeStrategy(ticketType);
    const disponibilityContext = new DisponibilityContext(disponibilityStrategy);

    const areFewLeft = FvNumber.build(maximumAllowed)
      .calculatePercentage(disponibilityContext.getPercentage())
      .floor()
      .isLessThanOrEqualTo(totalSales);

    const ticketsLeft = FvNumber.min(availableAllowed, availableTotalOptions).toPrimitive();

    const quantitySelector = QuantitySelector.build().calculate({
      ticketType,
      maximum: FvNumber.min(...maximumsAllowed),
      paylink,
    });

    isSoldOut ||= quantitySelector.isZeroMaximum();

    const currentOptionConfigIsDefined = currentOption.isDefined() && currentOption.get().config.isDefined();

    const currentOptionAvailable = currentOptionConfigIsDefined ? currentOption.get().config.get().availableAmount : ticketsLeft;
    const currentOptionTotalSold = currentOptionConfigIsDefined ? currentOption.get().config.get().totalSold : totalSales;
    const currentOptionMax = currentOption.isDefined() ? currentOption.get().max : maximumTotalOptions;

    const disponibility = disponibilityContext.apply({
      ticketsLeft: currentOptionAvailable,
      maxTickets: FvNumber.min(maximumAllowed, currentOptionMax).toPrimitive(),
      totalSales: currentOptionTotalSold,
    });

    const disponibilityByOptionId = options.reduce((disponibilities, option) => {
      if (option.config.isEmpty()) {
        return disponibilities;
      }

      const { availableAmount, totalSold } = option.config.get();
      const { max, id } = option;

      const disponibility = disponibilityContext.apply({
        ticketsLeft: availableAmount,
        maxTickets: max,
        totalSales: totalSold,
      });

      disponibilities.set(id, disponibility);

      return disponibilities;
    }, new Map<string, DisponibilityTypeResponse>());

    return right({
      areFewLeft,
      isSoldOut,
      maximum,
      disponibility,
      quantitySelector,
      disponibilityByOptionId,
      options,
      currentOption,
    });
  }
}
