import {
  FvDate, IdPrimitive, left, Maybe, right,
  contextualizeError,
} from '@discocil/fv-domain-library/domain';


import { IAssignTicketTypeLimitsService } from '../contracts/AssignTicketTypeLimitsContracts';
import { TicketTypeExtras } from '../contracts/TicketsTypesContracts';
import { TicketTypeOptions } from '../entities/TicketTypeOption';

import type { IGetOptionsWithConfigService } from '../contracts/GetOptionsWithConfigContracts';
import type { GetTicketTypesExtrasServiceEitherResponse, GetTicketTypesExtrasServiceRequest } from '../contracts/GetTicketTypesExtrasContracts';

export class GetTicketTypesExtrasService {
  constructor(
    private readonly getOptionsWithConfigService: IGetOptionsWithConfigService,
    private readonly assignTicketTypeLimitsService: IAssignTicketTypeLimitsService,
  ) {}

  @contextualizeError()
  async execute(request: GetTicketTypesExtrasServiceRequest): Promise<GetTicketTypesExtrasServiceEitherResponse> {
    const {
      ticketTypes, channel, event, paylink,
    } = request;

    const ticketTypesOptionsWithConfigResult = await this.getOptionsWithConfigService.execute({ event, ticketTypes });

    if (ticketTypesOptionsWithConfigResult.isLeft()) {
      return left(ticketTypesOptionsWithConfigResult.value);
    }

    const ticketTypesExtras = new Map<IdPrimitive, TicketTypeExtras>();

    for (const ticketType of ticketTypes.values()) {
      ticketType.setOptions(ticketTypesOptionsWithConfigResult.value.get(ticketType.id) as TicketTypeOptions);

      const currentOption = ticketType.getCurrentOptionOrLast();

      const extrasResult = await this.assignTicketTypeLimitsService.execute({
        ticketType, channel, options: ticketType.options, currentOption, paylink,
      });

      if (extrasResult.isLeft()) {
        return left(extrasResult.value);
      }

      const extras = extrasResult.value;

      const ticketTypeExtras: TicketTypeExtras = {
        to: currentOption.isDefined() ? Maybe.some(currentOption.get().getOptionEndDate(event.startDate)) : Maybe.none<FvDate>(),
        isSoldOut: extras.isSoldOut,
        areFewLeft: extras.areFewLeft,
        maximum: extras.maximum,
        quantitySelector: extras.quantitySelector,
        currentOptionId: currentOption.isDefined() ? Maybe.some(currentOption.get().id) : Maybe.none<string>(),
        disponibility: extras.disponibility,
        disponibilityByOptionId: extras.disponibilityByOptionId,
      };

      ticketTypesExtras.set(ticketType.id, ticketTypeExtras);
    }

    return right(ticketTypesExtras);
  }
}
