import {
  left,
  Money,
  right,
} from '@discocil/fv-domain-library/domain';

import { TicketType } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';

import { TicketTypeOptionPricingService } from './TicketTypeOptionPricingService';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { PricingLibraryError } from '@/cross-cutting/domain/errors/PricingLibraryError';
import type { FeePrimitives, Fees } from '@/fees/domain/entities/Fee';
import type { TicketTypes } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type {
  Either,
  IdPrimitive,
  InvalidArgumentError,
  MoneyError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';

type ApplyFeesToTicketTypesInput = {
  readonly customerCustomFees: Fees;
  readonly ticketTypes: TicketTypes;
};

type ApplyFeesToTicketTypesOutput = Either<
  InvalidArgumentError | NotFoundError | MapperError | UnexpectedError | MoneyError | PricingLibraryError,
  TicketTypes>;

export class ApplyFeesToTicketTypes {
  execute(input: ApplyFeesToTicketTypesInput): ApplyFeesToTicketTypesOutput {
    const { customerCustomFees, ticketTypes } = input;

    const customerCustomFeesPrimitives = customerCustomFees.toArray().map(item => item.toPrimitives());

    const ticketTypesWithFees = new Map<IdPrimitive, TicketType>();

    for (const ticketTypeOriginal of ticketTypes.values()) {
      const ticketTypeCopyOrError = TicketType.build(ticketTypeOriginal.toPrimitives());

      if (ticketTypeCopyOrError.isLeft()) {
        return left(ticketTypeCopyOrError.value);
      }

      const ticketTypeCopy = ticketTypeCopyOrError.value;

      const optionPricesChangedOrError = this.processTicketTypeOptions(ticketTypeCopy, customerCustomFeesPrimitives);

      if (optionPricesChangedOrError.isLeft()) {
        return left(optionPricesChangedOrError.value);
      }

      ticketTypesWithFees.set(ticketTypeOriginal.id, ticketTypeCopy);
    }

    return right(ticketTypesWithFees);
  }

  private processTicketTypeOptions(ticketType: TicketType, customerCustomFees: FeePrimitives[]): Either<PricingLibraryError | MoneyError, void> {
    for (const option of ticketType.getOptions()) {
      const ticketTypeOptionPricingOrError = TicketTypeOptionPricingService.execute({
        price: option.price,
        currency: ticketType.currency,
        customerCustomFees,
        adminFeeConfig: option.getAdminFeeConfig(),
      });

      if (ticketTypeOptionPricingOrError.isLeft()) {
        return left(ticketTypeOptionPricingOrError.value);
      }

      const ticketTypeOptionPricingWithFees = ticketTypeOptionPricingOrError.value;

      const optionPriceWithFeesMoneyResult = Money.build({
        amount: ticketTypeOptionPricingWithFees.totalAmount,
        currency: ticketType.currency,
      });

      if (optionPriceWithFeesMoneyResult.isLeft()) {
        return left(optionPriceWithFeesMoneyResult.value);
      }

      option.setPrice(optionPriceWithFeesMoneyResult.value);
    }

    return right(undefined);
  }
}
