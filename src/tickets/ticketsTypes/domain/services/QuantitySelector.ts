import { FvNumber } from '@discocil/fv-domain-library/domain';

import type { Paylink } from '@/paylinks/domain/entities/Paylink';
import type { Maybe } from '@discocil/fv-domain-library/domain';
import type { TicketType } from '../entities/TicketTypeEntity';

type Request = {
  readonly minimum: number;
  readonly maximum: number;
  readonly quantity: number;
};

type CalculateRequest = {
  readonly ticketType: TicketType;
  readonly maximum: FvNumber;
  readonly paylink: Maybe<Paylink>;
};

export class QuantitySelector {
  private _minimum: FvNumber;
  private _maximum: FvNumber;
  private _quantity: FvNumber;

  private constructor(request: Request) {
    this._minimum = FvNumber.build(request.minimum);
    this._maximum = FvNumber.build(request.maximum);
    this._quantity = FvNumber.build(request.quantity);
  }

  static build(): QuantitySelector {
    return new QuantitySelector({
      minimum: 0,
      maximum: 0,
      quantity: 0,
    });
  }

  get minimum(): number {
    return this._minimum.toPrimitive();
  }

  get maximum(): number {
    return this._maximum.toPrimitive();
  }

  get quantity(): number {
    return this._quantity.toPrimitive();
  }

  isZeroMaximum(): boolean {
    return this._maximum.isZero();
  }

  calculate(request: CalculateRequest): QuantitySelector {
    if (request.paylink.isEmpty()) {
      return this.calculetWithoutPaylink(
        request.ticketType,
        request.maximum,
      ).normalize();
    }

    return this.calculetWithPaylink(
      request.ticketType,
      request.maximum,
      request.paylink.get(),
    ).normalize();
  }

  private normalize(): QuantitySelector {
    if (this.isZeroMaximum()) {
      this._minimum = FvNumber.createZero();
    }

    return this;
  }

  private calculetWithPaylink(ticketType: TicketType, maximum: FvNumber, paylink: Paylink): QuantitySelector {
    const quantitySelector = QuantitySelector.build();
    const nRequested = FvNumber.build(paylink.nRequested);

    quantitySelector.setQuantity(maximum);
    quantitySelector.setMaximum(maximum);
    quantitySelector.setMinimum(ticketType.minClients);

    if (nRequested.isLessThanOrEqualTo(maximum)) {
      quantitySelector.setQuantity(paylink.nRequested);
      quantitySelector.setMaximum(paylink.nRequested);
    }

    if (ticketType.isEnablePacks()) {
      const maximum = this.calculateMaxTickets(FvNumber.build(paylink.nRequested), ticketType.ticketsPerPack).toPrimitive();

      quantitySelector.setQuantity(maximum);
      quantitySelector.setMaximum(maximum);
      quantitySelector.setMinimum(ticketType.ticketsPerPack);
    }

    return quantitySelector;
  }

  private calculetWithoutPaylink(ticketType: TicketType, maximum: FvNumber): QuantitySelector {
    const quantitySelector = QuantitySelector.build();

    quantitySelector.setMaximum(maximum);
    quantitySelector.setMinimum(ticketType.minClients);

    if (!ticketType.isEnablePacks()) {
      return quantitySelector;
    }

    let maxTickets = FvNumber.createZero().toPrimitive();

    if (maximum.isGreaterThanOrEqualTo(ticketType.ticketsPerPack)) {
      maxTickets = this.calculateMaxTickets(maximum, ticketType.ticketsPerPack).toPrimitive();
    }

    quantitySelector.setMaximum(maxTickets);

    return quantitySelector;
  }

  private calculateMaxTickets(maximum: FvNumber, ticketsPerPack: number): FvNumber {
    return maximum
      .divide(ticketsPerPack)
      .floor()
      .multiply(ticketsPerPack);
  }

  private setMinimum(value: FvNumber | number): this {
    this._minimum = FvNumber.is(value) ? FvNumber.build(value) : value;

    return this;
  }

  private setMaximum(value: FvNumber | number): this {
    this._maximum = FvNumber.is(value) ? FvNumber.build(value) : value;

    return this;
  }

  private setQuantity(value: FvNumber | number): this {
    this._quantity = FvNumber.is(value) ? FvNumber.build(value) : value;

    return this;
  }
}

