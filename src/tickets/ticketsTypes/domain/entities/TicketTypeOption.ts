import {
  EGGDD,
  FvDate,
  FvNumber,
  left,
  Maybe,
  Money,
  right,
} from '@discocil/fv-domain-library/domain';
import { EValueType, type AdminFeeConfigPrimitives } from '@discocil/fv-pricing-library/ticketing';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  Collection,
  DatePrimitive,
  ECurrency,
  Either,
  InvalidArgumentError,
  MoneyError,
  NotFoundError,
  Primitives,
  Properties,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';

export type TicketTypeOptions = Collection<TicketTypeOption>;

export type TicketTypeOptionEither = Either<MapperError | NotFoundError | InvalidArgumentError | UnexpectedError | MoneyError, TicketTypeOption>;
export type TicketTypeOptionsEither = Either<MapperError | NotFoundError | MoneyError, TicketTypeOptions>;

export type TicketTypeOptionPrimitives = Properties<Primitives<TicketTypeOption>>;

export type TicketTypeOptionKeys = keyof TicketTypeOption;

export type OptionConfig = {
  availableAmount: number;
  isFuture: boolean;
  isCurrent: boolean;
  totalSold: number;
};

export class TicketTypeOption {
  private constructor(
    readonly id: string,
    private _price: Money,
    readonly name: Maybe<string>,
    readonly ggddType: EGGDD,
    private readonly _ggddAmount: FvNumber,
    private readonly _age: FvNumber,
    readonly content: Maybe<string>,
    readonly additionalInfo: Maybe<string>,
    private readonly _to: FvDate,
    private _max: FvNumber,
    readonly image: Maybe<string>,
    private _config: Maybe<OptionConfig>,
  ) { }

  static build(primitives: TicketTypeOptionPrimitives, currency: ECurrency): TicketTypeOptionEither {
    const priceResult = Money.build({ amount: primitives.price, currency });

    if (priceResult.isLeft()) {
      return left(priceResult.value);
    }

    const price = priceResult.value;

    const ggddAmountNumber = FvNumber.build(primitives.ggddAmount);
    const ageNumber = FvNumber.build(primitives.age);
    const maxNumber = FvNumber.build(primitives.max);
    const toDate = FvDate.create(primitives.to);

    const name = primitives.name.map(name => name);
    const content = primitives.content.map(content => content);
    const additionalInfo = primitives.additionalInfo.map(additionalInfo => additionalInfo);
    const image = primitives.image.map(image => image);
    const config = primitives.config.map(config => config);

    const entity = new TicketTypeOption(
      primitives.id,
      price,
      name,
      primitives.ggddType,
      ggddAmountNumber,
      ageNumber,
      content,
      additionalInfo,
      toDate,
      maxNumber,
      image,
      config,
    );

    return right(entity);
  }

  get price(): number {
    return this._price.toDecimal();
  }

  get ggddAmount(): number {
    return this._ggddAmount.toPrimitive();
  }

  get age(): number {
    return this._age.toPrimitive();
  }

  get max(): number {
    return this._max.toPrimitive();
  }

  get to(): DatePrimitive {
    return this._to.toPrimitive();
  }

  get config(): Maybe<OptionConfig> {
    return this._config;
  }

  getToDateInSeconds(): number {
    return this._to.toSeconds();
  }

  setPrice(price: Money): this {
    this._price = price;

    return this;
  }

  setMax(max: number): this {
    this._max = FvNumber.build(max);

    return this;
  }

  setConfig(config: OptionConfig): this {
    this._config = Maybe.some(config);

    return this;
  }

  shouldSetTheDefaultMax(): boolean {
    return this._max.isEqualTo(0);
  }

  calculateAvailableAmount(totalSold: number): number {
    if (this.shouldSetTheDefaultMax()) {
      this.setMax(9_999_999);
    }

    const available = this._max.subtract(totalSold);

    return available.isPositive() ? available.toPrimitive() : 0;
  }

  shouldSumTheAvailableToTotal(): boolean {
    return this.config.isDefined() && (this.config.get().isCurrent || this.config.get().isFuture);
  }

  getAvailableAmount(): number {
    return this.config.isDefined() ? this.config.get().availableAmount : 0;
  }

  getTotalSold(): number {
    return this.config.isDefined() ? this.config.get().totalSold : 0;
  }

  isAvailable(): boolean {
    return this.config.isDefined() && this.config.get().availableAmount > 0 && this.config.get().isFuture;
  }

  substractOneToAvailableAmount(): this {
    if (this.config.isDefined()) {
      this._config = Maybe.some({
        ...this.config.get(),
        availableAmount: this.config.get().availableAmount - 1,
      });
    }

    return this;
  }

  calculateDiscountApplied(discountAmount: Money): Money {
    return discountAmount.lt(this._price) ? discountAmount : this._price;
  }

  calculatePriceWithDiscountApplied(discountAmount: Money): Money {
    const price = this._price.subtract(discountAmount);

    return price.isNegative() ? Money.buildZero(this._price.value.currency) : price;
  }

  getSortValueWith(option: TicketTypeOption): number {
    if (this._price.eq(option._price)) {
      return this._to.subtractEpoch(option._to).toMilliseconds();
    }

    return this._price.subtract(option._price).toDecimal();
  }

  isFuture(): boolean {
    return this.config.isDefined() && (this.config.get().isFuture && !this.config.get().isCurrent);
  }

  getOptionEndDate(eventStartDate: DatePrimitive): FvDate {
    return this._to.addEpoch(eventStartDate);
  }

  getToDateInISOString(): string {
    return this._to.toISODate();
  }

  getAdminFeeConfig(): AdminFeeConfigPrimitives {
    return {
      type: this.ggddType === EGGDD.FIX ? EValueType.FIXED : EValueType.PERCENTAGE,
      value: this.ggddAmount,
    };
  }
}
