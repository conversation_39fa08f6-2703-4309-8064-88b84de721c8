import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { ErrorMethodRequest } from '@/cross-cutting/domain/errors/ErrorRequest';
import type { UnknownObject } from '@discocil/fv-domain-library/domain';
import type { TicketType } from '../entities/TicketTypeEntity';

type Target = {
  readonly ticketType: TicketType;
  readonly amount: number;
};

export class InvalidPackSizeError extends FvError {
  static readonly defaultCause = EErrorKeys.INVALID_TICKET_TYPE_PACK_SIZE;

  static build(request: ErrorMethodRequest<UnknownObject, Target>): InvalidPackSizeError {
    const {
      context, error, data, target,
    } = request;

    let exceptionMessage = `Invalid Pack Size`;

    if (target) {
      const { ticketType, amount } = target;

      exceptionMessage += `Invalid Pack Size (${amount}) for ticket type "${ticketType.name}". Pack valid (${ticketType.ticketsPerPack})`;
    }

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
