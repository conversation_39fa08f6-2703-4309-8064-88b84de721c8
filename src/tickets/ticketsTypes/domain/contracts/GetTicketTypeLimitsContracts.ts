import type { InvalidFieldError } from '@/cross-cutting/domain/errors/InvalidFieldError';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { MicrositeChannel } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { TicketType } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type {
  Either,
  InvalidArgumentError,
  MoneyError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';

export type TicketTypeLimitsRequest = {
  readonly ticketType: TicketType;
  readonly channel: MicrositeChannel;
};

export type TicketTypeLimitsResponse = {
  readonly maximum: number;
  readonly totalSales: number;
  readonly available: number;
};

export type TicketTypeLimitsEither = Either<
  MapperError
  | NotFoundError
  | InvalidArgumentError
  | InvalidFieldError
  | UnexpectedError
  | MoneyError,
  TicketTypeLimitsResponse
>;

export interface IGetTicketTypeLimitsService {
  execute: (dto: TicketTypeLimitsRequest) => Promise<TicketTypeLimitsEither>;
}
