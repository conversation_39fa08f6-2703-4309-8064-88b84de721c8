import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { Paylink } from '@/paylinks/domain/entities/Paylink';
import type { EventEntity } from '@/events/events/domain/entities/EventEntity';
import type { MicrositeChannel } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { TicketTypesExtras } from '@/tickets/ticketsTypes/domain/contracts/TicketsTypesContracts';
import type { TicketTypes } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type {
  Either,
  InvalidArgumentError,
  Maybe,
  MoneyError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import type { InvalidFieldError } from '@/cross-cutting/domain/errors/InvalidFieldError';

export type GetTicketTypesExtrasServiceRequest = {
  readonly channel: MicrositeChannel;
  readonly ticketTypes: TicketTypes;
  readonly event: EventEntity;
  readonly paylink: Maybe<Paylink>;
};

export type GetTicketTypesExtrasServiceEitherResponse = Either<
  UnexpectedError | NotFoundError | MapperError | InvalidArgumentError | InvalidFieldError | MoneyError,
  TicketTypesExtras
>;

export interface IGetTicketTypesExtrasService {
  execute: (dto: GetTicketTypesExtrasServiceRequest) => Promise<GetTicketTypesExtrasServiceEitherResponse>;
}
