import type { TicketTypeOption } from '@/tickets/ticketsTypes/domain/entities/TicketTypeOption';
import type { InvalidFieldError } from '@/cross-cutting/domain/errors/InvalidFieldError';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { MicrositeChannel } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { DisponibilityTypeResponse } from '@/tickets/ticketsTypes/domain/contracts/TicketsTypesContracts';
import type { Paylink } from '@/paylinks/domain/entities/Paylink';
import type { TicketType } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type { QuantitySelector } from '@/tickets/ticketsTypes/domain/services/QuantitySelector';
import type {
  Either,
  InvalidArgumentError,
  Maybe,
  MoneyError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';

export type AssignTicketTypeLimitsRequest = {
  readonly ticketType: TicketType;
  readonly channel: MicrositeChannel;
  readonly options: TicketTypeOption[];
  readonly currentOption: Maybe<TicketTypeOption>;
  readonly paylink: Maybe<Paylink>;
};

export type AssignTicketTypeLimitsResponse = {
  readonly areFewLeft: boolean;
  readonly isSoldOut: boolean;
  readonly maximum: number;
  readonly quantitySelector: QuantitySelector;
  readonly disponibility: DisponibilityTypeResponse;
  readonly disponibilityByOptionId: Map<string, DisponibilityTypeResponse>;
};

export type AssignTicketTypeLimitsEither = Either<
  NotFoundError
  | MapperError
  | InvalidArgumentError
  | InvalidFieldError
  | UnexpectedError
  | MoneyError,
  AssignTicketTypeLimitsResponse
>;

export interface IAssignTicketTypeLimitsService {
  execute: (dto: AssignTicketTypeLimitsRequest) => Promise<AssignTicketTypeLimitsEither>;
}
