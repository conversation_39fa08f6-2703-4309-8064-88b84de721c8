import type { Either, UnexpectedError } from '@discocil/fv-domain-library/domain';
import type { EventEntity } from '@/events/events/domain/entities/EventEntity';
import type { TicketTypes } from '../entities/TicketTypeEntity';
import type { TicketTypeOptions } from '../entities/TicketTypeOption';

export type GetOptionsWithConfigRequest = {
  readonly ticketTypes: TicketTypes;
  readonly event: EventEntity;
};

export type GetOptionsWithConfigEither = Either<UnexpectedError, Map<string, TicketTypeOptions>>;

export interface IGetOptionsWithConfigService {
  execute: (dto: GetOptionsWithConfigRequest) => Promise<GetOptionsWithConfigEither>;
}
