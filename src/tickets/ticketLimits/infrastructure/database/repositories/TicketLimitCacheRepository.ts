import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { TicketLimitJsonMapper } from '@/tickets/ticketLimits/domain/mappers/TicketLimitJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { TicketLimitRepository } from '@/tickets/ticketLimits/domain/contracts/TicketLimitRepository';
import type {
  TicketLimit,
  TicketLimitEither,
  TicketLimits,
  TicketLimitsEither,
} from '@/tickets/ticketLimits/domain/entities/TicketLimit';
import type { TicketLimitJsonPrimitives } from '@/tickets/ticketLimits/domain/mappers/TicketLimitJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class TicketLimitCacheRepository extends CacheRepository implements TicketLimitRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: TicketLimitRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<TicketLimitEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: TicketLimitJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = TicketLimitJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        TicketLimitJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async save(ticketLimit: TicketLimit): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.save(ticketLimit);
  }

  async saveMany(ticketLimits: TicketLimits): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.saveMany(ticketLimits);
  }

  async search(criteria: Criteria): Promise<TicketLimitsEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: TicketLimitJsonPrimitives[] = JSON.parse(cacheHit);
      const ticketLimits: TicketLimits = new Map<IdPrimitive, TicketLimit>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = TicketLimitJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        ticketLimits.set(_primitive.id, entityOrError.value);
      }

      return right(ticketLimits);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const ticketLimits = repositoryResult.value;
      const ticketLimitsLength = ticketLimits.size;
      let ticketLimitsCacheIndex = 0;
      const jsonTicketLimits = new Array<TicketLimitJsonPrimitives>(ticketLimitsLength);

      if (ticketLimitsLength > 0) {
        for (const _event of ticketLimits.values()) {
          jsonTicketLimits[ticketLimitsCacheIndex] = TicketLimitJsonMapper.toJson(_event);

          ticketLimitsCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonTicketLimits, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}
