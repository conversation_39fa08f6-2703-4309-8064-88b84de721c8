import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { TicketLimit } from '@/tickets/ticketLimits/domain/entities/TicketLimit';

import { TicketLimitMapper } from '../mappers/TicketLimitMapper';
import { TicketLimitSchemaMapper } from '../mappers/TicketLimitSchemaMapper';
import { TicketLimitSchema } from '../schemas/TicketLimitSchema';

import type { TicketLimitRepository } from '@/tickets/ticketLimits/domain/contracts/TicketLimitRepository';
import type {
  TicketLimitEither,
  TicketLimitKeys,
  TicketLimits,
  TicketLimitsEither,
} from '@/tickets/ticketLimits/domain/entities/TicketLimit';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { TicketLimitSchemaType } from '../schemas/TicketLimitSchemaType';

type PropertiesMapper = Partial<Record<TicketLimitKeys, keyof TicketLimitSchemaType>>;

export class TicketLimitMongoRepository extends MongoRepository implements TicketLimitRepository {
  protected getSchema(): Schema {
    return new Schema(TicketLimitSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'entradas_limites';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      userId: 'usuario_id',
      eventId: 'evento_id',
      maximum: 'maximo',
      rateSlug: 'tarifa_slug',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<TicketLimitEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<TicketLimitSchemaType>();

    return queryResponse
      ? TicketLimitMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: TicketLimit.name }));
  }

  async save(ticketLimit: TicketLimit): Promise<void> {
    const toSave = TicketLimitSchemaMapper.execute(ticketLimit);

    const filter = { _id: ticketLimit.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(ticketLimits: TicketLimits): Promise<void> {
    const connection = await this.getConnection();
    const models: TicketLimitSchemaType[] = [];

    ticketLimits.forEach((ticketLimit: TicketLimit) => models.push(TicketLimitSchemaMapper.execute(ticketLimit)));

    await connection.insertMany(models);
  }

  async search(criteria: Criteria): Promise<TicketLimitsEither> {
    const response: TicketLimits = new Map<IdPrimitive, TicketLimit>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<TicketLimitSchemaType[]>();

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const ticketLimitOrError = TicketLimitMapper.execute(model);

      if (ticketLimitOrError.isLeft()) {
        return left(ticketLimitOrError.value);
      }

      const ticketLimit = ticketLimitOrError.value;

      response.set(ticketLimit.id, ticketLimit);
    }

    return right(response);
  }
}
