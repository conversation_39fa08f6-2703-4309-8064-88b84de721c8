import { Maybe } from '@discocil/fv-domain-library/domain';

import { TicketLimit } from '../entities/TicketLimit';

import type { IdPrimitive, Nullable } from '@discocil/fv-domain-library/domain';
import type { TicketLimitEither, TicketLimitPrimitives } from '../entities/TicketLimit';

export type TicketLimitJsonPrimitives = Omit<TicketLimitPrimitives,
  'organizationId'
  | 'userId'
> & Nullable<{
  readonly organizationId: IdPrimitive;
  readonly userId: IdPrimitive;
}>;

export class TicketLimitJsonMapper {
  static toEntity(primitives: TicketLimitJsonPrimitives): TicketLimitEither {
    return TicketLimit.build({
      ...primitives,
      organizationId: Maybe.fromValue(primitives.organizationId),
      userId: Maybe.fromValue(primitives.userId),
    });
  }

  static toJson(ticketLimit: TicketLimit): TicketLimitJsonPrimitives {
    return {
      id: ticketLimit.id,
      organizationId: ticketLimit.organizationId.fold(() => null, item => item),
      eventId: ticketLimit.eventId,
      rateSlug: ticketLimit.rateSlug,
      userId: ticketLimit.userId.fold(() => null, item => item),
      maximum: ticketLimit.maximum,
      createdAt: ticketLimit.createdAt,
      createdBy: ticketLimit.createdBy,
      updatedAt: ticketLimit.updatedAt,
      updatedBy: ticketLimit.updatedBy,
      removedAt: ticketLimit.removedAt,
      removedBy: ticketLimit.removedBy,
    };
  }
}
