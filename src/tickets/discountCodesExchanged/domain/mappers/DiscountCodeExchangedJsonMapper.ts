import { Maybe } from '@discocil/fv-domain-library/domain';

import { DiscountCodeExchanged } from '../entities/DiscountCodeExchanged';

import type { IdPrimitive, Nullable } from '@discocil/fv-domain-library/domain';
import type { DiscountCodeExchangedEither, DiscountCodeExchangedPrimitives } from '../entities/DiscountCodeExchanged';

export type DiscountCodeExchangedJsonPrimitives = Omit<DiscountCodeExchangedPrimitives,
  'paymentId'
> & Nullable<{
  readonly paymentId: IdPrimitive;
}>;

export class DiscountCodeExchangedJsonMapper {
  static toEntity(primitives: DiscountCodeExchangedJsonPrimitives): DiscountCodeExchangedEither {
    return DiscountCodeExchanged.build({
      ...primitives,
      paymentId: Maybe.fromValue(primitives.paymentId),
      discountCode: primitives.discountCode,
    });
  }

  static toJson(discountCodeExchanged: DiscountCodeExchanged): DiscountCodeExchangedJsonPrimitives {
    return {
      id: discountCodeExchanged.id,
      organizationId: discountCodeExchanged.organizationId,
      eventId: discountCodeExchanged.eventId,
      rateId: discountCodeExchanged.rateId,
      type: discountCodeExchanged.type,
      paymentId: discountCodeExchanged.paymentId.fold(() => null, item => item),
      isPaymentCompleted: discountCodeExchanged.isPaymentCompleted,
      discountCode: discountCodeExchanged.discountCode,
      discountId: discountCodeExchanged.discountId,
      originalDiscount: discountCodeExchanged.originalDiscount,
      resourceIds: discountCodeExchanged.resourceIds,
      resourceNumber: discountCodeExchanged.resourceNumber,
      originalAmount: discountCodeExchanged.originalAmount,
      paidAmount: discountCodeExchanged.paidAmount,
      appliedDiscount: discountCodeExchanged.appliedDiscount,
      currency: discountCodeExchanged.currency,
      createdAt: discountCodeExchanged.createdAt,
      createdBy: discountCodeExchanged.createdBy,
      updatedAt: discountCodeExchanged.updatedAt,
      updatedBy: discountCodeExchanged.updatedBy,
      removedAt: discountCodeExchanged.removedAt,
      removedBy: discountCodeExchanged.removedBy,
    };
  }
}
