import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { DiscountCodeExchanged } from '@/tickets/discountCodesExchanged/domain/entities/DiscountCodeExchanged';
import { DiscountCodeExchangedJsonMapper } from '@/tickets/discountCodesExchanged/domain/mappers/DiscountCodeExchangedJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { DiscountCodeExchangedRepository } from '@/tickets/discountCodesExchanged/domain/contracts/DiscountCodeExchangedRepository';
import type {
  DiscountCodeExchangedEither,
  DiscountCodesExchanged,
  TotalExchangedEither,
} from '@/tickets/discountCodesExchanged/domain/entities/DiscountCodeExchanged';
import type { DiscountCodeExchangedJsonPrimitives } from '@/tickets/discountCodesExchanged/domain/mappers/DiscountCodeExchangedJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export class DiscountCodeExchangedCacheRepository extends CacheRepository implements DiscountCodeExchangedRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: DiscountCodeExchangedRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<DiscountCodeExchangedEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: DiscountCodeExchangedJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = DiscountCodeExchangedJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        DiscountCodeExchangedJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async getTotalExchangedForEventCode(criteria: Criteria): Promise<TotalExchangedEither> {
    if (!this.otherRepository) {
      return right(DiscountCodeExchanged.TOTAL_EXCHANGED_DEFAULT);
    }

    return this.otherRepository.getTotalExchangedForEventCode(criteria);
  }

  async save(discountCodeExchanged: DiscountCodeExchanged): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.save(discountCodeExchanged);
  }

  async saveMany(discountCodesExchanged: DiscountCodesExchanged): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.saveMany(discountCodesExchanged);
  }
}
