import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { DiscountCodeExchanged } from '@/tickets/discountCodesExchanged/domain/entities/DiscountCodeExchanged';
import { DiscountCodeExchangedMapper } from '@/tickets/discountCodesExchanged/infrastructure/database/mappers/DiscountCodeExchangedMapper';
import { DiscountCodeExchangedchemaMapper } from '@/tickets/discountCodesExchanged/infrastructure/database/mappers/DiscountCodeExchangedSchemaMapper';
import { DiscountCodeExchangedchema } from '@/tickets/discountCodesExchanged/infrastructure/database/schemas/DiscountCodeExchangedSchema';

import type { DiscountCodeExchangedRepository } from '@/tickets/discountCodesExchanged/domain/contracts/DiscountCodeExchangedRepository';
import type {
  DiscountCodeExchangedEither,
  DiscountCodeExchangedKeys,
  DiscountCodesExchanged,
  TotalExchanged,
  TotalExchangedEither,
} from '@/tickets/discountCodesExchanged/domain/entities/DiscountCodeExchanged';
import type { DiscountCodeExchangedSchemaType } from '@/tickets/discountCodesExchanged/infrastructure/database/schemas/DiscountCodeExchangedSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

type PropertiesMapper = Partial<Record<DiscountCodeExchangedKeys, keyof DiscountCodeExchangedSchemaType>>;

export class DiscountCodeExchangedMongoRepository extends MongoRepository implements DiscountCodeExchangedRepository {
  protected getSchema(): Schema {
    return new Schema(DiscountCodeExchangedchema, { collection: this.getModel() });
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'codigos_descuento_canjeado';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      eventId: 'evento_id',
      discountCode: 'descuento_codigo',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<DiscountCodeExchangedEither> {
    criteria.setPage(1).setPerPage(1);

    const queryResponse = (await this.customQueryFinder<DiscountCodeExchangedSchemaType[]>(criteria)).shift();

    return queryResponse
      ? DiscountCodeExchangedMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: DiscountCodeExchanged.name }));
  }

  async getTotalExchangedForEventCode(criteria: Criteria): Promise<TotalExchangedEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const group = {
      _id: null,
      totalExchanges: { $sum: 1 },
      resourcesTotalExchanged: { $sum: '$n_recursos' },
      discountedAmount: { $sum: '$descuento_aplicado' },
    };

    const queryResponse = await connection.aggregate<TotalExchanged>([{ $match: filterQuery.filter }, { $group: group }]);

    if (queryResponse.length === 0) {
      return right(DiscountCodeExchanged.TOTAL_EXCHANGED_DEFAULT);
    }

    return right(queryResponse.shift() as TotalExchanged);
  }

  async save(discountCodeExchanged: DiscountCodeExchanged): Promise<void> {
    const toSave = DiscountCodeExchangedchemaMapper.execute(discountCodeExchanged);

    const filter = { _id: discountCodeExchanged.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(discountCodesExchanged: DiscountCodesExchanged): Promise<void> {
    const connection = await this.getConnection();
    const models = discountCodesExchanged.map((discountCodeExchanged: DiscountCodeExchanged) =>
      DiscountCodeExchangedchemaMapper.execute(discountCodeExchanged));

    await connection.insertMany(models);
  }

  private async customQueryFinder<T>(criteria: Criteria): Promise<T> {
    const connection = await this.getConnection();
    const pipeline = this.criteriaConverter.toPipeline(criteria);

    return connection.aggregate([
      ...pipeline,
      {
        $lookup: {
          from: 'eventos',
          let: { eventId: '$evento_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$eventId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'event',
        },
      },
      {
        $lookup: {
          from: 'negocios',
          let: { organizationId: '$negocio_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$organizationId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'organization',
        },
      },
      {
        $project: {
          main: '$$ROOT',
          eventObj: { $arrayElemAt: ['$event', 0] },
          organizationObj: { $arrayElemAt: ['$organization', 0] },
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              '$main',
              {
                event: { currency: { $ifNull: ['$eventObj.currency', null] } },
                organization: { currency: { $ifNull: ['$organizationObj.currency', null] } },
              },
            ],
          },
        },
      },
    ]) as unknown as T;
  }
}
