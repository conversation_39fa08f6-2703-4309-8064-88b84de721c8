import { left, NotFoundError } from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { DiscountCode } from '@/tickets/discountCodes/domain/entities/DiscountCode';
import { DiscountCodeMapper } from '@/tickets/discountCodes/infrastructure//database/mappers/DiscountCodeMapper';
import { DiscountCodeSchemaMapper } from '@/tickets/discountCodes/infrastructure//database/mappers/DiscountCodeSchemaMapper';
import { discountcodeSchema } from '@/tickets/discountCodes/infrastructure//database/schemas/DiscountCodeSchema';

import type { DiscountCodeRepository } from '@/tickets/discountCodes/domain/contracts/DiscountCodeRepository';
import type {
  DiscountCodeEither,
  DiscountCodeKeys,
  DiscountCodes,
} from '@/tickets/discountCodes/domain/entities/DiscountCode';
import type { DiscountCodeSchemaType } from '@/tickets/discountCodes/infrastructure//database/schemas/DiscountCodeSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

type PropertiesMapper = Partial<Record<DiscountCodeKeys, keyof DiscountCodeSchemaType>>;

export class DiscountCodeMongoRepository extends MongoRepository implements DiscountCodeRepository {
  protected getSchema(): Schema {
    return new Schema(discountcodeSchema, { collection: this.getModel() });
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'codigos_descuento';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      eventId: 'evento_id',
      type: 'tipo',
      code: 'codigo',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<DiscountCodeEither> {
    criteria.setPage(1).setPerPage(1);

    const queryResponse = (await this.customQueryFinder<DiscountCodeSchemaType[]>(criteria)).shift();

    return queryResponse
      ? DiscountCodeMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: DiscountCode.name }));
  }

  async save(discountcode: DiscountCode): Promise<void> {
    const toSave = DiscountCodeSchemaMapper.execute(discountcode);

    const filter = { _id: discountcode.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(discountcodes: DiscountCodes): Promise<void> {
    const connection = await this.getConnection();
    const models = discountcodes.map((discountcode: DiscountCode) => DiscountCodeSchemaMapper.execute(discountcode));

    await connection.insertMany(models);
  }

  private async customQueryFinder<T>(criteria: Criteria): Promise<T> {
    const connection = await this.getConnection();
    const pipeline = this.criteriaConverter.toPipeline(criteria);

    return connection.aggregate([
      ...pipeline,
      {
        $lookup: {
          from: 'eventos',
          let: { eventId: '$evento_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$eventId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'event',
        },
      },
      {
        $lookup: {
          from: 'negocios',
          let: { organizationId: '$negocio_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$organizationId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'organization',
        },
      },
      {
        $project: {
          main: '$$ROOT',
          eventObj: { $arrayElemAt: ['$event', 0] },
          organizationObj: { $arrayElemAt: ['$organization', 0] },
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              '$main',
              {
                event: { currency: { $ifNull: ['$eventObj.currency', null] } },
                organization: { currency: { $ifNull: ['$organizationObj.currency', null] } },
              },
            ],
          },
        },
      },
    ]) as unknown as T;
  }
}
