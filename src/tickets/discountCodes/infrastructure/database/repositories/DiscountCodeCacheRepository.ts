import { left, NotFoundError } from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { DiscountCodeJsonMapper } from '@/tickets/discountCodes/domain/mappers/DiscountCodeJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { DiscountCodeRepository } from '@/tickets/discountCodes/domain/contracts/DiscountCodeRepository';
import type {
  DiscountCode,
  DiscountCodeEither,
  DiscountCodePrimitives,
  DiscountCodes,
} from '@/tickets/discountCodes/domain/entities/DiscountCode';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export class DiscountCodeCacheRepository extends CacheRepository implements DiscountCodeRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: DiscountCodeRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<DiscountCodeEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: DiscountCodePrimitives = JSON.parse(cacheHit);
      const entityOrError = DiscountCodeJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        DiscountCodeJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async save(discountcode: DiscountCode): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.save(discountcode);
  }

  async saveMany(discountcodes: DiscountCodes): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.saveMany(discountcodes);
  }
}
