import { DiscountCode } from '../entities/DiscountCode';

import type { DiscountCodeEither, DiscountCodePrimitives } from '../entities/DiscountCode';

export class DiscountCodeJsonMapper {
  static toEntity(primitives: DiscountCodePrimitives): DiscountCodeEither {
    return DiscountCode.build(primitives);
  }

  static toJson(discountCode: DiscountCode): DiscountCodePrimitives {
    return {
      id: discountCode.id,
      organizationId: discountCode.organizationId,
      eventId: discountCode.eventId,
      type: discountCode.type,
      parent: discountCode.parent,
      code: discountCode.code,
      description: discountCode.description,
      isActive: discountCode.isActive,
      percentage: discountCode.percentage,
      fixed: discountCode.fixed,
      from: discountCode.from,
      minimumAmount: discountCode.minimumAmount,
      minimumQuantity: discountCode.minimumQuantity,
      maximumQuantity: discountCode.maximumQuantity,
      redeemableTimes: discountCode.redeemableTimes,
      maximumSales: discountCode.maximumSales,
      currency: discountCode.currency,
      createdAt: discountCode.createdAt,
      createdBy: discountCode.createdBy,
      updatedAt: discountCode.updatedAt,
      updatedBy: discountCode.updatedBy,
      removedAt: discountCode.removedAt,
      removedBy: discountCode.removedBy,
    };
  }
}
