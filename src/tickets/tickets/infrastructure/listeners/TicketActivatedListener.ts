import {
  DomainEvent,
  DomainEventClass,
  DomainEventSubscriber,
  Maybe,
} from '@discocil/fv-domain-library/domain';
import { singleton } from 'tsyringe';

import { CreateSubscriptionsUseCase } from '@/tickets/tickets/application/use-cases/CreateSubscriptionsUseCase';
import { CreateTicketSaleUseCase } from '@/tickets/tickets/application/use-cases/CreateTicketSaleUseCase';
import { TicketActivatedDomainEvent } from '@/tickets/tickets/domain/events/TicketActivatedDomainEvent';
import { TicketJsonMapper, TicketJsonPrimitives } from '@/tickets/tickets/domain/mappers/TicketJsonMapper';

import type { ITicketEmitter } from '@/tickets/tickets/domain/contracts/TicketEmitter';
import type { Logger } from '@discocil/fv-domain-library/domain';

type DomainEventAttributes = TicketJsonPrimitives & { ip: string; };

@singleton()
export class TicketActivatedListener implements DomainEventSubscriber<TicketActivatedDomainEvent> {
  constructor(
    private readonly ticketEmitter: ITicketEmitter,
    private readonly createTicketSaleUseCase: CreateTicketSaleUseCase,
    private readonly createSubscriptionsUseCase: CreateSubscriptionsUseCase,
    private readonly logger: Logger,
  ) {}

  subscribedTo(): DomainEventClass[] {
    return [TicketActivatedDomainEvent];
  }

  async on(domainEvent: DomainEvent): Promise<void> {
    const { attributes } = domainEvent;

    const { ip, ...ticketJsonPrimitives } = attributes as DomainEventAttributes;
    const ticketResult = TicketJsonMapper.toEntity(ticketJsonPrimitives);

    if (ticketResult.isLeft()) {
      return;
    }

    const ticket = ticketResult.value;

    const ticketSalePromise = this.createTicketSaleUseCase.execute({ ticket });

    const subscriptionsPromise = ticket.canSubscribe()
      ? this.createSubscriptionsUseCase.execute({
        referrerId: ticket.referrerId,
        ip: Maybe.fromValue(ip),
        applicationId: ticket.applicationId,
        name: ticket.name,
        email: ticket.email.get(),
        organizationId: ticket.organizationId,
        phone: ticket.phone,
      })
      : undefined;

    await Promise.all([
      ticketSalePromise,
      subscriptionsPromise,
      ticket.isActive() && this.ticketEmitter.toClients(ticket),
      ticket.paid && this.ticketEmitter.processTicketPostPaymentActions(ticket),
      this.ticketEmitter.processTicketClientRegistration(ticket),
      this.ticketEmitter.publish(domainEvent),
    ]);

    this.logger.info('Ticket Activated Listener');
  }
}
