import {
  left,
  PersonalDocument,
  PhoneNumber,
  right,
} from '@discocil/fv-domain-library/domain';

import { isObjectEmpty } from '@/cross-cutting/domain/helpers/objects';
import defaultTicketFields from '@/tickets/tickets/domain/config/DefaultTicketFields';
import { AssistantDataError } from '@/tickets/tickets/domain/errors/AssistantDataError';

import type { FormFields } from '@/cross-cutting/domain/contracts/FormFields';
import type {
  CheckAssistantDataRequest,
  CheckAssistantDataResponse,
  ICheckAssistantDataService,
} from '@/tickets/tickets/domain/contracts/PurchaseContracts';

export class CheckAssistantDataService implements ICheckAssistantDataService {
  async execute(request: CheckAssistantDataRequest): Promise<CheckAssistantDataResponse> {
    const {
      ticketType, assistants, organizationTicketFields,
    } = request;

    const fields = this._getFields(ticketType.fields, organizationTicketFields.fold(() => null, value => value));
    const checkPhone = 'phone' in fields;
    const checkPersonalDocument = 'personalDocumentType' in fields;

    for (const [index, assistant] of assistants.toArray().entries()) {
      if (checkPhone && assistant.phone.isDefined()) {
        const phoneResult = PhoneNumber.build(assistant.phone.get());

        if (phoneResult.isLeft()) {
          return left(AssistantDataError.phone({
            context: this.constructor.name,
            message: phoneResult.value.message,
            target: +index + 1,
            data: request,
          }));
        }

        const phoneNumberVO = phoneResult.value;

        assistant.setPhone(phoneNumberVO.number);
      }

      if (checkPersonalDocument && assistant.personalDocument.isDefined()) {
        const { type, number } = assistant.personalDocument.get();
        const personalDocumentResult = PersonalDocument.build(type, number);

        if (personalDocumentResult.isLeft()) {
          return left(AssistantDataError.documentNumber({
            context: this.constructor.name,
            message: personalDocumentResult.value.message,
            target: +index + 1,
            data: request,
          }));
        }

        const personalDocument = personalDocumentResult.value;

        assistant.setPersonalDocument(personalDocument);
      }
    }

    return right(true);
  }

  private _getFields(fields: FormFields | null, organizationTicketFields: FormFields | null): FormFields {
    if (!!fields && !isObjectEmpty(fields)) {
      return fields;
    }

    if (!!organizationTicketFields && !isObjectEmpty(organizationTicketFields)) {
      return organizationTicketFields;
    }

    const defaults = Object.entries(defaultTicketFields).filter(([, field]) => {
      if (Array.isArray(field)) {
        return field.filter(subField => subField.required);
      }

      return field.required;
    });

    return Object.fromEntries(defaults) as FormFields;
  }
}
