import { Paginator } from '@discocil/fv-criteria-converter-library/domain';
import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { Ticket } from '@/tickets/tickets/domain/entities/TicketEntity';

import { TicketMapper } from '../mappers/TicketMapper';
import { TicketSchemaMapper } from '../mappers/TicketSchemaMapper';
import { ticketSchema } from '../schemas/TicketSchema';

import type { AnyBulkWriteOperation } from 'mongoose';
import type { TicketRepository } from '@/tickets/tickets/domain/contracts/TicketRepository';
import type {
  TicketEither,
  TicketKeys,
  Tickets,
  TicketsSearchEither,
  TicketsSoldEither,
  TicketsTotalsEither,
} from '@/tickets/tickets/domain/entities/TicketEntity';
import type { TicketsTotals } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type { Criteria, RequiredCriteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { TicketSchemaType, TicketsSoldSchema } from '../schemas/TicketSchemaType';

type PropertiesMapper = Partial<Record<TicketKeys, keyof TicketSchemaType>>;

export class TicketMongoRepository extends MongoRepository implements TicketRepository {
  protected getSchema(): Schema {
    return new Schema(ticketSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'entradas';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      eventId: 'evento_id',
      typeId: 'tarifa_id',
      state: 'estado',
      saleType: 'tipo_venta',
      referrerId: 'referente_id',
      purchaseId: 'purchase_id',
      prompterId: 'apuntador_id',
      organizationAssignedId: 'negocio_apuntando',
      idx: 'idx',
      groups: 'grupos',
      code: 'codigo',
      activationCode: 'codigo_activacion',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<TicketEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<TicketSchemaType>();

    return queryResponse ? TicketMapper.execute(queryResponse) : left(NotFoundError.build({ context: this.constructor.name, target: Ticket.name }));
  }

  async remove(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteOne(filterQuery.filter);
  }

  async save(ticket: Ticket): Promise<void> {
    const toSave = TicketSchemaMapper.execute(ticket);

    const filter = { _id: ticket.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(tickets: Tickets): Promise<void> {
    const connection = await this.getConnection();

    const operations: AnyBulkWriteOperation[] = [];

    tickets.forEach((ticket: Ticket) => {
      const toSave = TicketSchemaMapper.execute(ticket);

      operations.push({
        updateOne: {
          filter: { _id: toSave._id },
          update: { $set: toSave },
          upsert: true,
        },
      });
    });

    await connection.bulkWrite(operations);
  }

  async numberOfTicketsSold(criteria: Criteria): Promise<TicketsSoldEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const project = {
      'opcion._id': 1,
      'apuntados': 1,
    };

    const group = {
      _id: '$opcion._id',
      totalSales: { $sum: '$apuntados' },
    };

    const queryResponse = await connection.aggregate<TicketsSoldSchema>([{ $match: filterQuery.filter }, { $project: project }, { $group: group }]);

    if (queryResponse.length === 0) {
      return right([]);
    }

    const ticketsSold = queryResponse.map((item: TicketsSoldSchema) => {
      return {
        id: item._id,
        totalSales: item.totalSales,
      };
    });

    return right(ticketsSold);
  }

  async getTotals(criteria: Criteria): Promise<TicketsTotalsEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const project = {
      apuntados: 1,
      entran: 1,
      recaudado: 1,
    };

    const group = {
      _id: null,
      totalSales: { $sum: '$apuntados' },
      totalIn: { $sum: '$entran' },
      totalCollected: { $sum: '$recaudado' },
    };

    const queryResponse = await connection.aggregate<TicketsTotals>([{ $match: filterQuery.filter }, { $project: project }, { $group: group }]);

    if (queryResponse.length === 0) {
      return right({
        totalSales: 0,
        totalIn: 0,
        totalCollected: 0,
      });
    }

    const {
      totalSales, totalIn, totalCollected,
    } = queryResponse.shift() as TicketsTotals;

    return right({
      totalSales, totalIn, totalCollected,
    });
  }

  async search(criteria: Criteria): Promise<TicketsSearchEither> {
    const response = new Map<IdPrimitive, Ticket>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<TicketSchemaType[]>();

    if (queryResponse.length === 0) {
      return right({ tickets: response });
    }

    for (const model of queryResponse) {
      const ticketResult = TicketMapper.execute(model);

      if (ticketResult.isLeft()) {
        return left(ticketResult.value);
      }

      const ticket = ticketResult.value;

      response.set(ticket.id, ticket);
    }

    if (criteria.pagination) {
      const total = await connection.countDocuments(filterQuery.filter);
      const requiredCriteria = criteria as RequiredCriteria;

      return right({
        tickets: response,
        pagination: Paginator.execute(total, requiredCriteria, queryResponse.length),
      });
    }

    return right({ tickets: response });
  }
}
