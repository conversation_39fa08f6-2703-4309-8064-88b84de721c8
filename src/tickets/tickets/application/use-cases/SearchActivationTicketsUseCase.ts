import {
  left,
  NotFoundError,
  right,
  UniqueEntityID,
  contextualizeError,
} from '@discocil/fv-domain-library/domain';

import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';
import { TicketCriteriaMother } from '@/tickets/tickets/domain/filters/TicketCriteriaMother';

import { Ticket } from '../../domain/entities/TicketEntity';

import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { SearchActivationTicketsDto, SearchActivationTicketsEither } from '@/tickets/tickets/domain/contracts/SearchActivationTicketsContracts';
import type { TicketRepository } from '@/tickets/tickets/domain/contracts/TicketRepository';
import type { UseCase } from '@discocil/fv-domain-library/application';

export class SearchActivationTicketsUseCase implements UseCase<SearchActivationTicketsDto, Promise<SearchActivationTicketsEither>> {
  constructor(
    private readonly ticketRepository: TicketRepository,
    private readonly organizationRepository: OrganizationRepository,
  ) {}

  @contextualizeError()
  async execute(dto: SearchActivationTicketsDto): Promise<SearchActivationTicketsEither> {
    const { ticketOrPurchaseId } = dto;

    const identifier = UniqueEntityID.build(ticketOrPurchaseId);
    const ticketCriteria = TicketCriteriaMother.idToMatch(identifier);
    const ticketOrError = await this.ticketRepository.find(ticketCriteria);

    let tickets;
    let organizationId;
    let pagination;

    if (ticketOrError.isLeft()) {
      const ticketsCriteria = TicketCriteriaMother.purchaseIdToMatch(identifier);
      const ticketsOrError = await this.ticketRepository.search(ticketsCriteria);

      if (ticketsOrError.isLeft()) {
        return left(ticketsOrError.value);
      }

      tickets = ticketsOrError.value.tickets;

      const firstTicket = tickets?.values().next().value;

      if (tickets.size === 0 || !firstTicket) {
        const exceptionError = NotFoundError.build({
          context: this.constructor.name,
          target: Ticket.name,
          data: { ticketOrPurchaseId },
        });

        return left(exceptionError.notAutoContextualizable());
      }

      pagination = ticketsOrError.value.pagination;

      organizationId = firstTicket.organizationId;
    } else {
      const ticket = ticketOrError.value;

      tickets = new Map([[ticket.id, ticket]]);
      organizationId = ticket.organizationId;
    }

    const organizationCriteria = OrganizationCriteriaMother.idToMatch(UniqueEntityID.build(organizationId));
    const organizationOrError = await this.organizationRepository.find(organizationCriteria);

    if (organizationOrError.isLeft()) {
      return left(organizationOrError.value);
    }

    const organization = organizationOrError.value;

    return right({
      tickets,
      organization,
      pagination,
    });
  }
}
