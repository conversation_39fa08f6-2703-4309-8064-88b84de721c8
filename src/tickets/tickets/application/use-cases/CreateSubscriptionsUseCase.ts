import { left, right } from '@discocil/fv-domain-library/domain';

import type { CreateSubscriptionsDto, CreateSubscriptionsEither } from '@/tickets/tickets/domain/contracts/CreateSubscriptionsContracts';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { ISubscribeService } from '@/tickets/tickets/domain/contracts/PurchaseContracts';
import type { SubscriptionRepository } from '@/subscription/domain/contracts/SubscriptionRepository';

export class CreateSubscriptionsUseCase implements UseCase<CreateSubscriptionsDto, Promise<CreateSubscriptionsEither>> {
  constructor(
    private readonly subscribeService: ISubscribeService,
    private readonly subscriptionRepository: SubscriptionRepository,
  ) {}

  async execute(dto: CreateSubscriptionsDto): Promise<CreateSubscriptionsEither> {
    const {
      ip,
      applicationId,
      referrerId,
      name,
      email,
      phone,
      organizationId,
    } = dto;

    const subscribeResult = await this.subscribeService.execute({
      referrerId,
      ip,
      applicationId,
      name,
      email,
      imported: false,
      organizationId,
      phone,
    });

    if (subscribeResult.isLeft()) {
      return left(subscribeResult.value);
    }

    const subscriptions = subscribeResult.value;

    await this.subscriptionRepository.saveMany(subscriptions);

    return right(subscriptions);
  }
}
