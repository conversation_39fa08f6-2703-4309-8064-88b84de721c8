/** NOTE
  * This use case should not be used individually; it is only used internally.
  * It must always call the parent use case: ActivateTicketUseCase.
  */

import {
  IdPrimitive,
  left,
  NotFoundError,
  right,
  UniqueEntityID,
  contextualizeError,
} from '@discocil/fv-domain-library/domain';


import { TicketRepository } from '../../domain/contracts/TicketRepository';
import { Ticket, Tickets } from '../../domain/entities/TicketEntity';
import { AssistantDataError } from '../../domain/errors/AssistantDataError';
import { TicketActivationError } from '../../domain/errors/TicketActivationError';
import { TicketCriteriaMother } from '../../domain/filters/TicketCriteriaMother';

import { ActivateSingleTicketUseCase } from './ActivateSingleTicketUseCase';

import type { ActivatePurchaseDto, ActivatePurchaseTicketsEither } from '@/tickets/tickets/domain/contracts/ActivateTicketContracts';
import type { UseCase } from '@discocil/fv-domain-library/application';

export class ActivatePurchaseTicketsUseCase implements UseCase<ActivatePurchaseDto, Promise<ActivatePurchaseTicketsEither>> {
  constructor(
    private readonly ticketRepository: TicketRepository,
    private readonly activateSingleTicketUseCase: ActivateSingleTicketUseCase,
  ) { }

  @contextualizeError()
  async execute(dto: ActivatePurchaseDto): Promise<ActivatePurchaseTicketsEither> {
    const { purchaseId } = dto;

    const ticketCriteria = TicketCriteriaMother.purchaseIdToMatch(UniqueEntityID.build(purchaseId));
    const ticketOrError = await this.ticketRepository.search(ticketCriteria);

    if (ticketOrError.isLeft()) {
      return left(ticketOrError.value);
    }

    const tickets = ticketOrError.value.tickets;

    if (tickets.size === 0) {
      const exceptionError = NotFoundError.build({
        context: this.constructor.name,
        target: Ticket.name,
        data: { purchaseId },
      });

      return left(exceptionError.notAutoContextualizable());
    }

    const activatedTickets: Tickets = new Map<IdPrimitive, Ticket>();

    for (const assistant of dto.assistants) {
      const ticket = tickets.get(assistant.ticketId);

      if (!ticket) {
        return left(AssistantDataError.notFound({
          context: this.constructor.name,
          data: { dto },
        }).notAutoContextualizable());
      }

      const useCaseResponse = await this.activateSingleTicketUseCase.execute({
        ...dto, ticket, assistant,
      });

      if (useCaseResponse.isLeft()) {
        const exceptionError = useCaseResponse.value;

        if (exceptionError instanceof TicketActivationError) {
          continue;
        }

        return left(exceptionError);
      }

      const activatedTicket = useCaseResponse.value;

      activatedTickets.set(activatedTicket.id, activatedTicket);
    }

    return right(activatedTickets);
  }
}
