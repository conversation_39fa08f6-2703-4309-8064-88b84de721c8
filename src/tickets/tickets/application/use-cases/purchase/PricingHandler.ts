import {
  FvNumber,
  left, Maybe, NotFoundError,
  right, UniqueEntityID,
} from '@discocil/fv-domain-library';
import { AbstractHandler } from '@discocil/fv-domain-library/application';
import { EValueType } from '@discocil/fv-pricing-library/ticketing';

import { FeeCalculationResult } from '@/feeCalculationResult/domain/entities/FeeCalculationResult';
import { TicketsPaymentTotalsService } from '@/tickets/tickets/domain/services/ticketsPricing/TicketsPaymentTotalsService';
import { TicketsPricingService } from '@/tickets/tickets/domain/services/ticketsPricing/TicketsPricingService';
import { getWarrantyPercentageStrategy } from '@/tickets/tickets/domain/services/warrantyStrategy/WarrantyPercentageStrategy';

import type { FeeCalculationResultEither } from '@/feeCalculationResult/domain/entities/FeeCalculationResult';
import type { TemplatePrimitives } from '@/organizations/organizations/domain/entities/RuleTemplate';
import type { PurchaseBuilderTicketOrder } from '@/tickets/tickets/domain/contracts/PurchaseContracts';
import type {
  FeesResponse,
  InputDiscountPrimitives,
  InputTicketPrimitives,
  InputWarrantyPrimitives,
  PaymentGatewayFeeConfigPrimitives,
} from '@discocil/fv-pricing-library/ticketing';

type Request = Pick<PurchaseBuilderTicketOrder, 'purchase'>;

export class PricingHandler extends AbstractHandler {
  private static instance: PricingHandler;

  static build(): PricingHandler {
    if (!PricingHandler.instance) {
      PricingHandler.instance = new PricingHandler();
    }

    return PricingHandler.instance;
  }

  async handle<T>(request: Request): Promise<T> {
    const { purchase } = request;

    const currency = purchase.getCurrency();

    const discountCode = purchase.getDiscountCode();

    const customerCustomFees = purchase.getCustomerCustomFees();

    const paymentGatewayTransactionFeeConfig = this.makePaymentGatewayTransactionFeeConfig(purchase.getGGDDTickets());

    const paymentGatewayWarrantyFeeConfig = this.makePaymentGatewayWarrantyFeeConfig(purchase.getOrganizationId());

    const ticketTypesAssistants = purchase.getAssistants();

    const discounts: InputDiscountPrimitives[] = [];

    const warranties: InputWarrantyPrimitives[] = [];

    const tickets: InputTicketPrimitives[] = [];

    for (const [ticketTypeId, assistants] of ticketTypesAssistants) {
      if (discountCode.isDefined()) {
        discounts.push({
          ticketTypeId,
          value: discountCode.get().getDiscountValue(),
          type: discountCode.get().getDiscountType(),
        });
      }

      const ticketType = purchase.findTicketType(ticketTypeId);

      if (ticketType.hasWarrantyEnabled()) {
        warranties.push({
          ticketTypeId,
          value: ticketType.getWarrantyValue(),
          type: ticketType.warranty.isFixed ? EValueType.FIXED : EValueType.PERCENTAGE,
        });
      }

      for (const assistant of assistants.toArray()) {
        const ticketTypeOption = assistant.getTicketTypeOption();

        const ticket: InputTicketPrimitives = {
          id: assistant.id,
          ticketTypeId,
          optionId: ticketTypeOption.id,
          unitPrice: ticketTypeOption.price,
          addOns: assistant.supplements.toArray().map(supplement => ({
            id: supplement.id.toPrimitive(),
            unitPrice: supplement.ticketTypeSupplement.price.toDecimal(),
            quantity: supplement.purchaseQuantity.toPrimitive(),
          })),
          isWarrantySelected: purchase.hasWarrantySelected(),
          adminFeeConfig: ticketTypeOption.getAdminFeeConfig(),
        };

        tickets.push(ticket);
      }
    }

    const ticketsPricingOrError = TicketsPricingService.execute({
      tickets,
      currency,
      warranties,
      customerCustomFees,
      discounts,
      paymentGatewayTransactionFeeConfig,
      paymentGatewayWarrantyFeeConfig,
    });

    if (ticketsPricingOrError.isLeft()) {
      return super.finish(ticketsPricingOrError);
    }

    const ticketsPricing = ticketsPricingOrError.value;

    let feeCalculationResult = purchase.getFeeCalculationResult();

    for (const ticketPricing of ticketsPricing) {
      if (!ticketPricing.id) {
        continue;
      }

      if (ticketPricing.customerCustomFees) {
        const feeCalculationResultOrError = this.addFeesResponse({
          feeCalculationResult,
          feesResponse: ticketPricing.customerCustomFees,
          ticketTypeId: ticketPricing.ticketTypeId,
          optionId: ticketPricing.optionId,
        });

        if (feeCalculationResultOrError.isRight()) {
          feeCalculationResult = Maybe.some(feeCalculationResultOrError.value);
        }
      }

      const assistants = purchase.findAssistants(ticketPricing.ticketTypeId);

      const assistant = assistants.getByIndex(ticketPricing.id);

      if (assistant.isEmpty()) {
        return super.finish(left(NotFoundError.build({
          context: this.constructor.name,
          data: { ticketPricing, assistants },
        })));
      }

      assistant.get().setTicketPricing({
        ...ticketPricing,
        id: Maybe.some(UniqueEntityID.build(ticketPricing.id)),
        ticketTypeId: UniqueEntityID.build(ticketPricing.ticketTypeId),
        customerCustomFees: Maybe.fromValue(ticketPricing.customerCustomFees),
      });
    }

    if (feeCalculationResult.isDefined()) {
      purchase.setFeeCalculationResult(feeCalculationResult.get());
    }

    const paymentTotalsOrError = TicketsPaymentTotalsService.execute({
      tickets: ticketsPricing,
      currency,
    });

    if (paymentTotalsOrError.isLeft()) {
      return super.finish(paymentTotalsOrError);
    }

    purchase.setPaymentTotals(paymentTotalsOrError.value);

    return super.handle(request);
  }

  private addFeesResponse({
    feeCalculationResult,
    feesResponse,
    ticketTypeId,
    optionId,
  }: {
    feeCalculationResult: Maybe<FeeCalculationResult>;
    feesResponse: FeesResponse;
    ticketTypeId: string;
    optionId: string;
  }): FeeCalculationResultEither {
    if (feeCalculationResult.isEmpty()) {
      const feeCalculationResultOrError = FeeCalculationResult.create();

      if (feeCalculationResultOrError.isLeft()) {
        return left(feeCalculationResultOrError.value);
      }

      feeCalculationResult = Maybe.some(feeCalculationResultOrError.value);
    }

    return right(feeCalculationResult.get().addFeesResponse(feesResponse, ticketTypeId, optionId));
  }

  private makePaymentGatewayTransactionFeeConfig(config: TemplatePrimitives): PaymentGatewayFeeConfigPrimitives {
    return {
      fixedAmount: config.fixed,
      percentageDecimalValue: FvNumber.build(config.percentage).toPercentage(),
      maximumAmount: config.maximum,
      minimumAmount: config.minimum,
    };
  }

  private makePaymentGatewayWarrantyFeeConfig(organizationId: UniqueEntityID): PaymentGatewayFeeConfigPrimitives {
    const warrantyStrategy = getWarrantyPercentageStrategy(organizationId);

    return {
      fixedAmount: 0,
      percentageDecimalValue: FvNumber.build(warrantyStrategy.getPercent()).toPercentage(),
      maximumAmount: 0,
      minimumAmount: 0,
    };
  }
}
