import { AbstractHand<PERSON> } from '@discocil/fv-domain-library/application';
import { left } from '@discocil/fv-domain-library/domain';

import { InvalidPackSizeError } from '@/tickets/ticketsTypes/domain/errors/InvalidPackSizeError';

import type { PurchaseBuilderTicketOrder } from '@/tickets/tickets/domain/contracts/PurchaseContracts';

type Request = Pick<PurchaseBuilderTicketOrder, 'purchase'>;

export class ValidatePackPerTicketHandler extends AbstractHandler {
  private static instance: ValidatePackPerTicketHandler;

  static build(): ValidatePackPerTicketHandler {
    if (!ValidatePackPerTicketHandler.instance) {
      ValidatePackPerTicketHandler.instance = new ValidatePackPerTicketHandler();
    }

    return ValidatePackPerTicketHandler.instance;
  }

  async handle<T>(request: Request): Promise<T> {
    const { purchase } = request;

    const ticketTypes = purchase.getTicketTypes();

    for (const ticketType of ticketTypes.values()) {
      const isPackSizeValid = purchase.isPackSizeValidForTicketType(ticketType.id);

      if (!isPackSizeValid) {
        const invalidPackSizeError = InvalidPackSizeError.build({
          context: this.constructor.name,
          target: {
            ticketType,
            amount: purchase.getAmountByTicketType(ticketType.id),
          },
        });

        return super.finish(left(invalidPackSizeError));
      }
    }

    return super.handle(request);
  }
}
