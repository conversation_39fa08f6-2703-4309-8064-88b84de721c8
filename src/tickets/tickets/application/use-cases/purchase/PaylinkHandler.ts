import { AbstractHandler } from '@discocil/fv-domain-library/application';

import type { PurchaseBuilderTicketOrder } from '@/tickets/tickets/domain/contracts/PurchaseContracts';

type Request = Pick<PurchaseBuilderTicketOrder, 'purchase'>;

export class PaylinkHandler extends AbstractHandler {
  private static instance: PaylinkHandler;

  static build(): PaylinkHandler {
    if (!PaylinkHandler.instance) {
      PaylinkHandler.instance = new PaylinkHandler();
    }

    return PaylinkHandler.instance;
  }

  async handle<T>(request: Request): Promise<T> {
    const { purchase } = request;

    purchase.modifyPaylink();

    return super.handle(request);
  }
}
