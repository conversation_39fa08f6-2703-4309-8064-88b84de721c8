import {
  left, right, UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { TicketTypeCriteriaMother } from '@/tickets/ticketsTypes/domain/filters/TicketTypeCriteriaMother';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';

import type { Sale } from '@/sale/domain/entities/Sale';
import type { TicketTypeRepository } from '@/tickets/ticketsTypes/domain/contracts/TicketTypeRepository';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type { ISalesService } from '@/tickets/tickets/domain/contracts/PurchaseContracts';
import type { SaleRepository } from '@/sale/domain/contracts/SaleRepository';
import type { CreateTicketSaleDto, CreateTicketSaleEither } from '@/tickets/tickets/domain/contracts/CreateTicketSaleContracts';

export class CreateTicketSaleUseCase implements UseCase<CreateTicketSaleDto, Promise<CreateTicketSaleEither>> {
  constructor(
    private readonly eventRepository: EventRepository,
    private readonly ticketTypeRepository: TicketTypeRepository,
    private readonly saleService: ISalesService,
    private readonly saleRepository: SaleRepository,
  ) {}

  async execute(dto: CreateTicketSaleDto): Promise<CreateTicketSaleEither> {
    const { ticket } = dto;

    const eventCriteria = EventCriteriaMother.idToMatch(UniqueEntityID.build(ticket.eventId));
    const ticketTypeCriteria = TicketTypeCriteriaMother.idToMatch(UniqueEntityID.build(ticket.typeId));

    const [eventResult, ticketTypeResult] = await Promise.all([
      this.eventRepository.find(eventCriteria),
      this.ticketTypeRepository.find(ticketTypeCriteria),
    ]);

    if (eventResult.isLeft()) {
      return left(eventResult.value);
    }

    if (ticketTypeResult.isLeft()) {
      return left(ticketTypeResult.value);
    }

    const event = eventResult.value;
    const ticketType = ticketTypeResult.value;

    const saleResult = await this.saleService.execute({
      tickets: new Map([[ticket.id, ticket]]),
      ticketTypes: new Map([[ticketType.id, ticketType]]),
      event,
    });

    if (saleResult.isLeft()) {
      return left(saleResult.value);
    }

    const sale = saleResult.value.values().next().value as Sale;

    await this.saleRepository.save(sale);

    return right(sale);
  }
}
