import {
  IdPrimitive,
  left,
  right,
  UniqueEntityID,
  contextualizeError,
} from '@discocil/fv-domain-library/domain';

import { InternalMessageBrokerClient } from '@/cross-cutting/domain/messageBroker/InternalMessageBrokerClientContracts';
import { TicketCriteriaMother } from '@/tickets/tickets/domain/filters/TicketCriteriaMother';

import { ActivateAssistant } from '../../domain/entities/Assistant';
import { Ticket } from '../../domain/entities/TicketEntity';
import { AssistantDataError } from '../../domain/errors/AssistantDataError';

import { ActivatePurchaseTicketsUseCase } from './ActivatePurchaseTicketsUseCase';
import { ActivateSingleTicketUseCase } from './ActivateSingleTicketUseCase';

import type {
  ActivatePurchaseDto,
  ActivatePurchaseTicketsEither,
  ActivateSingleTicketEither,
  ActivateTicketDto,
  ActivateTicketEither,
} from '@/tickets/tickets/domain/contracts/ActivateTicketContracts';
import type { TicketRepository } from '@/tickets/tickets/domain/contracts/TicketRepository';
import type { UseCase } from '@discocil/fv-domain-library/application';

export class ActivateTicketUseCase implements UseCase<ActivateTicketDto, Promise<ActivateTicketEither>> {
  constructor(
    private readonly ticketRepository: TicketRepository,
    private readonly singleActivateUseCase: ActivateSingleTicketUseCase,
    private readonly purchaseActivationUseCase: ActivatePurchaseTicketsUseCase,
    private readonly internalMessageBroker: InternalMessageBrokerClient,
  ) { }

  @contextualizeError()
  async execute(dto: ActivateTicketDto): Promise<ActivateTicketEither> {
    const identifier = UniqueEntityID.build(dto.identifier);
    const ticketCriteria = TicketCriteriaMother.idToMatch(identifier);
    const ticketOrError = await this.ticketRepository.find(ticketCriteria);

    if (ticketOrError.isLeft()) {
      const { identifier: purchaseId, ...newDto } = dto;
      const useCaseOrError = await this.purchaseActivation(newDto as ActivatePurchaseDto, purchaseId);

      return this.makeResponse(useCaseOrError);
    }

    const ticket = ticketOrError.value;

    if (!ticket.nominative) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { identifier, ...newDto } = dto;
      const useCaseOrError = await this.purchaseActivation(newDto as ActivatePurchaseDto, ticket.purchaseId.get());

      return this.makeResponse(useCaseOrError);
    }

    const assistant = dto.assistants.at(0);

    if (!assistant) {
      return left(AssistantDataError.notFound({
        context: this.constructor.name,
        data: { dto },
      }).notAutoContextualizable());
    }

    const useCaseOrError = await this.singleActivation(dto, ticket, assistant);

    return this.makeResponse(useCaseOrError);
  }

  private async singleActivation(dto: ActivateTicketDto, ticket: Ticket, assistant: ActivateAssistant): Promise<ActivateSingleTicketEither> {
    const useCaseOrError = await this.singleActivateUseCase.execute({
      ...dto, ticket, assistant,
    });

    if (useCaseOrError.isLeft()) {
      return left(useCaseOrError.value);
    }

    const activatedTicket = useCaseOrError.value;

    await this.ticketRepository.save(activatedTicket);
    void this.internalMessageBroker.publish(activatedTicket.pullDomainEvents());

    return right(activatedTicket);
  }

  private async purchaseActivation(dto: ActivatePurchaseDto, purchaseId: IdPrimitive): Promise<ActivatePurchaseTicketsEither> {
    const useCaseOrError = await this.purchaseActivationUseCase.execute({ ...dto, purchaseId });

    if (useCaseOrError.isLeft()) {
      return left(useCaseOrError.value);
    }

    const tickets = useCaseOrError.value;

    await this.ticketRepository.saveMany(tickets);

    for (const ticket of tickets.values()) {
      void this.internalMessageBroker.publish(ticket.pullDomainEvents());
    }

    return right(tickets);
  }

  private makeResponse(useCaseOrError: ActivateSingleTicketEither | ActivatePurchaseTicketsEither): ActivateTicketEither {
    return useCaseOrError.isLeft()
      ? left(useCaseOrError.value)
      : right(undefined);
  }
}
