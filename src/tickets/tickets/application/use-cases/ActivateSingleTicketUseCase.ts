/** NOTE
  * This use case should not be used individually; it is only used internally.
  * It must always call the parent use case: ActivateTicketUseCase.
  */

import {
  Collection,
  left,
  Maybe,
  right,
  UniqueEntityID,
  contextualizeError,
} from '@discocil/fv-domain-library/domain';

import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';
import { TicketTypeCriteriaMother } from '@/tickets/ticketsTypes/domain/filters/TicketTypeCriteriaMother';

import {
  Assistant, Assistants, AssistantSupplement,
} from '../../domain/entities/Assistant';

import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { ActivateSingleDto, ActivateSingleTicketEither } from '@/tickets/tickets/domain/contracts/ActivateTicketContracts';
import type { TicketTypeRepository } from '@/tickets/ticketsTypes/domain/contracts/TicketTypeRepository';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { ICheckAssistantDataService } from '../../domain/contracts/PurchaseContracts';

export class ActivateSingleTicketUseCase implements UseCase<ActivateSingleDto, Promise<ActivateSingleTicketEither>> {
  constructor(
    private readonly organizationRepository: OrganizationRepository,
    private readonly ticketTypeRepository: TicketTypeRepository,
    private readonly checkAssistantDataService: ICheckAssistantDataService,
  ) { }

  @contextualizeError()
  async execute(dto: ActivateSingleDto): Promise<ActivateSingleTicketEither> {
    const {
      ticket, assistant, remarketing, language, browser, device, os, ip, idx,
    } = dto;

    const ensureCanActivate = ticket.ensureCanActivate();

    if (ensureCanActivate.isLeft()) {
      return left(ensureCanActivate.value);
    }

    const organizationCriteria = OrganizationCriteriaMother.idToMatch(UniqueEntityID.build(ticket.organizationId));

    const ticketTypeCriteria = TicketTypeCriteriaMother.ticketActivationToMatch(
      UniqueEntityID.build(ticket.typeId),
      UniqueEntityID.build(ticket.eventId),
    );

    const [organizationOrError, ticketTypeOrError] = await Promise.all([
      this.organizationRepository.find(organizationCriteria),
      this.ticketTypeRepository.find(ticketTypeCriteria),
    ]);

    if (organizationOrError.isLeft()) {
      return left(organizationOrError.value);
    }

    if (ticketTypeOrError.isLeft()) {
      return left(ticketTypeOrError.value);
    }

    const organization = organizationOrError.value;
    const ticketType = ticketTypeOrError.value;

    const assistants: Assistants = Collection.new('id');

    const assistantEntity = Assistant.create({
      ...assistant,
      ticketTypeId: ticketType.id,
      optionId: Maybe.none<string>(),
      supplements: Collection.new<AssistantSupplement>('id'),
    });

    assistants.add(assistantEntity);

    const checkAssistantDataResult = await this.checkAssistantDataService.execute({
      ticketType,
      assistants,
      organizationTicketFields: organization.ticketFields,
    });

    if (checkAssistantDataResult.isLeft()) {
      return left(checkAssistantDataResult.value);
    }

    ticket.setAssistant(assistantEntity);
    ticket.setRemarketing(remarketing);
    ticket.setFingerPrintData({
      language,
      browser,
      device,
      os,
    });
    ticket.setIdx(idx);
    ticket.activate(ip);

    return right(ticket);
  }
}
