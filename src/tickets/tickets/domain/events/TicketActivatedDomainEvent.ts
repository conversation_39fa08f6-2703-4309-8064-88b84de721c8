import { DomainEvent } from '@discocil/fv-domain-library/domain';

import type { DomainEventRequest } from '@discocil/fv-domain-library/domain';

export class TicketActivatedDomainEvent extends DomainEvent {
  static readonly EVENT_NAME: string = 'cli.ticket.activated';

  static build(params: DomainEventRequest): TicketActivatedDomainEvent {
    return new this({
      ...params,
      type: this.EVENT_NAME,
    });
  }
}
