import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { TicketKeys } from '../entities/TicketEntity';

class FilterField extends FilterFieldBase<TicketKeys> {}

export class ActivationCodeFilter {
  private static readonly field: TicketKeys = 'activationCode';

  static buildEqual(activationCode: string): Filter {
    const activationCodeField = new FilterField(this.field);
    const activationCodeOperator = FilterOperator.equal();
    const activationCodeFilterValue = FilterValue.build(activationCode);

    return new Filter(activationCodeField, activationCodeOperator, activationCodeFilterValue);
  }
}
