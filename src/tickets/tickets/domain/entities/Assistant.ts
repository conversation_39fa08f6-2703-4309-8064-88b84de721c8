import {
  Collection, Entity, Maybe,
  Money,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import type { Supplement } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type { TicketTypeOption } from '@/tickets/ticketsTypes/domain/entities/TicketTypeOption';
import type {
  ECountryCode, ECurrency, EGender,
  FvDate,
  FvNumber,
  IdPrimitive,
  PersonalDocumentPrimitives,
  Primitives,
} from '@discocil/fv-domain-library/domain';
import type { TicketPricing } from '../contracts/PurchaseContracts';

export type ActivateAssistant = Omit<AssistantRequestPrimitives, 'supplements' | 'optionId'> & {
  readonly ticketId: IdPrimitive;
};

export type PurchaseSupplement = Primitives<Pick<AssistantSupplement, 'id' | 'purchaseQuantity'>>;

type CreateAssistantPrimitives = Primitives<Omit<Assistant, 'id'>>;

export type AssistantRequestPrimitives = Omit<CreateAssistantPrimitives, 'supplements' | 'ticketTypeId'> & {
  supplements: PurchaseSupplement[];
};

export type Assistants = Collection<Assistant>;

export type AssistantSupplements = Collection<AssistantSupplement>;

export type AssistantSupplement = {
  readonly id: UniqueEntityID;
  readonly purchaseQuantity: FvNumber;
  readonly ticketTypeSupplement: Supplement;
};

type Answer = {
  readonly id: string;
  readonly answer: string;
};

export class Assistant extends Entity {
  private _ticketTypeOption = Maybe.none<TicketTypeOption>();
  private _supplementsTotalPrice = Maybe.none<Money>();
  private _ticketPricing = Maybe.none<TicketPricing>();

  private constructor(
    id: UniqueEntityID,
    private readonly _ticketTypeId: UniqueEntityID,
    private readonly _supplements: AssistantSupplements,
    private readonly _optionId: Maybe<string>,
    private readonly _email: string,
    private readonly _emailConfirmation: Maybe<string>,
    private readonly _fullname: Maybe<string>,
    private _phone: Maybe<string>,
    private readonly _birthDate: Maybe<FvDate>,
    private readonly _gender: Maybe<EGender>,
    private _personalDocument: Maybe<PersonalDocumentPrimitives>,
    private readonly _address: Maybe<string>,
    private readonly _country: Maybe<ECountryCode>,
    private readonly _zipCode: Maybe<string>,
    private readonly _answers: Answer[],
  ) {
    super(id);
  }

  static create(primitives: CreateAssistantPrimitives): Assistant {
    const id = UniqueEntityID.create();
    const ticketTypeId = UniqueEntityID.build(primitives.ticketTypeId);
    const optionId = primitives.optionId.map(optionId => optionId);

    const supplements = Collection.new<AssistantSupplement>('id');

    for (const supplement of primitives.supplements) {
      supplements.add({
        id: supplement.id,
        purchaseQuantity: supplement.purchaseQuantity,
        ticketTypeSupplement: supplement.ticketTypeSupplement,
      });
    }

    const emailConfirmation = primitives.emailConfirmation.map(emailConfirmation => emailConfirmation);
    const fullname = primitives.fullname.map(fullname => fullname);
    const birthDate = primitives.birthDate.map(birthDate => birthDate);
    const gender = primitives.gender.map(gender => gender);
    const address = primitives.address.map(address => address);
    const country = primitives.country.map(country => country);
    const zipCode = primitives.zipCode.map(zipCode => zipCode);
    const phone = primitives.phone.map(phone => phone);
    const personalDocument = primitives.personalDocument.map(personalDocument => personalDocument);

    return new Assistant(
      id,
      ticketTypeId,
      supplements,
      optionId,
      primitives.email,
      emailConfirmation,
      fullname,
      phone,
      birthDate,
      gender,
      personalDocument,
      address,
      country,
      zipCode,
      primitives.answers,
    );
  }

  get id(): IdPrimitive {
    return this._id.toPrimitive();
  }

  get ticketTypeId(): IdPrimitive {
    return this._ticketTypeId.toPrimitive();
  }

  get optionId(): Maybe<string> {
    return this._optionId;
  }

  get email(): string {
    return this._email;
  }

  get emailConfirmation(): Maybe<string> {
    return this._emailConfirmation;
  }

  get fullname(): Maybe<string> {
    return this._fullname;
  }

  get phone(): Maybe<string> {
    return this._phone;
  }

  get birthDate(): Maybe<FvDate> {
    return this._birthDate;
  }

  get gender(): Maybe<EGender> {
    return this._gender;
  }

  get personalDocument(): Maybe<PersonalDocumentPrimitives> {
    return this._personalDocument;
  }

  get address(): Maybe<string> {
    return this._address;
  }

  get country(): Maybe<ECountryCode> {
    return this._country;
  }

  get zipCode(): Maybe<string> {
    return this._zipCode;
  }

  get answers(): Answer[] {
    return this._answers;
  }

  get supplements(): AssistantSupplements {
    return this._supplements;
  }

  setPhone(phone: string): this {
    this._phone = Maybe.some(phone);

    return this;
  }

  setPersonalDocument(personalDocument: PersonalDocumentPrimitives): this {
    this._personalDocument = Maybe.some(personalDocument);

    return this;
  }

  setTicketTypeOption(option: TicketTypeOption): this {
    this._ticketTypeOption = Maybe.some(option);

    return this;
  }

  getTicketTypeOption(): TicketTypeOption {
    return this._ticketTypeOption.get();
  }

  getSupplementsTotalPrice(currency: ECurrency): Money {
    if (this._supplementsTotalPrice.isDefined()) {
      return this._supplementsTotalPrice.get();
    }

    const totalPrice = this._supplements.toArray().reduce((acc, supplement) => {
      const supplementTotalPrice = supplement.ticketTypeSupplement.price.multiply(supplement.purchaseQuantity.toPrimitive());

      return acc.add(supplementTotalPrice);
    }, Money.buildZero(currency));

    this._supplementsTotalPrice = Maybe.some(totalPrice);

    return totalPrice;
  }

  setTicketPricing(ticketPricing: TicketPricing): this {
    this._ticketPricing = Maybe.some(ticketPricing);

    return this;
  }

  getTicketPricing(): TicketPricing {
    return this._ticketPricing.get();
  }
}
