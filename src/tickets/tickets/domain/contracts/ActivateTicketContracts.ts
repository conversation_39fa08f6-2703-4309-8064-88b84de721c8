import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { TicketActivationError } from '@/tickets/tickets/domain/errors/TicketActivationError';
import type {
  Either,
  IdPrimitive,
  InvalidArgumentError,
  MoneyError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import type { ActivateAssistant } from '../entities/Assistant';
import type { Ticket, Tickets } from '../entities/TicketEntity';
import type { AssistantDataError } from '../errors/AssistantDataError';

export type ActivateTicketDto = {
  readonly identifier: IdPrimitive;
  readonly assistants: ActivateAssistant[];
  readonly remarketing: boolean;
  readonly idx: string;
  readonly ip: string;
  readonly os: string;
  readonly language: string;
  readonly browser: string;
  readonly device: string;
};

type ActivateTicketEitherBase<RIGHT_RESPONSE> = Either<
  | AssistantDataError
  | InvalidArgumentError
  | TicketActivationError
  | MapperError
  | NotFoundError
  | MoneyError
  | UnexpectedError,
  RIGHT_RESPONSE
>;

export type ActivateTicketEither = ActivateTicketEitherBase<void>;
export type ActivateSingleTicketEither = ActivateTicketEitherBase<Ticket>;
export type ActivatePurchaseTicketsEither = ActivateTicketEitherBase<Tickets>;

export type ActivateSingleDto = Omit<ActivateTicketDto, 'identifier' | 'assistants'> & {
  readonly ticket: Ticket;
  readonly assistant: ActivateAssistant;
};

export type ActivatePurchaseDto = Omit<ActivateTicketDto, 'identifier'> & { readonly purchaseId: IdPrimitive; };
