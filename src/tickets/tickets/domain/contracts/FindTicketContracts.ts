import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  Either, InvalidArgumentError, NotFoundError,
} from '@discocil/fv-domain-library/domain';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { Ticket } from '../entities/TicketEntity';

export type FindTicketDto = {
  readonly idOrActivationCode: string;
};

export type FindTicketResponseType = {
  readonly ticket: Ticket;
  readonly organization: Organization;
};

export type FindTicketEither = Either<MapperError | NotFoundError | InvalidArgumentError, FindTicketResponseType>;
