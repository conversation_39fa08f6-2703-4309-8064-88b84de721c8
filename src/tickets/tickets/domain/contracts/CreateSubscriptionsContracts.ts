import type { Subscriptions } from '@/subscription/domain/entities/SubscriptionEntity';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  Either, Maybe, NotFoundError,
} from '@discocil/fv-domain-library/domain';

export type CreateSubscriptionsDto = {
  readonly ip: Maybe<string>;
  readonly applicationId: string;
  readonly referrerId: Maybe<string>;
  readonly name: Maybe<string>;
  readonly email: string;
  readonly phone: Maybe<string>;
  readonly organizationId: string;
};

export type CreateSubscriptionsEither = Either<
  | MapperError
  | NotFoundError,
  Subscriptions
>;
