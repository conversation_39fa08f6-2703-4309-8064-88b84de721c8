import type { Either, UnexpectedError } from '@discocil/fv-domain-library/domain';
import type { OptionPriceError } from '@/tickets/ticketsTypes/domain/errors/OptionPriceError';
import type { TicketTypeOption } from '@/tickets/ticketsTypes/domain/entities/TicketTypeOption';
import type { TicketType } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type { EventEntity } from '@/events/events/domain/entities/EventEntity';

export type GetOptionsRequest = {
  readonly ticketType: TicketType;
  readonly event: EventEntity;
  readonly amount: number;
};

export type GetOptionsEither = Either<UnexpectedError | OptionPriceError, TicketTypeOption[]>;

export interface IGetOptionsService {
  execute: (dto: GetOptionsRequest) => Promise<GetOptionsEither>;
}
