import type { Sale } from '@/sale/domain/entities/Sale';
import type { Ticket } from '../entities/TicketEntity';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  Either, InvalidArgumentError, NotFoundError, UnexpectedError,
} from '@discocil/fv-domain-library/domain';

export type CreateTicketSaleDto = {
  readonly ticket: Ticket;
};

export type CreateTicketSaleEither = Either<
  | InvalidArgumentError
  | MapperError
  | NotFoundError
  | UnexpectedError,
  Sale
>;
