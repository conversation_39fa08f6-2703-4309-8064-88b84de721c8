import type { InvalidFieldError } from '@/cross-cutting/domain/errors/InvalidFieldError';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { PaginationMetadataResponse } from '@discocil/fv-criteria-converter-library/domain';
import type {
  Either,
  InvalidArgumentError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import type { Tickets } from '../entities/TicketEntity';

export type SearchActivationTicketsDto = {
  readonly ticketOrPurchaseId: string;
};

export type SearchActivationTicketsResponse = PaginationMetadataResponse & {
  readonly tickets: Tickets;
  readonly organization: Organization;
};

export type SearchActivationTicketsEither = Either<
  InvalidArgumentError
  | InvalidFieldError
  | MapperError
  | NotFoundError
  | UnexpectedError,
  SearchActivationTicketsResponse
>;
