import { ECustomOrganizationWarranties } from '@/organizations/organizations/domain/enums/CustomOrganization';

import { DefaultPercentWarrantyStrategy } from './warrantyPercentageStrategy/DefaultPercentWarrantyStrategy';
import { ThirtyPercentWarrantyStrategy } from './warrantyPercentageStrategy/ThirtyPercentWarrantyStrategy';
import { TwentyPercentWarrantyStrategy } from './warrantyPercentageStrategy/TwentyPercentWarrantyStrategy';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import type { IPercentWarrantyStrategy } from '../../contracts/TicketTypesContracts';

export const getWarrantyPercentageStrategy = (organizationId: UniqueEntityID): IPercentWarrantyStrategy => {
  const match: Record<ECustomOrganizationWarranties, () => IPercentWarrantyStrategy> = {
    [ECustomOrganizationWarranties.BCM_MALLORCA]: () => new ThirtyPercentWarrantyStrategy(),
    [ECustomOrganizationWarranties.DESTINO_PACHA]: () => new TwentyPercentWarrantyStrategy(),
    [ECustomOrganizationWarranties.PACHA_IBIZA]: () => new TwentyPercentWarrantyStrategy(),
    [ECustomOrganizationWarranties.REGGAETON_MILLENNIAL_FESTIVAL]: () => new TwentyPercentWarrantyStrategy(),
  };

  const strategy = match[organizationId.value as ECustomOrganizationWarranties];

  return strategy ? strategy() : new DefaultPercentWarrantyStrategy();
};
