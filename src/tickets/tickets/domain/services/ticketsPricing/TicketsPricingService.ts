import { left, right } from '@discocil/fv-domain-library/domain';
import {
  GetTicketsPricingService, type TicketsPricingRequest, type TicketsPricingResponse,
} from '@discocil/fv-pricing-library/ticketing';

import { PricingLibraryError } from '@/cross-cutting/domain/errors/PricingLibraryError';

import type { Either } from '@discocil/fv-domain-library/domain';

type TicketsPricingServiceResponse = Either<PricingLibraryError, TicketsPricingResponse>;

export class TicketsPricingService {
  static execute(input: TicketsPricingRequest): TicketsPricingServiceResponse {
    try {
      const ticketsPricing = GetTicketsPricingService.execute(input);

      return right(ticketsPricing);
    } catch (error: unknown) {
      return left(PricingLibraryError.build({
        context: this.constructor.name,
        data: { input, error },
      }));
    }
  }
}
