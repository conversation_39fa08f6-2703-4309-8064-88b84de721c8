import { left, right } from '@discocil/fv-domain-library/domain';
import { GetPaymentTotalsService } from '@discocil/fv-pricing-library/ticketing';

import { PricingLibraryError } from '@/cross-cutting/domain/errors/PricingLibraryError';

import type { Either } from '@discocil/fv-domain-library/domain';
import type { PaymentTotalsRequest, PaymentTotalsResponse } from '@discocil/fv-pricing-library/ticketing';

type TicketsPaymentTotalsServiceResponse = Either<PricingLibraryError, PaymentTotalsResponse>;

export class TicketsPaymentTotalsService {
  static execute(input: PaymentTotalsRequest): TicketsPaymentTotalsServiceResponse {
    try {
      const ticketsPaymentTotals = GetPaymentTotalsService.execute(input);

      return right(ticketsPaymentTotals);
    } catch (error: unknown) {
      return left(PricingLibraryError.build({
        context: this.constructor.name,
        data: { input, error },
      }));
    }
  }
}
