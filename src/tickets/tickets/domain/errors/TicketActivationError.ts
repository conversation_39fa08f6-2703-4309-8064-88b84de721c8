import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { ErrorMethodRequest } from '@/cross-cutting/domain/errors/ErrorRequest';

export class TicketActivationError extends FvError {
  static badSaleType(request: ErrorMethodRequest): TicketActivationError {
    const exceptionMessage = 'The ticket activation is not possible due sale type different than RRPP';
    const cause = EErrorKeys.TICKET_ACTIVATION_NOT_POSSIBLE;

    const {
      context, data, error, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      cause,
      error,
      data,
      target,
    );
  }

  static alreadyActive(request: ErrorMethodRequest): TicketActivationError {
    const exceptionMessage = 'Ticket already active';
    const cause = EErrorKeys.TICKET_ACTIVATION_NOT_POSSIBLE;

    const {
      context, data, error, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      cause,
      error,
      data,
      target,
    );
  }

  static isAnnulled(request: ErrorMethodRequest): TicketActivationError {
    const exceptionMessage = 'Ticket annulled';
    const cause = EErrorKeys.TICKET_ACTIVATION_NOT_POSSIBLE;

    const {
      context, data, error, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      cause,
      error,
      data,
      target,
    );
  }
}
