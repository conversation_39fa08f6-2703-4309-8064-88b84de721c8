import { Maybe } from '@discocil/fv-domain-library/domain';

import { Ticket } from '@/tickets/tickets/domain/entities/TicketEntity';

import type { TDiscountCodeExchangedPrimitive } from '@/tickets/discountCodes/domain/entities/DiscountCode';
import type {
  TicketCommissionsPrimitives, TicketEither, TicketPrimitives, TicketSupplementPrimitives,
} from '@/tickets/tickets/domain/entities/TicketEntity';
import type { PurchaseLimitPrimitives } from '@/tickets/ticketsTypes/domain/value-objects/PurchaseLimitValueObject';
import type {
  ECountryCode, EDocumentType, EGender, EMerchantNames,
} from '@discocil/fv-domain-library/domain';
import type { PurchaseQuantityValueObjectPrimitives } from '../value-objects/PurchaseQuantityValueObject';

export type TicketJsonPrimitives = Omit<TicketPrimitives,
  'saleId'
  | 'paymentId'
  | 'discountCode'
  | 'referrerId'
  | 'linkId'
  | 'subscriberId'
  | 'prompterId'
  | 'birthDate'
  | 'canceledAt'
  | 'entryDate'
  | 'activatedAt'
  | 'refundedAt'
  | 'revertedAt'
  | 'commissions'
  | 'warranty'
  | 'purchaseId'
  | 'orderId'
  | 'supplements'
  | 'customerCustomFeeCalculationId'
  | 'fb'
  | 'clientId'
  | 'platform'
  | 'name'
  | 'activationCode'
  | 'dni'
  | 'email'
  | 'phone'
  | 'zipCode'
  | 'gender'
  | 'personalDocumentNumber'
  | 'personalDocumentType'
  | 'birthDate'
  | 'address'
  | 'country'
  | 'nullifierId'
  | 'canceledAt'
  | 'observationsReception'
  | 'receptionistId'
  | 'receptionistAppId'
  | 'receptionistPlatform'
  | 'doorName'
  | 'entryDate'
  | 'statusInfo'
  | 'activatedAt'
  | 'processPurchaseId'
  | 'language'
  | 'country'
  | 'browser'
  | 'device'
  | 'os'
  | 'paymentMerchantName'
  | 'idx'
  | 'refundedAt'
  | 'revertedAt'
  | 'groupCode'
> & {
  dni: string | null;
  email: string | null;
  phone: string | null;
  zipCode: string | null;
  gender: EGender | null;
  personalDocumentNumber: string | null;
  personalDocumentType: EDocumentType | null;
  address: string | null;
  receptionistId: string | null;
  receptionistAppId: string | null;
  nullifierId: string | null;
  observationsReception: string | null;
  receptionistPlatform: string | null;
  doorName: string | null;
  statusInfo: string | null;
  processPurchaseId: string | null;
  language: string | null;
  country: ECountryCode | null;
  browser: string | null;
  device: string | null;
  os: string | null;
  paymentMerchantName: EMerchantNames | null;
  idx: string | null;
  groupCode: string | null;
  activationCode: string | null;
  name: string | null;
  platform: string | null;
  clientId: string | null;
  referrerId: string | null;
  saleId: string | null;
  paymentId: string | null;
  linkId: string | null;
  subscriberId: string | null;
  discountCode: Omit<TDiscountCodeExchangedPrimitive, 'exchangedId'> & {
    exchangedId: string | null;
  } | null;
  prompterId: string | null;
  birthDate: Date | null;
  canceledAt: Date | null;
  entryDate: Date | null;
  activatedAt: Date | null;
  refundedAt: Date | null;
  revertedAt: Date | null;
  commissions: TicketCommissionsPrimitives | null;
  warranty: {
    isFixed: boolean | null;
    fixedPrice: number | null;
    percentage: number | null;
    hours: number | null;
    total: number | null;
    cost: number | null;
  } | null;
  supplements: (Omit<TicketSupplementPrimitives, 'purchaseQuantity' | 'redemptionDeadlineValue'> & {
    purchaseQuantity: (Omit<PurchaseQuantityValueObjectPrimitives, 'purchaseLimit'> & {
      purchaseLimit: Omit<PurchaseLimitPrimitives, 'maxQuantity'> & {
        maxQuantity: number | null;
      };
    }) | null;
    redemptionDeadlineValue: number | null;
  })[];
  purchaseId: string | null;
  orderId: string | null;
  customerCustomFeeCalculationId: string | null;
  fb: {
    fbp: string | null;
    fbc: string | null;
    cookiesAnalytics: boolean;
  };
};

export class TicketJsonMapper {
  static toEntity(jsonPrimitives: TicketJsonPrimitives): TicketEither {
    return Ticket.build({
      ...jsonPrimitives,
      dni: Maybe.fromValue(jsonPrimitives.dni),
      email: Maybe.fromValue(jsonPrimitives.email),
      phone: Maybe.fromValue(jsonPrimitives.phone),
      zipCode: Maybe.fromValue(jsonPrimitives.zipCode),
      gender: Maybe.fromValue(jsonPrimitives.gender),
      personalDocumentNumber: Maybe.fromValue(jsonPrimitives.personalDocumentNumber),
      personalDocumentType: Maybe.fromValue(jsonPrimitives.personalDocumentType),
      address: Maybe.fromValue(jsonPrimitives.address),
      receptionistId: Maybe.fromValue(jsonPrimitives.receptionistId),
      receptionistAppId: Maybe.fromValue(jsonPrimitives.receptionistAppId),
      nullifierId: Maybe.fromValue(jsonPrimitives.nullifierId),
      observationsReception: Maybe.fromValue(jsonPrimitives.observationsReception),
      receptionistPlatform: Maybe.fromValue(jsonPrimitives.receptionistPlatform),
      doorName: Maybe.fromValue(jsonPrimitives.doorName),
      statusInfo: Maybe.fromValue(jsonPrimitives.statusInfo),
      processPurchaseId: Maybe.fromValue(jsonPrimitives.processPurchaseId),
      language: Maybe.fromValue(jsonPrimitives.language),
      country: Maybe.fromValue(jsonPrimitives.country),
      browser: Maybe.fromValue(jsonPrimitives.browser),
      device: Maybe.fromValue(jsonPrimitives.device),
      os: Maybe.fromValue(jsonPrimitives.os),
      paymentMerchantName: Maybe.fromValue(jsonPrimitives.paymentMerchantName),
      idx: Maybe.fromValue(jsonPrimitives.idx),
      activationCode: Maybe.fromValue(jsonPrimitives.activationCode),
      name: Maybe.fromValue(jsonPrimitives.name),
      platform: Maybe.fromValue(jsonPrimitives.platform),
      clientId: Maybe.fromValue(jsonPrimitives.clientId),
      groupCode: Maybe.fromValue(jsonPrimitives.groupCode),
      saleId: Maybe.fromValue(jsonPrimitives.saleId),
      paymentId: Maybe.fromValue(jsonPrimitives.paymentId),
      discountCode: jsonPrimitives.discountCode
        ? Maybe.some({
          ...jsonPrimitives.discountCode,
          exchangedId: Maybe.fromValue(jsonPrimitives.discountCode.exchangedId),
        })
        : Maybe.none(),
      referrerId: Maybe.fromValue(jsonPrimitives.referrerId),
      linkId: Maybe.fromValue(jsonPrimitives.linkId),
      subscriberId: Maybe.fromValue(jsonPrimitives.subscriberId),
      prompterId: Maybe.fromValue(jsonPrimitives.prompterId),
      birthDate: Maybe.fromValue(jsonPrimitives.birthDate),
      canceledAt: Maybe.fromValue(jsonPrimitives.canceledAt),
      entryDate: Maybe.fromValue(jsonPrimitives.entryDate),
      activatedAt: Maybe.fromValue(jsonPrimitives.activatedAt),
      refundedAt: Maybe.fromValue(jsonPrimitives.refundedAt),
      revertedAt: Maybe.fromValue(jsonPrimitives.revertedAt),
      commissions: Maybe.fromValue(jsonPrimitives.commissions),
      warranty: jsonPrimitives.warranty
        ? Maybe.some({
          isFixed: Maybe.fromValue(jsonPrimitives.warranty.isFixed),
          fixedPrice: Maybe.fromValue(jsonPrimitives.warranty.fixedPrice),
          percentage: Maybe.fromValue(jsonPrimitives.warranty.percentage),
          hours: Maybe.fromValue(jsonPrimitives.warranty.hours),
          total: Maybe.fromValue(jsonPrimitives.warranty.total),
          cost: Maybe.fromValue(jsonPrimitives.warranty.cost),
        })
        : Maybe.none(),
      purchaseId: Maybe.fromValue(jsonPrimitives.purchaseId),
      orderId: Maybe.fromValue(jsonPrimitives.orderId),
      customerCustomFeeCalculationId: Maybe.fromValue(jsonPrimitives.customerCustomFeeCalculationId),
      supplements: jsonPrimitives.supplements.map((supplement) => {
        const purchaseQuantity = supplement.purchaseQuantity
          ? Maybe.some({
            quantity: supplement.purchaseQuantity.quantity,
            purchaseLimit: {
              ...supplement.purchaseQuantity.purchaseLimit,
              maxQuantity: Maybe.fromValue(supplement.purchaseQuantity.purchaseLimit.maxQuantity),
            },
          })
          : Maybe.none<PurchaseQuantityValueObjectPrimitives>();

        return {
          ...supplement,
          purchaseQuantity,
          redemptionDeadlineValue: Maybe.fromValue(supplement.redemptionDeadlineValue),
        };
      }),
      fb: {
        fbp: Maybe.fromValue(jsonPrimitives.fb.fbp),
        fbc: Maybe.fromValue(jsonPrimitives.fb.fbc),
        cookiesAnalytics: jsonPrimitives.fb.cookiesAnalytics,
      },
    });
  }

  static toJson(ticket: Ticket): TicketJsonPrimitives {
    return {
      id: ticket.id,
      applicationId: ticket.applicationId,
      organizationId: ticket.organizationId,
      eventId: ticket.eventId,
      typeId: ticket.typeId,
      type: ticket.type,
      referrerId: ticket.referrerId.fold(() => null, value => value),
      nominative: ticket.nominative,
      prompterId: ticket.prompterId.fold(() => null, value => value),
      organizationAssignedId: ticket.organizationAssignedId,
      linkId: ticket.linkId.fold(() => null, value => value),
      subscriberId: ticket.subscriberId.fold(() => null, value => value),
      groups: ticket.groups,
      clientId: ticket.clientId.fold(() => null, value => value),
      remarketing: ticket.remarketing,
      platform: ticket.platform.fold(() => null, value => value),
      name: ticket.name.fold(() => null, value => value),
      targeted: ticket.targeted,
      getIn: ticket.getIn,
      option: ticket.option,
      answers: ticket.answers,
      supplements: ticket.supplements.map(supplement => ({
        ...supplement,
        purchaseQuantity: supplement.purchaseQuantity.fold(() => null, purchaseQuantity => ({
          quantity: purchaseQuantity.quantity,
          purchaseLimit: {
            ...purchaseQuantity.purchaseLimit,
            maxQuantity: purchaseQuantity.purchaseLimit.maxQuantity.fold(() => null, value => value),
          },
        })),
        redemptionDeadlineValue: supplement.redemptionDeadlineValue.fold(() => null, value => value.toSeconds()),
      })),
      supplementsPrice: ticket.supplementsPrice,
      code: ticket.code,
      activationCode: ticket.activationCode.fold(() => null, value => value),
      price: ticket.price,
      importTotal: ticket.importTotal,
      discountCode: ticket.discountCode.fold(() => null, discountCode => ({
        ...discountCode,
        exchangedId: discountCode.exchangedId.fold(() => null, value => value),
      })),
      discountAmountApplied: ticket.discountAmountApplied,
      collected: ticket.collected,
      dni: ticket.dni.fold(() => null, value => value),
      email: ticket.email.fold(() => null, value => value),
      phone: ticket.phone.fold(() => null, value => value),
      zipCode: ticket.zipCode.fold(() => null, value => value),
      gender: ticket.gender.fold(() => null, value => value),
      personalDocumentNumber: ticket.personalDocumentNumber.fold(() => null, value => value),
      personalDocumentType: ticket.personalDocumentType.fold(() => null, value => value),
      birthDate: ticket.birthDate.fold(() => null, value => value),
      quality: ticket.quality,
      annulled: ticket.annulled,
      nullifierId: ticket.nullifierId.fold(() => null, value => value),
      canceledAt: ticket.canceledAt.fold(() => null, value => value),
      paid: ticket.paid,
      observationsReception: ticket.observationsReception.fold(() => null, value => value),
      receptionistId: ticket.receptionistId.fold(() => null, value => value),
      receptionistAppId: ticket.receptionistAppId.fold(() => null, value => value),
      receptionistPlatform: ticket.receptionistPlatform.fold(() => null, value => value),
      receptionistType: ticket.receptionistType,
      doorName: ticket.doorName.fold(() => null, value => value),
      entryDate: ticket.entryDate.fold(() => null, value => value),
      state: ticket.state,
      statusInfo: ticket.statusInfo.fold(() => null, value => value),
      resentSms: ticket.resentSms,
      activatedAt: ticket.activatedAt.fold(() => null, value => value),
      commissions: ticket.commissions.fold(() => null, value => value),
      processPurchaseId: ticket.processPurchaseId.fold(() => null, value => value),
      language: ticket.language.fold(() => null, value => value),
      country: ticket.country.fold(() => null, value => value),
      browser: ticket.browser.fold(() => null, value => value),
      device: ticket.device.fold(() => null, value => value),
      os: ticket.os.fold(() => null, value => value),
      saleId: ticket.saleId.fold(() => null, value => value),
      saleType: ticket.saleType,
      paymentId: ticket.paymentId.fold(() => null, value => value),
      paymentCurrency: ticket.paymentCurrency,
      paymentProvider: ticket.paymentProvider,
      paymentMerchantName: ticket.paymentMerchantName.fold(() => null, value => value),
      idx: ticket.idx.fold(() => null, value => value),
      purchaseId: ticket.purchaseId.fold(() => null, value => value),
      orderId: ticket.orderId.fold(() => null, value => value),
      customerCustomFeeCalculationId: ticket.customerCustomFeeCalculationId.fold(() => null, value => value),
      customerCustomTaxAmount: ticket.customerCustomTaxAmount,
      customerCustomFeeAmount: ticket.customerCustomFeeAmount,
      metadata: ticket.metadata,
      refunded: ticket.refunded,
      refundedAt: ticket.refundedAt.fold(() => null, value => value),
      refunds: ticket.refunds,
      nameChange: ticket.nameChange,
      reversed: ticket.reversed,
      revertedAt: ticket.revertedAt.fold(() => null, value => value),
      groupCode: ticket.groupCode.fold(() => null, value => value),
      userWebData: ticket.userWebData,
      address: ticket.address.fold(() => null, value => value),
      sitting: ticket.sitting,
      warranty: ticket.warranty.fold(() => null, warranty => ({
        fixedPrice: warranty.fixedPrice.fold(() => null, value => value),
        total: warranty.total.fold(() => null, value => value),
        cost: warranty.cost.fold(() => null, value => value),
        isFixed: warranty.isFixed.fold(() => null, value => value),
        percentage: warranty.percentage.fold(() => null, value => value),
        hours: warranty.hours.fold(() => null, value => value),
      })),
      ingoodInsurance: ticket.ingoodInsurance,
      boxOfficePoiid: ticket.boxOfficePoiid,
      boxOfficeSellerDeviceId: ticket.boxOfficeSellerDeviceId,
      boxOfficeSellerId: ticket.boxOfficeSellerId,
      fb: {
        ...ticket.fb,
        fbp: ticket.fb.fbp.fold(() => null, value => value),
        fbc: ticket.fb.fbc.fold(() => null, value => value),
      },
      shouldEmitPurchaseEvent: ticket.shouldEmitPurchaseEvent,
      createdAt: ticket.createdAt,
      createdBy: ticket.createdBy,
      updatedAt: ticket.updatedAt,
      updatedBy: ticket.updatedBy,
      removedAt: ticket.removedAt,
      removedBy: ticket.removedBy,
    };
  }
}
