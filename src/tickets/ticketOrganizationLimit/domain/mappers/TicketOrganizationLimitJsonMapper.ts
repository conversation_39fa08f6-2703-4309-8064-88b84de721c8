import { TicketOrganizationLimit } from '../entities/TicketOrganizationLimitEntity';

import type { TicketOrganizationLimitEither, TicketOrganizationLimitPrimitives } from '../entities/TicketOrganizationLimitEntity';

export class TicketOrganizationLimitJsonMapper {
  static toEntity(primitives: TicketOrganizationLimitPrimitives): TicketOrganizationLimitEither {
    return TicketOrganizationLimit.build(primitives);
  }

  static toJson(ticketOrganizationLimit: TicketOrganizationLimit): TicketOrganizationLimitPrimitives {
    return {
      id: ticketOrganizationLimit.id,
      organizationId: ticketOrganizationLimit.organizationId,
      organizationLimitedId: ticketOrganizationLimit.organizationLimitedId,
      typeId: ticketOrganizationLimit.typeId,
      eventId: ticketOrganizationLimit.eventId,
      limit: ticketOrganizationLimit.limit,
      createdAt: ticketOrganizationLimit.createdAt,
      createdBy: ticketOrganizationLimit.createdBy,
      updatedAt: ticketOrganizationLimit.updatedAt,
      updatedBy: ticketOrganizationLimit.updatedBy,
      removedAt: ticketOrganizationLimit.removedAt,
      removedBy: ticketOrganizationLimit.removedBy,
    };
  }
}
