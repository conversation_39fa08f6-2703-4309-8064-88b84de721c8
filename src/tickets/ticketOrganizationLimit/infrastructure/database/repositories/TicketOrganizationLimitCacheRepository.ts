import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { TicketOrganizationLimitJsonMapper } from '@/tickets/ticketOrganizationLimit/domain/mappers/TicketOrganizationLimitJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { TicketOrganizationLimitRepository } from '@/tickets/ticketOrganizationLimit/domain/contracts/TicketOrganizationLimitRepository';
import type {
  TicketOrganizationLimit,
  TicketOrganizationLimitEither,
  TicketOrganizationLimitPrimitives,
  TicketOrganizationLimits,
  TicketOrganizationLimitsEither,
} from '@/tickets/ticketOrganizationLimit/domain/entities/TicketOrganizationLimitEntity';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class TicketOrganizationLimitCacheRepository extends CacheRepository implements TicketOrganizationLimitRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: TicketOrganizationLimitRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<TicketOrganizationLimitEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: TicketOrganizationLimitPrimitives = JSON.parse(cacheHit);
      const entityOrError = TicketOrganizationLimitJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        TicketOrganizationLimitJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async search(criteria: Criteria): Promise<TicketOrganizationLimitsEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: TicketOrganizationLimitPrimitives[] = JSON.parse(cacheHit);
      const ticketOrganizationLimits: TicketOrganizationLimits = new Map<IdPrimitive, TicketOrganizationLimit>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = TicketOrganizationLimitJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        ticketOrganizationLimits.set(_primitive.id, entityOrError.value);
      }

      return right(ticketOrganizationLimits);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const ticketOrganizationLimits = repositoryResult.value;
      const ticketOrganizationLimitsLength = ticketOrganizationLimits.size;
      let ticketOrganizationLimitsCacheIndex = 0;
      const jsonTicketOrganizationLimits = new Array<TicketOrganizationLimitPrimitives>(ticketOrganizationLimitsLength);

      if (ticketOrganizationLimitsLength > 0) {
        for (const _event of ticketOrganizationLimits.values()) {
          jsonTicketOrganizationLimits[ticketOrganizationLimitsCacheIndex] = TicketOrganizationLimitJsonMapper.toJson(_event);

          ticketOrganizationLimitsCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonTicketOrganizationLimits, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}
