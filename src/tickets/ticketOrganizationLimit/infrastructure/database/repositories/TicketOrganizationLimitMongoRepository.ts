import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { Ticket } from '@/tickets/tickets/domain/entities/TicketEntity';

import { TicketOrganizationLimitMapper } from '../mappers/TicketOrganizationLimitMapper';
import { ticketOrganizationLimitSchema } from '../schemas/TicketOrganizationLimitSchema';

import type { TicketOrganizationLimitRepository } from '@/tickets/ticketOrganizationLimit/domain/contracts/TicketOrganizationLimitRepository';
import type {
  TicketOrganizationLimit,
  TicketOrganizationLimitEither,
  TicketOrganizationLimitKeys,
  TicketOrganizationLimits,
  TicketOrganizationLimitsEither,
} from '@/tickets/ticketOrganizationLimit/domain/entities/TicketOrganizationLimitEntity';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { TicketOrganizationLimitSchemaType } from '../schemas/TicketOrganizationLimitSchemaType';

type PropertiesMapper = Partial<Record<TicketOrganizationLimitKeys, keyof TicketOrganizationLimitSchemaType>>;

export class TicketOrganizationLimitMongoRepository extends MongoRepository implements TicketOrganizationLimitRepository {
  protected getSchema(): Schema {
    return new Schema(ticketOrganizationLimitSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'ticket_organization_limits';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      typeId: 'ticket_type_id',
      limit: 'limit',
      organizationLimitedId: 'negocio_limited_id',
      removedAt: 'removed_at',
      eventId: 'event_id',
    };
  }

  async find(criteria: Criteria): Promise<TicketOrganizationLimitEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<TicketOrganizationLimitSchemaType>();

    return queryResponse
      ? TicketOrganizationLimitMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: Ticket.name }));
  }

  async search(criteria: Criteria): Promise<TicketOrganizationLimitsEither> {
    const response: TicketOrganizationLimits = new Map<IdPrimitive, TicketOrganizationLimit>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<TicketOrganizationLimitSchemaType[]>();

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const ticketOrganizationLimitOrError = TicketOrganizationLimitMapper.execute(model);

      if (ticketOrganizationLimitOrError.isLeft()) {
        return left(ticketOrganizationLimitOrError.value);
      }

      const ticketOrganizationLimit = ticketOrganizationLimitOrError.value;

      response.set(ticketOrganizationLimit.id, ticketOrganizationLimit);
    }

    return right(response);
  }
}
