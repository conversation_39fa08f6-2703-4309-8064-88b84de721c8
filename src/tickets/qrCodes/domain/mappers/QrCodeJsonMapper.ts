import { QrCode } from '../entities/QrCodeEntity';

import type { QrC<PERSON>Either, QrCodePrimitives } from '../entities/QrCodeEntity';

export class QrCodeJsonMapper {
  static toEntity(primitives: QrCodePrimitives): QrCodeEither {
    return QrCode.build(primitives);
  }

  static toJson(qrCode: QrCode): QrCodePrimitives {
    return {
      id: qrCode.id,
      organizationId: qrCode.organizationId,
      code: qrCode.code,
      isUsed: qrCode.isUsed,
      slug: qrCode.slug,
      service: qrCode.service,
      product: qrCode.product,
      externalId: qrCode.externalId,
      resourcesIds: qrCode.resourcesIds,
      createdAt: qrCode.createdAt,
      createdBy: qrCode.createdBy,
      updatedAt: qrCode.updatedAt,
      updatedBy: qrCode.updatedBy,
      removedAt: qrCode.removedAt,
      removedBy: qrCode.removedBy,
    };
  }
}
