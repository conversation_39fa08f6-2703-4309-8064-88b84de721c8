import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { QrCodeJsonMapper } from '@/tickets/qrCodes/domain/mappers/QrCodeJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { QrCodeRepository } from '@/tickets/qrCodes/domain/contracts/QrCodeRepository';
import type {
  QrCode,
  QrCodeEither,
  QrCodePrimitives,
  QrCodes,
  QrCodesEither,
} from '@/tickets/qrCodes/domain/entities/QrCodeEntity';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive, UniqueEntityID } from '@discocil/fv-domain-library/domain';

export class QrCodeCacheRepository extends CacheRepository implements QrCodeRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: QrCodeRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async addResource(qrCode: QrCode, resourceId: UniqueEntityID): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.addResource(qrCode, resourceId);
  }

  async find(criteria: Criteria): Promise<QrCodeEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: QrCodePrimitives = JSON.parse(cacheHit);
      const entityOrError = QrCodeJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        QrCodeJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async save(qrCode: QrCode): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.save(qrCode);
  }

  async saveMany(qrCodes: QrCodes): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.saveMany(qrCodes);
  }

  async search(criteria: Criteria): Promise<QrCodesEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: QrCodePrimitives[] = JSON.parse(cacheHit);
      const qrCodes: QrCodes = new Map<IdPrimitive, QrCode>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = QrCodeJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        qrCodes.set(_primitive.id, entityOrError.value);
      }

      return right(qrCodes);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const qrCodes = repositoryResult.value;
      const qrCodesLength = qrCodes.size;
      let qrCodesCacheIndex = 0;
      const jsonQrCodes = new Array<QrCodePrimitives>(qrCodesLength);

      if (qrCodesLength > 0) {
        for (const _event of qrCodes.values()) {
          jsonQrCodes[qrCodesCacheIndex] = QrCodeJsonMapper.toJson(_event);

          qrCodesCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonQrCodes, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}
