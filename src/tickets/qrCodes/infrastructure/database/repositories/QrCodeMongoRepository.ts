import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { QrCode } from '@/tickets/qrCodes/domain/entities/QrCodeEntity';

import { QrCodeMapper } from '../mappers/QrCodeMapper';
import { QrCodeSchemaMapper } from '../mappers/QrCodeSchemaMapper';
import { qrCodeSchema } from '../schemas/QrCodeSchema';

import type { QrCodeRepository } from '@/tickets/qrCodes/domain/contracts/QrCodeRepository';
import type {
  QrCodeEither,
  QrCodeKeys,
  QrCodes,
  QrCodesEither,
} from '@/tickets/qrCodes/domain/entities/QrCodeEntity';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive, UniqueEntityID } from '@discocil/fv-domain-library/domain';
import type { QrCodeSchemaType } from '../schemas/QrCodeSchemaType';

type PropertiesMapper = Partial<Record<QrCodeKeys, keyof QrCodeSchemaType>>;

export class QrCodeMongoRepository extends MongoRepository implements QrCodeRepository {
  protected getSchema(): Schema {
    return new Schema(qrCodeSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'qr_codes';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      isUsed: 'used',
      slug: 'slug',
      removedAt: 'removed_at',
    };
  }

  async addResource(qrCode: QrCode, resourceId: UniqueEntityID): Promise<void> {
    const filter = { _id: qrCode.id };

    const update = { $addToSet: { recursos_ids: resourceId.value } };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update);
  }

  async find(criteria: Criteria): Promise<QrCodeEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<QrCodeSchemaType>();

    return queryResponse ? QrCodeMapper.execute(queryResponse) : left(NotFoundError.build({ context: this.constructor.name, target: QrCode.name }));
  }

  async save(qrCode: QrCode): Promise<void> {
    const toSave = QrCodeSchemaMapper.execute(qrCode);

    const filter = { _id: qrCode.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(qrCodes: QrCodes): Promise<void> {
    const promises = [...qrCodes.values()].map(async (qrCode: QrCode) => this.save(qrCode));

    await Promise.all(promises);
  }

  async search(criteria: Criteria): Promise<QrCodesEither> {
    const response = new Map<IdPrimitive, QrCode>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<QrCodeSchemaType[]>();

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const qrCodeResult = QrCodeMapper.execute(model);

      if (qrCodeResult.isLeft()) {
        return left(qrCodeResult.value);
      }

      const qrCode = qrCodeResult.value;

      response.set(qrCode.id, qrCode);
    }

    return right(response);
  }
}
