import { type Criteria } from '@discocil/fv-criteria-converter-library/domain';
import { left, NotFoundError } from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { LegacyPass } from '@/legacyPasses/legacyPass/domain/entities/LegacyPass';
import { LegacyPassMapper } from '@/legacyPasses/legacyPass/infrastructure/database/mappers/LegacyPassMapper';
import { LegacyPassSchemaMapper } from '@/legacyPasses/legacyPass/infrastructure/database/mappers/LegacyPassSchemaMapper';
import { legacyPassSchema } from '@/legacyPasses/legacyPass/infrastructure/database/schemas/LegacyPassSchema';

import type { LegacyPassRepository } from '@/legacyPasses/legacyPass/domain/contracts/LegacyPassRepository';
import type { LegacyPassEither, LegacyPasses } from '@/legacyPasses/legacyPass/domain/entities/LegacyPass';
import type { LegacyPassSchemaType } from '@/legacyPasses/legacyPass/infrastructure/database/schemas/LegacyPassSchemaType';

export class LegacyPassMongoRepository extends MongoRepository implements LegacyPassRepository {
  protected getSchema(): Schema {
    return new Schema(legacyPassSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'permanentes';
  }

  async find(criteria: Criteria): Promise<LegacyPassEither> {
    criteria.setPage(1).setPerPage(1);

    const queryResponse = (await this.customQueryFinder<LegacyPassSchemaType[]>(criteria)).shift();

    return queryResponse
      ? LegacyPassMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: LegacyPass.name }));
  }

  async save(legacyPass: LegacyPass): Promise<void> {
    const toSave = LegacyPassSchemaMapper.execute(legacyPass);

    const filter = { _id: legacyPass.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(legacyPasses: LegacyPasses): Promise<void> {
    const connection = await this.getConnection();
    const models: Array<Partial<LegacyPassSchemaType>> = [];

    legacyPasses.forEach((legacyPass: LegacyPass) => models.push(LegacyPassSchemaMapper.execute(legacyPass)));

    await connection.insertMany(models);
  }

  private async customQueryFinder<T>(criteria: Criteria): Promise<T> {
    const connection = await this.getConnection();
    const pipeline = this.criteriaConverter.toPipeline(criteria);

    return connection.aggregate([
      ...pipeline,
      {
        $lookup: {
          from: 'eventos',
          let: { eventId: '$evento_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$eventId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'event',
        },
      },
      {
        $lookup: {
          from: 'negocios',
          let: { organizationId: '$negocio_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$organizationId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'organization',
        },
      },
      {
        $project: {
          main: '$$ROOT',
          eventObj: { $arrayElemAt: ['$event', 0] },
          organizationObj: { $arrayElemAt: ['$organization', 0] },
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              '$main',
              {
                event: { currency: { $ifNull: ['$eventObj.currency', null] } },
                organization: { currency: { $ifNull: ['$organizationObj.currency', null] } },
              },
            ],
          },
        },
      },
    ]) as unknown as T;
  }
}
