import { left, NotFoundError } from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { LegacyPassStore } from '@/legacyPasses/legacyPassStore/domain/entities/LegacyPassStore';
import { LegacyPassStoreMapper } from '@/legacyPasses/legacyPassStore/infrastructure/database/mappers/LegacyPassStoreMapper';
import { LegacyPassStoreSchemaMapper } from '@/legacyPasses/legacyPassStore/infrastructure/database/mappers/LegacyPassStoreSchemaMapper';
import { legacyPassStoreSchema } from '@/legacyPasses/legacyPassStore/infrastructure/database/schemas/LegacyPassStoreSchema';

import type { LegacyPassStoreRepository } from '@/legacyPasses/legacyPassStore/domain/contracts/LegacyPassStoreRepository';
import type { LegacyPassStoreEither, LegacyPassStorees } from '@/legacyPasses/legacyPassStore/domain/entities/LegacyPassStore';
import type { LegacyPassStoreSchemaType } from '@/legacyPasses/legacyPassStore/infrastructure/database/schemas/LegacyPassStoreSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export class LegacyPassStoreMongoRepository extends MongoRepository implements LegacyPassStoreRepository {
  protected getSchema(): Schema {
    return new Schema(legacyPassStoreSchema, { collection: this.getModel() });
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'permanentes_almacen';
  }

  async find(criteria: Criteria): Promise<LegacyPassStoreEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<LegacyPassStoreSchemaType>();

    return queryResponse
      ? LegacyPassStoreMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: LegacyPassStore.name }));
  }

  async save(legacyPassStore: LegacyPassStore): Promise<void> {
    const toSave = LegacyPassStoreSchemaMapper.execute(legacyPassStore);

    const filter = { _id: legacyPassStore.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(legacyPassStore: LegacyPassStorees): Promise<void> {
    const connection = await this.getConnection();
    const models = legacyPassStore.map((legacyPassStore: LegacyPassStore) => LegacyPassStoreSchemaMapper.execute(legacyPassStore));

    await connection.insertMany(models);
  }
}
