import { left, NotFoundError } from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { LegacyPassType } from '@/legacyPasses/legacyPassTypes/domain/entities/LegacyPassType';
import { LegacyPassTypeMapper } from '@/legacyPasses/legacyPassTypes/infrastructure/database/mappers/LegacyPassTypeMapper';
import { LegacyPassTypeSchemaMapper } from '@/legacyPasses/legacyPassTypes/infrastructure/database/mappers/LegacyPassTypeSchemaMapper';
import { legacyPassTypeSchema } from '@/legacyPasses/legacyPassTypes/infrastructure/database/schemas/LegacyPassTypeSchema';

import type { LegacyPassTypeRepository } from '@/legacyPasses/legacyPassTypes/domain/contracts/LegacyPassTypeRepository';
import type { LegacyPassTypeEither, LegacyPassTypes } from '@/legacyPasses/legacyPassTypes/domain/entities/LegacyPassType';
import type { LegacyPassTypeSchemaType } from '@/legacyPasses/legacyPassTypes/infrastructure/database/schemas/LegacyPassTypeSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export class LegacyPassTypeMongoRepository extends MongoRepository implements LegacyPassTypeRepository {
  protected getSchema(): Schema {
    return new Schema(legacyPassTypeSchema, { collection: this.getModel() });
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'permanentes_tarifa';
  }

  async find(criteria: Criteria): Promise<LegacyPassTypeEither> {
    criteria.setPage(1).setPerPage(1);

    const queryResponse = (await this.customQueryFinder<LegacyPassTypeSchemaType[]>(criteria)).shift();

    return queryResponse
      ? LegacyPassTypeMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: LegacyPassType.name }));
  }

  async save(legacyPassType: LegacyPassType): Promise<void> {
    const toSave = LegacyPassTypeSchemaMapper.execute(legacyPassType);

    const filter = { _id: legacyPassType.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(legacyPassType: LegacyPassTypes): Promise<void> {
    const connection = await this.getConnection();
    const models: Array<Partial<LegacyPassType>> = [];

    legacyPassType.forEach((legacyPassType: LegacyPassType) => models.push(LegacyPassTypeSchemaMapper.execute(legacyPassType)));

    await connection.insertMany(models);
  }

  private async customQueryFinder<T>(criteria: Criteria): Promise<T> {
    const connection = await this.getConnection();
    const pipeline = this.criteriaConverter.toPipeline(criteria);

    return connection.aggregate([
      ...pipeline,
      {
        $lookup: {
          from: 'eventos',
          let: { eventId: '$evento_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$eventId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'event',
        },
      },
      {
        $lookup: {
          from: 'negocios',
          let: { organizationId: '$negocio_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$organizationId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'organization',
        },
      },
      {
        $project: {
          main: '$$ROOT',
          eventObj: { $arrayElemAt: ['$event', 0] },
          organizationObj: { $arrayElemAt: ['$organization', 0] },
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              '$main',
              {
                event: { currency: { $ifNull: ['$eventObj.currency', null] } },
                organization: { currency: { $ifNull: ['$organizationObj.currency', null] } },
              },
            ],
          },
        },
      },
    ]) as unknown as T;
  }
}
