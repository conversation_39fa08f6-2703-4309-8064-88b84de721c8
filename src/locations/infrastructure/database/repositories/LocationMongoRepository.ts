import {
  left,
  NotFoundError,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { LocationEntity } from '@/locations/domain/entities/LocationEntity';
import { LocationAliasCriteriaMother } from '@/locations/domain/filters/LocationAliasCriteriaMother';
import { LocationMapper } from '@/locations/infrastructure/database/mappers/LocationMapper';
import { locationSchema } from '@/locations/infrastructure/database/schemas/LocationSchema';

import { LocationAliasRepositoryMongo } from './LocationAliasRepository';

import type { LocationRepository } from '@/locations/domain/contracts/LocationRepository';
import type { LocationEither, LocationsEither } from '@/locations/domain/entities/LocationEntity';
import type { LocationSchemaType } from '@/locations/infrastructure/database/schemas/LocationSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

type PropertiesMapper = Partial<Record<keyof LocationEntity, keyof LocationSchemaType>>;

export class LocationMongoRepository extends MongoRepository implements LocationRepository {
  private readonly aliasRepository = new LocationAliasRepositoryMongo(this.connection);

  protected getSchema(): Schema {
    return new Schema(locationSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      removedAt: 'removed_at',
      organizations: 'negocios',
    };
  }

  protected getModel(): string {
    return 'ubicaciones';
  }

  async find(criteria: Criteria): Promise<LocationEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const locationQueryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<LocationSchemaType>();

    if (!locationQueryResponse) {
      return left(NotFoundError.build({ context: this.constructor.name, target: LocationEntity.name }));
    }

    const locationUid = UniqueEntityID.build(locationQueryResponse._id);
    const organizationUids = locationQueryResponse.negocios.map(id => UniqueEntityID.build(id));

    const aliasCriteria = LocationAliasCriteriaMother.find(locationUid, organizationUids);
    const aliases = await this.aliasRepository.search(aliasCriteria);

    return LocationMapper.execute(locationQueryResponse, aliases);
  }

  async search(criteria: Criteria): Promise<LocationsEither> {
    const locations = new Map<IdPrimitive, LocationEntity>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const locationQueryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<LocationSchemaType[]>();

    if (!locationQueryResponse) {
      return left(NotFoundError.build({ context: this.constructor.name, target: LocationEntity.name }));
    }

    const locationIds = new Set<IdPrimitive>();
    const organizationIds = new Set<IdPrimitive>();

    for (const _location of locationQueryResponse) {
      locationIds.add(_location._id);

      _location.negocios.forEach((id) => {
        organizationIds.add(id);
      });
    }

    const aliasCriteria = LocationAliasCriteriaMother.search(locationIds, organizationIds);
    const aliases = await this.aliasRepository.search(aliasCriteria);

    for (const _location of locationQueryResponse) {
      const locationAliases = aliases.filter(_alias => _alias.ubicacion_id === _location._id);

      const locationResult = LocationMapper.execute(_location, locationAliases);

      if (locationResult.isLeft()) {
        return left(locationResult.value);
      }

      const location = locationResult.value;

      locations.set(location.id, location);
    }

    return right(locations);
  }
}
