import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { LocationJsonMapper } from '@/locations/domain/mappers/LocationJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { LocationRepository } from '@/locations/domain/contracts/LocationRepository';
import type {
  LocationEither,
  LocationEntity,
  Locations,
  LocationsEither,
} from '@/locations/domain/entities/LocationEntity';
import type { LocationJsonPrimitives } from '@/locations/domain/mappers/LocationJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class LocationCacheRepository extends CacheRepository implements LocationRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: LocationRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<LocationEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: LocationJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = LocationJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        LocationJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async search(criteria: Criteria): Promise<LocationsEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: LocationJsonPrimitives[] = JSON.parse(cacheHit);
      const locations: Locations = new Map<IdPrimitive, LocationEntity>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = LocationJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        locations.set(_primitive.id, entityOrError.value);
      }

      return right(locations);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const locations = repositoryResult.value;
      const locationsLength = locations.size;
      let locationsCacheIndex = 0;
      const jsonLocations = new Array<LocationJsonPrimitives>(locationsLength);

      if (locationsLength > 0) {
        for (const _location of locations.values()) {
          jsonLocations[locationsCacheIndex] = LocationJsonMapper.toJson(_location);

          locationsCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonLocations, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}
