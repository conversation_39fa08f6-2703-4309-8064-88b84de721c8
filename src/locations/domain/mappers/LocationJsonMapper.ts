import { Maybe } from '@discocil/fv-domain-library/domain';

import { LocationEntity } from '../entities/LocationEntity';

import type { TimezoneType } from '@/cross-cutting/domain/contracts/CommonContracts';
import type { IdPrimitive, Nullable } from '@discocil/fv-domain-library/domain';
import type {
  Aliases,
  LocationAlias, LocationBuildPrimitives, LocationEither,
} from '../entities/LocationEntity';

type TimezoneTypeJson = {
  readonly id: string;
  readonly name: string;
  readonly dstOffset: number | null;
  readonly rawOffset: number | null;
};

export type LocationJsonPrimitives = Omit<LocationBuildPrimitives,
  'address'
  | 'addressComplete'
  | 'number'
  | 'postalCode'
  | 'municipality'
  | 'province'
  | 'provinceShort'
  | 'region'
  | 'regionShort'
  | 'slug'
  | 'country'
  | 'countryShort'
  | 'timezone'
  | 'alias'
  | 'aliases'
> & Nullable<{
  readonly address: string;
  readonly addressComplete: string;
  readonly number: string;
  readonly postalCode: string;
  readonly municipality: string;
  readonly province: string;
  readonly provinceShort: string;
  readonly region: string;
  readonly regionShort: string;
  readonly slug: string;
  readonly country: string;
  readonly countryShort: string;
  readonly timezone: TimezoneTypeJson;
  readonly alias: string;
}> & {
  readonly aliases: [IdPrimitive, LocationAlias][];
};

export class LocationJsonMapper {
  static toEntity(primitives: LocationJsonPrimitives): LocationEither {
    const buildTimezone = (timezone: TimezoneTypeJson | null | undefined): Maybe<TimezoneType> => {
      if (!timezone) {
        return Maybe.none<TimezoneType>();
      }

      return Maybe.fromValue({
        ...timezone,
        dstOffset: Maybe.fromValue(timezone.dstOffset),
        rawOffset: Maybe.fromValue(timezone.rawOffset),
      });
    };

    const aliases: Aliases = new Map<IdPrimitive, LocationAlias>();

    primitives.aliases.forEach(([id, alias]) => aliases.set(id, alias));

    return LocationEntity.build({
      ...primitives,
      address: Maybe.fromValue(primitives.address),
      addressComplete: Maybe.fromValue(primitives.addressComplete),
      number: Maybe.fromValue(primitives.number),
      postalCode: Maybe.fromValue(primitives.postalCode),
      municipality: Maybe.fromValue(primitives.municipality),
      province: Maybe.fromValue(primitives.province),
      provinceShort: Maybe.fromValue(primitives.provinceShort),
      region: Maybe.fromValue(primitives.region),
      regionShort: Maybe.fromValue(primitives.regionShort),
      slug: Maybe.fromValue(primitives.slug),
      country: Maybe.fromValue(primitives.country),
      countryShort: Maybe.fromValue(primitives.countryShort),
      timezone: buildTimezone(primitives.timezone),
      aliases,
    });
  }

  static toJson(location: LocationEntity): LocationJsonPrimitives {
    return {
      id: location.id,
      address: location.address.fold(() => null, item => item),
      addressComplete: location.addressComplete.fold(() => null, item => item),
      number: location.number.fold(() => null, item => item),
      postalCode: location.postalCode.fold(() => null, item => item),
      municipality: location.municipality.fold(() => null, item => item),
      province: location.province.fold(() => null, item => item),
      provinceShort: location.provinceShort.fold(() => null, item => item),
      region: location.region.fold(() => null, item => item),
      regionShort: location.regionShort.fold(() => null, item => item),
      slug: location.slug.fold(() => null, item => item),
      country: location.country.fold(() => null, item => item),
      countryShort: location.countryShort.fold(() => null, item => item),
      coordinates: location.coordinates,
      organizations: location.organizations,
      timezone: {
        ...location.timezone,
        dstOffset: location.timezone.dstOffset.fold(() => null, item => item),
        rawOffset: location.timezone.rawOffset.fold(() => null, item => item),
      },
      alias: location.alias.fold(() => null, item => item),
      aliases: Array.from(location.getAliases()),
      createdAt: location.createdAt,
      createdBy: location.createdBy,
      updatedAt: location.updatedAt,
      updatedBy: location.updatedBy,
      removedAt: location.removedAt,
      removedBy: location.removedBy,
    };
  }
}
