import { Criteria, Filters } from '@discocil/fv-criteria-converter-library/domain';
import { UniqueEntityID } from '@discocil/fv-domain-library/domain';

import { OrganizationIdFilter } from '@/cross-cutting/domain/filters/OrganizationIdFilter';
import { RemovedAtFilter } from '@/cross-cutting/domain/filters/RemovedAtFilter';
import { AliasLocationIdFilter } from '@/locations/domain/filters/AliasLocationIdFilter';

export class LocationAliasCriteriaMother {
  static find(locationId: UniqueEntityID, organizationIds: UniqueEntityID[]): Criteria {
    const filters = Filters.build();

    filters.add(AliasLocationIdFilter.build(locationId));
    filters.add(OrganizationIdFilter.buildIn(organizationIds));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static search(locationIds: Set<string>, organizationIds: Set<string>): Criteria {
    const filters = Filters.build();

    const locationUids = [...locationIds].map(id => UniqueEntityID.build(id));
    const organizationUids = [...organizationIds].map(id => UniqueEntityID.build(id));

    filters.add(AliasLocationIdFilter.buildIn(locationUids));
    filters.add(OrganizationIdFilter.buildIn(organizationUids));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }
}
