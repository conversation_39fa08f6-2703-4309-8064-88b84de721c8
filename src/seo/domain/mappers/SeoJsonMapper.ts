import { Maybe } from '@discocil/fv-domain-library/domain';

import { Seo } from '../entities/Seo';

import type { Nullable } from '@discocil/fv-domain-library/domain';
import type { SeoEither, SeoPrimitives } from '../entities/Seo';

type SeoDescriptionItemJson = Nullable<{
  readonly relatedToPlace: string;
  readonly local: string;
}>;

type SeoDescriptionsJson = {
  readonly short: string | null;
  readonly lead: SeoDescriptionItemJson;
  readonly body: SeoDescriptionItemJson;
};

export type SeoJsonPrimitives = Omit<SeoPrimitives,
  'title'
  | 'keywords'
  | 'place'
  | 'descriptions'
> & Nullable<{
  readonly title: string;
  readonly keywords: string;
  readonly place: string;
}> & {
  readonly descriptions: SeoDescriptionsJson;
};

export class SeoJsonMapper {
  static toEntity(primitives: SeoJsonPrimitives): SeoEither {
    return Seo.build({
      ...primitives,
      title: Maybe.fromValue(primitives.title),
      keywords: Maybe.fromValue(primitives.keywords),
      place: Maybe.fromValue(primitives.place),
      descriptions: {
        short: Maybe.fromValue(primitives.descriptions.short),
        lead: {
          relatedToPlace: Maybe.fromValue(primitives.descriptions.lead.relatedToPlace),
          local: Maybe.fromValue(primitives.descriptions.lead.local),
        },
        body: {
          relatedToPlace: Maybe.fromValue(primitives.descriptions.body.relatedToPlace),
          local: Maybe.fromValue(primitives.descriptions.body.local),
        },
      },
    });
  }

  static toJson(seo: Seo): SeoJsonPrimitives {
    return {
      id: seo.id,
      organizationId: seo.organizationId,
      title: seo.title.fold(() => null, item => item),
      keywords: seo.keywords.fold(() => null, item => item),
      place: seo.place.fold(() => null, item => item),
      descriptions: {
        short: seo.shortDescription.fold(() => null, item => item),
        lead: {
          relatedToPlace: seo.leadRelatedToPlaceDescription.fold(() => null, item => item),
          local: seo.leadLocalDescription.fold(() => null, item => item),
        },
        body: {
          relatedToPlace: seo.bodyRelatedToPlaceDescription.fold(() => null, item => item),
          local: seo.bodyLocalDescription.fold(() => null, item => item),
        },
      },
      createdAt: seo.createdAt,
      createdBy: seo.createdBy,
      updatedAt: seo.updatedAt,
      updatedBy: seo.updatedBy,
      removedAt: seo.removedAt,
      removedBy: seo.removedBy,
    };
  }
}
