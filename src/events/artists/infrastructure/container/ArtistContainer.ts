import { container, instanceCachingFactory } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';

import { ArtistDependencyIdentifier } from '../../domain/dependencyIdentifier/ArtistDependencyIdentifier';
import { ArtistCacheRepository } from '../database/repositories/ArtistCacheRepository';
import { ArtistMongoRepository } from '../database/repositories/ArtistMongoRepository';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';

export const ArtistContainer = {
  register: (): void => {
    container.register(ArtistDependencyIdentifier.ArtistRepository, {
      useFactory: instanceCachingFactory((container) => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);
        const mongoRepository = new ArtistMongoRepository(dbConnection);

        const cacheHander = container.resolve<ICacheRepository>(DependencyIdentifier.ICacheHandler);
        const cacheConfig = container.resolve<CacheConfig>(DependencyIdentifier.CacheConfig);

        return new ArtistCacheRepository(cacheHander, cacheConfig, mongoRepository);
      }),
    });
  },
};
