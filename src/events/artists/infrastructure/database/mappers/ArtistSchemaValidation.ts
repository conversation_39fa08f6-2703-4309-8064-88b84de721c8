import type { ArtistSchemaType } from '@/events/artists/infrastructure/database/schemas/ArtistSchemaType';
import type { JSONSchemaType } from 'ajv';

export const artistValidationSchema: JSONSchemaType<ArtistSchemaType> = {
  title: 'artist <PERSON><PERSON>',
  required: ['_id', 'slug', 'name', 'image'],
  type: 'object',
  properties: {
    _id: { type: 'string' },
    name: { type: 'string' },
    slug: { type: 'string' },
    image: { type: 'string' },
    images: {
      type: 'array',
      items: {
        type: 'object',
        required: ['height', 'url', 'width'],
        properties: {
          height: { type: 'number' },
          url: { type: 'string' },
          width: { type: 'number' },
        },
      },
    },
    biografia: { type: 'string', nullable: true },
    soundcloud_id: { type: 'string', nullable: true },
    spotify_id: { type: 'string', nullable: true },
    deezer_id: { type: 'string', nullable: true },
    facebook_url: { type: 'string', nullable: true },
    facebook_id: { type: 'string', nullable: true },
    twitter_user: { type: 'string', nullable: true },
    genres: {
      type: 'array',
      items: { type: 'string' },
    },
    source: { type: 'string' },
    spotify: {
      type: 'object',
      required: [],
      properties: {
        href: { type: 'string', nullable: true },
        popularity: { type: 'number', nullable: true },
        uri: { type: 'string', nullable: true },
      },
    },

    created_at: { type: 'number' },
    created_by: { type: 'string' },
    updated_at: { type: 'number' },
    updated_by: { type: 'string' },
    removed_at: { type: 'number' },
    removed_by: { type: 'string' },
  },
};
