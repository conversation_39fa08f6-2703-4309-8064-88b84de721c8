import { EArtistSource, Maybe } from '@discocil/fv-domain-library/domain';
import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import { Artist } from '@/events/artists/domain/entities/Artist';

import { artistValidationSchema } from './ArtistSchemaValidation';

import type { ArtistEither } from '@/events/artists/domain/entities/Artist';
import type Ajv from 'ajv';
import type { ArtistSchemaType } from '../schemas/ArtistSchemaType';

export class ArtistMapper {
  static execute(data: ArtistSchemaType): ArtistEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(artistValidationSchema);

    if (!validate(data) || validate.errors) {
      MapperError.logger({
        context: ArtistMapper.name,
        data,
        target: validate.errors,
      });

      return ArtistSoftMapper.execute(data);
    }

    return this.buildEntity(data);
  }

  static buildEntity(data: ArtistSchemaType): ArtistEither {
    return Artist.build({
      id: data._id,
      name: data.name,
      slug: data.slug,
      image: data.image,
      images: data.images ?? [],
      biography: Maybe.fromValue(data.biografia),
      soundcloudId: Maybe.fromValue(data.soundcloud_id),
      deezerId: Maybe.fromValue(data.deezer_id),
      facebookUrl: Maybe.fromValue(data.facebook_url),
      facebookId: Maybe.fromValue(data.facebook_id),
      twitterUser: Maybe.fromValue(data.twitter_user),
      genres: data.genres,
      source: data.source ?? EArtistSource.SPOTIFY,
      spotify: {
        id: Maybe.fromValue(data.spotify_id),
        href: Maybe.fromValue(data.spotify.href),
        popularity: Maybe.fromValue(data.spotify.popularity),
        uri: Maybe.fromValue(data.spotify.uri),
      },
      createdAt: data.created_at,
      createdBy: data.created_by,
      updatedAt: data.updated_at,
      updatedBy: data.updated_by,
      removedAt: data.removed_at,
      removedBy: data.removed_by,
    });
  }
}

export class ArtistSoftMapper extends ArtistMapper {
  static execute(data: ArtistSchemaType): ArtistEither {
    return this.buildEntity({
      ...data,
      _id: data._id,
      name: data.name,
      slug: data.slug,
      image: data.image,
      biografia: data.biografia ?? undefined,
      soundcloud_id: data.soundcloud_id ?? undefined,
      deezer_id: data.deezer_id ?? undefined,
      facebook_url: data.facebook_url ?? undefined,
      facebook_id: data.facebook_id ?? undefined,
      twitter_user: data.twitter_user ?? undefined,
      genres: data.genres ?? [],
    });
  }
}
