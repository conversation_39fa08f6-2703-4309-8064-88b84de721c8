import type { Artist } from '@/events/artists/domain/entities/Artist';
import type { ArtistSchemaType } from '../schemas/ArtistSchemaType';

export class ArtistSchemaMapper {
  static execute(artist: Artist): ArtistSchemaType {
    return {
      _id: artist.id,
      name: artist.name,
      slug: artist.slug,
      image: artist.image,
      images: artist.images,
      biografia: artist.biography.fold(() => undefined, item => item),
      soundcloud_id: artist.soundcloudId.fold(() => undefined, item => item),
      spotify_id: artist.spotify.id.fold(() => undefined, item => item),
      deezer_id: artist.deezerId.fold(() => undefined, item => item),
      facebook_url: artist.facebookUrl.fold(() => undefined, item => item),
      facebook_id: artist.facebookId.fold(() => undefined, item => item),
      twitter_user: artist.twitterUser.fold(() => undefined, item => item),
      genres: artist.genres,
      source: artist.source,
      spotify: {
        href: artist.spotify.href.fold(() => undefined, item => item),
        popularity: artist.spotify.popularity.fold(() => undefined, item => item),
        uri: artist.spotify.uri.fold(() => undefined, item => item),
      },
      created_at: artist.createdAt,
      created_by: artist.createdBy,
      updated_at: artist.updatedAt,
      updated_by: artist.updatedBy,
      removed_at: artist.removedAt,
      removed_by: artist.removedBy,
    };
  }
}
