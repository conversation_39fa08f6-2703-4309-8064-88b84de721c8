import type { ArtistImages } from '@/events/artists/domain/entities/Artist';
import type { EArtistSource, EMusicalGenres } from '@discocil/fv-domain-library/domain';

export type ArtistSchemaType = {
  _id: string;
  name: string;
  slug: string;
  image: string;
  images: ArtistImages[];
  biografia?: string;
  soundcloud_id?: string;
  spotify_id?: string;
  deezer_id?: string;
  facebook_url?: string;
  facebook_id?: string;
  twitter_user?: string;
  genres: EMusicalGenres[];
  source: EArtistSource;
  spotify: {
    href?: string;
    popularity?: number;
    uri?: string;
  };
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
  removed_at: number;
  removed_by: string;
};
