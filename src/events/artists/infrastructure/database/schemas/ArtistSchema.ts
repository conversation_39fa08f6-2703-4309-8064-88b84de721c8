import { EArtistSource } from '@discocil/fv-domain-library/domain';

export const artistSchema = {
  _id: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  slug: {
    type: String,
    required: true,
    index: true,
  },
  image: {
    type: String,
    required: true,
  },
  images: {
    type: Array,
    default: [],
  },
  biografia: { type: String },
  soundcloud_id: { type: String },
  spotify_id: { type: String },
  deezer_id: { type: String },
  facebook_url: { type: String },
  facebook_id: { type: String },
  twitter_user: { type: String },
  genres: { type: [String] },
  source: {
    type: String,
    enum: Object.values(EArtistSource),
    default: EArtistSource.SPOTIFY,
  },
  spotify: {
    href: { type: String },
    popularity: { type: Number },
    uri: { type: String },
  },
  created_at: { type: Number },
  created_by: { type: String },
  updated_at: { type: Number },
  updated_by: { type: String },
  removed_at: {
    type: Number,
    index: true,
  },
  removed_by: { type: String },
};
