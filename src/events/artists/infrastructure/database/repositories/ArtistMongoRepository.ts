import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { Artist } from '@/events/artists/domain/entities/Artist';

import { ArtistMapper } from '../mappers/ArtistMapper';
import { artistSchema } from '../schemas/ArtistSchema';

import type { ArtistRepository } from '@/events/artists/domain/contracts/ArtistRepository';
import type {
  <PERSON><PERSON><PERSON><PERSON>,
  ArtistKeys,
  Artists,
  ArtistsEither,
} from '@/events/artists/domain/entities/Artist';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { ArtistSchemaType } from '../schemas/ArtistSchemaType';

type PropertiesMapper = Partial<Record<ArtistKeys, keyof ArtistSchemaType>>;

export class ArtistMongoRepository extends MongoRepository implements ArtistRepository {
  protected getSchema(): Schema {
    return new Schema(artistSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'artistas';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      'id': '_id',
      'spotify.id': 'spotify_id',
      'removedAt': 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<ArtistEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<ArtistSchemaType>();

    return queryResponse ? ArtistMapper.execute(queryResponse) : left(NotFoundError.build({ context: this.constructor.name, target: Artist.name }));
  }

  async search(criteria: Criteria): Promise<ArtistsEither> {
    const response: Artists = new Map<IdPrimitive, Artist>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<ArtistSchemaType[]>();

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const artistResult = ArtistMapper.execute(model);

      if (artistResult.isLeft()) {
        return left(artistResult.value);
      }

      const artist = artistResult.value;

      response.set(artist.id, artist);
    }

    return right(response);
  }
}
