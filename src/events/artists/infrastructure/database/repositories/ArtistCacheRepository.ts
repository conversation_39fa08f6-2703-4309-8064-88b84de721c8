import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { ArtistJsonMapper } from '@/events/artists/domain/mappers/ArtistJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { ArtistRepository } from '@/events/artists/domain/contracts/ArtistRepository';
import type {
  Artist,
  ArtistEither,
  Artists,
  ArtistsEither,
} from '@/events/artists/domain/entities/Artist';
import type { ArtistJsonPrimitives } from '@/events/artists/domain/mappers/ArtistJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class ArtistCacheRepository extends CacheRepository implements ArtistRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: ArtistRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<ArtistEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: ArtistJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = ArtistJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        ArtistJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async search(criteria: Criteria): Promise<ArtistsEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: ArtistJsonPrimitives[] = JSON.parse(cacheHit);
      const artists: Artists = new Map<IdPrimitive, Artist>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = ArtistJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        artists.set(_primitive.id, entityOrError.value);
      }

      return right(artists);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const artists = repositoryResult.value;
      const artistsLength = artists.size;
      let eventCacheIndex = 0;
      const jsonArtists = new Array<ArtistJsonPrimitives>(artistsLength);

      if (artistsLength > 0) {
        for (const _event of artists.values()) {
          jsonArtists[eventCacheIndex] = ArtistJsonMapper.toJson(_event);

          eventCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonArtists, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}
