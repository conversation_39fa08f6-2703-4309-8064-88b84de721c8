import { Maybe } from '@discocil/fv-domain-library/domain';

import { Artist } from '../entities/Artist';

import type { Nullable } from '@discocil/fv-domain-library/domain';
import type { ArtistEither, ArtistPrimitives } from '../entities/Artist';

type ArtistSpotifyJsonPrimitive = Nullable<{
  readonly id: string;
  readonly href: string;
  readonly popularity: number;
  readonly uri: string;
}>;

export type ArtistJsonPrimitives = Omit<ArtistPrimitives,
  'biography'
  | 'soundcloudId'
  | 'deezerId'
  | 'facebookUrl'
  | 'facebookId'
  | 'twitterUser'
  | 'spotify'
> & Nullable<{
  readonly biography: string;
  readonly soundcloudId: string;
  readonly deezerId: string;
  readonly facebookUrl: string;
  readonly facebookId: string;
  readonly twitterUser: string;
}> & {
  readonly spotify: ArtistSpotifyJsonPrimitive;
};

export class ArtistJsonMapper {
  static toEntity(primitives: ArtistJsonPrimitives): ArtistEither {
    return Artist.build({
      ...primitives,
      biography: Maybe.fromValue(primitives.biography),
      soundcloudId: Maybe.fromValue(primitives.soundcloudId),
      deezerId: Maybe.fromValue(primitives.deezerId),
      facebookUrl: Maybe.fromValue(primitives.facebookUrl),
      facebookId: Maybe.fromValue(primitives.facebookId),
      twitterUser: Maybe.fromValue(primitives.twitterUser),
      spotify: {
        id: Maybe.fromValue(primitives.spotify.id),
        href: Maybe.fromValue(primitives.spotify.href),
        popularity: Maybe.fromValue(primitives.spotify.popularity),
        uri: Maybe.fromValue(primitives.spotify.uri),
      },
    });
  }

  static toJson(artist: Artist): ArtistJsonPrimitives {
    return {
      id: artist.id,
      name: artist.name,
      slug: artist.slug,
      image: artist.image,
      images: artist.images,
      biography: artist.biography.fold(() => null, item => item),
      soundcloudId: artist.soundcloudId.fold(() => null, item => item),
      deezerId: artist.deezerId.fold(() => null, item => item),
      facebookUrl: artist.facebookUrl.fold(() => null, item => item),
      facebookId: artist.facebookId.fold(() => null, item => item),
      twitterUser: artist.twitterUser.fold(() => null, item => item),
      genres: artist.genres,
      source: artist.source,
      spotify: {
        id: artist.spotify.id.fold(() => null, item => item),
        href: artist.spotify.href.fold(() => null, item => item),
        popularity: artist.spotify.popularity.fold(() => null, item => item),
        uri: artist.spotify.uri.fold(() => null, item => item),
      },
      createdAt: artist.createdAt,
      createdBy: artist.createdBy,
      updatedAt: artist.updatedAt,
      updatedBy: artist.updatedBy,
      removedAt: artist.removedAt,
      removedBy: artist.removedBy,
    };
  }
}
