import { Criteria, Filters } from '@discocil/fv-criteria-converter-library/domain';

import { IdFilter } from '@/cross-cutting/domain/filters/IdFilter';
import { RemovedAtFilter } from '@/cross-cutting/domain/filters/RemovedAtFilter';

import { SpotifyIdFilter } from './SpotifyIdFilter';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';

export class ArtistCriteriaMother {
  static idToMatch(id: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(id));

    return Criteria.build(filters);
  }

  static spotifyIdsToMatch(spotifyIds: string[]): Criteria {
    const filters = Filters.build();

    filters.add(SpotifyIdFilter.buildIn(spotifyIds));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }
}
