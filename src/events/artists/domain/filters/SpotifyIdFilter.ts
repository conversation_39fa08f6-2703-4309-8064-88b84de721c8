import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { ArtistKeys } from '../entities/Artist';

class FilterField extends FilterFieldBase<ArtistKeys> {}

export class SpotifyIdFilter {
  private static readonly field: ArtistKeys = 'spotify.id';

  static buildIn(spotifyIds: string[]): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.in();
    const filterValues: FilterValue[] = spotifyIds.map((spotifyId: string) => FilterValue.build(spotifyId));

    return new Filter(field, operator, filterValues);
  }
}
