import {
  AggregateRoot, right, UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  CreatedAt,
  CreatedBy,
  EArtistSource,
  Either,
  EMusicalGenres,
  FlattenKeys,
  IdPrimitive,
  Maybe,
  NotFoundError,
  Primitives,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';

export type ArtistImages = {
  height: number;
  url: string;
  width: number;
};

export type ArtistSpotify = {
  id: Maybe<string>;
  href: Maybe<string>;
  popularity: Maybe<number>;
  uri: Maybe<string>;
};

export type ArtistPrimitives = Primitives<Artist>;

export type Artists = Map<IdPrimitive, Artist>;

export type ArtistEither = Either<NotFoundError | MapperError, Artist>;
export type ArtistsEither = Either<NotFoundError | MapperError, Artists>;

export type ArtistKeys = FlattenKeys<Artist>;

export class Artist extends AggregateRoot {
  private constructor(
    id: UniqueEntityID,
    readonly name: string,
    readonly slug: string,
    readonly image: string,
    readonly images: ArtistImages[],
    readonly biography: Maybe<string>,
    readonly soundcloudId: Maybe<string>,
    readonly deezerId: Maybe<string>,
    readonly facebookUrl: Maybe<string>,
    readonly facebookId: Maybe<string>,
    readonly twitterUser: Maybe<string>,
    readonly genres: EMusicalGenres[],
    readonly source: EArtistSource,
    readonly spotify: ArtistSpotify,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);
  }

  static build(primitives: ArtistPrimitives): ArtistEither {
    const id = UniqueEntityID.build(primitives.id);

    const biography = primitives.biography.map(item => item);
    const soundcloudId = primitives.soundcloudId.map(item => item);
    const deezerId = primitives.deezerId.map(item => item);
    const facebookUrl = primitives.facebookUrl.map(item => item);
    const facebookId = primitives.facebookId.map(item => item);
    const twitterUser = primitives.twitterUser.map(item => item);

    const spotify: ArtistSpotify = {
      id: primitives.spotify.id.map(item => item),
      href: primitives.spotify.href.map(item => item),
      popularity: primitives.spotify.popularity.map(item => item),
      uri: primitives.spotify.uri.map(item => item),
    };

    const stamps = stampValueObjects(primitives);

    const entity = new Artist(
      id,
      primitives.name,
      primitives.slug,
      primitives.image,
      primitives.images,
      biography,
      soundcloudId,
      deezerId,
      facebookUrl,
      facebookId,
      twitterUser,
      primitives.genres,
      primitives.source,
      spotify,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return right(entity);
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }
}
