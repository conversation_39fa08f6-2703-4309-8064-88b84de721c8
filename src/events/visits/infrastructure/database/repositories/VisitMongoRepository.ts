
import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';

import { VisitSchemaMapper } from '../mappers/VisitSchemaMapper';
import { VisitSchema } from '../schemas/VisitSchema';

import type { VisitRepository } from '@/events/visits/domain/contracts/VisitRepository';
import type { Visit } from '@/events/visits/domain/entities/Visit';
import type { Schema } from 'mongoose';

export class VisitMongoRepository extends MongoRepository implements VisitRepository {
  protected getSchema(): Schema {
    return VisitSchema;
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'visitas';
  }

  async save(visit: Visit): Promise<void> {
    const toSave = VisitSchemaMapper.execute(visit);

    const filter = { _id: visit.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const modelQuery = await this.getConnection();

    await modelQuery.updateOne(filter, update, options);
  }
}
