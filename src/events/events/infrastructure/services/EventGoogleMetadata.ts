
import { FvDate } from '@discocil/fv-domain-library';

import type { Artists } from '@/events/artists/domain/entities/Artist';
import type { EventEntity } from '@/events/events/domain/entities/EventEntity';
import type { GuestListTypes } from '@/guestLists/guestListsTypes/domain/entities/GuestListType';
import type { LocationEntity } from '@/locations/domain/entities/LocationEntity';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { TicketTypes } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type { ECurrency, Maybe } from '@discocil/fv-domain-library';

type Location = {
  '@type': string;
  'name': string;
  'address': Address;
  'geo': Geo;
};

type Address = {
  '@type': string;
  'streetAddress': string;
  'postalCode': string;
  'addressLocality': string;
  'addressCountry': string;
  'addressRegion': string;
};

type Geo = {
  '@type': string;
  'latitude': number;
  'longitude': number;
};

type Organizer = {
  '@type': string;
  'name': string;
  'logo': string;
  'url': string;
  'description': string;
};

type Performer = {
  '@type': string;
  'name': string;
  'image': string;
};

type Offer = {
  '@type': string;
  'priceCurrency': string;
  'name': string;
  'price': number;
  'validFrom': string;
  'validThrough': string;
  'availabilityStarts'?: string;
  'availabilityEnds'?: string;
  'availability': string;
  'acceptedPaymentMethod': string[];
  'url'?: string;
};

type EventMetadataJSON = {
  '@type': string;
  '@context': string;
  'name': string;
  'eventStatus': string;
  'typicalAgeRange': string;
  'startDate': string;
  'endDate': string;
  'doorDate': string;
  'description': string;
  'eventAttendanceMode': string;
  'image': string;
  'duration': string;
  'location'?: Location;
  'organizer': Organizer;
  'performers'?: Performer[];
  'offers': Offer[];
};

type EventMetadataRequest = {
  readonly event: EventEntity;
  readonly organization: Organization;
  readonly artists: Maybe<Artists>;
  readonly micrositeUrl: string;
  readonly ticketTypes: Maybe<TicketTypes>;
  readonly guestListTypes: Maybe<GuestListTypes>;
};

type MakeOfferProps = {
  readonly currency: ECurrency;
  readonly price: number;
  readonly name: string;
  readonly eventStartDateInIso: string;
  readonly eventEndDateInIso: string;
  readonly availabilityStartInIso?: string;
  readonly availabilityEndInIso?: string;
  readonly isAvailable: boolean;
  readonly url?: string;
};

type MakeOfferUrlProps = {
  readonly micrositeUrl: string;
  readonly organization: Organization;
  readonly event: EventEntity;
  readonly offerId: string;
};

export class EventGoogleMetadata {
  static execute(request: EventMetadataRequest): EventMetadataJSON {
    const {
      event, organization, artists, micrositeUrl, ticketTypes, guestListTypes,
    } = request;

    const location = event.getLocation();

    const json: Partial<EventMetadataJSON> = {
      '@type': 'http://schema.org/MusicEvent',
      '@context': 'http://schema.org',
      'name': event.name,
      'eventStatus': event.canceled ? 'https://schema.org/EventCancelled' : 'https://schema.org/EventScheduled',
      'eventAttendanceMode': 'https://schema.org/OfflineEventAttendanceMode',
      'typicalAgeRange': `${event.age}-`,
      'startDate': event.getStartDateInISO(),
      'endDate': event.getEndDateInISO(),
      'doorDate': event.getStartDateInISO(),
      'description': event.description.getOrElse('none'),

      'image': this.makeMediumImage(event),
      'duration': this.makeDuration(event),

      'organizer': this.makeOrganizer(organization, micrositeUrl),
    };

    if (location.isDefined()) {
      json.location = this.makeLocation(location.get());
    }

    if (artists) {
      json.performers = this.makePerformers(artists);
    }

    json.offers = this.makeOffers({
      event,
      organization,
      ticketTypes,
      guestListTypes,
      micrositeUrl,
    });

    return json as EventMetadataJSON;
  }

  private static makeDuration(event: EventEntity): string {
    const durationMs = event.startDate.getTime() - event.endDate.getTime();

    return FvDate.createFromMilliSeconds(durationMs).toISODuration(); // 'PT1H40M';
  }

  private static makeMediumImage(event: EventEntity): string {
    if (event.images.isEmpty()) {
      return '';
    }

    if (event.images.get().medium.isEmpty()) {
      return '';
    }

    return event.images.get().medium.get().original ?? '';
  }

  private static makeLocation(location: LocationEntity): Location {
    return {
      '@type': 'Place',
      'name': location.alias.getOrElse(''),
      'address': {
        '@type': 'PostalAddress',
        'streetAddress': location.addressComplete.getOrElse(''),
        'postalCode': location.postalCode.getOrElse(''),
        'addressLocality': location.municipality.getOrElse(''),
        'addressCountry': location.countryShort.getOrElse(''),
        'addressRegion': location.regionShort.getOrElse(''),
      },
      'geo': {
        '@type': 'GeoCoordinates',
        'latitude': location.coordinates.latitude,
        'longitude': location.coordinates.longitude,
      },
    };
  }

  private static makeOrganizer(organization: Organization, micrositeUrl: string): Organizer {
    return {
      '@type': 'Organization',
      'name': organization.name,
      'logo': organization.image.getOrElse(''),
      'url': `${micrositeUrl}/${organization.slug}`,
      'description': organization.description.getOrElse(''),
    };
  }

  private static makePerformers(artists: Maybe<Artists>): Performer[] {
    const performers: Performer[] = [];

    if (artists.isDefined()) {
      for (const _artist of artists.get().values()) {
        performers.push({
          '@type': 'Person',
          'name': _artist.name,
          'image': _artist.image,
        });
      }
    }

    return performers;
  }

  private static makeOffers({
    event,
    organization,
    ticketTypes,
    guestListTypes,
    micrositeUrl,
  }: {
    event: EventEntity;
    organization: Organization;
    ticketTypes: Maybe<TicketTypes>;
    guestListTypes: Maybe<GuestListTypes>;
    micrositeUrl: string;
  }): Offer[] {
    const offers: Offer[] = [];

    if (ticketTypes.isDefined()) {
      for (const _ticketType of ticketTypes.get().values()) {
        const options = _ticketType.getOptions();

        for (const _option of options) {
          const isAvailable = _ticketType.isActive && !_ticketType.isComplete;

          const url = this.makeOfferUrl({
            micrositeUrl, organization, event, offerId: _ticketType.id,
          });

          offers.push(this.makeOffer({
            currency: organization.currency,
            price: _option.price,
            name: _option.name.fold(() => _ticketType.name, item => item),
            eventStartDateInIso: event.getStartDateInISO(),
            eventEndDateInIso: event.getEndDateInISO(),
            availabilityStartInIso: _ticketType.getFromDateInISO(),
            availabilityEndInIso: _option.getToDateInISOString(),
            isAvailable,
            url,
          }));
        }
      }
    }

    if (guestListTypes.isDefined()) {
      for (const _guestListType of guestListTypes.get().values()) {
        const options = _guestListType.options.values();

        const url = this.makeOfferUrl({
          micrositeUrl, organization, event, offerId: _guestListType.id,
        });

        for (const _option of options) {
          offers.push(this.makeOffer({
            currency: organization.currency,
            price: _option.price ?? 0,
            name: _guestListType.name.getOrElse(''),
            eventStartDateInIso: event.getStartDateInISO(),
            eventEndDateInIso: event.getEndDateInISO(),
            availabilityStartInIso: _guestListType.getDateFromInISO().fold(() => undefined, item => item),
            availabilityEndInIso: _guestListType.getDateToInISO().fold(() => undefined, item => item),
            isAvailable: true,
            url,
          }));
        }
      }
    }

    return offers;
  }

  private static makeOffer({
    currency,
    price,
    name,
    eventStartDateInIso,
    eventEndDateInIso,
    availabilityStartInIso,
    availabilityEndInIso,
    isAvailable,
    url,
  }: MakeOfferProps): Offer {
    return {
      '@type': 'Offer',
      'priceCurrency': currency,
      price,
      name,
      'validFrom': eventStartDateInIso,
      'validThrough': eventEndDateInIso,
      'availabilityStarts': availabilityStartInIso,
      'availabilityEnds': availabilityEndInIso,
      'availability': isAvailable
        ? 'http://schema.org/InStock'
        : 'http://schema.org/SoldOut',
      'acceptedPaymentMethod': [
        'http://purl.org/goodrelations/v1#MasterCard',
        'http://purl.org/goodrelations/v1#VISA',
        'http://purl.org/goodrelations/v1#AmericanExpress',
        'http://purl.org/goodrelations/v1#GoogleCheckout',
        'http://purl.org/goodrelations/v1#DinersClub',
        'http://purl.org/goodrelations/v1#Discover',
        'http://purl.org/goodrelations/v1#JCB',
        'http://purl.org/goodrelations/v1#Maestro',
        'http://purl.org/goodrelations/v1#DinersClub',
      ],
      url,
    };
  }

  private static makeOfferUrl({
    micrositeUrl,
    organization,
    event,
    offerId,
  }: MakeOfferUrlProps): string | undefined {
    return organization.slug.isDefined()
      ? `${micrositeUrl}/${organization.slug.get()}/events/${event.code}/tickets/${offerId}`
      : undefined;
  }
}
