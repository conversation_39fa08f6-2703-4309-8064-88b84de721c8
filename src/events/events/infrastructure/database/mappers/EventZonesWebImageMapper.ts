import { Maybe } from '@discocil/fv-domain-library';

import type { EventZonesWebImageSchemaType } from '@/events/events/infrastructure/database/schemas/EventZonesWebImageSchemaType';

export class EventZonesWebImageMapper {
  static toDomain(response: EventZonesWebImageSchemaType): Maybe<string> {
    const { distributionWebImageUrl } = response;

    if (!distributionWebImageUrl) {
      return Maybe.none<string>();
    }

    return Maybe.some(distributionWebImageUrl);
  }
}
