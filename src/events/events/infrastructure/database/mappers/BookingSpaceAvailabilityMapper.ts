import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import { BookingSpaceAvailability } from '@/events/events/domain/entities/BookingSpaceAvailability';

import { eventGroupSchemaValidation } from './EventGroupSchemaValidation';

import type { BookingSpaceAvailabilityEither } from '@/events/events/domain/entities/BookingSpaceAvailability';
import type { BookingSpaceAvailabilitySchemaType } from '@/events/events/infrastructure/database/schemas/BookingSpaceAvailabilitySchemaType';
import type Ajv from 'ajv';

export class BookingSpaceAvailabilityMapper {
  static execute(data: BookingSpaceAvailabilitySchemaType): BookingSpaceAvailabilityEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(eventGroupSchemaValidation);

    if (!validate(data) || validate.errors) {
      MapperError.logger({
        context: BookingSpaceAvailabilityMapper.name,
        data,
        target: validate.errors,
      });
    }

    return BookingSpaceAvailability.build({
      id: data._id,
      token: data.token,
      expiredAt: data.expired_at,
      collaboratorId: data.collaborator_id,
      eventId: data.event_id,
    });
  }
}
