import { HttpCriteriaConverter } from '@discocil/fv-criteria-converter-library/infrastructure';
import {
  left, NotFoundError, right, UnexpectedError,
} from '@discocil/fv-domain-library/domain';


import { BaseHttpRepository } from '@/cross-cutting/infrastructure/database/repositories/HttpRepository';
import { HTTP_CODES } from '@app/http/HttpCodes';
import { Zone } from '@/reservations/zones/domain/entities/Zone';
import { EventZonesWebImageMapper } from '@/events/events/infrastructure/database/mappers/EventZonesWebImageMapper';

import type { EventZonesWebImageSchemaType } from '@/events/events/infrastructure/database/schemas/EventZonesWebImageSchemaType';
import type { HttpRepository } from '@/cross-cutting/infrastructure/contracts/HttpRepository';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { EventZonesWebImageRepository, FindResponse } from '@/events/events/domain/contracts/EventZonesWebImageRepository';

export class EventZonesWebImageHttpRepository extends BaseHttpRepository implements EventZonesWebImageRepository {
  constructor(
    protected readonly apiKey: string,
    baseUrl: string,
    httpRepository: HttpRepository,
  ) {
    super(baseUrl, httpRepository);
  }

  async find(criteria: Criteria): Promise<FindResponse> {
    const path = `web-images/distribution`;
    const convertedParamsOrError = HttpCriteriaConverter.convert(criteria);

    if (convertedParamsOrError.isLeft()) {
      return left(convertedParamsOrError.value);
    }

    const convertedParams = convertedParamsOrError.value;

    const url = this.makeUrl({ queryParams: convertedParams, url: path });
    const headers = this.makeHeaders();

    const repositoryResponse = await this.httpRepository.get<EventZonesWebImageSchemaType>({ url, headers });

    if (repositoryResponse.isLeft()) {
      if (repositoryResponse.value.status === HTTP_CODES.NOT_FOUND_404) {
        return left(NotFoundError.build({ context: this.constructor.name, target: Zone.name }));
      }

      return left(UnexpectedError.build({
        context: this.constructor.name,
        target: Zone.name,
        data: { criteria },
      }));
    }

    const { data } = repositoryResponse.value;
    const eventZonesWebImage = EventZonesWebImageMapper.toDomain(data);

    return right(eventZonesWebImage);
  }

  protected makeHeaders(): Headers {
    const headers = new Headers();

    headers.set('Accept', 'application/json');
    headers.set('Content-Type', 'application/json');
    headers.set('x-api-key', this.apiKey);

    return headers;
  }
}
