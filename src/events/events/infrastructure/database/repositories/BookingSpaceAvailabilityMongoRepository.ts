import { left } from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { BookingSpaceAvailability } from '@/events/events/domain/entities/BookingSpaceAvailability';
import { BookingSpaceAvailabilityNotFound } from '@/events/events/domain/errors/BookingSpaceAvailabilityNotFound';

import { BookingSpaceAvailabilityMapper } from '../mappers/BookingSpaceAvailabilityMapper';
import { bookingSpaceAvailabilitySchema } from '../schemas/BookingSpaceAvailabilitySchema';

import type { BookingSpaceAvailabilityRepository } from '@/events/events/domain/contracts/bookingSpaceAvailability/BookingSpaceAvailabilityRepository';
import type { BookingSpaceAvailabilityEither, BookingSpaceAvailabilityKeys } from '@/events/events/domain/entities/BookingSpaceAvailability';
import type { <PERSON><PERSON><PERSON> } from '@discocil/fv-criteria-converter-library/domain';
import type { BookingSpaceAvailabilitySchemaType } from '../schemas/BookingSpaceAvailabilitySchemaType';

type PropertiesMapper = Partial<Record<BookingSpaceAvailabilityKeys, keyof BookingSpaceAvailabilitySchemaType>>;

export class BookingSpaceAvailabilityMongoRepository extends MongoRepository implements BookingSpaceAvailabilityRepository {
  protected getSchema(): Schema {
    return new Schema(bookingSpaceAvailabilitySchema);
  }

  protected getModel(): string {
    return 'reservations_availability_links';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      token: 'token',
      expiredAt: 'expired_at',
      collaboratorId: 'collaborator_id',
      eventId: 'event_id',
    };
  }

  async find(criteria: Criteria): Promise<BookingSpaceAvailabilityEither> {
    const connection = await this.getConnection();

    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter)
      .lean<BookingSpaceAvailabilitySchemaType>();

    return queryResponse
      ? BookingSpaceAvailabilityMapper.execute(queryResponse)
      : left(BookingSpaceAvailabilityNotFound.build({ context: this.constructor.name, target: BookingSpaceAvailability.name }));
  }
}
