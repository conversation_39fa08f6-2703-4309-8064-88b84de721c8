import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { EventGroupJsonMapper } from '@/events/events/domain/mappers/EventGroupJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { EventGroupRepository } from '@/events/events/domain/contracts/eventGroup/EventGroupRepository';
import type {
  EventGroup,
  EventGroupEither,
  EventGroups,
  EventGroupsEither,
} from '@/events/events/domain/entities/EventGroup';
import type { EventGroupJsonPrimitives } from '@/events/events/domain/mappers/EventGroupJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class EventGroupCacheRepository extends CacheRepository implements EventGroupRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: EventGroupRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<EventGroupEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: EventGroupJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = EventGroupJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        EventGroupJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async search(criteria: Criteria): Promise<EventGroupsEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: EventGroupJsonPrimitives[] = JSON.parse(cacheHit);
      const eventGroups: EventGroups = new Map<IdPrimitive, EventGroup>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = EventGroupJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        eventGroups.set(_primitive.id, entityOrError.value);
      }

      return right(eventGroups);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const eventGroups = repositoryResult.value;
      const eventGroupsLength = eventGroups.size;
      let eventGroupsCacheIndex = 0;
      const jsonEventGroups = new Array<EventGroupJsonPrimitives>(eventGroupsLength);

      if (eventGroupsLength > 0) {
        for (const _event of eventGroups.values()) {
          jsonEventGroups[eventGroupsCacheIndex] = EventGroupJsonMapper.toJson(_event);

          eventGroupsCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonEventGroups, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}
