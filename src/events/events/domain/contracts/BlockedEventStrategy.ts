import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { EventEntity } from '../entities/EventEntity';

export interface BlockedEventStrategy {
  execute(event: EventEntity, organization: Organization): boolean;
  execute(event: EventEntity, organization: IdPrimitive[]): boolean;
  execute(event: EventEntity, organization: Organization | IdPrimitive[]): boolean;
}
