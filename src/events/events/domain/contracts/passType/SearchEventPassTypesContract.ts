import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { BadSlugError } from '@/microsite/domain/errors/BadSlugError';
import type { PassTypes } from '@/passes/passTypes/domain/entities/PassType';
import type { PaginationDto, PaginationMetadataResponse } from '@discocil/fv-criteria-converter-library/domain';
import type {
  Either, InvalidArgumentError, MoneyError, NotFoundError, UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import type { EventEntity } from '../../entities/EventEntity';
import type { ServiceNotAvailableError } from '../../errors/ServiceNotAvailableError';

export type SearchEventPassTypesDto = PaginationDto & {
  readonly idOrCode: string;
  readonly slug: string;
  readonly organizationSlugs: Set<string>;
  readonly language: string;
  readonly smsSale: boolean;
};

export type SearchEventPassTypesResponse = PaginationMetadataResponse & {
  readonly event: EventEntity;
  readonly passTypes: PassTypes;
};

export type SearchEventPassTypesError = ServiceNotAvailableError
  | InvalidArgumentError
  | MapperError
  | NotFoundError
  | UnexpectedError
  | BadSlugError
  | MoneyError;

export type SearchEventPassTypesEither = Either<
  SearchEventPassTypesError,
  SearchEventPassTypesResponse
>;
