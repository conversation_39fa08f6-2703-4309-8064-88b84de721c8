import type { BookingZones } from '@/bookings/bookingZones/domain/entities/BookingZoneEntity';
import type { User } from '@/user/domain/entities/User';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { EventEntity } from '../../entities/EventEntity';

export type FindBookingSpaceAvailabilityDto = {
  readonly token: IdPrimitive;
};

export type SpacesPrices = Map<string, number>;
export type BookingZonesSpacesPrices = Map<IdPrimitive, SpacesPrices>;
export type SpacesCapacities = Map<string, number>;
export type BookingZonesSpacesCapacities = Map<IdPrimitive, SpacesCapacities>;

export type ZonesSpacesBookingTypes = { prices: BookingZonesSpacesPrices; bookingTypeCapacities: BookingZonesSpacesCapacities; };

export type EventAvailability = {
  readonly event: EventEntity;
  readonly zones: BookingZones;
  readonly user: User;
  readonly zonesSpacesBookingTypes: ZonesSpacesBookingTypes;
};
