import type { <PERSON><PERSON><PERSON>, CriteriaConverterError } from '@discocil/fv-criteria-converter-library/domain';
import type {
  Either,
  Maybe,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';

export type FindResponse = Either<NotFoundError | UnexpectedError | CriteriaConverterError, Maybe<string>>;

export interface EventZonesWebImageRepository {
  find: (criteria: Criteria) => Promise<FindResponse>;
}
