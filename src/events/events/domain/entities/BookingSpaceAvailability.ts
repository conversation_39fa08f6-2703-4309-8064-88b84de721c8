import {
  Entity,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  Either,
  IdPrimitive,
  Primitives,
  Properties,
} from '@discocil/fv-domain-library/domain';
import type { BookingSpaceAvailabilityNotFound } from '../errors/BookingSpaceAvailabilityNotFound';

export type BookingSpaceAvailabilityEither = Either<MapperError | BookingSpaceAvailabilityNotFound, BookingSpaceAvailability>;
export type BookingSpaceAvailabilityKeys = keyof Properties<BookingSpaceAvailability>;

export type BookingSpaceAvailabilityExternalPrimitives = Primitives<BookingSpaceAvailability>;

export class BookingSpaceAvailability extends Entity {
  private constructor(
    id: UniqueEntityID,
    private readonly _token: IdPrimitive,
    private readonly _expiredAt: number,
    private readonly _collaboratorId: UniqueEntityID,
    private readonly _eventId: UniqueEntityID,
  ) {
    super(id);
  }

  static build(primitives: BookingSpaceAvailabilityExternalPrimitives): BookingSpaceAvailabilityEither {
    const id = UniqueEntityID.build(primitives.id);

    const entity = new BookingSpaceAvailability(
      id,
      primitives.token,
      primitives.expiredAt,
      UniqueEntityID.build(primitives.collaboratorId),
      UniqueEntityID.build(primitives.eventId),
    );

    return right(entity);
  }

  get token(): IdPrimitive {
    return this._token;
  }

  get expiredAt(): number {
    return this._expiredAt;
  }

  get collaboratorId(): IdPrimitive {
    return this._collaboratorId.toPrimitive();
  }

  get eventId(): IdPrimitive {
    return this._eventId.toPrimitive();
  }

  isExpired(): boolean {
    return this._expiredAt <= Date.now();
  }

  toPrimitive(): BookingSpaceAvailabilityExternalPrimitives {
    return {
      id: this.id,
      token: this.token,
      expiredAt: this.expiredAt,
      collaboratorId: this.collaboratorId,
      eventId: this.eventId,
    };
  }
}
