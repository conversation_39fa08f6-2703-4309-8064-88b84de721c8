import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { ErrorMethodRequest } from '@/cross-cutting/domain/errors/ErrorRequest';

export class BookingSpaceAvailabilityUnauthorized extends FvError {
  static readonly defaultCause = EErrorKeys.INVALID_USER;

  static build(request: ErrorMethodRequest): BookingSpaceAvailabilityUnauthorized {
    const {
      context, error, data, target,
    } = request;

    const exceptionMessage = 'User is not authorized to access this booking spaces availability';

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
