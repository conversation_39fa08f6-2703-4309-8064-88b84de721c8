import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { ErrorMethodRequest } from '@/cross-cutting/domain/errors/ErrorRequest';

export class BookingSpaceAvailabilityNotFound extends FvError {
  static readonly defaultCause = EErrorKeys.BOOKING_SPACE_AVAILABILITY_NOT_FOUND;

  static build(request: ErrorMethodRequest): BookingSpaceAvailabilityNotFound {
    const {
      context, error, data, target,
    } = request;

    const exceptionMessage = 'Booking spaces availability not found';

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
