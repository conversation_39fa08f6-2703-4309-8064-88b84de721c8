import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { ErrorMethodRequest } from '@/cross-cutting/domain/errors/ErrorRequest';

export class BookingSpaceAvailabilityExpired extends FvError {
  static readonly defaultCause = EErrorKeys.BOOKING_SPACE_AVAILABILITY_EXPIRED;

  static build(request: ErrorMethodRequest): BookingSpaceAvailabilityExpired {
    const {
      context, error, data, target,
    } = request;

    const exceptionMessage = 'Booking spaces availability has expired';

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
