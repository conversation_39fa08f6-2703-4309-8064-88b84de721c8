import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { BookingSpaceAvailabilityKeys } from '../../entities/BookingSpaceAvailability';

class FilterField extends FilterFieldBase<BookingSpaceAvailabilityKeys> {}

export class TokenFilter {
  static readonly field: BookingSpaceAvailabilityKeys = 'token';

  static buildEqual(token: IdPrimitive): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(token);

    return new Filter(field, operator, filterValue);
  }
}
