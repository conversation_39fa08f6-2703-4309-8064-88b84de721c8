
import { EventGroup } from '../entities/EventGroup';

import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { EventGroupEither, EventGroupPrimitives } from '../entities/EventGroup';

export type EventGroupJsonPrimitives = Omit<EventGroupPrimitives,
  'eventIds'
> & {
  readonly eventIds: IdPrimitive[];
};

export class EventGroupJsonMapper {
  static toEntity(primitives: EventGroupJsonPrimitives): EventGroupEither {
    return EventGroup.build({
      ...primitives,
      eventIds: new Set(primitives.eventIds),
    });
  }

  static toJson(eventGroup: EventGroup): EventGroupJsonPrimitives {
    return {
      id: eventGroup.id,
      organizationId: eventGroup.organizationId,
      eventIds: Array.from(eventGroup.eventIds),
      code: eventGroup.code,
      createdAt: eventGroup.createdAt,
      createdBy: eventGroup.createdBy,
      updatedAt: eventGroup.updatedAt,
      updatedBy: eventGroup.updatedBy,
      removedAt: eventGroup.removedAt,
      removedBy: eventGroup.removedBy,
    };
  }
}
