import { BookingSpaceAvailability } from '../entities/BookingSpaceAvailability';

import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type {
  BookingSpaceAvailabilityEither,
  BookingSpaceAvailabilityExternalPrimitives,
} from '../entities/BookingSpaceAvailability';

export type BookingSpaceAvailabilityJsonPrimitives = Omit<BookingSpaceAvailabilityExternalPrimitives,
  'token' | 'collaboratorId' | 'eventId'
> & {
  readonly token: IdPrimitive;
  readonly collaboratorId: IdPrimitive;
  readonly eventId: IdPrimitive;
};

export class BookingSpaceAvailabilityJsonMapper {
  static toEntity(primitives: BookingSpaceAvailabilityJsonPrimitives): BookingSpaceAvailabilityEither {
    return BookingSpaceAvailability.build({
      ...primitives,
      token: primitives.token,
      collaboratorId: primitives.collaboratorId,
      eventId: primitives.eventId,
    });
  }

  static toJson(entity: BookingSpaceAvailability): BookingSpaceAvailabilityJsonPrimitives {
    return {
      id: entity.id,
      token: entity.token,
      expiredAt: entity.expiredAt,
      collaboratorId: entity.collaboratorId,
      eventId: entity.eventId,
    };
  }
}
