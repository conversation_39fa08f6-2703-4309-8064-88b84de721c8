import { BlockedEventContext } from './blockedEventStrategy/BlockedEventContext';
import { getBlockedEventStrategy } from './blockedEventStrategy/BlockedEventStrategy';

import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { EventEntity } from '../entities/EventEntity';

export class IsBlockedFor {
  constructor(
    private readonly event: EventEntity,
    private readonly organization: Organization | string[],
  ) { }

  execute(): boolean {
    const blockedEventStrategy = getBlockedEventStrategy(this.organization);
    const blockedEventContext = new BlockedEventContext(blockedEventStrategy);

    return blockedEventContext.apply(this.event, this.organization);
  }
}
