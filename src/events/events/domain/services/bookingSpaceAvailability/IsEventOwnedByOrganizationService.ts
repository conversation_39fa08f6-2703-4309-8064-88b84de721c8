import { left, right } from '@discocil/fv-domain-library/domain';

import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';

import { BookingSpaceAvailabilityUnauthorized } from '../../errors/BookingSpaceAvailabilityUnauthorized';

import type { Either, UniqueEntityID } from '@discocil/fv-domain-library/domain';
import type { EventRepository } from '../../contracts/EventRepository';

export class IsEventOwnedByOrganizationService {
  constructor(private readonly eventRepository: EventRepository) { }

  async execute(eventId: UniqueEntityID, organizationIds: Set<string>): Promise<Either<BookingSpaceAvailabilityUnauthorized, true>> {
    const criteria = EventCriteriaMother.idToMatch(eventId);
    const eventOrError = await this.eventRepository.find(criteria);

    if (eventOrError.isLeft()) {
      return left(this.bookingSpaceAvailabilityUnauthorized(eventId, organizationIds));
    }

    const event = eventOrError.value;
    const organizationFound = organizationIds.has(event.organizationId);

    return organizationFound
      ? right(organizationFound)
      : left(this.bookingSpaceAvailabilityUnauthorized(eventId, organizationIds));
  }

  private bookingSpaceAvailabilityUnauthorized(eventId: UniqueEntityID, organizationIds: Set<string>): BookingSpaceAvailabilityUnauthorized {
    return BookingSpaceAvailabilityUnauthorized.build({
      context: this.constructor.name,
      data: { eventId, organizationIds },
    });
  }
}
