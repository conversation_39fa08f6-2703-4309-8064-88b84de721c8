import {
  FvN<PERSON>ber,
  left,
  Maybe,
  right,
} from '@discocil/fv-domain-library/domain';

import { BookingTypeCriteriaMother } from '@/bookings/bookingTypes/domain/filters/BookingTypeCriteriaMother';

import type { BookingTypeRepository } from '@/bookings/bookingTypes/domain/contracts/BookingTypeRepository';
import type { BookingTypes } from '@/bookings/bookingTypes/domain/entities/BookingTypeEntity';
import type { BookingZones } from '@/bookings/bookingZones/domain/entities/BookingZoneEntity';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type {
  Either,
  IdPrimitive,
  InvalidDateError,
  MoneyError,
  NotFoundError,
} from '@discocil/fv-domain-library/domain';
import type {
  BookingZonesSpacesCapacities,
  BookingZonesSpacesPrices,
  SpacesCapacities,
  SpacesPrices,
  ZonesSpacesBookingTypes,
} from '../../contracts/bookingSpaceAvailability/FindBookingSpaceAvailabilityContract';
import type { EventEntity } from '../../entities/EventEntity';

type Request = {
  readonly event: EventEntity;
  readonly zones: BookingZones;
};

type AssignBookingTypesToSpacesEitherResponse = Either<NotFoundError | MapperError | InvalidDateError | MoneyError,
  ZonesSpacesBookingTypes>;

export class AssignBookingTypesToSpacesService {
  constructor(private readonly bookingTypeRepository: BookingTypeRepository) {}

  async execute(request: Request): Promise<AssignBookingTypesToSpacesEitherResponse> {
    const { event, zones } = request;

    const criteria = this.getBookingRatesCriteria(zones, event.id);
    const typesOrError = await this.bookingTypeRepository.search(criteria);

    if (typesOrError.isLeft()) {
      return left(typesOrError.value);
    }

    const bookingTypes = typesOrError.value;
    const bookingZonesSpacesPrices = this.assignPricesToSpaces(zones, bookingTypes);
    const bookingZonesSpacesCapacities = this.assignCapacitiesToSpaces(zones, bookingTypes);

    return right({ prices: bookingZonesSpacesPrices, bookingTypeCapacities: bookingZonesSpacesCapacities });
  }

  private getBookingRatesCriteria(zones: BookingZones, eventId: IdPrimitive): Criteria {
    const slugsTypes = new Set<string>();

    for (const zone of zones.values()) {
      for (const space of zone.spaces.values()) {
        for (const slugType of space.slugsTypes) {
          slugsTypes.add(slugType);
        }
      }
    }

    return BookingTypeCriteriaMother.slugsToMatchByEvent(slugsTypes, eventId);
  }

  private assignCapacitiesToSpaces(zones: BookingZones, bookingTypes: BookingTypes): BookingZonesSpacesCapacities {
    const bookingZonesSpacesCapacities: BookingZonesSpacesCapacities = new Map<IdPrimitive, SpacesCapacities>();

    for (const zone of zones.values()) {
      for (const space of zone.spaces.values()) {
        for (const bookingType of bookingTypes.values()) {
          if (!space.slugsTypes.includes(bookingType.slug)) {
            continue;
          }

          const existingCapacities = bookingZonesSpacesCapacities.get(zone.id) ?? new Map<string, number>();

          existingCapacities.set(space.id, bookingType.maxClients);
          bookingZonesSpacesCapacities.set(zone.id, existingCapacities);
        }
      }
    }

    return bookingZonesSpacesCapacities;
  }

  private assignPricesToSpaces(zones: BookingZones, bookingTypes: BookingTypes): BookingZonesSpacesPrices {
    const bookingZonesSpacesPrices: BookingZonesSpacesPrices = new Map<IdPrimitive, SpacesPrices>();

    for (const zone of zones.values()) {
      for (const space of zone.spaces.values()) {
        for (const bookingType of bookingTypes.values()) {
          if (!space.slugsTypes.includes(bookingType.slug)) {
            continue;
          }

          const minPrice = this.calculateMinPrice(new Map([[bookingType.id, bookingType]]));

          if (minPrice.isEmpty()) {
            continue;
          }

          const existingPrices = bookingZonesSpacesPrices.get(zone.id) ?? new Map<string, number>();

          existingPrices.set(space.id, minPrice.get());
          bookingZonesSpacesPrices.set(zone.id, existingPrices);
        }
      }
    }

    return bookingZonesSpacesPrices;
  }

  private calculateMinPrice(bookingTypes: BookingTypes): Maybe<number> {
    const prices: number[] = [];

    for (const bookingType of bookingTypes.values()) {
      if (bookingType.hasPrice()) {
        prices.push(bookingType.getPriceAmount());
      }
    }

    return prices.length ? Maybe.fromValue(FvNumber.min(...prices).toPrimitive()) : Maybe.none();
  }
}
