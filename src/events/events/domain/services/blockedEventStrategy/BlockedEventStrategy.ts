import { Organization } from '@/organizations/organizations/domain/entities/Organization';

import { BlockedForAllUserParents } from './strategies/BlockedForAllUserParents';
import { BlockedForOrganization } from './strategies/BlockedForOrganization';

import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { BlockedEventStrategy } from '../../contracts/BlockedEventStrategy';

export const getBlockedEventStrategy = (organization: Organization | IdPrimitive[]): BlockedEventStrategy => {
  if (organization instanceof Organization) {
    return new BlockedForOrganization();
  }

  return new BlockedForAllUserParents();
};
