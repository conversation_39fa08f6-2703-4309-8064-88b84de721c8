import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { BlockedEventStrategy } from '../../contracts/BlockedEventStrategy';
import type { EventEntity } from '../../entities/EventEntity';

export class BlockedEventContext {
  constructor(private readonly strategy: BlockedEventStrategy) {}

  apply(event: EventEntity, organization: Organization | string[]): boolean {
    return this.strategy.execute(event, organization);
  }
}
