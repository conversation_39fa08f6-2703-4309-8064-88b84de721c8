import { UniqueEntityID } from '@discocil/fv-domain-library/domain';

import { Organization } from '@/organizations/organizations/domain/entities/Organization';

import type { BlockedEventStrategy } from '../../../contracts/BlockedEventStrategy';
import type { EventEntity } from '../../../entities/EventEntity';

export class BlockedForOrganization implements BlockedEventStrategy {
  execute(event: EventEntity, channelOrganization: unknown): boolean {
    if (!(channelOrganization instanceof Organization)) {
      throw new Error(`Invalid type for organization in ${this.constructor.name}`);
    }

    const organization = channelOrganization as Organization;
    const isOwn = event.organizationId === organization.id;

    if (isOwn) {
      return false;
    }

    const existsCollaboration = event.getOrganization().fold(
      () => false,
      eventOrganization => channelOrganization.hasThisHost(UniqueEntityID.build(eventOrganization.id)),
    );

    if (existsCollaboration) {
      const existsBlock = event.getBlockedOrganizationIds().has(channelOrganization.id);

      return existsBlock;
    }

    return true;
  }
}
