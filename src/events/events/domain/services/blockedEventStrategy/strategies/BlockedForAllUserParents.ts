import { intersect } from '@/cross-cutting/domain/helpers/Intersect';

import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { BlockedEventStrategy } from '../../../contracts/BlockedEventStrategy';
import type { EventEntity } from '../../../entities/EventEntity';

export class BlockedForAllUserParents implements BlockedEventStrategy {
  execute(event: EventEntity, organization: unknown): boolean {
    if (!Array.isArray(organization)) {
      throw new Error(`Invalid type for organization in ${this.constructor.name}`);
    }

    const organizationIds = organization as IdPrimitive[];
    const isOwn = organizationIds.includes(event.organizationId);

    if (isOwn) {
      return false;
    }

    const blockedOrganizationIds = [...event.getBlockedOrganizationIds()];

    const intersection = intersect(
      organizationIds,
      blockedOrganizationIds,
    );

    return intersection.length >= organizationIds.length;
  }
}
