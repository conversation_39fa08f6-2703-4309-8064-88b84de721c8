import { left, right } from '@discocil/fv-domain-library/domain';

import type {
  Either, Maybe, NotFoundError, UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import type { Criteria, CriteriaConverterError } from '@discocil/fv-criteria-converter-library/domain';
import type { EventZonesWebImageRepository } from '@/events/events/domain/contracts/EventZonesWebImageRepository';

type Response = Either<NotFoundError | UnexpectedError | CriteriaConverterError, Maybe<string>>;

export class EventZonesWebImageFinder {
  constructor(private readonly eventZonesWebImageRepository: EventZonesWebImageRepository) {}

  async execute(criteria: Criteria): Promise<Response> {
    const eventZonesWebImageOrError = await this.eventZonesWebImageRepository.find(criteria);

    if (eventZonesWebImageOrError.isLeft()) {
      return left(eventZonesWebImageOrError.value);
    }

    return right(eventZonesWebImageOrError.value);
  }
}
