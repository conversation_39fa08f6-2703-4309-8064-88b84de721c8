import {
  Maybe, UniqueEntityID, left, right,
} from '@discocil/fv-domain-library/domain';

import { BillingAddressCriteriaMother } from '@/billingAddresses/domain/filters/BillingAddressCriteriaMother';
import { ArtistCriteriaMother } from '@/events/artists/domain/filters/ArtistCriteriaMother';
import { LocationCriteriaMother } from '@/locations/domain/filters/LocationCriteriaMother';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';

import { EventCriteriaMother } from '../filters/EventCriteriaMother';

import type { BillingAddressRepository } from '@/billingAddresses/domain/contracts/BillingAddressRepository';
import type { BillingAddress } from '@/billingAddresses/domain/entities/BillingAddress';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { ArtistRepository } from '@/events/artists/domain/contracts/ArtistRepository';
import type {
  Artist,
  Artists, ArtistsEither,
} from '@/events/artists/domain/entities/Artist';
import type { LocationEntity } from '@/locations/domain/entities/LocationEntity';
import type { LocationFinder } from '@/locations/domain/services/LocationFinder';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { Either, NotFoundError } from '@discocil/fv-domain-library/domain';
import type { EventRepository } from '../contracts/EventRepository';
import type { EventEntity } from '../entities/EventEntity';

type GetEventRelatedEntitiesDto = {
  readonly idOrCode: string;
};

class EventRelationsDto {
  constructor(
    readonly event: EventEntity,
    readonly organization: Organization,
    readonly location: Maybe<LocationEntity>,
    readonly billingAddress: Maybe<BillingAddress>,
    readonly artists: Maybe<Artists>,
  ) { }

  hasLocation(): boolean {
    return this.location.isDefined();
  }

  hasBillingAddress(): boolean {
    return this.billingAddress.isDefined();
  }

  hasArtists(): boolean {
    return this.artists.isDefined();
  }
}

type EventRelatedEntitiesEither = Either<
  NotFoundError | MapperError,
  EventRelationsDto
>;

export class GetEventRelatedEntities {
  constructor(
    private readonly eventRepository: EventRepository,
    private readonly locationFinder: LocationFinder,
    private readonly organizationRepository: OrganizationRepository,
    private readonly billingAddressRepository: BillingAddressRepository,
    private readonly artistRepository: ArtistRepository,
  ) { }

  async execute(dto: GetEventRelatedEntitiesDto): Promise<EventRelatedEntitiesEither> {
    const { idOrCode } = dto;

    const isUid = UniqueEntityID.isValid(idOrCode);

    const eventCriteria = isUid
      ? EventCriteriaMother.findForMicrositeByIdToMatch(UniqueEntityID.build(idOrCode), false)
      : EventCriteriaMother.findForMicrositeByCodeToMatch(idOrCode, false);

    const eventResult = await this.eventRepository.find(eventCriteria);

    if (eventResult.isLeft()) {
      return left(eventResult.value);
    }

    const event = eventResult.value;
    const eventOrganizationId = UniqueEntityID.build(event.organizationId);

    const locationCriteria = LocationCriteriaMother.idToMatch(UniqueEntityID.build(event.locationId));
    const organizationCriteria = OrganizationCriteriaMother.idToMatch(eventOrganizationId);
    const billingAddressCriteria = BillingAddressCriteriaMother.organizationToMatch(eventOrganizationId);

    const [
      location,
      organizationResult,
      billingAddressResult,
      artistsResult,
    ] = await Promise.all([
      this.locationFinder.execute(locationCriteria),
      this.organizationRepository.find(organizationCriteria),
      this.billingAddressRepository.find(billingAddressCriteria),
      this.searchArtists(event.artists),
    ]);

    if (organizationResult.isLeft()) {
      return left(organizationResult.value);
    }

    const eventRelationsDto = new EventRelationsDto(
      event,
      organizationResult.value,
      location,
      billingAddressResult.isLeft() ? Maybe.none() : Maybe.some(billingAddressResult.value),
      artistsResult.isLeft() ? Maybe.none() : Maybe.some(artistsResult.value),
    );

    return right(eventRelationsDto);
  }

  private async searchArtists(artists: string[]): Promise<ArtistsEither> {
    if (artists.length === 0) {
      return right(new Map<string, Artist>());
    }

    const criteria = ArtistCriteriaMother.spotifyIdsToMatch(artists);

    return this.artistRepository.search(criteria);
  }
}
