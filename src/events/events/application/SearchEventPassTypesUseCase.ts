import {
  Maybe,
  UniqueEntityID,
  contextualizeError,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';
import { SearchPassTypesWithFeesUseCase } from '@/passes/passTypes/application/SearchPassTypesWithFeesUseCase';
import { PassTypesSorting } from '@/passes/passTypes/domain/services/PassTypesSorting';

import type { IMicrositeChannelService } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { IMicrositeEventChannelValidation } from '@/microsite/domain/contracts/MicrositeEventChannelValidationContracts';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { EventRepository } from '../domain/contracts/EventRepository';
import type { SearchEventPassTypesDto, SearchEventPassTypesEither } from '../domain/contracts/passType/SearchEventPassTypesContract';

export class SearchEventPassTypesUseCase implements UseCase<SearchEventPassTypesDto, Promise<SearchEventPassTypesEither>> {
  constructor(
    private readonly eventRepository: EventRepository,
    private readonly organizationRepository: OrganizationRepository,
    private readonly micrositeChannelService: IMicrositeChannelService,
    private readonly micrositeEventChannelValidation: IMicrositeEventChannelValidation,
    private readonly searchPassTypesWithFeesUseCase: SearchPassTypesWithFeesUseCase,
  ) {}

  @contextualizeError()
  async execute(dto: SearchEventPassTypesDto): Promise<SearchEventPassTypesEither> {
    const {
      idOrCode, slug, organizationSlugs, smsSale, pagination,
    } = dto;

    const isUid = UniqueEntityID.isValid(idOrCode);

    const eventCriteria = isUid
      ? EventCriteriaMother.findForMicrositeByIdToMatch(UniqueEntityID.build(idOrCode), smsSale)
      : EventCriteriaMother.findForMicrositeByCodeToMatch(idOrCode, smsSale);

    const [eventResult, channelResult] = await Promise.all([
      this.eventRepository.find(eventCriteria),
      this.micrositeChannelService.execute({ slug, organizationSlugs }),
    ]);

    if (eventResult.isLeft()) {
      return left(eventResult.value);
    }

    if (channelResult.isLeft()) {
      return left(channelResult.value);
    }

    const event = eventResult.value;
    const channel = channelResult.value;

    const organizationId = UniqueEntityID.build(event.organizationId);

    const organizationCriteria = OrganizationCriteriaMother.idToMatch(organizationId);

    const organizationResult = await this.organizationRepository.find(organizationCriteria);

    if (organizationResult.isLeft()) {
      return left(organizationResult.value);
    }

    const eventOrganization = organizationResult.value;

    const validation = await this.micrositeEventChannelValidation.execute({
      channel, eventOrganization, event,
    });

    if (validation.isLeft()) {
      return left(validation.value);
    }

    const passTypesResult = await this.searchPassTypesWithFeesUseCase.execute({
      eventId: Maybe.some(event.id),
      organizationId: event.organizationId,
      pagination,
    });

    if (passTypesResult.isLeft()) {
      return left(passTypesResult.value);
    }

    const { passTypes, pagination: paginationResult } = passTypesResult.value;

    const sortedPassTypes = PassTypesSorting.execute(passTypes);

    return right({
      event,
      passTypes: sortedPassTypes,
      pagination: paginationResult,
    });
  }
}
