import {
  left, right, contextualizeError,
} from '@discocil/fv-domain-library/domain';

import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';

import type { FindEventZonesWebImageDto } from '@/events/events/application/contracts/FindEventZonesWebImageDto';
import type { EventZonesWebImageFinder } from '@/events/events/domain/services/EventZonesWebImageFinder';
import type { CriteriaConverterError } from '@discocil/fv-criteria-converter-library/domain';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type {
  Either, Maybe, NotFoundError, UnexpectedError,
} from '@discocil/fv-domain-library/domain';

type Response = Either<NotFoundError | UnexpectedError | CriteriaConverterError, Maybe<string>>;

export class FindEventZonesWebImageUseCase implements UseCase<FindEventZonesWebImageDto, Promise<Response>> {
  constructor(
    private readonly eventZonesWebImageFinder: EventZonesWebImageFinder,
  ) {}

  @contextualizeError()
  async execute(dto: FindEventZonesWebImageDto): Promise<Response> {
    const { eventId, organizationId } = dto;

    const criteria = EventCriteriaMother.idAndOrganizationIdToMatch(eventId, organizationId);

    const eventZonesWebImageOrError = await this.eventZonesWebImageFinder.execute(criteria);

    if (eventZonesWebImageOrError.isLeft()) {
      return left(eventZonesWebImageOrError.value);
    }

    const eventZonesWebImage = eventZonesWebImageOrError.value;

    return right(eventZonesWebImage);
  }
}

