import { OrderTypes } from '@discocil/fv-criteria-converter-library/domain';
import {
  left,
  Maybe,
  NotFoundError,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { LogSoftError } from '@/cross-cutting/domain/errors/LogSoftError';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { GuestListTypeCriteriaMother } from '@/guestLists/guestListsTypes/domain/filters/GuestListTypeCriteriaMother';
import { TicketTypeCriteriaMother } from '@/tickets/ticketsTypes/domain/filters/TicketTypeCriteriaMother';


import { EventEntity } from '../domain/entities/EventEntity';
import { GetEventRelatedEntities } from '../domain/services/GetEventRelatedEntities';

import type { BillingAddressRepository } from '@/billingAddresses/domain/contracts/BillingAddressRepository';
import type { FetchAllPages } from '@/cross-cutting/domain/contracts/FetchAllPages';
import type { ArtistRepository } from '@/events/artists/domain/contracts/ArtistRepository';
import type { GuestListTypes } from '@/guestLists/guestListsTypes/domain/entities/GuestListType';
import type { LocationFinder } from '@/locations/domain/services/LocationFinder';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { TicketTypes } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type { PaginationOption } from '@discocil/fv-criteria-converter-library/domain';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { EventRepository } from '../domain/contracts/EventRepository';
import type { FindEventMetadataDto } from '../domain/contracts/FindEventMetadataContract';
import type {
  SearchEventGuestListError, SearchEventGuestListsEither, SearchEventGuestListsResponse,
} from '../domain/contracts/guestListType/EventGuestListContracts';
import type { SearchEventGuestListTypesDto } from '../domain/contracts/guestListType/SearchEventGuestListTypesContract';
import type {
  SearchEventTicketsTypeResponse, SearchEventTicketsTypesEither, SearchEventTicketTypesError,
} from '../domain/contracts/ticketType/EventTicketTypeTypes';
import type { SearchEventTicketTypesDto } from '../domain/contracts/ticketType/SearchEventTicketTypesContract';
import type { FindEventMetadataEither } from '../domain/entities/EventEntity';
import type { SearchEventGuestListTypesUseCase } from './SearchEventGuestListTypesUseCase';
import type { SearchEventTicketsTypesUseCase } from './SearchEventTicketsTypesUseCase';

export class FindEventMetadataUseCase implements UseCase<FindEventMetadataDto, Promise<FindEventMetadataEither>> {
  constructor(
    private readonly eventRepository: EventRepository,
    private readonly locationFinder: LocationFinder,
    private readonly artistRepository: ArtistRepository,
    private readonly organizationRepository: OrganizationRepository,
    private readonly billingAddressRepository: BillingAddressRepository,
    private readonly searchEventTicketsTypesUseCase: SearchEventTicketsTypesUseCase,
    private readonly searchEventGuestListTypesUseCase: SearchEventGuestListTypesUseCase,
    private readonly fetchAllPages: FetchAllPages,
  ) {}

  async execute(dto: FindEventMetadataDto): Promise<FindEventMetadataEither> {
    const { idOrCode, language } = dto;

    const isUid = UniqueEntityID.isValid(idOrCode);

    const eventCriteria = isUid
      ? EventCriteriaMother.findForMicrositeByIdToMatch(UniqueEntityID.build(idOrCode), false)
      : EventCriteriaMother.findForMicrositeByCodeToMatch(idOrCode, false);

    const eventResult = await this.eventRepository.find(eventCriteria);

    if (eventResult.isLeft()) {
      return left(eventResult.value);
    }

    const event = eventResult.value;

    const getEventRelatedEntities = new GetEventRelatedEntities(
      this.eventRepository,
      this.locationFinder,
      this.organizationRepository,
      this.billingAddressRepository,
      this.artistRepository,
    );

    const relationsOrError = await getEventRelatedEntities.execute({ idOrCode });

    if (relationsOrError.isLeft()) {
      return left(relationsOrError.value);
    }

    const eventRelationsDto = relationsOrError.value;

    if (eventRelationsDto.organization.slug.isEmpty()) {
      return left(NotFoundError.build({
        context: this.constructor.name,
        target: EventEntity.name,
      }));
    }

    if (!eventRelationsDto.hasBillingAddress()) {
      this.logSoft(event.organizationId);

      return left(NotFoundError.build({
        context: this.constructor.name,
        target: EventEntity.name,
      }));
    }

    if (eventRelationsDto.hasLocation()) {
      event.setLocation(eventRelationsDto.location.get());
    }

    const organization = eventRelationsDto.organization;
    const artists = eventRelationsDto.artists;

    const perPage = 200;

    const searchEventTicketTypesPagination: PaginationOption = {
      page: 1,
      perPage,
      order: {
        type: OrderTypes.ASC,
        by: Maybe.some(TicketTypeCriteriaMother.defaultOrderKey),
      },
    };

    const searchEventTicketTypesDto: SearchEventTicketTypesDto = {
      idOrCode,
      slug: eventRelationsDto.organization.slug.get(),
      organizationSlugs: new Set(),
      language,
      smsSale: false,
      pagination: searchEventTicketTypesPagination,
    };

    const allTicketTypesResponsesOrError = await this.fetchAllPages.execute<
      SearchEventTicketsTypeResponse,
      SearchEventTicketsTypesEither,
      SearchEventTicketTypesError
    >(
      searchEventTicketTypesPagination,
      pagination => this.searchEventTicketsTypesUseCase.execute({ ...searchEventTicketTypesDto, pagination }),
    );

    if (allTicketTypesResponsesOrError.isLeft()) {
      return left(allTicketTypesResponsesOrError.value);
    }

    const allTicketTypeResponses = allTicketTypesResponsesOrError.value;

    const allTicketTypes: TicketTypes = new Map();

    for (const _useCaseResponse of allTicketTypeResponses) {
      const { ticketTypes } = _useCaseResponse;

      for (const ticketType of ticketTypes.values()) {
        allTicketTypes.set(ticketType.id, ticketType);
      }
    }

    const searchEventGuestListTypesPagination: PaginationOption = {
      page: 1,
      perPage,
      order: {
        type: OrderTypes.ASC,
        by: Maybe.some(GuestListTypeCriteriaMother.defaultOrderKey),
      },
    };

    const searchGuestListTypesDto: SearchEventGuestListTypesDto = {
      id: idOrCode,
      slug: eventRelationsDto.organization.slug.getOrElse(''),
      organizationSlugs: new Set(),
      language: '',
      smsSale: false,
      pagination: searchEventGuestListTypesPagination,
    };

    const allGuestListTypesResponsesOrError = await this.fetchAllPages.execute<
      SearchEventGuestListsResponse,
      SearchEventGuestListsEither,
      SearchEventGuestListError
    >(
      searchEventGuestListTypesPagination,
      pagination => this.searchEventGuestListTypesUseCase.execute({ ...searchGuestListTypesDto, pagination }),
    );

    if (allGuestListTypesResponsesOrError.isLeft()) {
      return left(allGuestListTypesResponsesOrError.value);
    }

    const allGuestListTypesResponses = allGuestListTypesResponsesOrError.value;

    const allGuestListTypes: GuestListTypes = new Map();

    for (const _useCaseResponse of allGuestListTypesResponses) {
      const { guestListTypes } = _useCaseResponse;

      for (const guestListType of guestListTypes.values()) {
        allGuestListTypes.set(guestListType.id, guestListType);
      }
    }

    return right({
      event,
      organization,
      artists,
      ticketTypes: allTicketTypes.size === 0 ? Maybe.none() : Maybe.some(allTicketTypes),
      guestListTypes: allGuestListTypes.size === 0 ? Maybe.none() : Maybe.some(allGuestListTypes),
    });
  }

  private logSoft(organizationId: IdPrimitive): void {
    LogSoftError.billingAddressNotFound(organizationId);
  }
}
