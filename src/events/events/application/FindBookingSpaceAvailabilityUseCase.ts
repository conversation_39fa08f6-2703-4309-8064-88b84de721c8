import {
  Either,
  InvalidDateError,
  left,
  MoneyError,
  NotFoundError,
  right,
  UniqueEntityID,
  contextualizeError,
} from '@discocil/fv-domain-library/domain';

import { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';
import { UserRepository } from '@/user/domain/contracts/UserRepository';
import { UserCriteriaMother } from '@/user/domain/filters/UserCriteriaMother';

import { EventRepository } from '../domain/contracts/EventRepository';
import { BookingSpaceAvailabilityExpired } from '../domain/errors/BookingSpaceAvailabilityExpired';
import { BookingSpaceAvailabilityNotFound } from '../domain/errors/BookingSpaceAvailabilityNotFound';
import { BookingSpaceAvailabilityUnauthorized } from '../domain/errors/BookingSpaceAvailabilityUnauthorized';
import { AssignBookingTypesToSpacesService } from '../domain/services/bookingSpaceAvailability/AssignBookingTypesToSpacesService';
import { IsEventOwnedByOrganizationService } from '../domain/services/bookingSpaceAvailability/IsEventOwnedByOrganizationService';

import type { CriteriaConverterError } from '@discocil/fv-criteria-converter-library/domain';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { EventAvailability, FindBookingSpaceAvailabilityDto } from '../domain/contracts/bookingSpaceAvailability/FindBookingSpaceAvailabilityContract';
import type { IGetBookingZonesServices } from '../domain/contracts/bookingZones/GetBookingZonesContracts';

type BookingSpaceAvailabilityEither = Either<
  NotFoundError |
  MapperError |
  InvalidDateError |
  MoneyError |
  BookingSpaceAvailabilityExpired |
  BookingSpaceAvailabilityUnauthorized |
  BookingSpaceAvailabilityNotFound |
  CriteriaConverterError,
  EventAvailability
>;

export class FindBookingSpaceAvailabilityUseCase implements UseCase<FindBookingSpaceAvailabilityDto, Promise<BookingSpaceAvailabilityEither>> {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly eventRepository: EventRepository,
    private readonly organizationRepository: OrganizationRepository,
    private readonly isEventOwnedByOrganizationService: IsEventOwnedByOrganizationService,
    private readonly assignBookingTypesToSpacesService: AssignBookingTypesToSpacesService,
    private readonly getBookingZonesServices: IGetBookingZonesServices,
  ) {}

  @contextualizeError()
  async execute(dto: FindBookingSpaceAvailabilityDto): Promise<BookingSpaceAvailabilityEither> {
    const { token } = dto;

    const criteria = EventCriteriaMother.bookingSpaceAvailabilityByTokenToMatch(token);
    const eventOrError = await this.eventRepository.find(criteria);

    if (eventOrError.isLeft()) {
      return left(eventOrError.value);
    }

    const event = eventOrError.value;

    const organizationCriteria = OrganizationCriteriaMother.idToMatch(UniqueEntityID.build(event.organizationId));
    const organizationOrError = await this.organizationRepository.find(organizationCriteria);

    if (organizationOrError.isLeft()) {
      return left(organizationOrError.value);
    }

    const organization = organizationOrError.value;

    event.setOrganization(organization);

    const eventHasBookingSpaceAvailability = event.ensureHasBookingSpaceAvailability();

    if (eventHasBookingSpaceAvailability.isLeft()) {
      return left(eventHasBookingSpaceAvailability.value);
    }

    const eventBookingSpaceAvailabilityIsAvailable = event.ensureBookingSpaceAvailabilityIsAvailable();

    if (eventBookingSpaceAvailabilityIsAvailable.isLeft()) {
      return left(eventBookingSpaceAvailabilityIsAvailable.value);
    }

    const userCriteria = UserCriteriaMother.idToMatch(UniqueEntityID.build(event.getBookingSpacesAvailabilityCollaboratorId().get()));
    const userOrError = await this.userRepository.find(userCriteria);

    if (userOrError.isLeft()) {
      return left(userOrError.value);
    }

    const user = userOrError.value;
    const userOrganizationsSet = new Set<string>(user.organizations);

    const isEventInOrganization = await this.isEventOwnedByOrganizationService.execute(
      UniqueEntityID.build(event.id),
      userOrganizationsSet,
    );

    if (isEventInOrganization.isLeft()) {
      return left(isEventInOrganization.value);
    }

    const zonesOrError = await this.getBookingZonesServices.execute(event);

    if (zonesOrError.isLeft()) {
      return left(zonesOrError.value);
    }

    const zones = zonesOrError.value.zones;

    const zonesSpacesBookingTypesOrError = await this.assignBookingTypesToSpacesService.execute({ event, zones });

    if (zonesSpacesBookingTypesOrError.isLeft()) {
      return left(zonesSpacesBookingTypesOrError.value);
    }

    const zonesSpacesBookingTypes = zonesSpacesBookingTypesOrError.value;

    return right({
      event, user, zones, zonesSpacesBookingTypes,
    });
  }
}
