import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { EventConfiguration } from '@/events/eventConfigurations/domain/entities/EventConfiguration';

import { EventConfigurationMapper } from '../mappers/EventConfigurationMapper';
import { eventconfigurationSchema } from '../schemas/EventConfigurationSchema';

import type { EventConfigurationRepository } from '@/events/eventConfigurations/domain/contracts/EventConfigurationRepository';
import type {
  EventConfigurationEither,
  EventConfigurationKey,
  EventConfigurationKeys,
  EventConfigurations,
  EventConfigurationsEither,
} from '@/events/eventConfigurations/domain/entities/EventConfiguration';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { EventConfigurationSchemaType } from '../schemas/EventConfigurationSchemaType';

type PropertiesMapper = Partial<Record<EventConfigurationKeys, keyof EventConfigurationSchemaType>>;

export class EventConfigurationMongoRepository extends MongoRepository implements EventConfigurationRepository {
  protected getSchema(): Schema {
    return new Schema(eventconfigurationSchema, { collection: this.getModel() });
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'eventos_configuracion';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      eventId: 'evento_id',
      key: 'clave',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<EventConfigurationEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<EventConfigurationSchemaType>();

    return queryResponse
      ? EventConfigurationMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: EventConfiguration.name }));
  }

  async search(criteria: Criteria): Promise<EventConfigurationsEither> {
    const response: EventConfigurations = new Map<EventConfigurationKey, EventConfiguration>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<EventConfigurationSchemaType[]>();

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const eventConfigurationResult = EventConfigurationMapper.execute(model);

      if (eventConfigurationResult.isLeft()) {
        return left(eventConfigurationResult.value);
      }

      const eventConfiguration = eventConfigurationResult.value;

      response.set(eventConfiguration.key, eventConfiguration);
    }

    return right(response);
  }
}
