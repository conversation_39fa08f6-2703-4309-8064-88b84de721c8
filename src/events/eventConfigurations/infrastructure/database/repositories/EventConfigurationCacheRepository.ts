import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { EventConfigurationJsonMapper } from '@/events/eventConfigurations/domain/mappers/EventConfigurationJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { EventConfigurationRepository } from '@/events/eventConfigurations/domain/contracts/EventConfigurationRepository';
import type {
  EventConfiguration,
  EventConfigurationEither,
  EventConfigurationKey,
  EventConfigurationPrimitives,
  EventConfigurations,
  EventConfigurationsEither,
} from '@/events/eventConfigurations/domain/entities/EventConfiguration';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export class EventConfigurationCacheRepository extends CacheRepository implements EventConfigurationRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: EventConfigurationRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<EventConfigurationEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: EventConfigurationPrimitives = JSON.parse(cacheHit);
      const entityOrError = EventConfigurationJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        EventConfigurationJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async search(criteria: Criteria): Promise<EventConfigurationsEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: EventConfigurationPrimitives[] = JSON.parse(cacheHit);
      const eventConfigurations: EventConfigurations = new Map<EventConfigurationKey, EventConfiguration>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = EventConfigurationJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        const entity = entityOrError.value;

        eventConfigurations.set(entity.key, entity);
      }

      return right(eventConfigurations);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const eventConfigurations = repositoryResult.value;
      const eventConfigurationsLength = eventConfigurations.size;
      let eventCacheIndex = 0;
      const jsonEventConfigurations = new Array<EventConfigurationPrimitives>(eventConfigurationsLength);

      if (eventConfigurationsLength > 0) {
        for (const _event of eventConfigurations.values()) {
          jsonEventConfigurations[eventCacheIndex] = EventConfigurationJsonMapper.toJson(_event);

          eventCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonEventConfigurations, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}
