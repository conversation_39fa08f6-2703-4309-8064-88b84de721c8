import type { JSONSchemaType } from 'ajv';
import type { EventConfigurationSchemaType } from '../schemas/EventConfigurationSchemaType';

export const eventConfigurationValidationSchema: JSONSchemaType<Omit<EventConfigurationSchemaType, 'valor'>> = {
  title: 'EventConfiguration Json Schema Validation',
  required: ['evento_id', 'clave'],
  type: 'object',
  properties: {
    _id: { type: 'string' },
    evento_id: { type: 'string' },
    clave: { type: 'string' },
    ticket_cancellation_time: { type: 'number' },
    created_at: { type: 'number' },
    created_by: { type: 'string' },
    updated_at: { type: 'number' },
    updated_by: { type: 'string' },
    removed_at: { type: 'number' },
    removed_by: { type: 'string' },
  },
};
