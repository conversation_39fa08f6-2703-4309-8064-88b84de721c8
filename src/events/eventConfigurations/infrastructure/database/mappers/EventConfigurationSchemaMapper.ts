import type { EventConfiguration } from '@/events/eventConfigurations/domain/entities/EventConfiguration';
import type { EventConfigurationSchemaType } from '../schemas/EventConfigurationSchemaType';

export class EventConfigurationSchemaMapper {
  static execute(eventconfiguration: EventConfiguration): EventConfigurationSchemaType {
    return {
      _id: eventconfiguration.id,
      evento_id: eventconfiguration.eventId,
      clave: eventconfiguration.key,
      valor: eventconfiguration,
      ticket_cancellation_time: eventconfiguration.ticketCancellationTime,
      created_at: eventconfiguration.createdAt,
      created_by: eventconfiguration.createdBy,
      updated_at: eventconfiguration.updatedAt,
      updated_by: eventconfiguration.updatedBy,
      removed_at: eventconfiguration.removedAt,
      removed_by: eventconfiguration.removedBy,
    };
  }
}
