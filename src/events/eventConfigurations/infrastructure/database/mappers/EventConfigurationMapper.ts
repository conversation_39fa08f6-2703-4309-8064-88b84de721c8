import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import { EventConfiguration } from '@/events/eventConfigurations/domain/entities/EventConfiguration';

import { eventConfigurationValidationSchema } from './EventConfigurationSchemaValidation';

import type { EventConfigurationEither } from '@/events/eventConfigurations/domain/entities/EventConfiguration';
import type Ajv from 'ajv';
import type { EventConfigurationSchemaType } from '../schemas/EventConfigurationSchemaType';

export class EventConfigurationMapper {
  static execute(data: EventConfigurationSchemaType): EventConfigurationEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(eventConfigurationValidationSchema);

    if (!validate(data) || validate.errors) {
      MapperError.logger({
        context: EventConfigurationMapper.name,
        data,
        target: validate.errors,
      });

      return EventConfigurationSoftMapper.execute(data);
    }

    return this.buildEntity(data);
  }

  static buildEntity(data: EventConfigurationSchemaType): EventConfigurationEither {
    return EventConfiguration.build({
      id: data._id,
      eventId: data.evento_id,
      key: data.clave,
      value: data.valor,
      ticketCancellationTime: data.ticket_cancellation_time ?? -1,
      createdAt: data.created_at,
      createdBy: data.created_by,
      updatedAt: data.updated_at,
      updatedBy: data.updated_by,
      removedAt: data.removed_at,
      removedBy: data.removed_by,
    });
  }
}

export class EventConfigurationSoftMapper extends EventConfigurationMapper {
  static execute(data: EventConfigurationSchemaType): EventConfigurationEither {
    return this.buildEntity({
      ...data,
      _id: data._id,
      evento_id: data.evento_id ?? null,
      clave: data.clave ?? null,
      valor: data.valor ?? null,
      ticket_cancellation_time: data.ticket_cancellation_time ?? -1,
    });
  }
}
