import {
  AggregateRoot,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  CreatedAt,
  CreatedBy,
  Either,
  IdPrimitive,
  NotFoundError,
  Primitives,
  Properties,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';

export type EventConfigurations = Map<EventConfigurationKey, EventConfiguration>;

export type EventConfigurationEither = Either<MapperError | NotFoundError, EventConfiguration>;
export type EventConfigurationsEither = Either<MapperError | NotFoundError, EventConfigurations>;

export type EventConfigurationKeys = keyof Properties<EventConfiguration>;

export type EventConfigurationPrimitives = Primitives<EventConfiguration>;

export type EventConfigurationKey = string;

export class EventConfiguration extends AggregateRoot {
  private constructor(
    id: UniqueEntityID,
    private readonly _eventId: UniqueEntityID,
    readonly key: EventConfigurationKey,
    readonly value: unknown,
    readonly ticketCancellationTime: number,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);
  }

  static build(primitives: EventConfigurationPrimitives): EventConfigurationEither {
    const id = UniqueEntityID.build(primitives.id);
    const eventId = UniqueEntityID.build(primitives.eventId);

    const stamps = stampValueObjects(primitives);

    const entity = new EventConfiguration(
      id,
      eventId,
      primitives.key,
      primitives.value,
      primitives.ticketCancellationTime,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return right(entity);
  }

  get eventId(): IdPrimitive {
    return this._eventId.toPrimitive();
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }
}
