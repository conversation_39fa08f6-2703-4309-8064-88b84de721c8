import { Criteria, Filters } from '@discocil/fv-criteria-converter-library/domain';

import { EventIdFilter } from '@/cross-cutting/domain/filters/EventIdFilter';
import { IdFilter } from '@/cross-cutting/domain/filters/IdFilter';

import { KeyFilter } from './KeyFilter';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';

export class EventConfigurationCriteriaMother {
  static idToMatch(id: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(id));

    return Criteria.build(filters);
  }

  static eventToMatch(eventId: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(EventIdFilter.buildEqual(eventId));

    return Criteria.build(filters);
  }

  static sittingEnabledToMatch(eventId: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(EventIdFilter.buildEqual(eventId));
    filters.add(KeyFilter.buildEqualsSittingEnabled());

    return Criteria.build(filters);
  }
}
