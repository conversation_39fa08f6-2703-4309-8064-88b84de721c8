import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';
import { EEventConfigurationKeys } from '@discocil/fv-domain-library/domain';

import type { EventConfigurationKeys } from '../entities/EventConfiguration';

class FilterField extends FilterFieldBase<EventConfigurationKeys> {}

export class KeyFilter {
  private static readonly field: EventConfigurationKeys = 'key';

  static buildEqualsSittingEnabled(): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(EEventConfigurationKeys.SITTING_ENABLED);

    return new Filter(field, operator, filterValue);
  }
}
