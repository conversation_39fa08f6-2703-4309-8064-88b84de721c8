import { EventConfiguration } from '../entities/EventConfiguration';

import type { EventConfigurationEither, EventConfigurationPrimitives } from '../entities/EventConfiguration';

export class EventConfigurationJsonMapper {
  static toEntity(primitives: EventConfigurationPrimitives): EventConfigurationEither {
    return EventConfiguration.build(primitives);
  }

  static toJson(eventConfiguration: EventConfiguration): EventConfigurationPrimitives {
    return {
      id: eventConfiguration.id,
      eventId: eventConfiguration.eventId,
      key: eventConfiguration.key,
      value: eventConfiguration.value,
      ticketCancellationTime: eventConfiguration.ticketCancellationTime,
      createdAt: eventConfiguration.createdAt,
      createdBy: eventConfiguration.createdBy,
      updatedAt: eventConfiguration.updatedAt,
      updatedBy: eventConfiguration.updatedBy,
      removedAt: eventConfiguration.removedAt,
      removedBy: eventConfiguration.removedBy,
    };
  }
}
