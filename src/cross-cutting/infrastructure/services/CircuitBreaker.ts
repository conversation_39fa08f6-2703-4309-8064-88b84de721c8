import { left } from '@discocil/fv-domain-library/domain';
import CircuitBreaker from 'opossum';

import { ServiceTemporallyUnavailable } from '../errors/ServiceTemporallyUnavailable';

import type { Logger } from '@discocil/fv-domain-library/domain';

const fnCallback = async (operation: () => Promise<unknown>): Promise<unknown> => await operation();

type fnCallbackType = typeof fnCallback;

type MethodKeys<T> = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [K in keyof T]: T[K] extends (...args: any) => any ? K : never;
}[keyof T];

type CircuitBreakerConfig = {
  readonly timeout: number;
  readonly errorThresholdPercentage: number;
  readonly resetTimeout: number;
};

class CircuitBreakerDecorator {
  private static instance: CircuitBreakerDecorator;

  private constructor(private readonly breaker: CircuitBreaker<[fnCallbackType]>) { }

  static build(config: CircuitBreakerConfig, logger: Logger): CircuitBreakerDecorator {
    if (!CircuitBreakerDecorator.instance) {
      const breaker = new CircuitBreaker(fnCallback, config);

      breaker.fallback(() => {
        return left(ServiceTemporallyUnavailable.build({ context: this.constructor.name }));
      });

      CircuitBreakerDecorator.instance = new CircuitBreakerDecorator(breaker);

      this.startEvents(logger);
    }

    return CircuitBreakerDecorator.instance;
  }

  private static startEvents(logger: Logger): void {
    const breaker = CircuitBreakerDecorator.instance.breaker;

    breaker.on('open', () => {
      logger.log('⛓️‍💥 Open Circuit');
    });

    breaker.on('halfOpen', () => {
      logger.log('⛓️‍💥 Circuit in test status');
    });

    breaker.on('close', () => {
      logger.log('⛓️‍💥 Circuit closed, operation restored');
    });

    breaker.on('timeout', () => {
      logger.log('⛓️‍💥 Timeout reached');
    });

    breaker.on('reject', () => {
      logger.log('⛓️‍💥 Request rejected');
    });
  }

  async fire<T>(operation: () => Promise<T>): Promise<T> {
    return this.breaker.fire(operation) as T;
  }
}

// TODO: actualmente tengo uno general y deberia tener uno por cada servicio. Ej: mongo, ticket-api, reservetion-api.
export const FvCircuitBreaker = <T extends object>(objectClass: T, logger: Logger, config: CircuitBreakerConfig): T => {
  const handler: ProxyHandler<T> = {
    get(target: T, prop: MethodKeys<T> | string | symbol) {
      const originalMethod = target[prop as MethodKeys<T>];

      if (typeof originalMethod === 'function') {
        const breaker = CircuitBreakerDecorator.build(config, logger);

        return async (...args: unknown[]) =>
          breaker.fire(() => originalMethod.apply(target, args));
      }

      return target[prop as keyof T];
    },
  };

  return new Proxy(objectClass as object, handler) as T;
};
