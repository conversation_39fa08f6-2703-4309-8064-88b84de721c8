import { Maybe } from '@discocil/fv-domain-library/domain';

import type { PaginationMetadata } from '@discocil/fv-criteria-converter-library/domain';

export type PaginationMetadataPrimitives = Omit<
  PaginationMetadata,
  'next'
  | 'previous'
> & {
  next: number | null;
  previous: number | null;
};

export class PaginationMetadataMapper {
  static fromPrimitives(primitives?: PaginationMetadataPrimitives): PaginationMetadata | undefined {
    if (!primitives) {
      return undefined;
    }

    return {
      page: primitives.page,
      perPage: primitives.perPage,
      total: primitives.total,
      next: Maybe.fromValue(primitives.next),
      previous: Maybe.fromValue(primitives.previous),
      totalPages: primitives.totalPages,
      order: primitives.order,
    };
  }

  static toPrimitives(pagination?: PaginationMetadata): PaginationMetadataPrimitives | undefined {
    if (!pagination) {
      return undefined;
    }

    return {
      page: pagination.page,
      perPage: pagination.perPage,
      total: pagination.total,
      next: pagination.next.fold(() => null, item => item),
      previous: pagination.previous.fold(() => null, item => item),
      totalPages: pagination.totalPages,
      order: pagination.order,
    };
  }
}
