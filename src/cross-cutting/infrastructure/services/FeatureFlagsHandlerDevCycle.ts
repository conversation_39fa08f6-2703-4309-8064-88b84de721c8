import config from '@config/index';

import type {
  DevCycleClient,
  DevCycleUser,
  DVCVariableValue,
} from '@devcycle/nodejs-server-sdk';

type FeatureFlagConfig = {
  flagName: string;
  defaultValue: string | number | boolean | unknown;
};

export interface FeatureFlagsHandler {
  getAllFlags: () => Record<string, unknown>;
  isEnabled: (dto: FeatureFlagConfig) => boolean;
}

export class FeatureFlagsHandlerDevCycle implements FeatureFlagsHandler {
  static defaultFlags: Record<string, unknown>;
  private static instance: FeatureFlagsHandler;
  private readonly defaultUser: DevCycleUser = { user_id: config.devCycle.userId };

  constructor(private readonly client: DevCycleClient) {
    this.setDefaultFlags();
  }

  static create(client: DevCycleClient): void {
    if (FeatureFlagsHandlerDevCycle.instance) {
      return;
    }

    FeatureFlagsHandlerDevCycle.instance = new FeatureFlagsHandlerDevCycle(client);
  }

  static getInstance(): FeatureFlagsHandler {
    if (!FeatureFlagsHandlerDevCycle.instance) {
      throw new Error('Feature Flag Handler not initialized!!');
    }

    return FeatureFlagsHandlerDevCycle.instance;
  }

  isEnabled({ flagName, defaultValue }: FeatureFlagConfig): boolean {
    const isEnabled = this.client.variableValue(this.defaultUser, flagName, defaultValue as DVCVariableValue);

    return !!isEnabled;
  }

  getAllFlags(): Record<string, unknown> {
    return this.client.allVariables(this.defaultUser) as Record<string, unknown>;
  }

  private setDefaultFlags(): void {
    FeatureFlagsHandlerDevCycle.defaultFlags = this.getAllFlags();
  }
}
