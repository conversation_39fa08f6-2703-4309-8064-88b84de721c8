import { PutObjectCommand, S3 } from '@aws-sdk/client-s3';
import {
  left,
  right,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';

import type {
  StorageManager,
  StorageManagerUploadEither,
  StorageManagerUploadOptions,
} from '@/cross-cutting/domain/contracts/StorageManager';
import type { Configuration } from '@config/Configuration';

type Request = Configuration['aws']['s3'];

export class StorageManagerS3 implements StorageManager {
  private readonly s3Instance: S3;

  constructor(private readonly options: Request) {
    const {
      accessKey, secretKey, region,
    } = this.options;

    this.s3Instance = new S3({
      credentials: {
        accessKeyId: accessKey,
        secretAccessKey: secretKey,
      },
      region,
    });
  }

  async upload(options: StorageManagerUploadOptions): Promise<StorageManagerUploadEither> {
    const {
      key, body, contentType,
    } = options;

    try {
      const putCommand = new PutObjectCommand({
        Body: body,
        ContentType: contentType,
        Key: key,
        Bucket: this.options.name,
      });

      await this.s3Instance.send(putCommand);
    } catch (error) {
      const castedError = error as Error;

      return left(UnexpectedError.build({
        context: StorageManagerS3.name,
        error: castedError,
      }));
    }

    return right(true);
  }
}
