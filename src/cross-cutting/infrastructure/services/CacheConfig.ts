import { CryptoService, FvString } from '@discocil/fv-domain-library/domain';

import { stringify } from '@/cross-cutting/domain/helpers/stringify';

import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { Maybe } from '@discocil/fv-domain-library/domain';

type CacheConfigProps = {
  globallyEnabled: boolean;
  enabled: boolean;
  ttl: number;
};

type KeyMakerRequest = {
  name: Maybe<string>;
  method: string;
  url: string;
  query: Maybe<unknown>;
  body: Maybe<unknown>;
  language: Maybe<string>;
};

const hash = (stringToHash: string, prefix?: FvString): string => {
  const hashed = CryptoService.createHash(stringToHash, 'SHA-256');

  let finalHash = FvString.build(hashed);

  if (prefix) {
    finalHash = FvString.build(`${prefix}::`).concat(finalHash);
  }

  return finalHash.toPrimitive();
};

export const buildControllerCacheKey = (request: KeyMakerRequest): string => {
  const {
    name,
    method,
    url,
    query,
    body,
    language,
  } = request;

  const prefix = name.fold(
    () => FvString.empty(),
    name => FvString.build(name).replace(/controller/ig, '').toCamelCase(),
  );

  const unhashedString = FvString.build(method)
    .toLowerCase()
    .concat(url)
    .concat(language.getOrElse(''))
    .concat(stringify(query.getOrElse({})))
    .concat(stringify(body.getOrElse({})))
    .toPrimitive();

  return hash(unhashedString, prefix);
};

export const buildRepositoryCacheKey = (request: Criteria, prefix?: string): string => {
  const stringifiedCriteria = JSON.stringify(request);

  const unhashedString = FvString.build(stringifiedCriteria)
    .toLowerCase()
    .toPrimitive();

  const hashPrefix = prefix ? FvString.build(prefix).toCamelCase() : FvString.empty();

  return hash(unhashedString, hashPrefix);
};

export class CacheConfig {
  globallyEnabled: boolean;
  enabled: boolean;
  ttl: number;

  constructor(config: CacheConfigProps) {
    this.globallyEnabled = config.globallyEnabled;
    this.enabled = config.enabled;
    this.ttl = config.ttl;
  }

  isEnabled(): boolean {
    return this.globallyEnabled && this.enabled;
  }
}
