import {
  left, right, type Either, type FvError,
} from '@discocil/fv-domain-library';

import type { PaginationMetadataResponse, PaginationOption } from '@discocil/fv-criteria-converter-library/domain';
import type { FetchAllPages } from '../../domain/contracts/FetchAllPages';

export class FetchAllPagesService implements FetchAllPages {
  async execute<
    Response extends PaginationMetadataResponse,
    FnEitherResponse extends Either<ErrorResponse, Response>,
    ErrorResponse extends FvError = FvError
  >(
    currentPagination: PaginationOption,
    fn: (pagination: PaginationOption) => Promise<FnEitherResponse>,
  ): Promise<Either<ErrorResponse, Response[]>> {
    let nextPage = 0;

    const response: Response[] = [];

    let shouldFetchNextPage = false;

    do {
      const useCaseResultOrError = await fn(currentPagination);

      if (useCaseResultOrError.isLeft()) {
        return left(useCaseResultOrError.value);
      }

      const { pagination, ...rest } = useCaseResultOrError.value;

      response.push(rest as Response);

      if (pagination) {
        nextPage = pagination.next.getOrElse(0);
        shouldFetchNextPage = nextPage > 0;
      } else {
        shouldFetchNextPage = false;
      }

      currentPagination = { ...currentPagination, page: nextPage };
    } while (shouldFetchNextPage);

    return right(response);
  }
}
