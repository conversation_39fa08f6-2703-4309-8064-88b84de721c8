import pino from 'pino';
import PinoPretty from 'pino-pretty';

import { stringify } from '@/cross-cutting/domain/helpers/stringify';
import config, { isDeveloperEnv } from '@config/index';

import type { LoggerStrategy, UnknownObject } from '@discocil/fv-domain-library/domain';
import type { PinoLoggerOptions } from 'fastify/types/logger';
import type { BaseLogger } from 'pino';

export const prettyStream = PinoPretty({
  colorize: true,
  translateTime: true,
  ignore: 'pid,hostname',
});

export class <PERSON>no implements LoggerStrategy {
  private constructor(readonly client: BaseLogger) {}

  private static instance: Pino;

  static build(): Pino {
    if (!Pino.instance) {
      const client = this.initialize();

      Pino.instance = new Pino(client);
    }

    return Pino.instance;
  }

  static initialize(): BaseLogger {
    const {
      logger: { token: loggerToken, transport: target },
      env,
      serviceName,
      logLevel,
    } = config;

    const isLocal = isDeveloperEnv(env);

    const pinoProductionConfig = {
      level: logLevel,
      transport: {
        target,
        options: {
          rollbarOpts: {
            accessToken: loggerToken,
            payload: { environment: env },
          },
          logErrors: false,
        },
      },
    };

    const pinoConfig: PinoLoggerOptions = {
      name: serviceName,
      level: logLevel,
    };

    const logger = isLocal
      ? pino(pinoConfig, prettyStream)
      : pino({
        ...pinoConfig,
        ...pinoProductionConfig,
      });

    return logger;
  }

  error(message: string, context?: UnknownObject): void {
    this.client.error(`${message}\n ${stringify(context)}`, context);
  }

  info(message: string, context?: UnknownObject): void {
    this.client.info(message, context);
  }

  debug(message: string, context?: UnknownObject): void {
    this.client.debug(message, context);
  }

  warn(message: string, context?: UnknownObject): void {
    this.client.warn(message, context);
  }

  log(message: string, context?: UnknownObject): void {
    this.client.debug(message, context);
  }
}
