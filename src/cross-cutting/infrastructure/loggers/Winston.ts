import { LoggingWinston } from '@google-cloud/logging-winston';
import winston from 'winston';

import config from '@config/index';

import type { LoggerStrategy, UnknownObject } from '@discocil/fv-domain-library/domain';

export class Winston implements LoggerStrategy {
  private static instance: <PERSON>;

  private constructor(readonly client: winston.Logger) {}

  static build(): <PERSON> {
    if (!Winston.instance) {
      const client = this.initialize();

      Winston.instance = new Winston(client);
    }

    return Winston.instance;
  }

  static initialize(): winston.Logger {
    const {
      env,
      winston: {
        projectId,
        keyFilename,
        prefix,
        labels: { module: labelModule },
      },
    } = config;

    const loggingWinston = new LoggingWinston({
      projectId,
      keyFilename,
      labels: {
        env,
        labelModule,
      },
      prefix: `${prefix}${env}`,
    });

    return winston.createLogger({
      level: 'info',
      levels: winston.config.npm.levels,
      transports: [loggingWinston],
    });
  }

  error(message: string, context?: UnknownObject): void {
    this.client.error(message, context);
  }

  info(message: string, context?: UnknownObject): void {
    this.client.info(message, context);
  }

  debug(message: string, context?: UnknownObject): void {
    this.client.debug(message, context);
  }

  warn(message: string, context?: UnknownObject): void {
    this.client.warn(message, context);
  }

  log(message: string, context?: UnknownObject): void {
    this.client.debug(message, context);
  }
}
