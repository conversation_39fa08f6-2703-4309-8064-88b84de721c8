import { FvError, FvString } from '@discocil/fv-domain-library/domain';
import { createNamespace } from 'cls-hooked';

import { stringify } from '@/cross-cutting/domain/helpers/stringify';

import type {
  FvErrorStack,
  Logger as <PERSON>ogger,
  LoggerErrorMessage,
  LoggerMessage,
  UnknownObject,
} from '@discocil/fv-domain-library/domain';
import type { Namespace } from 'cls-hooked';
import type { FastifyError } from 'fastify';
import type { LoggerStrategy } from './LoggerStrategy';

type _LoggerErrorMessage = LoggerErrorMessage | FastifyError;

export class Logger implements ILogger {
  private readonly namespace: Namespace;

  constructor(private readonly loggerStrategy: LoggerStrategy, namespace: string) {
    this.namespace = createNamespace(namespace);
  }

  private getContext(): UnknownObject {
    return this.namespace.get('context') || {};
  }

  private buildLogContext(context?: UnknownObject): UnknownObject {
    return {
      ...this.getContext(),
      ...context,
    };
  }

  private buildErrorContext(message: _LoggerErrorMessage | LoggerMessage): FvErrorStack[] | undefined {
    if (message instanceof FvError) {
      return message.getStack();
    }

    return undefined;
  }

  private buildMessage(message: _LoggerErrorMessage | LoggerMessage): string {
    if (message instanceof Error) {
      return message.message;
    }

    if (FvString.is(message)) {
      return message;
    }

    return stringify(message);
  }

  addNameSpace(context: UnknownObject, done: () => void): void {
    this.namespace.run(() => {
      this.namespace.set('context', context);
      done();
    });
  }

  debug(message: _LoggerErrorMessage | LoggerMessage, context?: UnknownObject): void {
    const messageForLogger = this.buildMessage(message);
    const logContext = this.buildLogContext(context);
    const errorContext = this.buildErrorContext(message);

    const contextForError = {
      ...logContext,
      stack: errorContext,
    };

    this.loggerStrategy.debug(messageForLogger, contextForError);
  }

  info(message: LoggerMessage, context?: UnknownObject): void {
    const messageForLogger = this.buildMessage(message);
    const logContext = this.buildLogContext(context);
    const errorContext = this.buildErrorContext(message);

    const contextForError = {
      ...logContext,
      stack: errorContext,
    };

    this.loggerStrategy.info(messageForLogger, contextForError);
  }

  log(message: LoggerMessage, context?: UnknownObject): void {
    const messageForLogger = this.buildMessage(message);
    const logContext = this.buildLogContext(context);
    const errorContext = this.buildErrorContext(message);

    const contextForError = {
      ...logContext,
      stack: errorContext,
    };

    this.loggerStrategy.debug(messageForLogger, contextForError);
  }

  warn(message: _LoggerErrorMessage, context?: UnknownObject): void {
    const messageForLogger = this.buildMessage(message);
    const logContext = this.buildLogContext(context);
    const errorContext = this.buildErrorContext(message);

    const contextForError = {
      ...logContext,
      stack: errorContext,
    };

    this.loggerStrategy.warn(messageForLogger, contextForError);
  }

  error(message: _LoggerErrorMessage, context?: UnknownObject): void {
    const messageForLogger = this.buildMessage(message);
    const logContext = this.buildLogContext(context);
    const errorContext = this.buildErrorContext(message);

    const contextForError = {
      request: logContext,
      fvStack: errorContext,
    };

    this.loggerStrategy.error(messageForLogger, contextForError);
  }
}
