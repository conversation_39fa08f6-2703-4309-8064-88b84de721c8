
import { Enviroments } from '@config/enviroments';
import { isDeveloperEnv } from '@config/index';

import { <PERSON>no } from './Pino';
import { <PERSON> } from './<PERSON>';

import type { LogLevel } from '@config/Configuration';
import type { LoggerStrategy as ILoggerStrategy, UnknownObject } from '@discocil/fv-domain-library/domain';

type LoggerStrategyConfig = {
  rollbar: LogLevel[];
  googleCloud: LogLevel[];
};

export class LoggerStrategy {
  private readonly config = new Map<Enviroments, LoggerStrategyConfig>();

  constructor(private readonly environmet: Enviroments) {
    this.setConfiguration();
  }

  private execute(logLevel: LogLevel): ILoggerStrategy {
    const isLocal = isDeveloperEnv(this.environmet);

    if (isLocal) {
      return Pino.build();
    }

    const config = this.config.get(this.environmet);

    if (!config) {
      throw new Error(`Config not valid for ${this.environmet}`);
    }

    if (config.googleCloud.includes(logLevel)) {
      return Winston.build();
    }

    return Pino.build();
  }

  private setConfiguration(): void {
    const alphaConfig: LoggerStrategyConfig = {
      rollbar: ['error'],
      googleCloud: ['info', 'warn', 'debug', 'log'],
    };

    const productionConfig: LoggerStrategyConfig = {
      rollbar: ['error'],
      googleCloud: ['info', 'warn', 'debug', 'log'],
    };

    const lab1Config: LoggerStrategyConfig = {
      rollbar: [],
      googleCloud: ['info', 'warn', 'debug', 'log', 'error'],
    };

    this.config.set(Enviroments.ALPHA, alphaConfig);
    this.config.set(Enviroments.LAB1, lab1Config);
    this.config.set(Enviroments.PRODUCTION, productionConfig);
  }

  info(message: string, context?: UnknownObject): void {
    this.execute('info').info(message, context);
  }

  debug(message: string, context?: UnknownObject): void {
    this.execute('debug').debug(message, context);
  }

  log(message: string, context?: UnknownObject): void {
    this.execute('log').log(message, context);
  }

  warn(message: string, context?: UnknownObject): void {
    this.execute('warn').warn(message, context);
  }

  error(message: string, context?: UnknownObject): void {
    this.execute('error').error(message, context);
  }
}
