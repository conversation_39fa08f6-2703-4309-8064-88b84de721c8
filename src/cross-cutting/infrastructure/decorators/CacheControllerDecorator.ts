import { Maybe } from '@discocil/fv-domain-library/domain';

import config from '@config/index';

import { buildControllerCacheKey } from '../services/CacheConfig';

import type { FastifyReply, FastifyRequest } from 'fastify';

type CacheProps = {
  readonly ttl?: number;
};

export const cacheController = (cacheConfig?: CacheProps) => <TRequest extends FastifyRequest, TReply extends FastifyReply>(
  target: unknown,
  _propertyKey: string,
  descriptor: PropertyDescriptor,
): PropertyDescriptor => {
  const { enabled, endpoint } = config.cache;

  if (!enabled || !endpoint.enabled) {
    return descriptor;
  }

  const originalMethod = descriptor.value;

  descriptor.value = async function (...args: unknown[]): Promise<void> {
    const [request, reply] = args as [TRequest, TReply];

    const cachekey = buildControllerCacheKey({
      name: Maybe.fromValue(target?.constructor.name),
      method: request.method,
      url: request.originalUrl,
      query: Maybe.fromValue(request.query),
      body: Maybe.fromValue(request.body),
      language: Maybe.fromValue(request.fingerPrint.fingerPrint.language.veryShort),
    });

    const cacheditem = await request.cache.getItem(cachekey);

    if (cacheditem.isDefined()) {
      reply.send(cacheditem.get());

      return;
    }

    request.cache.setTtl(cacheConfig?.ttl);

    const originalMethodApplied = await originalMethod.apply(this, args);

    return originalMethodApplied;
  };

  return descriptor;
};
