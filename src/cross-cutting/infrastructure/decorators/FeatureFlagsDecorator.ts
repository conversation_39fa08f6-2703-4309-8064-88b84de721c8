import { Decorator } from '@discocil/fv-domain-library/application';

import { FeatureFlagsHandlerDevCycle } from '@/cross-cutting/infrastructure/services/FeatureFlagsHandlerDevCycle';

import type { UseCase } from '@discocil/fv-domain-library/application';
import type { Logger } from '@discocil/fv-domain-library/domain';

export class FeatureFlagsDecorator<I, O> extends Decorator<I, O> {
  constructor(
    private readonly logger: Logger,
    private readonly flagName: string,
    component: UseCase<I, Promise<O>>,
  ) {
    super(component);
  }

  async execute(dto: I): Promise<O> {
    const featureFlagsHandler = FeatureFlagsHandlerDevCycle.getInstance();
    const enabledInUseCase = featureFlagsHandler.isEnabled({ flagName: this.flagName, defaultValue: false });

    this.logger.debug({ enabledInUseCase });

    return await super.execute(dto);
  }
}
