/* eslint-disable object-curly-newline */

import Ajv from 'ajv';
import { container, instanceCachingFactory } from 'tsyringe';

import { BillingAddressContainer } from '@/billingAddresses/infrastructure/container/BillingAddressContainer';
import { BlockContainer } from '@/blocks/infrastructure/container/BlockContainer';
import { BookingContainer } from '@/bookings/bookings/infrastructure/container/BookingContainer';
import { BookingTypeContainer } from '@/bookings/bookingTypes/infrastructure/container/BookingTypeContainer';
import { BookingZoneContainer } from '@/bookings/bookingZones/infrastructure/container/BookingZoneContainer';
import { ClientContainer } from '@/clients/infrastructure/container/ClientContainer';
import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { RedisCacheRepository } from '@/cross-cutting/infrastructure/cache/RedisCacheHandler';
import { MongoConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import { Logger } from '@/cross-cutting/infrastructure/loggers/Logger';
import { LoggerStrategy } from '@/cross-cutting/infrastructure/loggers/LoggerStrategy';
import { StorageManagerS3 } from '@/cross-cutting/infrastructure/services/StorageManagerS3';
import { ArtistContainer } from '@/events/artists/infrastructure/container/ArtistContainer';
import { EventContainer } from '@/events/events/infrastructure/container/EventContainer';
import { VisitContainer } from '@/events/visits/infrastructure/container/VisitContainer';
import { FeatureFlagContainer } from '@/featureFlags/infrastructure/container/FeatureFlagContainer';
import { FeeCalculationResultContainer } from '@/feeCalculationResult/infrastructure/container/FeeCalculationResultContainer';
import { FeeContainer } from '@/fees/infrastructure/container/FeeContainer';
import { GatewayAccountContainer } from '@/gatewayAccount/infrastructure/container/GatewayAccountContainer';
import { GuestListLimitContainer } from '@/guestLists/guestListLimits/infrastructure/container/GuestListLimitContainer';
import { GuestListOrganizationLimitContainer } from '@/guestLists/guestListOrganizationLimits/infrastructure/container/GuestListOrganizationLimitContainer';
import { GuestListContainer } from '@/guestLists/guestLists/infrastructure/container/GuestListContainer';
import { GuestListTypeContainer } from '@/guestLists/guestListsTypes/infrastructure/container/GuestListTypeContainer';
import { ImageContainer } from '@/image/infrastructure/container/ImageContainer';
import { LocationContainer } from '@/locations/infrastructure/container/LocationContainer';
import { MicrositeContainer } from '@/microsite/infrastructure/container/MicrositeContainer';
import { OrderPaymentContainer } from '@/orders/orderPayments/infrastructure/container/OrderPaymentContainer';
import { OrderContainer } from '@/orders/orders/infrastructure/container/OrderContainer';
import { OrganizationContainer } from '@/organizations/organizations/infrastructure/container/OrganizationContainer';
import { PassContainer } from '@/passes/passes/infrastructure/container/PassContainer';
import { PassTypeContainer } from '@/passes/passTypes/infrastructure/container/PassTypeContainer';
import { PriceContainer } from '@/passes/prices/infrastructure/container/PriceContainer';
import { PaylinkContainer } from '@/paylinks/infrastructure/container/PaylinkContainer';
import { PaymentContainer } from '@/payments/infrastructure/container/PaymentContainer';
import { PhotoContainer } from '@/photos/infrastructure/container/PhotoContainer';
import { ReservationContainer } from '@/reservations/shared/infrastructure/container/ReservationContainer';
import { SaleContainer } from '@/sale/infrastructure/container/SaleContainer';
import { SeoContainer } from '@/seo/infrastructure/container/SeoContainer';
import { SubscriptionContainer } from '@/subscription/infrastructure/container/SubscriptionContainer';
import { DiscountCodeContainer } from '@/tickets/discountCodes/infrastructure/container/DiscountCodeContainer';
import { DiscountCodeExchangedContainer } from '@/tickets/discountCodesExchanged/infrastructure/container/DiscountCodeExchangedContainer';
import { QrCodeContainer } from '@/tickets/qrCodes/infrastructure/container/QrCodeContainer';
import { TicketLimitContainer } from '@/tickets/ticketLimits/infrastructure/container/TicketLimitContainer';
import { TicketOrganizationLimitContainer } from '@/tickets/ticketOrganizationLimit/infrastructure/container/TicketOrganizationLimitContainer';
import { TicketContainer } from '@/tickets/tickets/infrastructure/container/TicketContainer';
import { TicketTypeContainer } from '@/tickets/ticketsTypes/infrastructure/container/TicketTypeContainer';
import { TrackingContainer } from '@/tracking/infrastructure/container/TrackingContainer';
import { UpsellingContainer } from '@/upselling/infrastructure/container/UpsellingContainer';
import { UserContainer } from '@/user/infrastructure/container/UserContainer';
import { ZipCodeContainer } from '@/zipCode/infrastructure/container/ZipCodeContainer';
import { Server } from '@app/http/Server';
import config, { isDeveloperEnv } from '@config/index';

import { FetchHttpRepository } from '../database/repositories/FetchHttpRepository';
import { CacheConfig } from '../services/CacheConfig';
import { TranslatorI18nService } from '../translator/TranslatorI18nService';

import { MessageBrokerContainer } from './MessageBrokerContainer';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { InternalMessageBrokerClient } from '@/cross-cutting/domain/messageBroker/InternalMessageBrokerClientContracts';
import type { HttpRepository } from '@/cross-cutting/infrastructure/contracts/HttpRepository';
import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import type { BrokerConnection } from '@/cross-cutting/infrastructure/MessageBroker/contracts/BrokerConnection';

const containers = async (): Promise<void> => {
  container.register(Logger, {
    useFactory: instanceCachingFactory(() => {
      return new Logger(
        new LoggerStrategy(config.env),
        config.serviceName,
      );
    }),
  });

  container.register(Ajv, {
    useFactory: instanceCachingFactory(() => {
      return new Ajv({
        logger: container.resolve(DependencyIdentifier.Logger),
        formats: {
          'date-time': true,
          'time': true,
          'date': true,
        },
      });
    }),
  });

  container.register(DependencyIdentifier.Logger, {
    useFactory: instanceCachingFactory(container => container.resolve(Logger)),
  });

  const {
    env,
    mongo: { url, useCache, options },
  } = config;

  const mongoConnectionOrError = await MongoConnection.initialize({
    url,
    useCache,
    logger: container.resolve(DependencyIdentifier.Logger),
    isLocalEnv: isDeveloperEnv(env),
    options,
  });

  if (mongoConnectionOrError.isLeft()) {
    const errorException = mongoConnectionOrError.value;

    throw new Error(errorException.toString());
  }

  const mongoConnection = mongoConnectionOrError.value;

  const translatorService = await TranslatorI18nService.initialize(
    container.resolve(DependencyIdentifier.Logger),
    config.localization,
  );

  container.register(DependencyIdentifier.Translator, {
    useFactory: instanceCachingFactory(() => translatorService),
  });

  container.register(DependencyIdentifier.DatabaseConnection, {
    useFactory: instanceCachingFactory(() => mongoConnection),
  });

  container.register(DependencyIdentifier.CacheConfig, {
    useFactory: instanceCachingFactory(() => {
      return new CacheConfig({
        ...config.cache.database,
        globallyEnabled: config.cache.enabled,
      });
    }),
  });

  container.register(DependencyIdentifier.Ajv, {
    useFactory: instanceCachingFactory(container => container.resolve(Ajv)),
  });

  container.register(DependencyIdentifier.StorageManager, {
    useFactory: instanceCachingFactory(() => {
      const awsConfig = config.aws.s3;

      return new StorageManagerS3(awsConfig);
    }),
  });

  const redisConnection = await RedisCacheRepository.initialize(
    config.redis,
    container.resolve(DependencyIdentifier.Logger),
  );

  container.register(DependencyIdentifier.ICacheHandler, {
    useFactory: instanceCachingFactory<ICacheRepository>(() => redisConnection),
  });

  container.register(DependencyIdentifier.HttpRepository, {
    useFactory: instanceCachingFactory<HttpRepository>(() => new FetchHttpRepository()),
  });

  /**
   ** The order of the Containers is important because of internal dependencies.
   */
  ImageContainer.register();
  VisitContainer.register();
  GatewayAccountContainer.register();
  ClientContainer.register();
  ArtistContainer.register();
  BillingAddressContainer.register();
  PhotoContainer.register();
  LocationContainer.register();
  BlockContainer.register();
  ZipCodeContainer.register();
  SubscriptionContainer.register();
  OrganizationContainer.register();
  SeoContainer.register();
  UserContainer.register();
  PassTypeContainer.register();
  PaylinkContainer.register();
  PassContainer.register();
  PaymentContainer.register();
  SaleContainer.register();
  EventContainer.register();
  DiscountCodeContainer.register();
  DiscountCodeExchangedContainer.register();
  TicketLimitContainer.register();
  BookingTypeContainer.register();
  BookingZoneContainer.register();
  BookingContainer.register();
  GuestListLimitContainer.register();
  GuestListOrganizationLimitContainer.register();
  GuestListTypeContainer.register();
  GuestListContainer.register();
  TicketOrganizationLimitContainer.register();
  QrCodeContainer.register();
  TicketTypeContainer.register();
  TicketContainer.register();
  MicrositeContainer.register();
  OrderContainer.register();
  OrderPaymentContainer.register();
  PriceContainer.register();
  ReservationContainer.register();
  UpsellingContainer.register();
  FeatureFlagContainer.register();
  FeeContainer.register();
  FeeCalculationResultContainer.register();
  TrackingContainer.register();
};

const initializeContainer = async (): Promise<void> => {
  await containers();
  await MessageBrokerContainer.register();

  await TranslatorI18nService.initialize(
    container.resolve<Logger>(DependencyIdentifier.Logger),
    config.localization,
  );
};

const shutdownContainer = async (): Promise<void> => {
  const logger = container.resolve<Logger>(DependencyIdentifier.Logger);

  const app = container.resolve(Server);
  const brokerConnection = container.resolve<BrokerConnection>(DependencyIdentifier.BrokerConnection);
  const inMemoryConnection = container.resolve<InternalMessageBrokerClient>(DependencyIdentifier.InternalMessageBrokerClient);
  const cacheHandler = container.resolve<ICacheRepository>(DependencyIdentifier.ICacheHandler);
  const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

  try {
    await Promise.allSettled([
      app.stop(),
      dbConnection.disconnect(),
      brokerConnection.close(),
      inMemoryConnection.close(),
      cacheHandler.disconnect(),
    ]);
  } catch (error) {
    logger.error(`👎🏽 | Error while closing app: ${(error as Error).message}`);
  }
};

export { initializeContainer, shutdownContainer };
