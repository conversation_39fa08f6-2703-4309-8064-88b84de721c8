/* eslint-disable object-curly-newline */

import { container, instanceCachingFactory } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { InMemoryAsyncMessageBrokerClient } from '@/cross-cutting/infrastructure/MessageBroker/InMemory/InMemoryAsyncMessageBrokerClient';
import { AsyncMessageBrokerClientRabbit } from '@/cross-cutting/infrastructure/MessageBroker/rabbit/AsyncMessageBrokerClientRabbit';
import { RabbitMQConnection } from '@/cross-cutting/infrastructure/MessageBroker/rabbit/RabbitMQConnection';
import { FeeCalculationResultDependencyIdentifier } from '@/feeCalculationResult/domain/dependencyIdentifier/FeeCalculationResultDependencyIdentifier';
import { FeeCalculationResultListener } from '@/feeCalculationResult/infrastructure/listeners/FeeCalculationResultListener';
import { PassDependencyIdentifier } from '@/passes/passes/domain/dependencyIdentifier/PassDependencyIdentifier';
import { SubscriptionAddedOnPassPurchased } from '@/passes/passes/infrastructure/listeners/SubscriptionAddedOnPassPurchased';
import { ReservationModifiedListener } from '@/reservations/reservations/infrastructure/listeners/ReservationModifiedListener';
import { SaveReservationSubscriptionUseCase } from '@/subscription/application/SaveReservationSubscriptionUseCase';
import { CreateSubscriptionsUseCase } from '@/tickets/tickets/application/use-cases/CreateSubscriptionsUseCase';
import { CreateTicketSaleUseCase } from '@/tickets/tickets/application/use-cases/CreateTicketSaleUseCase';
import { TicketDependencyIdentifier } from '@/tickets/tickets/domain/dependencyIdentifier/TicketDependencyIdentifier';
import { TicketActivatedListener } from '@/tickets/tickets/infrastructure/listeners/TicketActivatedListener';
import { TicketPurchasedListener } from '@/tickets/tickets/infrastructure/listeners/TicketPurchasedListener';
import { TicketTypeListener } from '@/tickets/ticketsTypes/infrastructure/listeners/TicketTypeListener';
import { TrackingEventPurchaseListener } from '@/tracking/infrastructure/listeners/TrackingEventPurchaseListener';
import config from '@config/index';

import { exchangesConfigurations } from '../MessageBroker/rabbit/RoutingConfigs';

export const MessageBrokerContainer = {
  register: async (): Promise<void> => {
    const rabbitConnectionOrError = await RabbitMQConnection.initialize(config.rabbit.url, container.resolve(DependencyIdentifier.Logger));
    const eventBusOrError = InMemoryAsyncMessageBrokerClient.initialize(container.resolve(DependencyIdentifier.Logger));

    if (rabbitConnectionOrError.isLeft()) {
      const errorException = rabbitConnectionOrError.value;

      throw new Error(errorException.toString());
    }

    if (eventBusOrError.isLeft()) {
      const errorException = eventBusOrError.value;

      throw new Error(errorException.toString());
    }

    const rabbitConnection = rabbitConnectionOrError.value;
    const eventBus = eventBusOrError.value;

    container.register(DependencyIdentifier.BrokerConnection, {
      useFactory: instanceCachingFactory(() => rabbitConnection),
    });

    const asyncMessageBrokerClientRabbit = await AsyncMessageBrokerClientRabbit.initialize({
      connection: rabbitConnection,
      logger: container.resolve(DependencyIdentifier.Logger),
      exchangesConfigurations,
    });

    container.register(DependencyIdentifier.ExternalMessageBrokerClient, {
      useFactory: instanceCachingFactory(() => asyncMessageBrokerClientRabbit),
    });

    container.register(DependencyIdentifier.InternalMessageBrokerClient, {
      useFactory: instanceCachingFactory((container) => {
        const subscriptionAddedOnPassPurchased = new SubscriptionAddedOnPassPurchased(
          container.resolve(DependencyIdentifier.SubscriptionPassPresenter),
          container.resolve(PassDependencyIdentifier.IPassEmitter),
        );

        const reservationModifiedListener = new ReservationModifiedListener(
          container.resolve(SaveReservationSubscriptionUseCase),
        );

        const ticketPurchasedListener = new TicketPurchasedListener(
          container.resolve(TicketDependencyIdentifier.ITicketEmitter),
          container.resolve(TicketDependencyIdentifier.ITicketTypeTotalService),
          container.resolve(TicketDependencyIdentifier.IUpdateEventInCacheFromPurchaseService),
          container.resolve(DependencyIdentifier.Logger),
        );

        const ticketActivatedListener = new TicketActivatedListener(
          container.resolve(TicketDependencyIdentifier.ITicketEmitter),
          container.resolve(CreateTicketSaleUseCase),
          container.resolve(CreateSubscriptionsUseCase),
          container.resolve(DependencyIdentifier.Logger),
        );

        const ticketTypeListener = new TicketTypeListener(
          container.resolve(DependencyIdentifier.ExternalMessageBrokerClient),
          container.resolve(DependencyIdentifier.Logger),
        );

        const trackingEventPurchaseListener = new TrackingEventPurchaseListener(
          container.resolve(TicketDependencyIdentifier.TicketRepository),
          container.resolve(DependencyIdentifier.Logger),
        );

        const feeCalculationResultListener = new FeeCalculationResultListener(
          container.resolve(FeeCalculationResultDependencyIdentifier.FeeCalculationResultRepository),
          container.resolve(DependencyIdentifier.Logger),
        );

        eventBus.addSubscribers([
          subscriptionAddedOnPassPurchased,
          ticketPurchasedListener,
          ticketActivatedListener,
          reservationModifiedListener,
          ticketTypeListener,
          trackingEventPurchaseListener,
          feeCalculationResultListener,
        ]);

        return eventBus;
      }),
    });
  },
};
