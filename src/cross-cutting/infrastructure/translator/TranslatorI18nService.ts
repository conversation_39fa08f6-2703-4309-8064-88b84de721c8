import path from 'node:path';

import i18next, { t } from 'i18next';
import i18nBackend from 'i18next-fs-backend';

import type { TranslationKeys } from '@/cross-cutting/domain/contracts/TranslationKeys';
import type { Translator } from '@/cross-cutting/domain/contracts/TranslatorContracts';
import type { Configuration } from '@config/Configuration';
import type { Logger, UnknownObject } from '@discocil/fv-domain-library/domain';
import type { FsBackendOptions } from 'i18next-fs-backend';

export class TranslatorI18nService implements Translator {
  private static instance: TranslatorI18nService;

  private constructor() { }

  static async initialize(
    logger: Logger,
    localizationConfig: Configuration['localization'],
  ): Promise<TranslatorI18nService> {
    if (TranslatorI18nService.instance) {
      return TranslatorI18nService.instance;
    }

    try {
      await i18next
        .use(i18nBackend)
        .init<FsBackendOptions>({
          lng: localizationConfig.defaultLocale,
          fallbackLng: localizationConfig.fallbackLocale,
          supportedLngs: localizationConfig.supportedLocales,
          load: 'all',
          initAsync: false,
          backend: { loadPath: path.join(`${localizationConfig.localesPath}`, '{{lng}}', '{{ns}}.json') },
          saveMissing: false,
          returnEmptyString: false,
          ns: localizationConfig.namespaces,
          interpolation: { escapeValue: false },
          contextSeparator: ':',
        });

      logger.info(`✅ Localization initialized`);
    } catch (error) {
      const parsedError = error as Error;

      logger.error(`Localization failed to initialize: ${parsedError.message}`);
    }


    this.instance = new TranslatorI18nService();

    return this.instance;
  }

  setLanguage(lang: string): this {
    i18next.changeLanguage(lang);

    return this;
  }

  translate(key: TranslationKeys, options: UnknownObject = {}): string {
    return t(key, options);
  }
}
