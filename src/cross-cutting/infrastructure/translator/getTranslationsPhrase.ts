import fs from 'node:fs';
import https from 'node:https';
import path from 'node:path';

import dotenv from 'dotenv';

import type { RequestOptions } from 'node:https';

dotenv.config();
// If .env is present will load it, otherwise env variables should be set from outside source

// TODO: temporal solution
const { PHRASE_KEY = '****************************************************************' } = process.env;

const localizationConfig = {
  // TODO: temporal until we figure out how to import ts files into natively transpiled js file
  localesPath: 'src/cross-cutting/domain/locales',
  defaultLocale: 'en',
  fallbackLocale: 'es',
  supportedLocales: ['es', 'en', 'fr', 'it', 'nl', 'ca', 'pt'],
  namespaces: [
    'common',
    'pos',
  ],
  phraseApiUrl: 'https://api.phrase.com/v2/projects/',
  phraseProjectId: 'adfcbcff78823027d3367d4af5e2eca7',
};

const FILE_FORMATS = {
  i18next_4: 'i18next_4',
  angular_translate: 'angular_translate',
} as const;

function makeRequest(url: string, options: RequestOptions): Promise<string | ArrayBufferView<ArrayBufferLike>> {
  return new Promise((resolve, reject) => {
    const request = https.request(url, options, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`HTTP request failed with status code ${response.statusCode}`));
      }

      let receivedData = '';

      response.on('data', (chunk) => {
        receivedData += chunk;
      });
      response.on('end', () => {
        resolve(receivedData);
      });
    });

    request.on('error', (error) => {
      reject(error);
    });

    request.end();
  });
}

function ensureDirectoryExists(dirPath: string): void {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

type ProcessNamespacesRequest = {
  namespaces: Array<string>;
  locales: Array<string>;
  fileFormat: keyof typeof FILE_FORMATS;
  apiKey: string;
};

async function processNamespaces({
  namespaces,
  locales,
  fileFormat,
  apiKey,
}: ProcessNamespacesRequest): Promise<Set<string>> {
  const localeKeys = new Set<string>();

  try {
    const options = { auth: `${apiKey}:` };

    let createdFileCount = 0;

    for await (const _namespace of namespaces) {
      for await (const _locale of locales) {
        createdFileCount++;

        const queryparams = new URLSearchParams();

        queryparams.append('file_format', fileFormat);
        queryparams.append('include_empty_translations', 'true');
        queryparams.append('include_unverified_translations', 'true');
        queryparams.append('fallback_locale_id', localizationConfig.fallbackLocale);
        queryparams.append('tags', _namespace);

        const url = `${localizationConfig.phraseApiUrl}${localizationConfig.phraseProjectId}/locales/${_locale}/download?${queryparams.toString()}`;

        const outputDir = path.join(localizationConfig.localesPath, _locale);

        ensureDirectoryExists(outputDir);

        const outputPath = path.join(outputDir, `${_namespace}.json`);

        const response = await makeRequest(url, options) as string;

        fs.writeFileSync(outputPath, response, 'utf-8');

        if (_locale === localizationConfig.defaultLocale) {
          const parsedResponse = JSON.parse(response);

          const extractedKeys = getKeysRecursively(parsedResponse);

          for (const _translationKey of extractedKeys) {
            localeKeys.add(`${_namespace}:${_translationKey}`);
          }
        }

        // eslint-disable-next-line
        console.log(`[${createdFileCount}] Created ${_locale}/${_namespace}`);

      }
    }
  } catch (error) {
    const parsedError = error as Error;

    // eslint-disable-next-line
    console.error('Error downloading translations:', parsedError);
  } finally {
    return localeKeys;
  }
}

function getKeysRecursively(localeObj: Record<string, unknown>, prefix = ''): string[] {
  const keys: string[] = [];

  for (const [key, value] of Object.entries(localeObj)) {
    const currentKey = prefix ? `${prefix}.${key}` : key;

    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys.push(...getKeysRecursively(value as Record<string, unknown>, currentKey));
    } else {
      keys.push(currentKey);
    }
  }

  return keys;
}

// Commented for future use and reference
/*
async function generateTypes(localeKeys: Set<string>): Promise<void> {
  const typesFilePath = 'src/cross-cutting/domain/contracts/TranslationKeys.ts';

  if (!fs.existsSync(typesFilePath)) {
    throw new Error(`Could not find ${typesFilePath}`);
  }

  let typeStr = '';

  const localeKeysList = [...localeKeys];

  for (let _i = 0; _i < localeKeysList.length; _i++) {
    const prefix = _i === 0 ? '' : '\n  | ';
    const suffix = _i === localeKeys.size - 1 ? '\n' : '';

    typeStr += `${prefix}'${localeKeysList[_i]}'${suffix}`;
  }

  if (typeStr.length === 0) {
    typeStr = 'string';
  }

  // Regex playground with current example: https://regex101.com/r/oKQW13/1
  const regex = new RegExp(/^(\/\/.*\n)?(export type TranslationKeys\s+=\s)((\|?((.|string)+?)\s*)+?);/gm);

  const headerComment = `// WARNING: This type was automatically generated by 'npm run locales' script at ${new Date().toISOString()}`;

  const content = fs.readFileSync(typesFilePath, 'utf-8').replace(regex, `${headerComment}\n$2${typeStr};`);

  fs.writeFileSync(typesFilePath, content);
}
*/

async function main(): Promise<void> {
  if (!PHRASE_KEY) {
    throw new Error('Missing process.env.PHRASE_KEY');
  }

  const options = { auth: `${PHRASE_KEY}:` };

  const localesUrl = `https://api.phrase.com/v2/projects/${localizationConfig.phraseProjectId}/locales`;
  const localesResponse = await makeRequest(localesUrl, options) as string;
  const locales = JSON.parse(localesResponse).map((locale: Record<string, unknown>) => locale.code);

  // eslint-disable-next-line
  console.log(`Found locales: ${locales.join(', ')}`);

  await processNamespaces({
    namespaces: localizationConfig.namespaces,
    locales,
    fileFormat: FILE_FORMATS.i18next_4,
    apiKey: PHRASE_KEY,
  });

  // eslint-disable-next-line
  console.log(`All translations downloaded in ${localizationConfig.localesPath}`);
}

main();
