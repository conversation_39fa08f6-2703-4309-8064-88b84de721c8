import { FvError, type FvErrorRequest } from '@discocil/fv-domain-library';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

export class ServiceTemporallyUnavailable extends FvError {
  static readonly defaultCause = EErrorKeys.INTERNAL_ERROR;

  static build(request: FvErrorRequest): ServiceTemporallyUnavailable {
    const exceptionMessage = 'Service temporarilly unavailable';

    const {
      context, error, data, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
