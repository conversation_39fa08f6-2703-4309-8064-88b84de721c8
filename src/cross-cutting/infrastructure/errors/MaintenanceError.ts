import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { ErrorMethodRequest } from '@/cross-cutting/domain/errors/ErrorRequest';

export class MaintenanceError extends FvError {
  static readonly defaultCause = EErrorKeys.INVALID_FIELD;

  static build(request: ErrorMethodRequest): MaintenanceError {
    const exceptionMessage = `Application in maintenance. Try again later`;

    const {
      context, error, data, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
