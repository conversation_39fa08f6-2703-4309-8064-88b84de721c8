import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { FvErrorRequest } from '@discocil/fv-domain-library/domain';

export class UnauthorizedError extends FvError {
  static readonly defaultCause = EErrorKeys.INVALID_FIELD;

  static build(request: FvErrorRequest): UnauthorizedError {
    const {
      context, error, data, target,
    } = request;

    const defaultMessage = 'Unauthorized';

    return new this(
      defaultMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
