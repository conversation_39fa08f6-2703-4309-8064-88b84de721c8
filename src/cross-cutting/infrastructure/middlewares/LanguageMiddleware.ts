import { container, singleton } from 'tsyringe';


import { Translator } from '@/cross-cutting/domain/contracts/TranslatorContracts';
import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';

import type { FastifyRequest } from 'fastify';

@singleton()
export class LanguageMiddleware {
  async handle(request: FastifyRequest): Promise<void> {
    const language = request.fingerPrint.fingerPrint.language.veryShort;

    const translator = container.resolve<Translator>(DependencyIdentifier.Translator);

    translator.setLanguage(language);
  }
}
