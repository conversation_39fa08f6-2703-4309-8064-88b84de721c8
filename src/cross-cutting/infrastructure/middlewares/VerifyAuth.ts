import {
  Maybe,
  left,
  right,
} from '@discocil/fv-domain-library/domain';


import config from '@config/index';

import { UnauthorizedError } from '../errors/UnauthorizedError';

import type { Either } from '@discocil/fv-domain-library/domain';
import type { FastifyRequest } from 'fastify';

const availableAuthPrefixes = ['Bearer', 'Fv'] as const;

type VerifyAuthEither = Either<UnauthorizedError, boolean>;
type Prefix = (typeof availableAuthPrefixes)[number];

type GetAuthPartsResult = {
  readonly prefix: Maybe<string>;
  readonly value: Maybe<string>;
};

export class VerifyAuth {
  private constructor() {}

  static async handle(request: FastifyRequest): Promise<VerifyAuthEither> {
    const headers = request.headers;
    const authHeader = headers.authorization;

    if (!authHeader) {
      return left(UnauthorizedError.build({
        context: this.constructor.name,
        data: { url: request.url },
      }));
    }

    const { prefix, value } = this.getAuthParts(authHeader);

    const prefixResult = this.verifyPrefix(prefix);

    if (prefixResult.isLeft()) {
      return left(prefixResult.value);
    }

    if (value.isEmpty()) {
      return left(UnauthorizedError.build({
        context: this.constructor.name,
        data: { url: request.url },
      }));
    }

    if (prefix.get() === 'Fv') {
      return this.verifyFvToken(value.get());
    }

    return await this.verifyJwt(request);
  }

  private static verifyPrefix(prefix: Maybe<string>): VerifyAuthEither {
    if (prefix.isEmpty()) {
      return left(UnauthorizedError.build({
        context: this.constructor.name,
        data: { prefix },
      }));
    }

    if (!availableAuthPrefixes.includes(prefix.get() as Prefix)) {
      return left(UnauthorizedError.build({
        context: this.constructor.name,
        data: { prefix },
      }));
    }

    return right(true);
  }

  private static async verifyJwt(request: FastifyRequest): Promise<VerifyAuthEither> {
    try {
      const jwtVerifyResponse = await request.jwtVerify();

      return right(Boolean(jwtVerifyResponse));

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (_error) {
      return left(UnauthorizedError.build({
        context: this.constructor.name,
        data: { url: request.url },
      }));
    }
  }

  private static verifyFvToken(fvTokenFromAuth: string): VerifyAuthEither {
    const fvToken = config.apikey;

    if (fvTokenFromAuth !== fvToken) {
      return left(UnauthorizedError.build({ context: this.constructor.name }));
    }

    return right(true);
  }

  private static getAuthParts(auth: string): GetAuthPartsResult {
    const [rawPrefix, rawValue] = auth.split(' ');

    const prefix = Maybe.fromValue(rawPrefix);
    const authValue = Maybe.fromValue(rawValue);

    return { prefix, value: authValue };
  }
}
