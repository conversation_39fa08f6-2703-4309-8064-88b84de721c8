import { CryptoService, FvDate } from '@discocil/fv-domain-library/domain';
import { singleton } from 'tsyringe';
import useragent from 'useragent';

import config from '@config/index';

import type { FastifyRequest } from 'fastify';
import type { FingerPrint } from './contracts/FingerPrint';

@singleton()
export class FingerPrintMiddleware {
  private _makeHash(value: string): string {
    return CryptoService.createHash(value, 'md5');
  }

  async handle(request: FastifyRequest): Promise<void> {
    const startTime = FvDate.create().toMilliseconds();

    const { headers } = request;
    const { accept } = headers;

    const agent = useragent.parse(headers['user-agent']) ?? {};
    const language = headers['accept-language'] || config.language;
    const ip = headers['x-real-ip'] ?? headers.ip ?? '127.0.0.1';
    const deviceId = headers['device-id'] ?? '';
    const geo = {}; // Geoip.lookup(ip);

    const hash = this._makeHash(
      JSON.stringify({
        browser: { name: agent.family },
        device: { name: agent.device.family },
        os: { name: agent.os.family },
        accept,
        language: { short: language.substring(0, 5) },
      }),
    );

    const fingerPrint: FingerPrint = {
      browser: {
        source: agent.source,
        name: agent.family,
        versions: {
          major: +agent.major,
          minor: +agent.minor,
          patch: +agent.patch,
        },
      },
      device: {
        id: deviceId.toString(),
        hash: this._makeHash(hash + deviceId.toString()),
        name: agent.device.family,
        versions: {
          major: +agent.device.major,
          minor: +agent.device.minor,
          patch: +agent.device.patch,
        },
      },
      os: {
        name: agent.os.family,
        versions: {
          major: +agent.os.major,
          minor: +agent.os.minor,
          patch: +agent.os.patch,
        },
      },
      language: {
        name: language,
        short: language.substring(0, 5),
        veryShort: language.substring(0, 2),
      },
      geo: {
        geo,
        hash: this._makeHash(JSON.stringify(geo)),
      },
      accept,
      hash,
      ip: ip.toString(),
      country: '',
    };

    request.fingerPrint = {
      fingerPrint,
      cost: FvDate.create().toMilliseconds() - startTime,
    } as const;
  }
}
