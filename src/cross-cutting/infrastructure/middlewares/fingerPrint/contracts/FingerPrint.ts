type Versions = {
  readonly major: number;
  readonly minor: number;
  readonly patch: number;
};

export type FingerPrint = {
  readonly browser: {
    readonly source: string;
    readonly name: string;
    readonly versions: Versions;
  };
  readonly device: {
    readonly id: string;
    readonly hash: string;
    readonly name: string;
    readonly versions: Versions;
  };
  readonly os: {
    readonly name: string;
    readonly versions: Versions;
  };
  readonly language: {
    readonly name: string;
    readonly short: string;
    readonly veryShort: string;
  };
  readonly geo: {
    readonly geo: Record<string, unknown>;
    readonly hash: string;
  };
  readonly accept?: string;
  readonly hash: string;
  readonly ip: string;
  readonly country: string;
};
