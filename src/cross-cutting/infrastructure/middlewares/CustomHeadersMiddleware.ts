import { FvString } from '@discocil/fv-domain-library/domain';
import { singleton } from 'tsyringe';

import config from '@config/index';

import type { FastifyRequest } from 'fastify';

export enum ECustomHeaders {
  X_APPLICATION = 'x-application'
}

@singleton()
export class CustomHeadersMiddleware {
  async handle(request: FastifyRequest): Promise<void> {
    const { headers } = request;

    const header = headers[ECustomHeaders.X_APPLICATION];
    const sanitizideHeader = FvString.removeSpaces(header).toPrimitive();

    const applicationId = sanitizideHeader || config.fv.apps.clients.applicationId;

    request.application = { id: applicationId } as const;
  }
}
