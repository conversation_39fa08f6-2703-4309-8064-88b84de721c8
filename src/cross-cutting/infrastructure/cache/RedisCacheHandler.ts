import {
  FvDate, FvString, PerformanceMeasurement,
} from '@discocil/fv-domain-library/domain';
import { createClient } from 'redis';

import { RedisEventsHandler } from './RedisEventsHandler';

import type { Configuration } from '@config/Configuration';
import type { Logger } from '@discocil/fv-domain-library/domain';
import type { RedisClientType } from 'redis';
import type { ICacheRepository } from '../../domain/contracts/CacheHandler';

type RedisConfig = Configuration['redis'];

export class RedisCacheRepository implements ICacheRepository {
  private static instance: RedisCacheRepository | undefined;

  private constructor(
    private readonly client: RedisClientType,
    private readonly config: RedisConfig,
    private readonly logger: Logger,
  ) { }

  static async initialize(config: RedisConfig, logger: Logger): Promise<RedisCacheRepository> {
    if (RedisCacheRepository.instance) {
      return RedisCacheRepository.instance;
    }

    const client = this.createClient(config);

    const events = new RedisEventsHandler(
      client,
      logger,
      PerformanceMeasurement.create(this.constructor.name),
    );

    events.bind();

    try {
      await client.connect();
    } catch (error) {
      this.handleError(error as Error, logger);
    }

    RedisCacheRepository.instance = new RedisCacheRepository(client, config, logger);

    return RedisCacheRepository.instance;
  }

  private static createClient(config: RedisConfig): RedisClientType {
    const {
      host, port, database,
    } = config;
    const url = `redis://${host}:${port}`;

    const reconnectStrategy = (retries: number): false | Error | number => {
      return retries === 5 ? new Error('Max retries reached') : FvDate.IN_MS.FIVE_SECOND;
    };

    return createClient({
      url,
      database,
      socket: { reconnectStrategy },
    });
  }

  private static handleError(cacheError: Error, logger: Logger): void {
    const { message } = cacheError;

    if (message.includes('ECONNREFUSED')) {
      logger.error(`Error on Redis client connection: ${message}`, { error: cacheError });

      return;
    }

    logger.error(`Error on redis client: ${message}`, { error: cacheError });
  }

  // private async selectDatabase(database: number): Promise<void> {
  //   await this.client.select(database);
  // }

  async get(key: string): Promise<string | null> {
    // await this.selectDatabase(this.config.database);

    return this.client.get(key);
  }

  async set(key: string, value: unknown, ttl: number = this.config.ttl): Promise<void> {
    // await this.selectDatabase(this.config.database);

    const stringifiedValue = FvString.is(value) ? value : JSON.stringify(value);

    await this.client.set(key, stringifiedValue, { EX: ttl });
  }

  async remove(key: string): Promise<void> {
    // await this.selectDatabase(this.config.database);

    await this.client.del([key]);
  }

  async removeMany(keys: string[]): Promise<void> {
    // await this.selectDatabase(this.config.database);

    await this.client.del(keys);
  }

  async disconnect(): Promise<void> {
    this.client.destroy();

    RedisCacheRepository.instance = undefined;

    this.logger.info(`👊🏽 | Redis connection closed successfully`);
  }
}
