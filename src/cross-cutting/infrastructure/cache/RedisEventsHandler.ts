import type { Logger, PerformanceMeasurement } from '@discocil/fv-domain-library/domain';
import type { RedisClientType } from 'redis';

export class RedisEventsHandler {
  constructor(
    private readonly client: RedisClientType,
    private readonly logger: Logger,
    private readonly performanceMeasurement: PerformanceMeasurement,
  ) {}

  bind(): void {
    this.client.on('error', (error: Error) => {
      this.logger.error('❌ Redis client error', { error });
    });

    this.client.on('connect', () => {
      this.logger.info('🔌 Redis client connected');
    });

    this.client.on('reconnecting', () => {
      this.logger.info('🔄 Redis client reconnecting');
    });

    this.client.on('end', () => {
      this.logger.info('🔌 Redis client disconnected');
    });

    this.client.on('ready', () => {
      this.logger.info(`✅ Redis client ready in ${this.performanceMeasurement.end()} ms`);
    });

    this.client.on('warning', (warning: Error) => {
      this.logger.warn('⚠️ Redis client warning', { warning });
    });

    this.client.on('close', () => {
      this.logger.info('❌ Redis client closed');
    });

    this.client.on('select', (db: number) => {
      this.logger.info('📂 Redis client selected db', { db });
    });
  }
}
