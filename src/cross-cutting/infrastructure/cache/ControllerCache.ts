import {
  FvDate, FvNumber, Maybe,
} from '@discocil/fv-domain-library/domain';
import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';

export class ControllerCache {
  private _ttl = FvDate.IN_SECONDS.TEN;
  private _shouldCache = true;
  private _key = Maybe.none<string>();
  private _isHit = false;

  private readonly _client = container.resolve<ICacheRepository>(DependencyIdentifier.ICacheHandler);

  private constructor() {}

  static build(): ControllerCache {
    return new this();
  }

  setTtl(ttl?: number): ControllerCache {
    if (FvNumber.is(ttl)) {
      this._ttl = ttl;
    }

    return this;
  }

  inactiveShouldCache(): ControllerCache {
    this._shouldCache = false;

    return this;
  }

  private setKey(key: string): ControllerCache {
    this._key = Maybe.fromValue(key);

    return this;
  }

  activeHit(): ControllerCache {
    this._isHit = true;

    return this;
  }

  setItem(payload: unknown): this {
    if (this.shouldCache && this._key.isDefined()) {
      this._client.set(this._key.get(), payload, this._ttl);
    }

    return this;
  }

  async getItem(key: string): Promise<Maybe<unknown>> {
    this.setKey(key);

    const cachedItem = await this._client.get(key);

    if (!cachedItem) {
      return Maybe.none();
    }

    this.activeHit();
    this.inactiveShouldCache();

    return Maybe.fromValue(JSON.parse(cachedItem));
  }

  get ttl(): number {
    return this._ttl;
  }

  get shouldCache(): boolean {
    return this._shouldCache;
  }

  get isHit(): boolean {
    return this._isHit;
  }
}
