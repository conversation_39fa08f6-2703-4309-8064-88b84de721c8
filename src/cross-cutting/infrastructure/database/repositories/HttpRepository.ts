import type { HttpBodyParams } from '../../contracts/HttpParams';
import type { HttpRepository } from '../../contracts/HttpRepository';

type UrlParams = {
  readonly url?: string;
  readonly queryParams?: URLSearchParams;
  readonly urlParam?: string;
  readonly bodyParam?: HttpBodyParams;
};

export abstract class BaseHttpRepository {
  constructor(
    protected readonly baseUrl: string,
    protected readonly httpRepository: HttpRepository,
  ) { }

  protected abstract makeHeaders(): Headers;

  protected makeUrl(params?: UrlParams): URL {
    const url = new URL(`${this.baseUrl}/${params?.url}`);

    if (params?.urlParam) {
      url.pathname += url.pathname.endsWith('/')
        ? params.urlParam
        : `/${params.urlParam}`;
    }

    if (params?.queryParams) {
      url.search = params.queryParams.toString();
    }

    return url;
  }
}
