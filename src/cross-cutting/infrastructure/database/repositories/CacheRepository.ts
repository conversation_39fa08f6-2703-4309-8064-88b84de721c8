import { buildRepositoryCacheKey } from '@/cross-cutting/infrastructure/services/CacheConfig';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

type CacheResponse = {
  readonly cacheKey: string;
  readonly cacheHit: string | null;
};

export abstract class CacheRepository {
  protected abstract getConfig(): CacheConfig;
  protected abstract getHandler(): ICacheRepository;

  protected async getCache(criteria: Criteria): Promise<CacheResponse> {
    const cacheKey = buildRepositoryCacheKey(criteria, this.constructor.name);
    const cacheHit = this.getConfig().isEnabled() ? await this.getHandler().get(cacheKey) : null;

    return {
      cacheKey,
      cacheHit,
    };
  }
}
