import {
  left, PerformanceMeasurement, right,
} from '@discocil/fv-domain-library/domain';
import mongoose from 'mongoose';

import type { Configuration } from '@config/Configuration';
import type { Either, Logger } from '@discocil/fv-domain-library/domain';

type Connection = mongoose.Connection;
type ConnectionOptions = Configuration['mongo']['options'];

type InitializeRequest = {
  readonly url: string;
  readonly logger: Logger;
  readonly useCache?: boolean;
  readonly isLocalEnv?: boolean;
  readonly options?: ConnectionOptions;
};

export interface DatabaseConnection {
  disconnect: () => Promise<void>;
  getPort: () => number | undefined;
  logServerStatus: () => Promise<void>;
  useDb: (dbName: string) => Connection;
}

export class MongoConnection implements DatabaseConnection {
  private static instance: MongoConnection | undefined;

  private constructor(
    private readonly connection: Connection,
    private readonly logger: Logger,
    private readonly isLocalEnv: boolean,
    private readonly useCache: boolean,
  ) {}

  static async initialize(request: InitializeRequest): Promise<Either<Error, DatabaseConnection>> {
    if (MongoConnection.instance) {
      return right(MongoConnection.instance);
    }

    const {
      url, logger, isLocalEnv, options, useCache,
    } = request;

    try {
      const performanceMeasurement = PerformanceMeasurement.create(this.constructor.name);

      const connection = await mongoose.createConnection(url, options).asPromise();

      MongoConnection.instance = new MongoConnection(connection, logger, isLocalEnv ?? false, useCache ?? false);

      logger.info(`✅ Connected to Mongo Database in ${performanceMeasurement.end()} ms`);

      return right(MongoConnection.instance);
    } catch (error) {
      const parsedError = error as Error;

      logger.debug(parsedError.message);

      return left(parsedError);
    }
  }

  useDb(dbName: string): Connection {
    return this.connection.useDb(dbName, { useCache: this.useCache });
  }

  getPort(): number | undefined {
    return this.connection.getClient().options.hosts[0]?.port;
  }

  async logServerStatus(): Promise<void> {
    if (!this.isLocalEnv) {
      return;
    }

    const adminDb = this.connection.db?.admin();

    if (!adminDb) {
      return;
    }

    const serverStatus = await adminDb.command({ serverStatus: 1 });

    this.logger.debug({
      connections: {
        current: serverStatus.connections.current,
        available: serverStatus.connections.available,
        totalCreated: serverStatus.connections.totalCreated,
      },
    });
  }

  async disconnect(): Promise<void> {
    await this.connection.close();

    MongoConnection.instance = undefined;

    this.logger.info(`👊🏽 | Database connection closed successfully`);
  }
}
