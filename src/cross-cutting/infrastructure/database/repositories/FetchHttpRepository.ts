import { left, right } from '@discocil/fv-domain-library/domain';

import { HttpError } from '../../errors/HttpError';

import type {
  HttpRepository,
  HttpResponse,
  HttpResponseEither,
} from '@/cross-cutting/infrastructure/contracts/HttpRepository';
import type { HTTP_CODES } from '@app/http/HttpCodes';
import type { Either } from '@discocil/fv-domain-library/domain';
import type { HttpParams } from '../../contracts/HttpParams';

export class FetchHttpRepository implements HttpRepository {
  async get<RESPONSE>(params: HttpParams): Promise<HttpResponseEither<RESPONSE>> {
    const { url, headers } = params;

    const response = await fetch(url, { headers });

    if (!response.ok) {
      return this.errorResponse(response, params);
    }

    return right(await response.json() as HttpResponse<RESPONSE>);
  }

  async post<RESPONSE>(params: HttpParams): Promise<HttpResponseEither<RESPONSE>> {
    const {
      url, bodyParams, headers,
    } = params;

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(bodyParams ?? {}),
    });

    if (!response.ok) {
      return this.errorResponse(response, params);
    }

    return right(await response.json() as HttpResponse<RESPONSE>);
  }

  async patch<RESPONSE>(params: HttpParams): Promise<HttpResponseEither<RESPONSE>> {
    const {
      url, bodyParams, headers,
    } = params;

    const response = await fetch(url, {
      method: 'PATCH',
      headers,
      body: JSON.stringify(bodyParams ?? {}),
    });

    if (!response.ok) {
      return this.errorResponse(response, params);
    }

    return right(await response.json() as HttpResponse<RESPONSE>);
  }

  private async errorResponse(response: Response, params: HttpParams): Promise<Either<HttpError, never>> {
    const errorBody = JSON.parse(await response.text());

    return left(HttpError.build(params, response.status as HTTP_CODES, errorBody));
  }
}
