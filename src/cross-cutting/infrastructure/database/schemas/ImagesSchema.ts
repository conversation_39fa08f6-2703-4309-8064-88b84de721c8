const imageSizesSchema = {
  original: { type: String },
  versioned: { type: String },
  sized: { type: String },
  isDefault: { type: Boolean },
};

export const imagesSchema = {
  med: imageSizesSchema,
  min: imageSizesSchema,
  sm: imageSizesSchema,
};

export type ImageSizesSchema = {
  readonly original: string;
  readonly versioned: string;
  readonly sized: string;
  readonly isDefault: boolean;
};

export type ImagesSchema = {
  readonly med?: ImageSizesSchema;
  readonly min?: ImageSizesSchema;
  readonly sm?: ImageSizesSchema;
};

export type CoversSchema = {
  readonly original: {
    readonly url: string;
  };
};
