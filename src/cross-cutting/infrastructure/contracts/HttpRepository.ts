import type { HttpError } from '@/cross-cutting/infrastructure/errors/HttpError';
import type { Either } from '@discocil/fv-domain-library/domain';
import type { HttpParams } from './HttpParams';

export type HttpResponse<DATA> = {
  readonly data: DATA;
  readonly metadata?: unknown;
};

export type HttpResponseEither<RESPONSE> = Either<HttpError, HttpResponse<RESPONSE>>;

export interface HttpRepository {
  get: <RESPONSE>(params: HttpParams) => Promise<HttpResponseEither<RESPONSE>>;
  post: <RESPONSE>(params: HttpParams) => Promise<HttpResponseEither<RESPONSE>>;
  patch: <RESPONSE>(params: HttpParams) => Promise<HttpResponseEither<RESPONSE>>;
}
