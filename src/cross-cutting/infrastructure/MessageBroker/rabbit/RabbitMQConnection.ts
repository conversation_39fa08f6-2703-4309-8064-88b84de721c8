import {
  left, PerformanceMeasurement, right,
} from '@discocil/fv-domain-library/domain';
import { connect } from 'amqplib';

import { ExternalMessageBrokerClientError } from '@/cross-cutting/domain/messageBroker/ExternalMessageBrokerClientError';

import type { BrokerConnectionResponse } from '@/cross-cutting/domain/messageBroker/ExternalMessageBrokerClientContracts';
import type { Either, Logger } from '@discocil/fv-domain-library/domain';
import type { Connection } from 'amqplib';
import type { BrokerConnection } from '../contracts/BrokerConnection';

export class RabbitMQConnection implements BrokerConnection {
  private static instance: RabbitMQConnection | undefined;

  private constructor(
    readonly client: Connection,
    private readonly logger: Logger,
  ) {}

  static async initialize(url: string, logger: Logger, retries = 5): Promise<Either<Error, RabbitMQConnection>> {
    if (RabbitMQConnection.instance) {
      return right(RabbitMQConnection.instance);
    }

    let lastError: Error | undefined;

    for (let i = 0; i < retries; i++) {
      try {
        const performanceMeasurement = PerformanceMeasurement.create(this.constructor.name);

        const rabbitConnection = await connect(url);

        rabbitConnection.on('error', (error) => {
          logger.error(`RabbitMQ error: ${error.message}`);
        });

        rabbitConnection.on('close', () => {
          logger.info('Connection to RabbitMQ closed');
        });

        logger.info(`✅ Connected to RabbitMQ in ${performanceMeasurement.end()} ms`);

        RabbitMQConnection.instance = new RabbitMQConnection(rabbitConnection, logger);

        return right(RabbitMQConnection.instance);
      } catch (error) {
        const parsedError = error as Error;

        logger.debug(parsedError.message);
        lastError = parsedError;

        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    let exceptionMessage = 'Failed to connect to RabbitMQ';

    if (lastError) {
      exceptionMessage = `, reason ${lastError.message}`;
    }

    return left(new Error(exceptionMessage));
  }

  async close(): Promise<BrokerConnectionResponse> {
    try {
      await this.client.close();

      RabbitMQConnection.instance = undefined;

      this.logger.info(`👊🏽 | RabbitMQ connection closed successfully`);
    } catch (error) {
      const parsedError = error as Error;

      return left(
        ExternalMessageBrokerClientError.build({
          context: this.constructor.name,
          error: parsedError,
        }),
      );
    }

    return right(true);
  }
}
