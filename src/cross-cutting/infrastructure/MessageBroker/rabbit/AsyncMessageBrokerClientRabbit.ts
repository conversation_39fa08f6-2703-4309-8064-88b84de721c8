import {
  left, PerformanceMeasurement, right,
} from '@discocil/fv-domain-library/domain';


import { ExternalMessageBrokerClientError } from '@/cross-cutting/domain/messageBroker/ExternalMessageBrokerClientError';

import type {
  BrokerConnectionResponse, IExternalMessageBrokerClient, SendMessageToQueueOptions,
} from '@/cross-cutting/domain/messageBroker/ExternalMessageBrokerClientContracts';
import type { DomainEvent, Logger } from '@discocil/fv-domain-library/domain';
import type { BrokerConnection } from '../contracts/BrokerConnection';
import type { ExchangesConfigurations } from './RoutingConfigs';

type BrokerClientConfig = {
  readonly connection: BrokerConnection;
  readonly logger: Logger;
  readonly exchangesConfigurations: ExchangesConfigurations;
};

export class AsyncMessageBrokerClientRabbit implements IExternalMessageBrokerClient {
  private constructor(
    private readonly connection: BrokerConnection,
    private readonly logger: Logger,
    private readonly exchangesConfigurations: ExchangesConfigurations,
  ) {}

  static async initialize(params: BrokerClientConfig): Promise<AsyncMessageBrokerClientRabbit> {
    const {
      connection, logger, exchangesConfigurations,
    } = params;

    const channel = await connection.client.createChannel();

    for (const [exchange, routingConfigs] of exchangesConfigurations) {
      const performanceMeasurement = PerformanceMeasurement.create(this.constructor.name);

      await channel.assertExchange(exchange, 'topic', { durable: true });

      logger.info(`✅ Connected to exchange: ${exchange} in ${performanceMeasurement.end()} ms`);

      for (const routingConfig of routingConfigs.values()) {
        const { queue, routingKey } = routingConfig;

        await channel.assertQueue(queue, { durable: true });
        await channel.bindQueue(queue, exchange, routingKey);

        logger.info(`🔗 Queue [${queue}] bound to exchange [${exchange}] with routing key: [${routingKey}]`);
      }
    }

    return new AsyncMessageBrokerClientRabbit(connection, logger, exchangesConfigurations);
  }

  async publish(domainEvent: DomainEvent): Promise<BrokerConnectionResponse> {
    const allRouting = new Map(...this.exchangesConfigurations.values());

    try {
      const routingConfig = allRouting.get(domainEvent.type);

      if (!routingConfig) {
        return right(true);
      }

      const { exchange } = routingConfig;

      const domainEventStringified = JSON.stringify(domainEvent.toPrimitives());
      const channel = await this.connection.client.createChannel();

      await channel.assertExchange(exchange, 'topic', { durable: true });

      const bufferMessage = Buffer.from(domainEventStringified);
      const routingKey = domainEvent.type;

      channel.publish(exchange, routingKey, bufferMessage);

      await channel.close();

      this.logger.info(`📨 Message sent to exchange "${exchange}" with routing key: "${routingKey}"`);
    } catch (error) {
      const parsedError = error as Error;

      return left(
        ExternalMessageBrokerClientError.build({
          context: this.constructor.name,
          error: parsedError,
        }),
      );
    }

    return right(true);
  }

  async close(): Promise<BrokerConnectionResponse> {
    try {
      await this.connection.close();
    } catch (error) {
      const parsedError = error as Error;

      return left(
        ExternalMessageBrokerClientError.build({
          context: this.constructor.name,
          error: parsedError,
        }),
      );
    }

    return right(true);
  }

  async sendToGlobalQueue(dto: SendMessageToQueueOptions): Promise<void> {
    const channel = await this.connection.client.createConfirmChannel();

    const makeContent = (dto: SendMessageToQueueOptions): Buffer => {
      const { eventName, message } = dto;

      let payload = JSON.stringify(message);

      payload = `${eventName}:${payload}`;

      return Buffer.from(payload);
    };

    const content = makeContent(dto);

    channel.sendToQueue('global_events', content);

    await channel.close();

    this.logger.info(`📨 Message sent with name "${dto.eventName}" with message: ${JSON.stringify(dto.message)}`);
  }
}
