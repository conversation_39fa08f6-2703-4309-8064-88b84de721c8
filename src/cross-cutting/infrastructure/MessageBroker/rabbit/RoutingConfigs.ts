import { EventPreregisterUserCreatedDomainEvent } from '@/events/events/domain/events/EventPreregisterUserCreatedDomainEvent';
import { TicketCreatedDomainEvent } from '@/tickets/tickets/domain/events/TicketCreatedDomainEvent';
import { TicketActivatedDomainEvent } from '@/tickets/tickets/domain/events/TicketActivatedDomainEvent';
import { TicketTypeTotalsModified } from '@/tickets/ticketsTypes/domain/events/TicketTypeTotalsModified';

const enum EExchanges {
  CLI_DOMAIN_EVENTS = 'cli.domain_events',
  TICKET_DOMAIN_EVENTS = 'ticket.domain_events'
}

type RoutingConfig = {
  exchange: EExchanges;
  routingKey: string;
  queue: string;
};

export type RoutingConfigs = Map<string, RoutingConfig>;
export type ExchangesConfigurations = Map<EExchanges, RoutingConfigs>;

const exchangesConfigurations: ExchangesConfigurations = new Map<EExchanges, RoutingConfigs>();
const cliRouting = new Map<string, RoutingConfig>();
const ticketRouting = new Map<string, RoutingConfig>();

cliRouting.set(TicketCreatedDomainEvent.EVENT_NAME, {
  exchange: EExchanges.CLI_DOMAIN_EVENTS,
  routingKey: TicketCreatedDomainEvent.EVENT_NAME,
  queue: 'cli.api',
});
cliRouting.set(TicketActivatedDomainEvent.EVENT_NAME, {
  exchange: EExchanges.CLI_DOMAIN_EVENTS,
  routingKey: TicketActivatedDomainEvent.EVENT_NAME,
  queue: 'cli.api',
});
cliRouting.set(EventPreregisterUserCreatedDomainEvent.EVENT_NAME, {
  exchange: EExchanges.CLI_DOMAIN_EVENTS,
  routingKey: EventPreregisterUserCreatedDomainEvent.EVENT_NAME,
  queue: 'cli.preregister',
});

ticketRouting.set(TicketTypeTotalsModified.EVENT_NAME, {
  exchange: EExchanges.TICKET_DOMAIN_EVENTS,
  routingKey: TicketTypeTotalsModified.EVENT_NAME,
  queue: 'ticket.handle_ticket_type_event_assignation',
});

exchangesConfigurations.set(EExchanges.CLI_DOMAIN_EVENTS, cliRouting);
exchangesConfigurations.set(EExchanges.TICKET_DOMAIN_EVENTS, ticketRouting);

export { exchangesConfigurations };

