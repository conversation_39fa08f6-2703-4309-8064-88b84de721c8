import {
  left, PerformanceMeasurement, right,
} from '@discocil/fv-domain-library/domain';
import EventEmitter from 'eventemitter3';

import { InternalMessageBrokerClientError } from '@/cross-cutting/domain/messageBroker/InternalMessageBrokerClientError';

import type { InternalMessageBrokerClient, MessageBroker } from '@/cross-cutting/domain/messageBroker/InternalMessageBrokerClientContracts';
import type {
  DomainEvent, DomainEventSubscriber, Either, Logger,
} from '@discocil/fv-domain-library/domain';

export class InMemoryAsyncMessageBrokerClient extends EventEmitter implements InternalMessageBrokerClient {
  private static instance: InMemoryAsyncMessageBrokerClient | undefined;

  private constructor(private readonly logger: Logger) {
    super();
  }

  static initialize(logger: Logger): Either<InternalMessageBrokerClientError, InMemoryAsyncMessageBrokerClient> {
    if (InMemoryAsyncMessageBrokerClient.instance) {
      return right(InMemoryAsyncMessageBrokerClient.instance);
    }

    const performanceMeasurement = PerformanceMeasurement.create(this.constructor.name);

    try {
      InMemoryAsyncMessageBrokerClient.instance = new InMemoryAsyncMessageBrokerClient(logger);
    } catch (error) {
      const parsedError = error as Error;

      return left(
        InternalMessageBrokerClientError.build({
          context: this.constructor.name,
          error: parsedError,
        }),
      );
    }

    logger.info(`✅ In-memory async message broker client connected in ${performanceMeasurement.end()} ms`);

    return right(InMemoryAsyncMessageBrokerClient.instance);
  }

  async publish(events: DomainEvent[]): Promise<MessageBroker> {
    try {
      events.map(event => this.emit(event.type, event));

      return right(undefined);
    } catch (error) {
      const parsedError = error as Error;

      return left(
        InternalMessageBrokerClientError.build({
          context: this.constructor.name,
          error: parsedError,
        }),
      );
    }
  }

  addSubscribers(subscribers: Array<DomainEventSubscriber<DomainEvent>>): MessageBroker {
    try {
      subscribers.forEach((subscriber) => {
        subscriber.subscribedTo().forEach((event) => {
          this.on(event.EVENT_NAME, subscriber.on.bind(subscriber));
        });
      });

      return right(undefined);
    } catch (error) {
      const parsedError = error as Error;

      return left(
        InternalMessageBrokerClientError.build({
          context: this.constructor.name,
          error: parsedError,
        }),
      );
    }
  }

  async close(): Promise<MessageBroker> {
    try {
      this.removeAllListeners();

      InMemoryAsyncMessageBrokerClient.instance = undefined;

      this.logger.info(`👊🏽 | Message broker client closed`);

      return right(undefined);
    } catch (error) {
      const parsedError = error as Error;

      return left(
        InternalMessageBrokerClientError.build({
          context: this.constructor.name,
          error: parsedError,
        }),
      );
    }
  }
}
