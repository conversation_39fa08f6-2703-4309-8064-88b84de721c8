import {
  CreatedAt,
  CreatedBy,
  FvDate,
  FvNumber,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';

export type Stamps = {
  createdAt: CreatedAt;
  createdBy: CreatedBy;
  updatedAt: UpdatedAt;
  updatedBy: UpdatedBy;
  removedAt: RemovedAt;
  removedBy: RemovedBy;
};

export type StampsPrimitive = {
  createdAt: number;
  createdBy: string;
  updatedAt: number;
  updatedBy: string;
  removedAt: number;
  removedBy: string;
};

export type StampsJsonPrimitives = {
  createdAt: number;
  createdBy: string;
  updatedAt: number;
  updatedBy: string;
  removedAt: number;
  removedBy: string;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type ValueObjects = Record<string, any>;

type LocalPrimitives = Record<string, unknown>;

export const defaultStamps = (): StampsPrimitive => {
  return {
    createdAt: FvDate.create().toMilliseconds(),
    updatedAt: FvDate.create().toMilliseconds(),
    removedAt: 0,
    createdBy: '',
    updatedBy: '',
    removedBy: '',
  };
};

export const stampValueObjects = (primitives: LocalPrimitives): Stamps => {
  const valueObjects: Partial<Stamps> = {};

  const match: ValueObjects = {
    createdAt: CreatedAt,
    createdBy: CreatedBy,
    updatedAt: UpdatedAt,
    updatedBy: UpdatedBy,
    removedAt: RemovedAt,
    removedBy: RemovedBy,
  };

  for (const propertyName in match) {
    const propName = propertyName as keyof Stamps;
    const valueObject = match[propertyName].build(primitives[propName]);

    valueObjects[propName] = valueObject;
  }

  return valueObjects as Stamps;
};

export const entityStampsToJson = (entity: StampsPrimitive): StampsJsonPrimitives => {
  return {
    createdAt: entity.createdAt,
    updatedAt: entity.updatedAt,
    removedAt: entity.removedAt,
    createdBy: entity.createdBy,
    updatedBy: entity.updatedBy,
    removedBy: entity.removedBy,
  };
};

export const entityStampsFromJson = (json: StampsJsonPrimitives): StampsPrimitive => {
  return {
    createdAt: parseDate(json.createdAt),
    updatedAt: parseDate(json.updatedAt),
    removedAt: parseDate(json.removedAt),
    createdBy: json.createdBy,
    updatedBy: json.updatedBy,
    removedBy: json.removedBy,
  };
};

const parseDate = (date: number | string): number => {
  if (FvNumber.is(date)) {
    return date;
  }

  const resultOrError = FvDate.createFromISO(date);

  if (resultOrError.isLeft()) {
    throw new Error(`Invalid date: ${date}`);
  }

  return resultOrError.value.toMilliseconds();
};
