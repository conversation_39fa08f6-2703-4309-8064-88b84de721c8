import type { DomainEvent, Either } from '@discocil/fv-domain-library/domain';
import type { ExternalMessageBrokerClientError } from './ExternalMessageBrokerClientError';

export interface SubscriptionConfig {
  exchange: string;
  routingKey: string;
  callback: (message: string) => void;
}

export type BrokerConnectionResponse = Either<ExternalMessageBrokerClientError, true>;

export interface IExternalMessageBrokerClient {
  close: () => Promise<BrokerConnectionResponse>;
  publish: (domainEvent: DomainEvent) => Promise<BrokerConnectionResponse>;
  sendToGlobalQueue: (dto: SendMessageToQueueOptions) => Promise<void>;
}

export type SendMessageToQueueOptions = {
  eventName: string;
  message: Record<string, unknown>;
};
