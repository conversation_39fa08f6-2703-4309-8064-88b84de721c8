import type {
  DomainEvent,
  DomainEventSubscriber,
  Either,
} from '@discocil/fv-domain-library/domain';
import type { InternalMessageBrokerClientError } from './InternalMessageBrokerClientError';

export type MessageBroker = Either<InternalMessageBrokerClientError, void>;
export interface InternalMessageBrokerClient {
  publish: (events: DomainEvent[]) => Promise<MessageBroker>;
  addSubscribers: (subscribers: Array<DomainEventSubscriber<DomainEvent>>) => MessageBroker;
  close: () => Promise<MessageBroker>;
}
