import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '../enums/ErrorsEnum';

import type { ErrorMethodRequest } from '../errors/ErrorRequest';

export class ExternalMessageBrokerClientError extends FvError {
  static readonly defaultCause = EErrorKeys.INVALID_FIELD;

  static build(request: ErrorMethodRequest): ExternalMessageBrokerClientError {
    const {
      context, error, data, target,
    } = request;

    const exceptionMessage = 'External message broker client error';

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
