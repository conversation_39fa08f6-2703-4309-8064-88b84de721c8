import { InvalidArgumentError } from '@discocil/fv-domain-library/domain';

import { LogSoftError } from '../errors/LogSoftError';

type PropertyDescriptor<T, R> = TypedPropertyDescriptor<(value: T) => R>;

export function EnumLogSoft<T extends string, R>(valuesFn: () => T[]) {
  return (
    target: object & { name: string; },
    _propertyKey: string,
    descriptor: PropertyDescriptor<T, R>,
  ): PropertyDescriptor<T, R> => {
    const originalMethod = descriptor.value!;

    descriptor.value = function (value: T): R {
      if (!valuesFn().includes(value)) {
        const exceptionMessage = InvalidArgumentError.invalidValue({
          context: this.constructor.name,
          target: value,
          data: { targetName: target.name },
        });

        LogSoftError.log(exceptionMessage.toString());
      }

      return originalMethod.call(this, value);
    };

    return descriptor;
  };
}
