import {
  EMimeType,
  FvEnum,
  ImageError,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import type { Either } from '@discocil/fv-domain-library/domain';

type MimeTypeEither = Either<ImageError, MimeTypeValueObject>;

export class MimeTypeValueObject extends FvEnum<EMimeType> {
  static readonly values: EMimeType[] = Object.values(EMimeType);

  private constructor(value: EMimeType) {
    super(value, MimeTypeValueObject.values);
  }

  static build(value: string): MimeTypeEither {
    const newValue = value as EMimeType;

    return this.values.includes(newValue)
      ? right(new MimeTypeValueObject(newValue))
      : left(ImageError.invalidFormat({
        context: MimeTypeValueObject.name,
        data: { value },
      }));
  }
}
