import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import type { CommonFieldKeys } from './CommonFields';

class FilterField extends FilterFieldBase<CommonFieldKeys> {}

export class EventIdFilter {
  static readonly field: CommonFieldKeys = 'eventId';

  static buildEqual(eventId: UniqueEntityID): Filter {
    const eventField = new FilterField(this.field);
    const eventOperator = FilterOperator.equal();
    const eventValue = FilterValue.build(eventId.value);

    return new Filter(eventField, eventOperator, eventValue);
  }

  static buildIn(eventIds: UniqueEntityID[]): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.in();

    const filterValues: FilterValue[] = eventIds.map((value: UniqueEntityID) => FilterValue.build(value.value));

    return new Filter(field, operator, filterValues);
  }

  static notExists(): Filter {
    const eventField = new FilterField(this.field);
    const eventOperator = FilterOperator.exists();
    const eventValue = FilterValue.buildFalse();

    return new Filter(eventField, eventOperator, eventValue);
  }
}
