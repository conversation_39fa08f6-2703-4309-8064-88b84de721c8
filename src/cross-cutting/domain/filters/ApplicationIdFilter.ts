import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { CommonFieldKeys } from './CommonFields';

class FilterField extends FilterFieldBase<CommonFieldKeys> {}

export class ApplicationIdFilter {
  static readonly field: CommonFieldKeys = 'applicationId';

  static buildEqual(applicationId: string): Filter {
    const eventField = new FilterField(this.field);
    const eventOperator = FilterOperator.equal();
    const eventValue = FilterValue.build(applicationId);

    return new Filter(eventField, eventOperator, eventValue);
  }
}
