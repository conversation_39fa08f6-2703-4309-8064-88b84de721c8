import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { ECurrency } from '@discocil/fv-domain-library/domain';
import type { CommonFieldKeys } from './CommonFields';

class FilterField extends FilterFieldBase<CommonFieldKeys> {}

export class CurrencyFilter {
  static readonly field: CommonFieldKeys = 'currency';

  static buildEqual(currency: ECurrency): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(currency);

    return new Filter(field, operator, filterValue);
  }
}
