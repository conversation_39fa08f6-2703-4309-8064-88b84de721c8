import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import type { CommonFieldKeys } from './CommonFields';

class FilterField extends FilterFieldBase<CommonFieldKeys> {}

export class IdFilter {
  static readonly field: CommonFieldKeys = 'id';

  static buildEqual(organizationId: UniqueEntityID): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(organizationId.value);

    return new Filter(field, operator, filterValue);
  }

  static buildIn(ids: UniqueEntityID[]): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.in();

    const filterValues: FilterValue[] = ids.map((id: UniqueEntityID) => FilterValue.build(id.value));

    return new Filter(field, operator, filterValues);
  }

  static buildNotIn(ids: UniqueEntityID[]): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.notIn();

    const filterValues: FilterValue[] = ids.map((value: UniqueEntityID) => FilterValue.build(value.value));

    return new Filter(field, operator, filterValues);
  }
}
