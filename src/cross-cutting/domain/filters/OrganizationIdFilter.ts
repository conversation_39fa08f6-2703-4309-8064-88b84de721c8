import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import type { CommonFieldKeys } from './CommonFields';

class FilterField extends FilterFieldBase<CommonFieldKeys> {}

export class OrganizationIdFilter {
  static readonly field: CommonFieldKeys = 'organizationId';

  static buildEqual(organizationId: UniqueEntityID): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(organizationId.value);

    return new Filter(field, operator, filterValue);
  }

  static buildIn(organizationsIds: UniqueEntityID[]): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.in();

    const filterValues: FilterValue[] = organizationsIds.map((value: UniqueEntityID) => FilterValue.build(value.value));

    return new Filter(field, operator, filterValues);
  }

  static buildEmpty(): Filter {
    const groupsField = new FilterField(this.field);
    const groupsOperator = FilterOperator.in();
    const groupsValue: FilterValue[] = [FilterValue.buildNull(), FilterValue.buildEmpty()];

    return new Filter(groupsField, groupsOperator, groupsValue);
  }
}
