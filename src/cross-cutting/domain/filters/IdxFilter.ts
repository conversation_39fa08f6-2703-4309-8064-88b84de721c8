import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { CommonFieldKeys } from './CommonFields';

class FilterField extends FilterFieldBase<CommonFieldKeys> {}

export class IdxFilter {
  private static readonly field: CommonFieldKeys = 'idx';

  static buildEqual(idx: string): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(idx);

    return new Filter(field, operator, filterValue);
  }
}
