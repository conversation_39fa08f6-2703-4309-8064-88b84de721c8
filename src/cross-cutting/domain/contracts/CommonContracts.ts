import type { EDocumentType, Maybe } from '@discocil/fv-domain-library/domain';

export type Coordinates = {
  readonly latitude: number;
  readonly longitude: number;
};

export type TimezoneType = {
  id: string;
  name: string;
  dstOffset: Maybe<number>;
  rawOffset: Maybe<number>;
};

export type TPersonalDocument = {
  readonly number: string | null;
  readonly type: EDocumentType | null;
};

export type Device = {
  readonly browser: string | null;
  readonly device: string | null;
  readonly os: string | null;
};
