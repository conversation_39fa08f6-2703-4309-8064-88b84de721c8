import type { ECountryCode } from '@discocil/fv-domain-library/domain';

export type FieldName = 'name' | 'email' | 'phone' | 'birthDate' | 'zipCode' | 'gender' | 'address' | 'country' | 'personalDocumentType';

type SelectOptions = {
  readonly text: string;
  readonly value: string;
};

type SelectFieldOptions = {
  readonly type: string;
  readonly required: boolean;
  readonly label: string;
  readonly slug: string;
  readonly options: SelectOptions[] | BasicFieldOptions[] | ECountryCode[];
  readonly value: SelectOptions['value'];
};

type BasicFieldOptions = Omit<SelectFieldOptions, 'options' | 'value'>;

export type FieldOptions = BasicFieldOptions | SelectFieldOptions | BasicFieldOptions[] | SelectFieldOptions[];

export type FormFields = Partial<Record<FieldName, FieldOptions>>;
