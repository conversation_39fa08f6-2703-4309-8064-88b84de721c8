import type { Maybe } from '@discocil/fv-domain-library/domain';

export type ImageSizes = {
  readonly original: string;
  readonly versioned: string;
  readonly sized: string;
  readonly isDefault: boolean;
};

export type Images = {
  readonly medium: Maybe<ImageSizes>;
  readonly mini: Maybe<ImageSizes>;
  readonly small: Maybe<ImageSizes>;
};

export type ImagesExternalPrimitives = {
  readonly medium: ImageSizes | null;
  readonly mini: ImageSizes | null;
  readonly small: ImageSizes | null;
};

export type Covers = {
  readonly original: {
    readonly url: string;
  };
};
