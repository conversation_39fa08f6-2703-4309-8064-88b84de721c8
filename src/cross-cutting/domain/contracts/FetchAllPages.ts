import type { PaginationMetadataResponse, PaginationOption } from '@discocil/fv-criteria-converter-library/domain';
import type { Either, FvError } from '@discocil/fv-domain-library/domain';

export interface FetchAllPages {
  execute<
    Response extends PaginationMetadataResponse,
    FnEitherResponse extends Either<ErrorResponse, Response>,
    ErrorResponse extends FvError = FvError
  >(
    currentPagination: PaginationOption,
    fn: (pagination: PaginationOption) => Promise<FnEitherResponse>,
  ): Promise<Either<ErrorResponse, Response[]>>;
}
