import type { Either, UnexpectedError } from '@discocil/fv-domain-library/domain';

export type StorageManagerUploadOptions = {
  readonly key: string;
  readonly body: Buffer;
  readonly contentType?: string;
};

export type StorageManagerUploadEither = Either<UnexpectedError, true>;

export interface StorageManager {
  upload: (options: StorageManagerUploadOptions) => Promise<StorageManagerUploadEither>;
}
