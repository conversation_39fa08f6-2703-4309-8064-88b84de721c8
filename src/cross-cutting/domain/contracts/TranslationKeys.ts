import type common from '../locales/es/common.json';
import type pos from '../locales/es/pos.json';

type Join<K, P> = `${K & string}.${P & string}`;

type Prev = [never, 0, 1, 2, 3, 4, 5];

type DeepLeafKeyOf<T, D extends number = 5> = [D] extends [never]
  ? never
  : {
    [K in keyof T]: T[K] extends string | number | boolean
      ? `${K & string}`
      : T[K] extends object
        ? D extends 0
          ? never
          : Join<K, DeepLeafKeyOf<T[K], Prev[D]>>
        : never;
  }[keyof T];

type TranslationKey<Namespace extends string, T> = `${Namespace}:${DeepLeafKeyOf<T>}`;

export type TranslationKeys =
  TranslationKey<'common', typeof common>
  | TranslationKey<'pos', typeof pos>
;
