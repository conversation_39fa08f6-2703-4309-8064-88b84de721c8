import type { UnknownObject } from '@discocil/fv-domain-library/domain';

export const isObjectEmpty = (object: UnknownObject): boolean => Object.keys(object).length === 0;

export const removeUndefined = <T extends Record<string, unknown>>(value: T): T => {
  return Object.fromEntries(Object.entries(value).filter(([, v]) => v !== undefined)) as T;
};

export const hasTruthyProperty = (values: Record<string, unknown>): boolean => {
  return Object.values(values).some(value => Boolean(value));
};
