import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { ErrorMethodRequest } from '@/cross-cutting/domain/errors/ErrorRequest';

export class PricingLibraryError extends FvError {
  static readonly default = EErrorKeys.PRICING_LIBRARY_ERROR;
  static build(request: ErrorMethodRequest): PricingLibraryError {
    const exceptionMessage = 'Pricing library error';

    const {
      context, error, data, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      PricingLibraryError.default,
      error,
      data,
      target,
    );
  }
}
