import { FvError } from '@discocil/fv-domain-library/domain';
import { container } from 'tsyringe';

import { DependencyIdentifier } from '../dependencyIdentifier/DependencyIdentifier';
import { EErrorKeys } from '../enums/ErrorsEnum';

import type { Logger } from '@discocil/fv-domain-library/domain';

export class LogSoftError extends FvError {
  readonly defaultCause = EErrorKeys.INVALID_FIELD;

  static log(message: string): void {
    const logger = container.resolve<Logger>(DependencyIdentifier.Logger);

    logger.warn(`softError ${message}`);
  }

  static billingAddressNotFound(organizationId: string): void {
    this.log(`Billing address not found for organization ${organizationId}`);
  }
}
