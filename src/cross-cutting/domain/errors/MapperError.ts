import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '../enums/ErrorsEnum';
import { stringify } from '../helpers/stringify';

import { LogSoftError } from './LogSoftError';

import type { FvErrorRequest } from '@discocil/fv-domain-library/domain';
import type { ErrorMethodRequest } from './ErrorRequest';

export class MapperError extends FvError {
  static readonly defaultCause = EErrorKeys.INVALID_FIELD;
  static build(request: FvErrorRequest): MapperError {
    const defaultMessage = `Can't be process`;

    const {
      context, error, data, target,
    } = request;

    const { message = defaultMessage } = request;

    return new this(
      message,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static logger(request: ErrorMethodRequest): void {
    const {
      context, error, data, target,
    } = request;

    const exceptionMessage = 'Soft mapper error';

    const errorClass = this.build({
      context,
      message: exceptionMessage,
      error,
      data,
      target,
    });

    const errorForLog = `${errorClass.message} data: ${stringify(data)}`;

    LogSoftError.log(errorForLog);
  }
}
