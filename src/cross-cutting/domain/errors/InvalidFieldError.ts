import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '../enums/ErrorsEnum';

import type { FvErrorRequest } from '@discocil/fv-domain-library/domain';
import type { ErrorMethodRequest } from './ErrorRequest';

export class InvalidFieldError extends FvError {
  static readonly defaultCause = EErrorKeys.INVALID_FIELD;

  static build(request: FvErrorRequest): InvalidFieldError {
    const {
      context, message, error, data, target,
    } = request;

    return new this(
      message ?? 'Invalid field',
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static searchCriteriaRequired(request: ErrorMethodRequest): InvalidFieldError {
    const exceptionMessage = 'Search criteria is required';

    const {
      context, error, data, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
