import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '../enums/ErrorsEnum';

import type { ErrorMethodRequest } from './ErrorRequest';

export class InvalidUser extends FvError {
  static build(request: ErrorMethodRequest): InvalidUser {
    const exceptionMessage = `Invalid user`;

    const {
      context, error, data, target,
    } = request;

    const cause = EErrorKeys.INVALID_USER;

    return new this(
      exceptionMessage,
      context,
      cause,
      error,
      data,
      target,
    );
  }
}
