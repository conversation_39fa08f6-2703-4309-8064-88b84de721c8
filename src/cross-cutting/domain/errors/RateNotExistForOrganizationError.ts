import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { ErrorMethodRequest } from './ErrorRequest';

export class RateNotExistForOrganizationError extends FvError {
  static build(request: ErrorMethodRequest): RateNotExistForOrganizationError {
    const {
      context, error, data, target,
    } = request;

    const exceptionMessage = 'All rates must belong to the same organization';
    const cause = EErrorKeys.RATE_NOT_EXIST_FOR_ORGANIZATION;

    return new this(
      exceptionMessage,
      context,
      cause,
      error,
      data,
      target,
    );
  }
}
