import type { BlockSchemaType } from '@/blocks/infrastructure/database/schemas/BlockSchemaType';
import type { JSONSchemaType } from 'ajv';

export const blockValidationSchema: JSONSchemaType<Required<BlockSchemaType>> = {
  title: 'Block Json Schema Validation',
  required: ['negocio_id', 'negocio_apuntando'],
  type: 'object',
  properties: {
    _id: { type: 'string' },
    negocio_id: { type: 'string' },
    evento_id: { type: 'string' },
    grupo_id: { type: 'string' },
    usuario_id: { type: 'string' },
    negocio_apuntando: { type: 'string' },
    bloqueado: { type: 'boolean' },
    created_at: { type: 'number' },
    created_by: { type: 'string' },
    updated_at: { type: 'number' },
    updated_by: { type: 'string' },
    removed_at: { type: 'number' },
    removed_by: { type: 'string' },
  },
};
