import type { Block } from '@/blocks/domain/entities/Block';
import type { BlockSchemaType } from '@/blocks/infrastructure/database/schemas/BlockSchemaType';

export class BlockSchemaMapper {
  static execute(block: Block): BlockSchemaType {
    return {
      _id: block.id,
      negocio_apuntando: block.organizationAssignedId,
      negocio_id: block.organizationId,
      evento_id: block.eventId,
      grupo_id: block.groupId.fold(() => undefined, item => item),
      usuario_id: block.userId.fold(() => undefined, item => item),
      bloqueado: block.blocked,
      created_at: block.createdAt,
      created_by: block.createdBy,
      updated_at: block.updatedAt,
      updated_by: block.updatedBy,
      removed_at: block.removedAt,
      removed_by: block.removedBy,
    };
  }
}
