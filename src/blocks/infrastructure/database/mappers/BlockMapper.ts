import { Maybe, left } from '@discocil/fv-domain-library/domain';
import { container } from 'tsyringe';

import { Block } from '@/blocks/domain/entities/Block';
import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { MapperError } from '@/cross-cutting/domain/errors/MapperError';

import { blockValidationSchema } from './BlockSchemaValidation';

import type { BlockEither } from '@/blocks/domain/entities/Block';
import type Ajv from 'ajv';
import type { BlockSchemaType } from '../schemas/BlockSchemaType';

export class BlockMapper {
  static execute(data: BlockSchemaType): BlockEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(blockValidationSchema);

    if (!validate(data) || validate.errors) {
      MapperError.logger({
        context: BlockMapper.name,
        data,
        target: validate.errors,
      });

      return BlockSoftMapper.execute(data);
    }

    return this.buildEntity(data);
  }

  static buildEntity(data: BlockSchemaType): BlockEither {
    return Block.build({
      id: data._id,
      organizationAssignedId: data.negocio_apuntando,
      organizationId: data.negocio_id,
      eventId: data.evento_id,
      blocked: data.bloqueado ?? false,
      groupId: Maybe.fromValue(data.grupo_id),
      userId: Maybe.fromValue(data.usuario_id),
      createdAt: data.created_at,
      createdBy: data.created_by,
      updatedAt: data.updated_at,
      updatedBy: data.updated_by,
      removedAt: data.removed_at,
      removedBy: data.removed_by,
    });
  }
}

export class BlockSoftMapper extends BlockMapper {
  static execute(data: BlockSchemaType): BlockEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(blockValidationSchema);

    if (!validate(data) || validate.errors) {
      return left(MapperError.build({
        context: BlockMapper.name,
        data,
        target: validate.errors,
      }));
    }

    return this.buildEntity({
      ...data,
      _id: data._id,
      negocio_apuntando: data.negocio_apuntando ?? null,
      negocio_id: data.negocio_id ?? null,
      evento_id: data.evento_id ?? null,
    });
  }
}
