import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { Block } from '@/blocks/domain/entities/Block';
import { BlockMapper } from '@/blocks/infrastructure/database/mappers/BlockMapper';
import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';

import { blockSchema } from '../schemas/BlockSchema';

import type { BlockRepository } from '@/blocks/domain/contracts/BlockRepository';
import type {
  BlockEither,
  BlockKeys,
  Blocks,
  BlocksEither,
} from '@/blocks/domain/entities/Block';
import type { BlockSchemaType } from '@/blocks/infrastructure/database/schemas/BlockSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

type PropertiesMapper = Partial<Record<BlockKeys, keyof BlockSchemaType>>;

export class BlockMongoRepository extends MongoRepository implements BlockRepository {
  protected getSchema(): Schema {
    return new Schema(blockSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'bloqueos';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      eventId: 'evento_id',
      blocked: 'bloqueado',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<BlockEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<BlockSchemaType>();

    return queryResponse ? BlockMapper.execute(queryResponse) : left(NotFoundError.build({ context: this.constructor.name, target: Block.name }));
  }

  async search(criteria: Criteria): Promise<BlocksEither> {
    const response: Blocks = new Map<IdPrimitive, Block>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<BlockSchemaType[]>();

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const entityResult = BlockMapper.execute(model);

      if (entityResult.isLeft()) {
        return left(entityResult.value);
      }

      const entity = entityResult.value;

      response.set(entity.id, entity);
    }

    return right(response);
  }
}
