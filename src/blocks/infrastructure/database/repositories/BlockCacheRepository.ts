import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { BlockJsonMapper } from '@/blocks/domain/mappers/BlockJsonMapper';
import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';

import type { BlockRepository } from '@/blocks/domain/contracts/BlockRepository';
import type {
  Block,
  BlockEither,
  Blocks,
  BlocksEither,
} from '@/blocks/domain/entities/Block';
import type { BlockJsonPrimitives } from '@/blocks/domain/mappers/BlockJsonMapper';
import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class BlockCacheRepository extends CacheRepository implements BlockRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: BlockRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<BlockEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: BlockJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = BlockJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        BlockJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async search(criteria: Criteria): Promise<BlocksEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: BlockJsonPrimitives[] = JSON.parse(cacheHit);
      const blocks: Blocks = new Map<IdPrimitive, Block>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = BlockJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        blocks.set(_primitive.id, entityOrError.value);
      }

      return right(blocks);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const blocks = repositoryResult.value;
      const blocksLength = blocks.size;
      let blockCacheIndex = 0;
      const jsonBlocks = new Array<BlockJsonPrimitives>(blocksLength);

      if (blocksLength > 0) {
        for (const _block of blocks.values()) {
          jsonBlocks[blockCacheIndex] = BlockJsonMapper.toJson(_block);

          blockCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonBlocks, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}
