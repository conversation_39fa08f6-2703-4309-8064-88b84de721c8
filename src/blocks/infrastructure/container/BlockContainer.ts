import { container, instanceCachingFactory } from 'tsyringe';

import { BlockDependencyIdentifier } from '@/blocks/domain/dependencyIdentifier/BlockDependencyIdentifier';
import { BlockMongoRepository } from '@/blocks/infrastructure/database/repositories/BlockMongoRepository';
import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';

import { BlockCacheRepository } from '../database/repositories/BlockCacheRepository';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';

export const BlockContainer = {
  register: (): void => {
    container.register(BlockDependencyIdentifier.BlockRepository, {
      useFactory: instanceCachingFactory((container) => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);
        const mongoRepository = new BlockMongoRepository(dbConnection);

        const cacheHander = container.resolve<ICacheRepository>(DependencyIdentifier.ICacheHandler);
        const cacheConfig = container.resolve<CacheConfig>(DependencyIdentifier.CacheConfig);

        return new BlockCacheRepository(cacheHander, cacheConfig, mongoRepository);
      }),
    });
  },
};
