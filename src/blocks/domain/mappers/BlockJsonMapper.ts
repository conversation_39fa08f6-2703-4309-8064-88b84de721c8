import { Maybe } from '@discocil/fv-domain-library/domain';

import { Block } from '../entities/Block';

import type { BlockEither, BlockPrimitives } from '../entities/Block';

export type BlockJsonPrimitives = Omit<BlockPrimitives,
  'groupId'
  | 'userId'
> & {
  readonly groupId: string | null;
  readonly userId: string | null;
};

export class BlockJsonMapper {
  static toEntity(primitives: BlockJsonPrimitives): BlockEither {
    return Block.build({
      ...primitives,
      groupId: Maybe.fromValue(primitives.groupId),
      userId: Maybe.fromValue(primitives.userId),
    });
  }

  static toJson(block: Block): BlockJsonPrimitives {
    return {
      id: block.id,
      organizationId: block.organizationId,
      eventId: block.eventId,
      organizationAssignedId: block.organizationAssignedId,
      blocked: block.blocked,
      groupId: block.groupId.fold(() => null, item => item),
      userId: block.userId.fold(() => null, item => item),
      createdAt: block.createdAt,
      createdBy: block.createdBy,
      updatedAt: block.updatedAt,
      updatedBy: block.updatedBy,
      removedAt: block.removedAt,
      removedBy: block.removedBy,
    };
  }
}
