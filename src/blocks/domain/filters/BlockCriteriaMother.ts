import { Criteria, Filters } from '@discocil/fv-criteria-converter-library/domain';

import { EventIdFilter } from '@/cross-cutting/domain/filters/EventIdFilter';
import { IdFilter } from '@/cross-cutting/domain/filters/IdFilter';
import { OrganizationIdFilter } from '@/cross-cutting/domain/filters/OrganizationIdFilter';
import { RemovedAtFilter } from '@/cross-cutting/domain/filters/RemovedAtFilter';

import { BlockedFilter } from './BlockedFilter';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';

export class BlockCriteriaMother {
  static idToMatch(id: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(id));

    return Criteria.build(filters);
  }

  static eventBlockedToMatch(eventId: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(EventIdFilter.buildEqual(eventId));
    filters.add(BlockedFilter.buildTrue());
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static eventsBlockedToMatch(eventsId: UniqueEntityID[]): Criteria {
    const filters = Filters.build();

    filters.add(EventIdFilter.buildIn(eventsId));
    filters.add(BlockedFilter.buildTrue());
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static eventAndOrganizationBlockedToMatch(eventId: UniqueEntityID, organizationId: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(EventIdFilter.buildEqual(eventId));
    filters.add(BlockedFilter.buildTrue());
    filters.add(OrganizationIdFilter.buildEqual(organizationId));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static organizationIdToMatch(organizationId: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(OrganizationIdFilter.buildEqual(organizationId));
    filters.add(BlockedFilter.buildTrue());
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }
}
