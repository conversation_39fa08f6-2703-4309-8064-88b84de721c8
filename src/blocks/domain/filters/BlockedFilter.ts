import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { BlockKeys } from '../entities/Block';

class FilterField extends FilterFieldBase<BlockKeys> {}

export class BlockedFilter {
  private static readonly field: BlockKeys = 'blocked';

  static buildTrue(): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.buildTrue();

    return new Filter(field, operator, filterValue);
  }
}
