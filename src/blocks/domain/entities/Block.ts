import {
  AggregateRoot,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  CreatedAt,
  CreatedBy,
  Either,
  IdPrimitive,
  Maybe,
  NotFoundError,
  Primitives,
  Properties,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';

export type BlockPrimitives = Primitives<Block>;

export type Blocks = Map<IdPrimitive, Block>;

export type BlockEither = Either<MapperError | NotFoundError, Block>;
export type BlocksEither = Either<MapperError | NotFoundError, Blocks>;

export type BlockKeys = keyof Properties<Block>;

export class Block extends AggregateRoot {
  private constructor(
    id: UniqueEntityID,
    private readonly _organizationId: UniqueEntityID,
    private readonly _eventId: UniqueEntityID,
    private readonly _organizationAssignedId: UniqueEntityID,
    readonly blocked: boolean,
    private readonly _groupId: Maybe<UniqueEntityID>,
    private readonly _userId: Maybe<UniqueEntityID>,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);
  }

  static build(primitives: BlockPrimitives): BlockEither {
    const id = UniqueEntityID.build(primitives.id);
    const organizationId = UniqueEntityID.build(primitives.organizationId);
    const eventId = UniqueEntityID.build(primitives.eventId);
    const organizationAssignedId = UniqueEntityID.build(primitives.organizationAssignedId);
    const groupId = primitives.groupId.map(item => UniqueEntityID.build(item));
    const userId = primitives.userId.map(item => UniqueEntityID.build(item));

    const stamps = stampValueObjects(primitives);

    const entity = new Block(
      id,
      organizationId,
      eventId,
      organizationAssignedId,
      primitives.blocked,
      groupId,
      userId,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return right(entity);
  }

  get organizationId(): IdPrimitive {
    return this._organizationId.toPrimitive();
  }

  get eventId(): IdPrimitive {
    return this._eventId.toPrimitive();
  }

  get organizationAssignedId(): IdPrimitive {
    return this._organizationAssignedId.toPrimitive();
  }

  get groupId(): Maybe<IdPrimitive> {
    return this._groupId.map(item => item.toPrimitive());
  }

  get userId(): Maybe<IdPrimitive> {
    return this._userId.map(item => item.toPrimitive());
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }

  hasThisEvent(eventId: UniqueEntityID | IdPrimitive): boolean {
    return this._eventId.equals(eventId);
  }
}
