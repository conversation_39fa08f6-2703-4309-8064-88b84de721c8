import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { GuestListOrganizationLimit } from '@/guestLists/guestListOrganizationLimits/domain/entities/GuestListOrganizationLimit';

import { GuestListOrganizationLimitMapper } from '../mappers/GuestListOrganizationLimitMapper';
import { GuestListOrganizationLimitSchemaMapper } from '../mappers/GuestListOrganizationLimitSchemaMapper';
import { guestListOrganizationLimitSchema } from '../schemas/GuestListOrganizationLimitSchema';

import type { GuestListOrganizationLimitRepository } from '@/guestLists/guestListOrganizationLimits/domain/contracts/GuestListOrganizationLimitRepository';
import type {
  GuestListOrganizationLimitEither,
  GuestListOrganizationLimitKeys,
  GuestListOrganizationLimits,
  GuestListOrganizationLimitsEither,
} from '@/guestLists/guestListOrganizationLimits/domain/entities/GuestListOrganizationLimit';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { GuestListOrganizationLimitSchemaType } from '../schemas/GuestListOrganizationLimitSchemaType';

type PropertiesMapper = Partial<Record<GuestListOrganizationLimitKeys, keyof GuestListOrganizationLimitSchemaType>>;

export class GuestListOrganizationLimitMongoRepository extends MongoRepository implements GuestListOrganizationLimitRepository {
  protected getSchema(): Schema {
    return new Schema(guestListOrganizationLimitSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'list_organization_limits';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      limit: 'limit',
      typeId: 'list_type_id',
      organizationLimitedId: 'negocio_limited_id',
      removedAt: 'removed_at',
      eventId: 'event_id',
    };
  }

  async find(criteria: Criteria): Promise<GuestListOrganizationLimitEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<GuestListOrganizationLimitSchemaType>();

    return queryResponse
      ? GuestListOrganizationLimitMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: GuestListOrganizationLimit.name }));
  }

  async save(guestListOrganizationLimit: GuestListOrganizationLimit): Promise<void> {
    const toSave = GuestListOrganizationLimitSchemaMapper.execute(guestListOrganizationLimit);

    const filter = { _id: guestListOrganizationLimit.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(guestListOrganizationLimits: GuestListOrganizationLimits): Promise<void> {
    const connection = await this.getConnection();
    const models: Array<Partial<GuestListOrganizationLimitSchemaType>> = [];

    guestListOrganizationLimits.forEach((guestListOrganizationLimit: GuestListOrganizationLimit) =>
      models.push(GuestListOrganizationLimitSchemaMapper.execute(guestListOrganizationLimit)));

    await connection.insertMany(models);
  }

  async search(criteria: Criteria): Promise<GuestListOrganizationLimitsEither> {
    const response = new Map<IdPrimitive, GuestListOrganizationLimit>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter)
      .sort(filterQuery.sort)
      .lean<GuestListOrganizationLimitSchemaType[]>();

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const guestListOrganizationLimitResult = GuestListOrganizationLimitMapper.execute(model);

      if (guestListOrganizationLimitResult.isLeft()) {
        return left(guestListOrganizationLimitResult.value);
      }

      const guestListOrganizationLimit = guestListOrganizationLimitResult.value;

      response.set(guestListOrganizationLimit.id, guestListOrganizationLimit);
    }

    return right(response);
  }
}
