
import {
  left, NotFoundError, right,
} from '@discocil/fv-domain-library';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { GuestListLimitsJsonMapper } from '@/guestLists/guestListLimits/domain/mappers/GuestListLimitsJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { GuestListLimitRepository } from '@/guestLists/guestListLimits/domain/contracts/GuestListLimitRepository';
import type {
  GuestListLimit, GuestListLimitEither, GuestListLimits, GuestListLimitsEither,
} from '@/guestLists/guestListLimits/domain/entities/GuestListLimit';
import type { GuestListLimitsJsonPrimitives } from '@/guestLists/guestListLimits/domain/mappers/GuestListLimitsJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library';

export class GuestListLimitCacheRepository extends CacheRepository implements GuestListLimitRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: GuestListLimitRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<GuestListLimitEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: GuestListLimitsJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = GuestListLimitsJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        GuestListLimitsJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async search(criteria: Criteria): Promise<GuestListLimitsEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: GuestListLimitsJsonPrimitives[] = JSON.parse(cacheHit);
      const guestListLimits: GuestListLimits = new Map<IdPrimitive, GuestListLimit>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = GuestListLimitsJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        guestListLimits.set(_primitive.id, entityOrError.value);
      }

      return right(guestListLimits);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const guestListLimits = repositoryResult.value;
      const guestListLimitsLength = guestListLimits.size;
      let guestListLimitsCacheIndex = 0;
      const jsonGuestListLimits = new Array<GuestListLimitsJsonPrimitives>(guestListLimitsLength);

      if (guestListLimitsLength > 0) {
        for (const _guestListLimit of guestListLimits.values()) {
          jsonGuestListLimits[guestListLimitsCacheIndex] = GuestListLimitsJsonMapper.toJson(_guestListLimit);

          guestListLimitsCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonGuestListLimits, this.getConfig().ttl);
    }

    return repositoryResult;
  }

  async save(guestListLimit: GuestListLimit): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.save(guestListLimit);
  }

  async saveMany(guestListLimits: GuestListLimits): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.saveMany(guestListLimits);
  }
}

