import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { GuestListLimit } from '@/guestLists/guestListLimits/domain/entities/GuestListLimit';

import { GuestListLimitMapper } from '../mappers/GuestListLimitMapper';
import { GuestListLimitSchemaMapper } from '../mappers/GuestListLimitSchemaMapper';
import { guestListLimitSchema } from '../schemas/GuestListLimitSchema';

import type { GuestListLimitRepository } from '@/guestLists/guestListLimits/domain/contracts/GuestListLimitRepository';
import type {
  GuestListLimitEither,
  GuestListLimitKeys,
  GuestListLimits,
  GuestListLimitsEither,
} from '@/guestLists/guestListLimits/domain/entities/GuestListLimit';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { GuestListLimitSchemaType } from '../schemas/GuestListLimitSchemaType';

type PropertiesMapper = Partial<Record<GuestListLimitKeys, keyof GuestListLimitSchemaType>>;

export class GuestListLimitMongoRepository extends MongoRepository implements GuestListLimitRepository {
  protected getSchema(): Schema {
    return new Schema(guestListLimitSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'listas_limites';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      eventId: 'evento_id',
      maximum: 'maximo',
      typeSlug: 'tarifa_slug',
      userId: 'usuario_id',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<GuestListLimitEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter)
      .sort(filterQuery.sort)
      .lean<GuestListLimitSchemaType>();

    return queryResponse
      ? GuestListLimitMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: GuestListLimit.name }));
  }

  async save(guestListLimit: GuestListLimit): Promise<void> {
    const toSave = GuestListLimitSchemaMapper.execute(guestListLimit);

    const filter = { _id: guestListLimit.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(guestListLimits: GuestListLimits): Promise<void> {
    const connection = await this.getConnection();
    const models: Array<Partial<GuestListLimitSchemaType>> = [];

    guestListLimits.forEach((guestListLimit: GuestListLimit) => models.push(GuestListLimitSchemaMapper.execute(guestListLimit)));

    await connection.insertMany(models);
  }

  async search(criteria: Criteria): Promise<GuestListLimitsEither> {
    const response = new Map<IdPrimitive, GuestListLimit>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<GuestListLimitSchemaType[]>();

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const guestListTypeResult = GuestListLimitMapper.execute(model);

      if (guestListTypeResult.isLeft()) {
        return left(guestListTypeResult.value);
      }

      const guestListLimit = guestListTypeResult.value;

      response.set(guestListLimit.id, guestListLimit);
    }

    return right(response);
  }
}
