import {
  Maybe, type IdPrimitive, type Nullable,
} from '@discocil/fv-domain-library/domain';

import { entityStampsFromJson, entityStampsToJson } from '@/cross-cutting/domain/services/Stamps';

import { GuestListLimit } from '../entities/GuestListLimit';

import type { GuestListLimitEither, GuestListLimitPrimitives } from '../entities/GuestListLimit';

export type GuestListLimitsJsonPrimitives = Omit<GuestListLimitPrimitives,
  'organizationId'
  | 'userId'
> & Nullable<{
  readonly organizationId: IdPrimitive;
  readonly userId: IdPrimitive;
}>;

export class GuestListLimitsJsonMapper {
  static toEntity(primitives: GuestListLimitsJsonPrimitives): GuestListLimitEither {
    return GuestListLimit.build({
      ...primitives,
      organizationId: Maybe.fromValue(primitives.organizationId),
      userId: Maybe.fromValue(primitives.userId),
      ...entityStampsFrom<PERSON>son(primitives),
    });
  }

  static toJson(guestList: GuestListLimit): GuestListLimitsJsonPrimitives {
    return {
      ...guestList.toPrimitives(),
      organizationId: guestList.organizationId.fold(() => null, item => item),
      userId: guestList.userId.fold(() => null, item => item),
      ...entityStampsToJson(guestList),
    };
  }
}
