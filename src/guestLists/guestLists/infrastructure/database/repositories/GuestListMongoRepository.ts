import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { GuestList } from '@/guestLists/guestLists/domain/entities/GuestList';
import { GuestListMapper } from '@/guestLists/guestLists/infrastructure/database/mappers/GuestListMapper';
import { GuestListSchemaMapper } from '@/guestLists/guestLists/infrastructure/database/mappers/GuestListSchemaMapper';
import { guestListSchema } from '@/guestLists/guestLists/infrastructure/database/schemas/GuestListSchema';

import type { GuestListRepository } from '@/guestLists/guestLists/domain/contracts/GuestListRepository';
import type {
  GuestListEither,
  GuestListKey<PERSON>,
  GuestLists,
  GuestL<PERSON>Either,
  GuestListsTotals,
  GuestListsTotalsEither,
} from '@/guestLists/guestLists/domain/entities/GuestList';
import type { GuestListSchemaType } from '@/guestLists/guestLists/infrastructure/database/schemas/GuestListSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

type PropertiesMapper = Partial<Record<GuestListKeys, keyof GuestListSchemaType>>;

export class GuestListMongoRepository extends MongoRepository implements GuestListRepository {
  protected getSchema(): Schema {
    return new Schema(guestListSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'listas';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      eventId: 'evento_id',
      typeId: 'tarifa_id',
      groups: 'grupos',
      organizationAssignedId: 'negocio_apuntando',
      referrerId: 'referente_id',
      state: 'estado',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<GuestListEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<GuestListSchemaType>();

    return queryResponse
      ? GuestListMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: GuestList.name }));
  }

  async getTotals(criteria: Criteria): Promise<GuestListsTotalsEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const project = { apuntados: 1 };

    const group = {
      _id: null,
      totalRegistered: { $sum: '$apuntados' },
    };

    const queryResponse = await connection.aggregate<GuestListsTotals>([{ $match: filterQuery.filter }, { $project: project }, { $group: group }]);

    if (queryResponse.length === 0) {
      return right({ totalRegistered: 0 });
    }

    const { totalRegistered } = queryResponse.shift() as GuestListsTotals;

    return right({ totalRegistered });
  }

  async save(guestList: GuestList): Promise<void> {
    const toSave = GuestListSchemaMapper.execute(guestList);

    const filter = { _id: guestList.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(guestList: GuestLists): Promise<void> {
    const connection = await this.getConnection();
    const models: Array<Partial<GuestListSchemaType>> = [];

    guestList.forEach((guestList: GuestList) => models.push(GuestListSchemaMapper.execute(guestList)));

    await connection.insertMany(models);
  }

  async search(criteria: Criteria): Promise<GuestListsEither> {
    const response = new Map<IdPrimitive, GuestList>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<GuestListSchemaType[]>();

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const guestListResult = GuestListMapper.execute(model);

      if (guestListResult.isLeft()) {
        return left(guestListResult.value);
      }

      const ticket = guestListResult.value;

      response.set(ticket.id, ticket);
    }

    return right(response);
  }
}
