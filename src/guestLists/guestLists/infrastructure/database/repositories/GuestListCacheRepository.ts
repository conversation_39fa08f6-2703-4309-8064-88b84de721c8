import {
  left, NotFoundError, right,
} from '@discocil/fv-domain-library';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { GuestListJsonMapper } from '@/guestLists/guestLists/domain/mappers/GuestListJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { GuestListRepository } from '@/guestLists/guestLists/domain/contracts/GuestListRepository';
import type {
  GuestList, GuestListEither, GuestLists, GuestListsEither, GuestListsTotalsEither,
} from '@/guestLists/guestLists/domain/entities/GuestList';
import type { GuestListJsonPrimitives } from '@/guestLists/guestLists/domain/mappers/GuestListJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library';

export class GuestListCacheRepository extends CacheRepository implements GuestListRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: GuestListRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<GuestListEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: GuestListJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = GuestListJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        GuestListJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async getTotals(criteria: Criteria): Promise<GuestListsTotalsEither> {
    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    return this.otherRepository.getTotals(criteria);
  }

  async save(guestList: GuestList): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.save(guestList);
  }

  async saveMany(guestLists: GuestLists): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.saveMany(guestLists);
  }

  async search(criteria: Criteria): Promise<GuestListsEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: GuestListJsonPrimitives[] = JSON.parse(cacheHit);
      const guestLists: GuestLists = new Map<IdPrimitive, GuestList>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = GuestListJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        guestLists.set(_primitive.id, entityOrError.value);
      }

      return right(guestLists);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const guestLists = repositoryResult.value;
      const guestListLength = guestLists.size;
      const jsonGuestLists = new Array<GuestListJsonPrimitives>(guestListLength);
      let guestListCacheIndex = 0;


      if (guestListLength > 0) {
        for (const _guestList of guestLists.values()) {
          jsonGuestLists[guestListCacheIndex] = GuestListJsonMapper.toJson(_guestList);

          guestListCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonGuestLists, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}

