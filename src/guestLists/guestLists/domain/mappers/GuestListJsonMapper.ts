import { entityStamps<PERSON>rom<PERSON><PERSON>, entityStampsToJson } from '@/cross-cutting/domain/services/Stamps';

import { GuestList } from '../entities/GuestList';

import type { GuestListEither, GuestListPrimitives } from '../entities/GuestList';

export type GuestListJsonPrimitives = GuestListPrimitives;

export class GuestListJsonMapper {
  static toEntity(primitives: GuestListJsonPrimitives): GuestListEither {
    return GuestList.build({
      ...primitives,
      ...entityStampsFromJson(primitives),
    });
  }

  static toJson(guestList: GuestList): GuestListJsonPrimitives {
    return {
      ...guestList.toPrimitives(),
      ...entityStampsTo<PERSON><PERSON>(guestList),
    };
  }
}
