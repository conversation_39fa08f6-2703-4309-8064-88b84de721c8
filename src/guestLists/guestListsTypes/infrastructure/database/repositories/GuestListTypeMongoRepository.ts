import { Paginator } from '@discocil/fv-criteria-converter-library/domain';
import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { GuestListType } from '@/guestLists/guestListsTypes/domain/entities/GuestListType';

import { GuestListTypeMapper } from '../mappers/GuestListTypeMapper';
import { GuestListTypeSchemaMapper } from '../mappers/GuestListTypeSchemaMapper';
import { guestListTypeSchema } from '../schemas/GuestListTypeSchema';

import type { GuestListTypeRepository } from '@/guestLists/guestListsTypes/domain/contracts/GuestListTypeRepository';
import type {
  GuestListTypeEither,
  GuestListTypeKeys,
  GuestListTypes,
  GuestListTypesEither,
} from '@/guestLists/guestListsTypes/domain/entities/GuestListType';
import type { Criteria, RequiredCriteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { GuestListTypeSchemaType } from '../schemas/GuestListTypeSchemaType';

type PropertiesMapper = Partial<Record<GuestListTypeKeys, keyof GuestListTypeSchemaType>>;

export class GuestListTypeMongoRepository extends MongoRepository implements GuestListTypeRepository {
  protected getSchema(): Schema {
    return new Schema(guestListTypeSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'listas_tarifas';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      eventId: 'evento_id',
      type: 'tipo',
      maxCustomers: 'max_clientes',
      isActive: 'activa',
      removedAt: 'removed_at',
      order: 'orden',
    };
  }

  async find(criteria: Criteria): Promise<GuestListTypeEither> {
    criteria.setPage(1).setPerPage(1);

    const queryResponse = (await this.customQueryFinder<GuestListTypeSchemaType[]>(criteria)).shift();

    return queryResponse
      ? GuestListTypeMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: GuestListType.name }));
  }

  async save(guestListType: GuestListType): Promise<void> {
    const toSave = GuestListTypeSchemaMapper.execute(guestListType);

    const filter = { _id: guestListType.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(guestListTypes: GuestListTypes): Promise<void> {
    const connection = await this.getConnection();
    const models: Array<Partial<GuestListTypeSchemaType>> = [];

    guestListTypes.forEach((guestListType: GuestListType) => models.push(GuestListTypeSchemaMapper.execute(guestListType)));

    await connection.insertMany(models);
  }

  async search(criteria: Criteria): Promise<GuestListTypesEither> {
    const guestListTypes = new Map<IdPrimitive, GuestListType>();

    const queryResponse = await this.customQueryFinder<GuestListTypeSchemaType[]>(criteria);

    if (queryResponse.length === 0) {
      return right({ guestListTypes });
    }

    for (const model of queryResponse) {
      const guestListTypeResult = GuestListTypeMapper.execute(model);

      if (guestListTypeResult.isLeft()) {
        return left(guestListTypeResult.value);
      }

      const guestListType = guestListTypeResult.value;

      guestListTypes.set(guestListType.id, guestListType);
    }

    if (criteria.pagination) {
      const connection = await this.getConnection();
      const filterQuery = this.criteriaConverter.convert(criteria);
      const total = await connection.countDocuments(filterQuery.filter);
      const requiredCriteria = criteria as RequiredCriteria;

      return right({
        guestListTypes,
        paginationMetadata: Paginator.execute(total, requiredCriteria, queryResponse.length),
      });
    }

    return right({ guestListTypes });
  }

  private async customQueryFinder<T>(criteria: Criteria): Promise<T> {
    const connection = await this.getConnection();
    const pipeline = this.criteriaConverter.toPipeline(criteria);

    return connection.aggregate([
      ...pipeline,
      {
        $lookup: {
          from: 'eventos',
          let: { eventId: '$evento_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$eventId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'event',
        },
      },
      {
        $lookup: {
          from: 'negocios',
          let: { organizationId: '$negocio_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$organizationId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'organization',
        },
      },
      {
        $project: {
          main: '$$ROOT',
          eventObj: { $arrayElemAt: ['$event', 0] },
          organizationObj: { $arrayElemAt: ['$organization', 0] },
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              '$main',
              {
                event: { currency: { $ifNull: ['$eventObj.currency', null] } },
                organization: { currency: { $ifNull: ['$organizationObj.currency', null] } },
              },
            ],
          },
        },
      },
    ]) as unknown as T;
  }
}
