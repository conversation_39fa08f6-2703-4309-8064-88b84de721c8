import {
  Either2,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { PaymentJsonMapper } from '@/payments/domain/mappers/PaymentJsonMapper';

import { PaymentMongoRepository } from './PaymentMongoRepository';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { PaymentRepository } from '@/payments/domain/contracts/PaymentRepository';
import type {
  DeletePaymentEither,
  Payment,
  Payments,
  PaymentsEither,
} from '@/payments/domain/entities/Payment';
import type { PaymentJsonPrimitives } from '@/payments/domain/mappers/PaymentJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class PaymentCacheRepository extends CacheRepository implements PaymentRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: PaymentRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async save(payment: Payment): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.save(payment);
  }

  async search(criteria: Criteria): Promise<PaymentsEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: PaymentJsonPrimitives[] = JSON.parse(cacheHit);
      const payments: Payments = new Map<IdPrimitive, Payment>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = PaymentJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        payments.set(_primitive.id, entityOrError.value);
      }

      return Either2.right({ payments });
    }

    if (!this.otherRepository) {
      return Either2.left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return Either2.left(repositoryResult.get());
    }

    if (this.getConfig().isEnabled()) {
      const { payments } = repositoryResult.get();
      const paymentsLength = payments.size;
      let paymentsCacheIndex = 0;
      const jsonPayments = new Array<PaymentJsonPrimitives>(paymentsLength);

      if (paymentsLength > 0) {
        for (const _payment of payments.values()) {
          jsonPayments[paymentsCacheIndex] = PaymentJsonMapper.toJson(_payment);

          paymentsCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonPayments, this.getConfig().ttl);
    }

    return repositoryResult;
  }

  async remove(criteria: Criteria): Promise<DeletePaymentEither> {
    if (!this.otherRepository) {
      return Either2.left(UnexpectedError.build({
        context: this.constructor.name,
        data: { criteria },
        error: new Error(`${PaymentMongoRepository.name} is not defined`),
      }));
    }

    const responseOrError = await this.otherRepository.remove(criteria);

    if (responseOrError.isLeft()) {
      return Either2.left(responseOrError.get());
    }

    const deletedCount = responseOrError.get();

    return Either2.right(deletedCount);
  }
}
