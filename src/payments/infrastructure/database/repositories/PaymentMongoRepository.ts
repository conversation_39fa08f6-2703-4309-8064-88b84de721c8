import {
  Paginator, type Criteria, type RequiredCriteria,
} from '@discocil/fv-criteria-converter-library/domain';
import {
  Either2, FvDate, FvNumber, NotFoundError,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { Payment } from '@/payments/domain/entities/Payment';

import { PaymentMapper } from '../mappers/PaymentMapper';
import { PaymentSchemaMapper } from '../mappers/PaymentSchemaMapper';
import { paymentSchema } from '../schemas/PaymentSchema';

import type { PaymentRepository } from '@/payments/domain/contracts/PaymentRepository';
import type {
  Payments,
  PaymentsEither,
  DeletePaymentEither as RemovePaymentEither,
} from '@/payments/domain/entities/Payment';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { PaymentSchemaType } from '../schemas/PaymentSchemaType';

type PropertiesMapper = Partial<Record<keyof Payment, keyof PaymentSchemaType>>;

export class PaymentMongoRepository extends MongoRepository implements PaymentRepository {
  protected getSchema(): Schema {
    return new Schema(paymentSchema);
  }

  protected getModel(): string {
    return 'payments';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      eventId: 'evento_id',
      state: 'estado',
      userId: 'usuario_id',
      expiresAt: 'expires_at',
      removedAt: 'removed_at',
    };
  }

  async save(payment: Payment): Promise<void> {
    const filter = { _id: payment.id };
    const options = { upsert: true };
    const update = { $set: PaymentSchemaMapper.execute(payment) };

    const modelQuery = await this.getConnection();

    await modelQuery.updateOne(filter, update, options);
  }

  async search(criteria: Criteria): Promise<PaymentsEither> {
    const payments: Payments = new Map<IdPrimitive, Payment>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<PaymentSchemaType[]>();

    if (queryResponse.length === 0) {
      return Either2.right({ payments });
    }

    for (const model of queryResponse) {
      const paymentOrError = PaymentMapper.execute(model);

      if (paymentOrError.isLeft()) {
        return Either2.left(paymentOrError.value);
      }

      const payment = paymentOrError.value;

      payments.set(payment.id, payment);
    }

    if (criteria.pagination) {
      const total = await connection.countDocuments(filterQuery.filter);
      const requiredCriteria = criteria as RequiredCriteria;

      return Either2.right({
        payments,
        pagination: Paginator.execute(total, requiredCriteria, queryResponse.length),
      });
    }

    return Either2.right({ payments });
  }

  async remove(criteria: Criteria): Promise<RemovePaymentEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const deleteResult = await connection.updateOne(
      filterQuery.filter,
      { $set: { removed_at: FvDate.create().toMilliseconds() } },
    );

    if (deleteResult.modifiedCount === 0) {
      return Either2.left(NotFoundError.build({
        context: this.constructor.name,
        target: Payment.name,
        data: { criteria },
      }));
    }

    return Either2.right(FvNumber.build(deleteResult.upsertedCount));
  }
}
