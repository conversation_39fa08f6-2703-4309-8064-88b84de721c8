import { EResourceTypes, FvDate } from '@discocil/fv-domain-library/domain';

import type {
  PaymentResourceBookingInfoPrimitives,
  PaymentResourceInfoPrimitives,
  PaymentResourcePassesInfoPrimitives,
  PaymentResourceTicketsInfoPrimitives,
  PaymentResourceTicketsSupplementInfo,
} from '../contracts/PaymentResources';
import type {
  PaymentInfoBookingJsonPrimitives,
  PaymentInfoJsonPrimitives,
  PaymentInfoPassesJsonPrimitives,
  PaymentInfoTicketsJsonPrimitives,
  PaymentInfoTicketsSupplementJsonPrimitives,
} from './contracts/PaymentJsonMapper';

export class PaymentInfoPrimitiveToJsonMapper {
  static toJson(resourceType: EResourceTypes, primitives: PaymentResourceInfoPrimitives): PaymentInfoJsonPrimitives {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const match: Record<string, any> = {
      [EResourceTypes.PASSES]: (primitives: PaymentResourcePassesInfoPrimitives) => this.buildJsonPasses(primitives),
      [EResourceTypes.TICKETS]: (primitives: PaymentResourceTicketsInfoPrimitives) => this.buildJsonTickets(primitives),
      [EResourceTypes.BOOKINGS]: (primitives: PaymentResourceBookingInfoPrimitives) => this.buildJsonBooking(primitives),
      [EResourceTypes.UPSELLING]: (primitives: PaymentResourceTicketsSupplementInfo) => this.buildJsonTicketsSupplement(primitives),
    };

    if (!match[resourceType]) {
      throw new Error(`Unsupported resource type: ${resourceType}`);
    }

    return match[resourceType](primitives);
  }

  static buildJsonPasses(data: PaymentResourcePassesInfoPrimitives): PaymentInfoPassesJsonPrimitives {
    return {
      passes: data.passes.map(_pass => ({
        ..._pass,
        customer: {
          fullname: _pass.customer.fullname,
          image: _pass.customer.image.fold(() => null, item => item),
          email: _pass.customer.email.fold(() => null, item => item),
          phone: _pass.customer.phone.fold(() => null, item => item),
          gender: _pass.customer.gender.fold(() => null, item => item),
          personalDocumentNumber: {
            number: _pass.customer.personalDocumentNumber.number.fold(() => null, item => item),
            type: _pass.customer.personalDocumentNumber.type.fold(() => null, item => item),
          },
          address: _pass.customer.address.fold(() => null, item => item),
        },
      })),
      passCode: data.passCode,
      organization: data.organization,
    };
  }

  static buildJsonTickets(data: PaymentResourceTicketsInfoPrimitives): PaymentInfoTicketsJsonPrimitives {
    return {
      organization: { name: data.organization.name },
      tickets: {
        tickets: data.tickets.tickets.map((_ticket) => {
          return {
            id: _ticket.id,
            price: _ticket.price,
            importTotal: _ticket.importTotal,
            fees: _ticket.fees,
            option: {
              id: _ticket.option.id,
              price: _ticket.option.price,
            },
            discountAmountApplied: _ticket.discountAmountApplied,
            supplementsPrice: _ticket.supplementsPrice,
          };
        }),
        type: data.tickets.type
          ? { name: data.tickets.type.name }
          : null,

      },
      event: {
        name: data.event.name,
        date: FvDate.create(data.event.date).toISO(),
        tz: data.event.tz,
      },
    };
  }

  static buildJsonBooking(data: PaymentResourceBookingInfoPrimitives): PaymentInfoBookingJsonPrimitives {
    return {
      organization: { name: data.organization.name },
      event: {
        name: data.event.name.fold(() => null, item => item),
        date: data.event.date.fold(() => null, item => FvDate.create(item).toISO()),
        tz: data.event.tz.fold(() => null, item => item),
      },
      activationCode: data.activationCode,
    };
  }

  static buildJsonTicketsSupplement(data: PaymentResourceTicketsSupplementInfo): PaymentInfoTicketsSupplementJsonPrimitives {
    return {
      ticketId: data.ticketId,
      upselling: {
        supplements: data.upselling.supplements.map((_item) => {
          return {
            id: _item.id,
            purchaseQuantity: _item.purchaseQuantity,
            price: _item.price,
            label: _item.label,
          };
        }),
        totalPrice: data.upselling.totalPrice,
        supplementsPrice: data.upselling.supplementsPrice,
        commissions: {
          business: data.upselling.commissions.business,
          discocil: data.upselling.commissions.discocil,
        },
      },
      existingSupplements: data.existingSupplements.map((_item) => {
        return {
          id: _item.id,
          label: _item.label,
          price: _item.price,
          entrance: _item.entrance,
          hasFakePrice: _item.hasFakePrice,
          fakePrice: _item.fakePrice,
          description: _item.description,
          productQuantity: _item.productQuantity,
          productQuantityUsed: _item.productQuantityUsed,
          purchaseQuantity: _item.purchaseQuantity.fold(() => null, (_purchaseQuantityItem) => {
            return {
              quantity: _purchaseQuantityItem.quantity,
              purchaseLimit: {
                minQuantity: _purchaseQuantityItem.purchaseLimit.minQuantity,
                maxQuantity: _purchaseQuantityItem.purchaseLimit.maxQuantity.fold(() => null, item => item),
                isUnlimited: _purchaseQuantityItem.purchaseLimit.isUnlimited,
              },
            };
          }),
          redemptionDeadlineValue: _item.redemptionDeadlineValue.fold(() => null, item => FvDate.createFromSeconds(item).toISO()),
          receptionistId: _item.receptionistId,
          receptionistAppId: _item.receptionistAppId,
          platformReceptionist: _item.platformReceptionist,
        };
      }),
      supplementsTotalPrice: data.supplementsTotalPrice,
      ticketTotalPrice: data.ticketTotalPrice,
    };
  }
}
