import {
  EResourceTypes, FvDate,
  Maybe,
} from '@discocil/fv-domain-library/domain';

import type { PurchaseQuantityValueObjectPrimitives } from '@/tickets/tickets/domain/value-objects/PurchaseQuantityValueObject';
import type { DatePrimitive } from '@discocil/fv-domain-library/domain';
import type {
  PaymentResourceBookingInfoPrimitives,
  PaymentResourceInfoPrimitives,
  PaymentResourcePassesInfoPrimitives,
  PaymentResourceTicketsInfoPrimitives,
  PaymentResourceTicketsSupplementInfo,
} from '../contracts/PaymentResources';
import type {
  PaymentInfoBookingJsonPrimitives,
  PaymentInfoJsonPrimitives,
  PaymentInfoPassesJsonPrimitives,
  PaymentInfoTicketsJsonPrimitives,
  PaymentInfoTicketsSupplementJsonPrimitives,
} from './contracts/PaymentJsonMapper';

export class PaymentInfoJsonToPrimitiveMapper {
  static toPrimitive(resourceType: EResourceTypes, jsonPrimitives: PaymentInfoJsonPrimitives): PaymentResourceInfoPrimitives {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const match: Record<string, any> = {
      [EResourceTypes.PASSES]: (primitives: PaymentInfoPassesJsonPrimitives) => this.buildPrimitivePasses(primitives),
      [EResourceTypes.TICKETS]: (primitives: PaymentInfoTicketsJsonPrimitives) => this.buildPrimitiveTickets(primitives),
      [EResourceTypes.BOOKINGS]: (primitives: PaymentInfoBookingJsonPrimitives) => this.buildPrimitiveBooking(primitives),
      [EResourceTypes.UPSELLING]: (primitives: PaymentInfoTicketsSupplementJsonPrimitives) => this.buildPrimitiveTicketsSupplements(primitives),
    };

    if (!match[resourceType]) {
      throw new Error(`Unsupported resource type: ${resourceType}`);
    }

    return match[resourceType](jsonPrimitives);
  }

  static buildPrimitivePasses(jsonPrimitives: PaymentInfoPassesJsonPrimitives): PaymentResourcePassesInfoPrimitives {
    return {
      organization: jsonPrimitives.organization,
      passCode: jsonPrimitives.passCode,
      passes: jsonPrimitives.passes.map((_pass) => {
        return {
          id: _pass.id,
          applicationId: _pass.applicationId,
          organizationId: _pass.organizationId,
          organizationAssignedId: _pass.organizationAssignedId,
          customer: {
            fullname: _pass.customer.fullname,
            image: Maybe.fromValue(_pass.customer.image),
            email: Maybe.fromValue(_pass.customer.email),
            phone: Maybe.fromValue(_pass.customer.phone),
            gender: Maybe.fromValue(_pass.customer.gender),
            personalDocumentNumber: {
              number: Maybe.fromValue(_pass.customer.personalDocumentNumber.number),
              type: Maybe.fromValue(_pass.customer.personalDocumentNumber.type),
            },
            address: Maybe.fromValue(_pass.customer.address),
          },
          typeId: _pass.typeId,
          typeName: _pass.typeName,
          typeColor: _pass.typeColor,
          priceId: _pass.priceId,
          purchaseDate: _pass.purchaseDate,
          purchasePrice: _pass.purchasePrice,
          serviceFees: _pass.serviceFees,
          revenue: _pass.revenue,
          paymentId: _pass.paymentId,
          state: _pass.state,
          idx: _pass.idx,
        };
      }),
    };
  }

  static buildPrimitiveTickets(jsonPrimitives: PaymentInfoTicketsJsonPrimitives): PaymentResourceTicketsInfoPrimitives {
    return {
      organization: { name: jsonPrimitives.organization.name },
      event: {
        name: jsonPrimitives.event.name,
        date: (FvDate.createFromISO(jsonPrimitives.event.date).value as FvDate).toPrimitive(),
        tz: jsonPrimitives.event.tz,
      },
      tickets: {
        tickets: jsonPrimitives.tickets.tickets.map((_ticket) => {
          return {
            id: _ticket.id,
            price: _ticket.price,
            importTotal: _ticket.importTotal,
            fees: _ticket.fees,
            option: {
              id: _ticket.option.id,
              price: _ticket.option.price,
            },
            discountAmountApplied: _ticket.discountAmountApplied,
            supplementsPrice: _ticket.supplementsPrice,
          };
        }),
        type:
          jsonPrimitives.tickets.type
            ? { name: jsonPrimitives.tickets.type.name }
            : undefined,
      },

    };
  }

  static buildPrimitiveBooking(jsonPrimitives: PaymentInfoBookingJsonPrimitives): PaymentResourceBookingInfoPrimitives {
    const eventDateOrError = jsonPrimitives.event.date
      ? FvDate.createFromISO(jsonPrimitives.event.date)
      : null;

    const eventDate = eventDateOrError && eventDateOrError.isRight()
      ? Maybe.some(eventDateOrError.value.toPrimitive())
      : Maybe.none<DatePrimitive>();

    return {
      organization: { name: jsonPrimitives.organization.name },
      activationCode: jsonPrimitives.activationCode,
      event: {
        name: Maybe.fromValue(jsonPrimitives.event.name),
        date: eventDate,
        tz: Maybe.fromValue(jsonPrimitives.event.tz),
      },
    };
  };

  static buildPrimitiveTicketsSupplements(jsonPrimitives: PaymentInfoTicketsSupplementJsonPrimitives): PaymentResourceTicketsSupplementInfo {
    return {
      ticketId: jsonPrimitives.ticketId,
      upselling: {
        supplements: jsonPrimitives.upselling.supplements.map((_item) => {
          return {
            id: _item.id,
            purchaseQuantity: _item.purchaseQuantity,
            price: _item.price,
            label: _item.label,
          };
        }),
        totalPrice: jsonPrimitives.upselling.totalPrice,
        supplementsPrice: jsonPrimitives.upselling.supplementsPrice,
        commissions: {
          business: jsonPrimitives.upselling.commissions.business,
          discocil: jsonPrimitives.upselling.commissions.discocil,
        },
      },
      existingSupplements: jsonPrimitives.existingSupplements.map((_supplement) => {
        const redemptionDeadlineValueOrError = _supplement.redemptionDeadlineValue
          ? FvDate.createFromISO(_supplement.redemptionDeadlineValue)
          : null;

        const redemptionDeadlineValue = redemptionDeadlineValueOrError && redemptionDeadlineValueOrError.isRight()
          ? Maybe.some(redemptionDeadlineValueOrError.value.toSeconds())
          : Maybe.none<number>();

        const purchaseQuantity = _supplement.purchaseQuantity
          ? Maybe.some<PurchaseQuantityValueObjectPrimitives>({
            quantity: _supplement.purchaseQuantity.quantity,
            purchaseLimit: {
              minQuantity: _supplement.purchaseQuantity.purchaseLimit.minQuantity,
              maxQuantity: Maybe.fromValue(_supplement.purchaseQuantity.purchaseLimit.maxQuantity),
              isUnlimited: _supplement.purchaseQuantity.purchaseLimit.isUnlimited,
            },
          })
          : Maybe.none<PurchaseQuantityValueObjectPrimitives>();

        return {
          id: _supplement.id,
          label: _supplement.label,
          price: _supplement.price,
          entrance: _supplement.entrance,
          purchaseQuantity,
          redemptionDeadlineValue,
          receptionistId: _supplement.receptionistId,
          receptionistAppId: _supplement.receptionistAppId,
          platformReceptionist: _supplement.platformReceptionist,
          productQuantity: _supplement.productQuantity,
          productQuantityUsed: _supplement.productQuantityUsed,
          description: _supplement.description,
          hasFakePrice: _supplement.hasFakePrice,
          fakePrice: _supplement.fakePrice,
        };
      }),
      supplementsTotalPrice: jsonPrimitives.supplementsTotalPrice,
      ticketTotalPrice: jsonPrimitives.ticketTotalPrice,
    };
  }
}
