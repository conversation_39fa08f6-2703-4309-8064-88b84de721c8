import type {
  IdPrimitive, Nullable,
  Primitives,
} from '@discocil/fv-domain-library/domain';
import type {
  PaymentPass,
  PaymentResourceBookingInfoPrimitives,
  PaymentResourcePassesInfoPrimitives,
  PaymentResourceTicketsInfoPrimitives,
  PaymentResourceTicketsSupplementInfo,
} from '../../contracts/PaymentResources';

export type PaymentInfoPassesJsonPrimitives = Omit<PaymentResourcePassesInfoPrimitives,
  'passes'
> & {
  readonly passes: Array<
    Omit<Primitives<PaymentPass>, 'customer'> & {
      readonly customer: {
        readonly fullname: string;
        readonly image: string | null;
        readonly email: string | null;
        readonly phone: string | null;
        readonly gender: string | null;
        readonly personalDocumentNumber: {
          readonly number: string | null;
          readonly type: string | null;
        };
        readonly address: string | null;
      };
    }
  >;
};

export type PaymentInfoTicketsJsonPrimitives = Omit<PaymentResourceTicketsInfoPrimitives,
  'tickets'
  | 'event'
> & {
  readonly tickets: {
    readonly tickets: Array<{
      readonly id: IdPrimitive;
      readonly price: number;
      readonly importTotal: number;
      readonly fees: number;
      readonly option: {
        readonly id: string;
        readonly price: number;
      };
      readonly discountAmountApplied: number;
      readonly supplementsPrice: number;
    }>;
    readonly type: {
      readonly name: string;
    } | null;
  };
  readonly event: {
    readonly name: string;
    readonly date: string;
    readonly tz: string;
  };
};

export type PaymentInfoBookingJsonPrimitives = Omit<PaymentResourceBookingInfoPrimitives,
  'event'
> & {
  readonly event: Nullable<{
    readonly name: string;
    readonly date: string;
    readonly tz: string;
  }>;
};

export type PaymentInfoTicketsSupplementJsonPrimitives = Omit<PaymentResourceTicketsSupplementInfo,
  'upselling'
  | 'existingSupplements'
> & {
  readonly upselling: {
    readonly supplements: {
      readonly id: IdPrimitive;
      readonly purchaseQuantity: number;
      readonly price: number;
      readonly label: string;
    }[];
    readonly totalPrice: number;
    readonly supplementsPrice: number;
    readonly commissions: {
      readonly business: number;
      readonly discocil: number;
    };
  };
  existingSupplements: Array<{
    readonly id: IdPrimitive;
    readonly label: string;
    readonly price: number;
    readonly entrance: number;
    readonly hasFakePrice: boolean;
    readonly fakePrice: number | null;
    readonly description: string | null;
    readonly productQuantity: number | null;
    readonly productQuantityUsed: number | null;
    readonly purchaseQuantity: {
      readonly quantity: number;
      readonly purchaseLimit: {
        minQuantity: number;
        maxQuantity: number | null;
        isUnlimited: boolean;
      };
    } | null;
    readonly redemptionDeadlineValue: string | null;
    readonly receptionistId: string | null;
    readonly receptionistAppId: string | null;
    readonly platformReceptionist: string | null;
  }>;
};

export type PaymentInfoJsonPrimitives = PaymentInfoPassesJsonPrimitives
  | PaymentInfoTicketsJsonPrimitives
  | PaymentInfoBookingJsonPrimitives
  | PaymentInfoTicketsSupplementJsonPrimitives;
