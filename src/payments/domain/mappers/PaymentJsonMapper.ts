import {
  FvDate,
  left,
  Maybe,
} from '@discocil/fv-domain-library/domain';

import { entityStampsFromJson, entityStampsToJson } from '@/cross-cutting/domain/services/Stamps';
import { PaymentInfoPrimitiveToJsonMapper } from '@/payments/domain/mappers/PaymentInfoPrimitiveToJsonMapper';

import { Payment } from '../entities/Payment';

import { PaymentInfoJsonToPrimitiveMapper } from './PaymentInfoJsonToPrimitiveMapper';

import type { TemplatePrimitives } from '@/organizations/organizations/domain/entities/RuleTemplate';
import type { TDiscountCodeExchangedPrimitive } from '@/tickets/discountCodes/domain/entities/DiscountCode';
import type {
  DatePrimitive,
  IdPrimitive,
  Nullable,
} from '@discocil/fv-domain-library/domain';
import type {
  PaymentCustomerCustomFeesPrimitives,
  PaymentEither,
  PaymentPrimitives,
  PaymentSitting,
  PaymentSpotify,
} from '../entities/Payment';
import type { PaymentInfoJsonPrimitives } from './contracts/PaymentJsonMapper';

type TDiscountCodeExchangedJson = {
  readonly id: IdPrimitive;
  readonly code: string;
  readonly exchangedId: IdPrimitive | null;
};

export type PaymentJsonPrimitives = Omit<PaymentPrimitives,
  'eventId'
  | 'typeId'
  | 'userId'
  | 'paid'
  | 'info'
  | 'redirectUrl'
  | 'errorUrl'
  | 'merchantName'
  | 'paylinkId'
  | 'discount'
  | 'sitting'
  | 'spotify'
  | 'gatewayAccountId'
  | 'commissionRule'
  | 'expiresAt'
  | 'customerCustomFees'
> & Nullable<{
  readonly eventId: IdPrimitive;
  readonly typeId: IdPrimitive;
  readonly userId: IdPrimitive;
  readonly paid: string;
  readonly redirectUrl: string;
  readonly errorUrl: string;
  readonly merchantName: string;
  readonly paylinkId: IdPrimitive;
  readonly discount: TDiscountCodeExchangedJson;
  readonly sitting: PaymentSitting;
  readonly spotify: PaymentSpotify;
  readonly gatewayAccountId: IdPrimitive;
  readonly commissionRule: TemplatePrimitives;
  readonly customerCustomFees: PaymentCustomerCustomFeesPrimitives;
}> & {
  readonly info: PaymentInfoJsonPrimitives;
  readonly expiresAt: string;
};

export class PaymentJsonMapper {
  static toEntity(primitives: PaymentJsonPrimitives): PaymentEither {
    const isoToDate = (value: string): FvDate => FvDate.createFromISO(value).value as FvDate;

    const expiresAtOrError = FvDate.createFromISO(primitives.expiresAt);

    if (expiresAtOrError.isLeft()) {
      return left(expiresAtOrError.value);
    }

    const expiresAt = expiresAtOrError.value;
    const stamps = entityStampsFromJson(primitives);

    let paid = Maybe.none<DatePrimitive>();

    if (primitives.paid) {
      paid = Maybe.fromValue(isoToDate(primitives.paid).toPrimitive());
    }

    let discount = Maybe.none<TDiscountCodeExchangedPrimitive>();

    if (primitives.discount) {
      discount = Maybe.some({
        ...primitives.discount,
        exchangedId: Maybe.fromValue(primitives.discount.exchangedId),
      });
    }

    return Payment.build({
      ...primitives,
      eventId: Maybe.fromValue(primitives.eventId),
      typeId: Maybe.fromValue(primitives.typeId),
      userId: Maybe.fromValue(primitives.userId),
      paid,
      redirectUrl: Maybe.fromValue(primitives.redirectUrl),
      errorUrl: Maybe.fromValue(primitives.errorUrl),
      merchantName: Maybe.fromValue(primitives.merchantName),
      paylinkId: Maybe.fromValue(primitives.paylinkId),
      discount,
      sitting: Maybe.fromValue(primitives.sitting),
      spotify: Maybe.fromValue(primitives.spotify),
      gatewayAccountId: Maybe.fromValue(primitives.gatewayAccountId),
      commissionRule: Maybe.fromValue(primitives.commissionRule),
      customerCustomFees: Maybe.fromValue(primitives.customerCustomFees),
      expiresAt: expiresAt.toPrimitive(),
      info: PaymentInfoJsonToPrimitiveMapper.toPrimitive(primitives.resourceType, primitives.info),
      ...stamps,
    });
  }

  static toJson(payment: Payment): PaymentJsonPrimitives {
    return {
      id: payment.id,
      organizationId: payment.organizationId,
      eventId: payment.eventId.fold(() => null, item => item),
      typeId: payment.typeId.fold(() => null, item => item),
      userId: payment.userId.fold(() => null, item => item),
      resourceType: payment.resourceType,
      totalImportCts: payment.totalImportCts,
      resourceIds: payment.resourceIds,
      feesTotal: payment.feesTotal,
      discocilTotal: payment.discocilTotal,
      totalPrice: payment.totalPrice,
      totalImport: payment.totalImport,
      paid: payment.getPaidInISO().fold(() => null, item => item),
      info: PaymentInfoPrimitiveToJsonMapper.toJson(payment.resourceType, payment.info),
      nResources: payment.nResources,
      expiresAt: payment.getExpiresAtInISO(),
      state: payment.state,
      redirectUrl: payment.redirectUrl.fold(() => null, item => item),
      errorUrl: payment.errorUrl.fold(() => null, item => item),
      paymentsProvider: payment.paymentsProvider,
      merchantName: payment.merchantName.fold(() => null, item => item),
      currency: payment.currency,
      paylinkId: payment.paylinkId.fold(() => null, item => item),
      totalDiscount: payment.totalDiscount,
      supplements: {
        number: payment.numberSupplements,
        price: payment.priceSupplements,
      },
      warranty: {
        total: payment.totalWarranty,
        cost: payment.costWarranty,
      },
      discount: payment.discount.fold(() => null, (item) => {
        return {
          id: item.id,
          code: item.code,
          exchangedId: item.exchangedId.fold(() => null, item => item),
        };
      }),
      sitting: payment.sitting.fold(() => null, item => item),
      spotify: payment.spotify.fold(() => null, item => item),
      isFourvenuesGateway: payment.isFourvenuesGateway,
      gatewayAccountId: payment.gatewayAccountId.fold(() => null, item => item),
      commissionRule: payment.commissionRule.fold(() => null, item => item),
      saleType: payment.saleType,
      customerCustomFees: payment.customerCustomFees.fold(() => null, item => item),
      ...entityStampsToJson(payment),
    };
  }
}
