import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { PaymentKeys } from '../entities/Payment';

class FilterField extends FilterFieldBase<PaymentKeys> {}

export class UserIdFilter {
  private static readonly field: PaymentKeys = 'userId';

  static buildEqual(userId: string): Filter {
    const userIdField = new FilterField(this.field);
    const userIdOperator = FilterOperator.equal();
    const userIdFilterValue = FilterValue.build(userId);

    return new Filter(userIdField, userIdOperator, userIdFilterValue);
  }
}
