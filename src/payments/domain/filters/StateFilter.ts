import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { EPaymentStates } from '@discocil/fv-domain-library/domain';
import type { PaymentKeys } from '../entities/Payment';

class FilterField extends FilterFieldBase<PaymentKeys> {}

export class StateFilter {
  private static readonly field: PaymentKeys = 'state';

  static buildEqual(state: EPaymentStates): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(state);

    return new Filter(field, operator, filterValue);
  }
}
