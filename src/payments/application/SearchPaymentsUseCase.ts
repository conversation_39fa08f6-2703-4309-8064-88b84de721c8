import {
  contextualizeError,
  Either2,
  NotFoundError,
  UniqueEntityID,
  type UseCase,
} from '@discocil/fv-domain-library';

import { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import { EventEntity } from '@/events/events/domain/entities/EventEntity';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { TicketRepository } from '@/tickets/tickets/domain/contracts/TicketRepository';
import { Tickets } from '@/tickets/tickets/domain/entities/TicketEntity';
import { TicketCriteriaMother } from '@/tickets/tickets/domain/filters/TicketCriteriaMother';

import { PaymentRepository } from '../domain/contracts/PaymentRepository';
import {
  Payments,
  PaymentsWithTickets,
  SearchPaginatedPaymentsEither,
} from '../domain/entities/Payment';
import { PaymentCriteriaMother } from '../domain/filters/PaymentCriteriaMother';

import type { SearchPaymentsDto } from '../domain/contracts/SearchPaymentsDtoContract';

export class SearchPaymentsUseCase implements UseCase<SearchPaymentsDto, Promise<SearchPaginatedPaymentsEither>> {
  constructor(
    private readonly eventRepository: EventRepository,
    private readonly paymentRepository: PaymentRepository,
    private readonly ticketRepository: TicketRepository,
  ) { }

  @contextualizeError()
  async execute(dto: SearchPaymentsDto): Promise<SearchPaginatedPaymentsEither> {
    const {
      pagination,
      eventId,
      status,
    } = dto;

    const eventCriteria = EventCriteriaMother.idToMatch(dto.eventId);

    const eventOrError = await this.eventRepository.find(eventCriteria);

    if (eventOrError.isLeft()) {
      return Either2.left(eventOrError.value);
    }

    const event = eventOrError.value;

    if (!event.isPurchasable()) {
      return Either2.left(NotFoundError.build({
        context: this.constructor.name,
        target: EventEntity.name,
        data: { eventId },
      }));
    }

    const paymentCriteria = PaymentCriteriaMother.searchPayments(eventId, status, pagination);

    const paymentsOrError = await this.paymentRepository.search(paymentCriteria);

    if (paymentsOrError.isLeft()) {
      return Either2.left(paymentsOrError.get());
    }

    const { payments, pagination: paymentsPagination } = paymentsOrError.get();

    const tickets = await this.getTicketsFromPayments(payments);

    const paymentsWithTickets = this.filterPaymentsByTicketIdx(payments, tickets, dto.idx);

    return Either2.right({
      paymentsWithTickets,
      tickets,
      pagination: paymentsPagination,
    });
  }

  private async getTicketsFromPayments(payments: Payments): Promise<Tickets> {
    const tickets: Tickets = new Map();

    const ticketIds: UniqueEntityID[] = [];

    for (const _payment of payments.values()) {
      if (!_payment.isForTicket()) {
        continue;
      }

      ticketIds.push(..._payment.resourceIds.map(id => UniqueEntityID.build(id)));
    }

    const ticketsCriteria = TicketCriteriaMother.idsToMatch(ticketIds);

    const ticketsResult = await this.ticketRepository.search(ticketsCriteria);

    if (ticketsResult.isLeft()) {
      return tickets;
    }

    return ticketsResult.value.tickets;
  };

  private filterPaymentsByTicketIdx(payments: Payments, tickets: Tickets, idx: string | undefined): PaymentsWithTickets {
    const paymentWithTickets: PaymentsWithTickets = new Map();

    for (const _payment of payments.values()) {
      const relatedTickets = [...tickets.values()].filter((ticket) => {
        if (!_payment.includesResourceId(ticket.id)) {
          return false;
        }

        return ticket.isIdxEqualTo(idx);
      });

      paymentWithTickets.set(_payment.id, {
        payment: _payment,
        tickets: new Map(relatedTickets.map(ticket => [ticket.id, ticket])),
      });
    }

    return paymentWithTickets;
  }
}
