import {
  contextualizeError,
  Either2,
  type UseCase,
} from '@discocil/fv-domain-library';

import { PaymentRepository } from '../domain/contracts/PaymentRepository';
import { DeletePaymentEither } from '../domain/entities/Payment';
import { PaymentCriteriaMother } from '../domain/filters/PaymentCriteriaMother';

import type { DeletePaymentDto } from '../domain/contracts/DeletePaymentDtoContract';

export class DeletePaymentUseCase implements UseCase<DeletePaymentDto, Promise<DeletePaymentEither>> {
  constructor(private readonly paymentRepository: PaymentRepository) { }

  @contextualizeError()
  async execute(dto: DeletePaymentDto): Promise<DeletePaymentEither> {
    const { paymentId } = dto;

    const deleteCriteria = PaymentCriteriaMother.idToMatch(paymentId);

    const paymentOrError = await this.paymentRepository.find(deleteCriteria);

    if (paymentOrError.isLeft()) {
      return Either2.left(paymentOrError.value);
    }


    return await this.paymentRepository.remove(deleteCriteria);
  }
}
