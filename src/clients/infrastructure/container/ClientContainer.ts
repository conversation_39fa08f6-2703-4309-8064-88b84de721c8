import { container, instanceCachingFactory } from 'tsyringe';

import { ClientDependencyIdentifier } from '@/clients/domain/dependencyIdentifier/ClientDependencyIdentifier';
import { ClientMongoRepository } from '@/clients/infrastructure/database/repositories/ClientMongoRepository';
import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';

import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';

export const ClientContainer = {
  register: (): void => {
    container.register(ClientDependencyIdentifier.ClientRepository, {
      useFactory: instanceCachingFactory((container) => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

        return new ClientMongoRepository(dbConnection);
      }),
    });
  },
};
