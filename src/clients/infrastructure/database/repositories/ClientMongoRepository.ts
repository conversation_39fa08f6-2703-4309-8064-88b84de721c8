import { left, NotFoundError } from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { Client } from '@/clients/domain/entities/Client';
import { ClientMapper } from '@/clients/infrastructure/database/mappers/ClientMapper';
import { ClientSchemaMapper } from '@/clients/infrastructure/database/mappers/ClientSchemaMapper';
import { clientSchema } from '@/clients/infrastructure/database/schemas/ClientSchema';
import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';

import type { ClientRepository } from '@/clients/domain/contracts/ClientRepository';
import type { ClientEither, Clients } from '@/clients/domain/entities/Client';
import type { ClientSchemaType } from '@/clients/infrastructure/database/schemas/ClientSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export class ClientMongoRepository extends MongoRepository implements ClientRepository {
  protected getSchema(): Schema {
    return new Schema(clientSchema);
  }

  protected getDBName(): string {
    return EDBNames.CRM;
  }

  protected getModel(): string {
    return 'clientes';
  }

  async find(criteria: Criteria): Promise<ClientEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<ClientSchemaType>();

    return queryResponse ? ClientMapper.execute(queryResponse) : left(NotFoundError.build({ context: this.constructor.name, target: Client.name }));
  }

  async save(client: Client): Promise<void> {
    const toSave = ClientSchemaMapper.execute(client);

    const filter = { _id: client.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(clients: Clients): Promise<void> {
    const connection = await this.getConnection();
    const models: Array<Partial<ClientSchemaType>> = [];

    clients.forEach((client: Client) => models.push(ClientSchemaMapper.execute(client)));

    await connection.insertMany(models);
  }
}
