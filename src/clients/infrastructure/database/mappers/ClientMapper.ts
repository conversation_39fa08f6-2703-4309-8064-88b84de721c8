import { ELanguagesC<PERSON>, Maybe } from '@discocil/fv-domain-library/domain';
import { container } from 'tsyringe';

import { Client } from '@/clients/domain/entities/Client';
import { clientValidationSchema } from '@/clients/infrastructure/database/mappers/ClientSchemaValidation';
import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { MapperError } from '@/cross-cutting/domain/errors/MapperError';

import type { ClientEither } from '@/clients/domain/entities/Client';
import type { ClientSchemaType } from '@/clients/infrastructure/database/schemas/ClientSchemaType';
import type Ajv from 'ajv';

export class ClientMapper {
  static execute(data: ClientSchemaType): ClientEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(clientValidationSchema);

    if (!validate(data) || validate.errors) {
      MapperError.logger({
        context: ClientMapper.name,
        data,
        target: validate.errors,
      });

      return ClientSoftMapper.execute(data);
    }

    return this.buildEntity(data);
  }

  static buildEntity(data: ClientSchemaType): ClientEither {
    return Client.build({
      id: data._id,
      organizationId: data.negocio_id,
      userId: Maybe.fromValue(data.usuario_id),
      email: Maybe.fromValue(data.email),
      name: data.nombre,
      normalizedName: data.nombre_normalized,
      image: data.imagen,
      gender: data.genero,
      personalDocumentNumber: data.personal_document_number,
      personalDocumentType: data.personal_document_type,
      address: data.address,
      birthDate: data.nacimiento,
      phone: data.telefono,
      postalCode: data.codigo_postal,
      city: data.poblacion,
      province: data.provincia,
      idNumber: data.id_number,
      language: data.idioma ?? ELanguagesCodes.ES,
      tagsIds: data.etiquetas_ids,
      notes: data.notas,
      remarketing: data.remarketing ?? false,
      totalLists: data.total_listas ?? 0,
      totalListsSubscribed: data.total_listas_apuntados ?? 0,
      totalListsEnter: data.total_listas_entran ?? 0,
      totalListsEarned: data.total_listas_recaudado ?? 0,
      totalTickets: data.total_entradas ?? 0,
      totalTicketsEnter: data.total_entradas_entran ?? 0,
      totalTicketsEarned: data.total_entradas_recaudado ?? 0,
      totalReservations: data.total_reservas ?? 0,
      totalReservationsSubscribed: data.total_reservas_apuntados ?? 0,
      totalReservationsEnter: data.total_reservas_entran ?? 0,
      totalReservationsEarned: data.total_reservas_recaudado ?? 0,
      totalEarned: data.total_recaudado ?? 0,
      totalVisits: data.total_visitas ?? 0,
      lastAttendance: data.ultima_asistencia ?? 0,
      lastSale: data.ultima_venta ?? 0,
      imported: data.importado ?? false,
      createdAt: data.created_at,
      createdBy: data.created_by,
      updatedAt: data.updated_at,
      updatedBy: data.updated_by,
      removedAt: data.removed_at,
      removedBy: data.removed_by,
    });
  }
}

export class ClientSoftMapper extends ClientMapper {
  static execute(data: ClientSchemaType): ClientEither {
    return this.buildEntity({
      ...data,
      _id: data._id,
      negocio_id: data.negocio_id ?? null,
      usuario_id: data.usuario_id ?? null,
      email: data.email ?? null,
      nombre: data.nombre ?? null,
      nombre_normalized: data.nombre_normalized ?? null,
      imagen: data.imagen ?? null,
      genero: data.genero ?? null,
      personal_document_number: data.personal_document_number ?? null,
      personal_document_type: data.personal_document_type ?? null,
      address: data.address ?? null,
      nacimiento: data.nacimiento ?? null,
      telefono: data.telefono ?? null,
      codigo_postal: data.codigo_postal ?? null,
      poblacion: data.poblacion ?? null,
      provincia: data.provincia ?? null,
      id_number: data.id_number ?? null,
      idioma: data.idioma ?? null,
      etiquetas_ids: data.etiquetas_ids ?? null,
      notas: data.notas ?? null,
      total_listas: data.total_listas ?? 0,
      total_listas_apuntados: data.total_listas_apuntados ?? 0,
      total_listas_entran: data.total_listas_entran ?? null,
      total_listas_recaudado: data.total_listas_recaudado ?? 0,
      total_entradas: data.total_entradas ?? 0,
      total_entradas_entran: data.total_entradas_entran ?? 0,
      total_entradas_recaudado: data.total_entradas_recaudado ?? 0,
      total_reservas: data.total_reservas ?? 0,
      total_reservas_apuntados: data.total_reservas_apuntados ?? 0,
      total_reservas_entran: data.total_reservas_entran ?? 0,
      total_reservas_recaudado: data.total_reservas_recaudado ?? 0,
      total_recaudado: data.total_recaudado ?? 0,
      total_visitas: data.total_visitas ?? 0,
      ultima_asistencia: data.ultima_asistencia ?? 0,
      ultima_venta: data.ultima_venta ?? 0,
    });
  }
}
