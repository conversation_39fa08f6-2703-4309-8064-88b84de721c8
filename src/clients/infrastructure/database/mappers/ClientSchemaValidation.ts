import type { JSONSchemaType } from 'ajv';
import type { ClientSchemaType } from '../schemas/ClientSchemaType';

export const clientValidationSchema: JSONSchemaType<ClientSchemaType> = {
  title: 'client Json Schema Validation',
  required: ['negocio_id'],
  type: 'object',
  properties: {
    _id: { type: 'string' },
    negocio_id: { type: 'string' },
    usuario_id: { type: 'string' },
    email: { type: 'string' },
    nombre: { type: 'string' },
    nombre_normalized: { type: 'string' },
    imagen: { type: 'string' },
    genero: { type: 'string' },
    personal_document_number: { type: 'string' },
    personal_document_type: { type: 'string' },
    address: { type: 'string' },
    nacimiento: { type: 'number' },
    telefono: { type: 'string' },
    codigo_postal: { type: 'string' },
    poblacion: { type: 'string' },
    provincia: { type: 'string' },
    id_number: { type: 'string' },
    idioma: { type: 'string' },
    etiquetas_ids: { type: 'array', items: { type: 'string' } },
    notas: { type: 'string' },
    remarketing: { type: 'boolean' },
    total_listas: { type: 'number' },
    total_listas_apuntados: { type: 'number' },
    total_listas_entran: { type: 'number' },
    total_listas_recaudado: { type: 'number' },
    total_entradas: { type: 'number' },
    total_entradas_entran: { type: 'number' },
    total_entradas_recaudado: { type: 'number' },
    total_reservas: { type: 'number' },
    total_reservas_apuntados: { type: 'number' },
    total_reservas_entran: { type: 'number' },
    total_reservas_recaudado: { type: 'number' },
    total_recaudado: { type: 'number' },
    total_visitas: { type: 'number' },
    ultima_asistencia: { type: 'number' },
    ultima_venta: { type: 'number' },
    importado: { type: 'boolean' },
    created_at: { type: 'number' },
    created_by: { type: 'string' },
    updated_at: { type: 'number' },
    updated_by: { type: 'string' },
    removed_at: { type: 'number' },
    removed_by: { type: 'string' },
  },
};
