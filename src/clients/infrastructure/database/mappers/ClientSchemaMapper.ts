import type { Client } from '@/clients/domain/entities/Client';
import type { ClientSchemaType } from '@/clients/infrastructure/database/schemas/ClientSchemaType';

export class ClientSchemaMapper {
  static execute(client: Client): ClientSchemaType {
    return {
      _id: client.id,
      genero: client.gender,
      nombre: client.name,
      nombre_normalized: client.normalizedName,
      imagen: client.image,
      email: client.email.fold(() => '', item => item),
      personal_document_number: client.personalDocumentNumber,
      personal_document_type: client.personalDocumentType,
      address: client.address,
      nacimiento: client.birthDate,
      telefono: client.phone,
      codigo_postal: client.postalCode,
      poblacion: client.city,
      provincia: client.province,
      id_number: client.idNumber,
      idioma: client.language,
      etiquetas_ids: client.tagsIds,
      notas: client.notes,
      remarketing: client.remarketing,
      negocio_id: client.organizationId,
      usuario_id: client.userId.fold(() => '', item => item),
      total_listas: client.totalLists,
      total_listas_apuntados: client.totalListsSubscribed,
      total_listas_entran: client.totalListsEnter,
      total_listas_recaudado: client.totalListsEarned,
      total_entradas: client.totalTickets,
      total_entradas_entran: client.totalTicketsEnter,
      total_entradas_recaudado: client.totalTicketsEarned,
      total_reservas: client.totalReservations,
      total_reservas_apuntados: client.totalReservationsSubscribed,
      total_reservas_entran: client.totalReservationsEnter,
      total_reservas_recaudado: client.totalReservationsEarned,
      total_recaudado: client.totalEarned,
      total_visitas: client.totalVisits,
      ultima_asistencia: client.lastAttendance,
      ultima_venta: client.lastSale,
      importado: client.imported,
      created_at: client.createdAt,
      created_by: client.createdBy,
      updated_at: client.updatedAt,
      updated_by: client.updatedBy,
      removed_at: client.removedAt,
      removed_by: client.removedBy,
    };
  }
}
