import type { EGender } from '@discocil/fv-domain-library/domain';

export type ClientSchemaType = {
  _id: string;
  negocio_id: string;
  usuario_id: string;
  email: string;
  nombre: string;
  nombre_normalized: string;
  imagen: string;
  genero: EGender;
  personal_document_number: string;
  personal_document_type: string;
  address: string;
  nacimiento: number;
  telefono: string;
  codigo_postal: string;
  poblacion: string;
  provincia: string;
  id_number: string;
  idioma: string;
  etiquetas_ids: string[];
  notas: string;
  remarketing: boolean;
  total_listas: number;
  total_listas_apuntados: number;
  total_listas_entran: number;
  total_listas_recaudado: number;

  total_entradas: number;
  total_entradas_entran: number;
  total_entradas_recaudado: number;

  total_reservas: number;
  total_reservas_apuntados: number;
  total_reservas_entran: number;
  total_reservas_recaudado: number;

  total_recaudado: number;

  total_visitas: number;

  ultima_asistencia: number;
  ultima_venta: number;
  importado: boolean;

  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
  removed_at: number;
  removed_by: string;
};
