import { ELanguagesCodes, Gender } from '@discocil/fv-domain-library/domain';

export const clientSchema = {
  _id: {
    type: String,
    required: true,
  },
  negocio_id: {
    type: String,
    required: true,
    index: true,
  },
  usuario_id: {
    type: String,
    index: true,
    default: '',
  },
  email: {
    type: String,
    index: true,
    default: '',
  },
  nombre: { type: String },
  nombre_normalized: { type: String },
  imagen: { type: String },
  genero: {
    type: String,
    default: '',
    enum: Gender.values(),
  },
  personal_document_number: { type: String },
  personal_document_type: { type: String },
  address: {
    type: String,
    default: '',
  },
  nacimiento: { type: Number },
  telefono: { type: String },
  codigo_postal: { type: String },
  poblacion: { type: String },
  provincia: { type: String },
  id_number: { type: String },
  idioma: {
    type: String,
    default: ELanguagesCodes.ES,
  },
  etiquetas_ids: { type: [String] },
  notas: { type: String },
  remarketing: {
    type: Boolean,
    default: false,
  },
  total_listas: {
    type: Number,
    default: 0,
  },
  total_listas_apuntados: {
    type: Number,
    default: 0,
  },
  total_listas_entran: {
    type: Number,
    default: 0,
  },
  total_listas_recaudado: {
    type: Number,
    default: 0,
  },
  total_entradas: {
    type: Number,
    default: 0,
  },
  total_entradas_entran: {
    type: Number,
    default: 0,
  },
  total_entradas_recaudado: {
    type: Number,
    default: 0,
  },
  total_reservas: {
    type: Number,
    default: 0,
  },
  total_reservas_apuntados: {
    type: Number,
    default: 0,
  },
  total_reservas_entran: {
    type: Number,
    default: 0,
  },
  total_reservas_recaudado: {
    type: Number,
    default: 0,
  },
  total_recaudado: {
    type: Number,
    default: 0,
  },
  total_visitas: {
    type: Number,
    default: 0,
  },
  ultima_asistencia: {
    type: Number,
    default: 0,
  },
  ultima_venta: {
    type: Number,
    default: 0,
  },
  importado: {
    type: Boolean,
    default: false,
  },
  created_at: { type: Number },
  created_by: { type: String },
  updated_at: { type: Number },
  updated_by: { type: String },
  removed_at: {
    type: Number,
    index: true,
  },
  removed_by: { type: String },
};
