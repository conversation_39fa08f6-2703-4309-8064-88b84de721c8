import {
  AggregateRoot, UniqueEntityID, right,
} from '@discocil/fv-domain-library/domain';

import { stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  CreatedAt,
  CreatedBy,
  EGender,
  Either,
  IdPrimitive,
  Maybe,
  NotFoundError,
  Primitives,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';

export type Clients = Map<IdPrimitive, Client>;

export type ClientPrimitives = Primitives<Client>;

export type ClientEither = Either<MapperError | NotFoundError, Client>;

export class Client extends AggregateRoot {
  private constructor(
    id: UniqueEntityID,
    private readonly _organizationId: UniqueEntityID,
    private readonly _userId: Maybe<UniqueEntityID>,
    readonly email: Maybe<string>,
    readonly name: string,
    readonly normalizedName: string,
    readonly image: string,
    readonly gender: EGender,
    readonly personalDocumentNumber: string,
    readonly personalDocumentType: string,
    readonly address: string,
    readonly birthDate: number,
    readonly phone: string,
    readonly postalCode: string,
    readonly city: string,
    readonly province: string,
    readonly idNumber: string,
    readonly language: string,
    readonly tagsIds: string[],
    readonly notes: string,
    readonly remarketing: boolean,
    readonly totalLists: number,
    readonly totalListsSubscribed: number,
    readonly totalListsEnter: number,
    readonly totalListsEarned: number,
    readonly totalTickets: number,
    readonly totalTicketsEnter: number,
    readonly totalTicketsEarned: number,
    readonly totalReservations: number,
    readonly totalReservationsSubscribed: number,
    readonly totalReservationsEnter: number,
    readonly totalReservationsEarned: number,
    readonly totalEarned: number,
    readonly totalVisits: number,
    readonly lastAttendance: number,
    readonly lastSale: number,
    readonly imported: boolean,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);
  }

  static build(primitives: ClientPrimitives): ClientEither {
    const id = UniqueEntityID.build(primitives.id);
    const organizationId = UniqueEntityID.build(primitives.organizationId);
    const userId = primitives.userId.map(item => UniqueEntityID.build(item));
    const email = primitives.email.map(item => item);

    const stamps = stampValueObjects(primitives);

    const entity = new Client(
      id,
      organizationId,
      userId,
      email,
      primitives.name,
      primitives.normalizedName,
      primitives.image,
      primitives.gender,
      primitives.personalDocumentNumber,
      primitives.personalDocumentType,
      primitives.address,
      primitives.birthDate,
      primitives.phone,
      primitives.postalCode,
      primitives.city,
      primitives.province,
      primitives.idNumber,
      primitives.language,
      primitives.tagsIds,
      primitives.notes,
      primitives.remarketing,
      primitives.totalLists,
      primitives.totalListsSubscribed,
      primitives.totalListsEnter,
      primitives.totalListsEarned,
      primitives.totalTickets,
      primitives.totalTicketsEnter,
      primitives.totalTicketsEarned,
      primitives.totalReservations,
      primitives.totalReservationsSubscribed,
      primitives.totalReservationsEnter,
      primitives.totalReservationsEarned,
      primitives.totalEarned,
      primitives.totalVisits,
      primitives.lastAttendance,
      primitives.lastSale,
      primitives.imported,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return right(entity);
  }

  get organizationId(): IdPrimitive {
    return this._organizationId.toPrimitive();
  }

  get userId(): Maybe<IdPrimitive> {
    return this._userId.map(item => item.toPrimitive());
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }

  toPrimitives(): ClientPrimitives {
    return {
      id: this.id,
      organizationId: this.organizationId,
      userId: this.userId,
      remarketing: this.remarketing,
      gender: this.gender,
      email: this.email,
      phone: this.phone,
      postalCode: this.postalCode,
      birthDate: this.birthDate,
      name: this.name,
      normalizedName: this.normalizedName,
      image: this.image,
      personalDocumentNumber: this.personalDocumentNumber,
      personalDocumentType: this.personalDocumentType,
      address: this.address,
      city: this.city,
      province: this.province,
      idNumber: this.idNumber,
      language: this.language,
      tagsIds: this.tagsIds,
      notes: this.notes,
      totalLists: this.totalLists,
      totalListsSubscribed: this.totalListsSubscribed,
      totalListsEnter: this.totalListsEnter,
      totalListsEarned: this.totalListsEarned,
      totalTickets: this.totalTickets,
      totalTicketsEnter: this.totalTicketsEnter,
      totalTicketsEarned: this.totalTicketsEarned,
      totalReservations: this.totalReservations,
      totalReservationsSubscribed: this.totalReservationsSubscribed,
      totalReservationsEnter: this.totalReservationsEnter,
      totalReservationsEarned: this.totalReservationsEarned,
      totalEarned: this.totalEarned,
      totalVisits: this.totalVisits,
      lastAttendance: this.lastAttendance,
      lastSale: this.lastSale,
      imported: this.imported,
      createdAt: this.createdAt,
      createdBy: this.createdBy,
      updatedAt: this.updatedAt,
      updatedBy: this.updatedBy,
      removedAt: this.removedAt,
      removedBy: this.removedBy,
    };
  }
}
