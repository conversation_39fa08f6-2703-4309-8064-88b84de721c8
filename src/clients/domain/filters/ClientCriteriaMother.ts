import { Criteria, Filters } from '@discocil/fv-criteria-converter-library/domain';

import { IdFilter } from '@/cross-cutting/domain/filters/IdFilter';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';

export class ClientCriteriaMother {
  static idToMatch(id: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(id));

    return Criteria.build(filters);
  }
}
