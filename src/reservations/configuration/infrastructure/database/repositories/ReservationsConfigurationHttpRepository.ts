import {
  left,
  NotFoundError,
  right,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import { Zone } from 'seatsio';

import { BaseHttpRepository } from '@/cross-cutting/infrastructure/database/repositories/HttpRepository';
import { ReservationsConfigurationHttpMapper } from '@/reservations/configuration/infrastructure/database/mappers/ReservationsConfigurationHttpMapper';
import { HTTP_CODES } from '@app/http/HttpCodes';

import type { HttpRepository } from '@/cross-cutting/infrastructure/contracts/HttpRepository';
import type { ReservationsConfigurationRepository } from '@/reservations/configuration/domain/contracts/ReservationsConfigurationRepository';
import type { ReservationsConfigurationEither } from '@/reservations/configuration/domain/dpos/ReservationsConfiguration';
import type { ReservationsConfigurationSchemaType } from '@/reservations/configuration/infrastructure/database/schemas/ReservationsConfigurationSchemaType';

export class ReservationsConfigurationHttpRepository extends BaseHttpRepository implements ReservationsConfigurationRepository {
  constructor(
    protected readonly apiKey: string,
    baseUrl: string,
    httpRepository: HttpRepository,
  ) {
    super(baseUrl, httpRepository);
  }

  async find(organizationId: string): Promise<ReservationsConfigurationEither> {
    const path = `advanced-configuration/${organizationId}`;

    const url = this.makeUrl({ url: path });
    const headers = this.makeHeaders();

    const repositoryResponse = await this.httpRepository.get<ReservationsConfigurationSchemaType>({ url, headers });

    if (repositoryResponse.isLeft()) {
      if (repositoryResponse.value.status === HTTP_CODES.NOT_FOUND_404) {
        return left(NotFoundError.build({ context: this.constructor.name, target: Zone.name }));
      }

      return left(UnexpectedError.build({
        context: this.constructor.name,
        target: Zone.name,
        data: { organizationId },
      }));
    }

    const { data } = repositoryResponse.value;
    const configurationOrError = ReservationsConfigurationHttpMapper.toDomain(data);

    if (configurationOrError.isLeft()) {
      return left(configurationOrError.value);
    }

    return right(configurationOrError.value);
  }

  protected makeHeaders(): Headers {
    const headers = new Headers();

    headers.set('Accept', 'application/json');
    headers.set('Content-Type', 'application/json');
    headers.set('x-api-key', this.apiKey);

    return headers;
  }
}
