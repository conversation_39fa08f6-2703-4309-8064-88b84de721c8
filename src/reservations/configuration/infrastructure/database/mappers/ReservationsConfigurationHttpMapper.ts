import { left, right } from '@discocil/fv-domain-library/domain';


import { ReservationsConfiguration, type ReservationsConfigurationEither } from '@/reservations/configuration/domain/dpos/ReservationsConfiguration';

import type { ReservationsConfigurationSchemaType } from '@/reservations/configuration/infrastructure/database/schemas/ReservationsConfigurationSchemaType';

export class ReservationsConfigurationHttpMapper {
  static toDomain(configurationRaw: ReservationsConfigurationSchemaType): ReservationsConfigurationEither {
    const configurationOrError = ReservationsConfiguration.build({
      organizationId: configurationRaw.organizationId,
      clientsCanMakeReservations: configurationRaw.reservationPermissions.clients,
      clientsCanSelectSpace: configurationRaw.spaceSelectionPermissions.clients,
    });

    if (configurationOrError.isLeft()) {
      return left(configurationOrError.value);
    }

    return right(configurationOrError.value);
  }
}
