import {
  right,
  UniqueEntityID,
  type Either, type NotFoundError, type Primitives, type UnexpectedError,
} from '@discocil/fv-domain-library/domain';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { CriteriaConverterError } from '@discocil/fv-criteria-converter-library/domain';

export type ReservationsConfigurationPrimitives = Primitives<ReservationsConfiguration>;

export type ReservationsConfigurationEither = Either<
  CriteriaConverterError
  | MapperError
  | NotFoundError
  | UnexpectedError,
  ReservationsConfiguration
>;

export class ReservationsConfiguration {
  constructor(
    private readonly _organizationId: UniqueEntityID,
    private readonly _clientsCanMakeReservations: boolean,
    private readonly _clientsCanSelectSpace: boolean,
  ) { }

  static build(configuration: ReservationsConfigurationPrimitives): ReservationsConfigurationEither {
    const configurationOrError = new ReservationsConfiguration(
      UniqueEntityID.build(configuration.organizationId),
      configuration.clientsCanMakeReservations,
      configuration.clientsCanSelectSpace,
    );

    return right(configurationOrError);
  }

  get organizationId(): string {
    return this._organizationId.value;
  }

  get clientsCanMakeReservations(): boolean {
    return this._clientsCanMakeReservations;
  }

  get clientsCanSelectSpace(): boolean {
    return this._clientsCanSelectSpace;
  }

  toPrimitive(): ReservationsConfigurationPrimitives {
    return {
      organizationId: this._organizationId.value,
      clientsCanMakeReservations: this._clientsCanMakeReservations,
      clientsCanSelectSpace: this._clientsCanSelectSpace,
    };
  }
}
