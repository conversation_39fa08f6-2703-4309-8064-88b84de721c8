import {
  left, Maybe, right,
} from '@discocil/fv-domain-library/domain';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { FindReservationsConfigurationDto } from '@/reservations/configuration/application/contracts/FindReservationsConfigurationDto';
import type { ReservationsConfigurationRepository } from '@/reservations/configuration/domain/contracts/ReservationsConfigurationRepository';
import type { ReservationsConfiguration } from '@/reservations/configuration/domain/dpos/ReservationsConfiguration';
import type { CriteriaConverterError } from '@discocil/fv-criteria-converter-library/domain';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type {
  Either, NotFoundError, UnexpectedError,
} from '@discocil/fv-domain-library/domain';

type Response = Either<CriteriaConverterError | MapperError | NotFoundError | UnexpectedError, Maybe<ReservationsConfiguration>>;

export class FindReservationsConfigurationUseCase implements UseCase<FindReservationsConfigurationDto, Promise<Response >> {
  constructor(
    private readonly reservationsConfigurationRepository: ReservationsConfigurationRepository,
  ) {}

  async execute(dto: FindReservationsConfigurationDto): Promise<Response> {
    const { event } = dto;

    if (!event.hasReservations()) {
      return right(Maybe.none<ReservationsConfiguration>());
    }

    const configurationOrError = await this.reservationsConfigurationRepository.find(event.organizationId);

    if (configurationOrError.isLeft()) {
      return left(configurationOrError.value);
    }

    const configuration = configurationOrError.value;

    return right(Maybe.some(configuration));
  }
}

