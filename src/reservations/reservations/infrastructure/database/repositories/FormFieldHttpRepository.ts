import { HttpCriteriaConverter } from '@discocil/fv-criteria-converter-library/infrastructure';
import {
  left,
  NotFoundError,
  right,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import { Zone } from 'seatsio';

import { BaseHttpRepository } from '@/cross-cutting/infrastructure/database/repositories/HttpRepository';
import { FormFieldHttpMapper } from '@/reservations/reservations/infrastructure/database/mappers/FormFieldHttpMapper';
import { HTTP_CODES } from '@app/http/HttpCodes';

import type { HttpRepository } from '@/cross-cutting/infrastructure/contracts/HttpRepository';
import type { FormFieldRepository } from '@/reservations/reservations/domain/contracts/FormFieldRepository';
import type { FormFieldsEither } from '@/reservations/reservations/domain/entities/FormField';
import type { FormFieldsSchemaType } from '@/reservations/reservations/infrastructure/database/schemas/FormFieldSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';

export class FormFieldHttpRepository extends BaseHttpRepository implements FormFieldRepository {
  constructor(
    protected readonly apiKey: string,
    baseUrl: string,
    httpRepository: HttpRepository,
  ) {
    super(baseUrl, httpRepository);
  }

  async search(organizationId: UniqueEntityID, criteria: Criteria): Promise<FormFieldsEither> {
    const path = `form-fields/${organizationId.value}`;

    const convertedParamsOrError = HttpCriteriaConverter.convert(criteria);

    if (convertedParamsOrError.isLeft()) {
      return left(convertedParamsOrError.value);
    }

    const convertedParams = convertedParamsOrError.value;

    const url = this.makeUrl({ queryParams: convertedParams, url: path });
    const headers = this.makeHeaders();

    const repositoryResponse = await this.httpRepository.get<FormFieldsSchemaType>({ url, headers });

    if (repositoryResponse.isLeft()) {
      if (repositoryResponse.value.status === HTTP_CODES.NOT_FOUND_404) {
        return left(NotFoundError.build({ context: this.constructor.name, target: Zone.name }));
      }

      return left(UnexpectedError.build({
        context: this.constructor.name,
        target: Zone.name,
        data: { criteria },
      }));
    }

    const { data } = repositoryResponse.value;
    const formFieldsOrError = FormFieldHttpMapper.toDomainFromArray(data);

    if (formFieldsOrError.isLeft()) {
      return left(formFieldsOrError.value);
    }

    return right(formFieldsOrError.value);
  }

  protected makeHeaders(): Headers {
    const headers = new Headers();

    headers.set('Accept', 'application/json');
    headers.set('Content-Type', 'application/json');
    headers.set('x-api-key', this.apiKey);

    return headers;
  }
}
