import {
  left, Maybe, right,
} from '@discocil/fv-domain-library/domain';

import { FormFieldCollection } from '@/reservations/reservations/domain/collections/FormFieldCollection';
import { FormField } from '@/reservations/reservations/domain/entities/FormField';

import type { FormFieldEither, FormFieldsEither } from '@/reservations/reservations/domain/entities/FormField';
import type { FormFieldSchemaType } from '@/reservations/reservations/infrastructure/database/schemas/FormFieldSchemaType';

export class FormFieldHttpMapper {
  static toDomain(formFieldRaw: FormFieldSchemaType): FormFieldEither {
    const formFieldOrError = FormField.build({
      id: formFieldRaw.id,
      organizationId: formFieldRaw.organizationId,
      title: formFieldRaw.title,
      response: formFieldRaw.value,
      type: formFieldRaw.type,
      isRequired: formFieldRaw.isRequired,
      isActive: formFieldRaw.isActive,
      canBeUpdated: formFieldRaw.canBeUpdated,
      placeholder: Maybe.fromValue(formFieldRaw.placeholder),
      options: formFieldRaw.options || [],
    });

    if (formFieldOrError.isLeft()) {
      return left(formFieldOrError.value);
    }

    return right(formFieldOrError.value);
  }

  static toDomainFromArray(formFieldsRaw: FormFieldSchemaType[] = []): FormFieldsEither {
    const formFields = new Array<FormField>();

    for (const formField of formFieldsRaw) {
      const formFieldOrError = this.toDomain(formField);

      if (formFieldOrError.isLeft()) {
        return left(formFieldOrError.value);
      }

      formFields.push(formFieldOrError.value);
    }

    const formFieldsCollection = FormFieldCollection.fromPrimitives(formFields);

    return right(formFieldsCollection);
  }
}
