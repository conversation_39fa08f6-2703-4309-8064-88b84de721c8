import type {
  Collection,
  Maybe,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

type FormField = {
  readonly title: string;
  readonly value: unknown;
};

export type PurchaseReservationRequestDto = {
  readonly eventId: UniqueEntityID;
  readonly zoneId: UniqueEntityID;
  readonly organizationId: UniqueEntityID;
  readonly numberOfPeople: number;
  readonly isFullPayment: boolean;
  readonly purchaseId: UniqueEntityID;
  readonly rateId: UniqueEntityID;
  readonly successUrl: string;
  readonly failureUrl: string;
  readonly observations: Maybe<string>;
  readonly ip: string;
  readonly applicationId: string;
  readonly shouldAddSubscriber: boolean;
  readonly totalAmount: number;
  readonly formFields: Collection<FormField>;
  readonly referentId: Maybe<string>;
  readonly notificationLanguage: string;
};
