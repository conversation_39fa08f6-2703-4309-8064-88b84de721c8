import { Collection } from '@discocil/fv-domain-library/domain';

import type { FormField } from '@/reservations/reservations/domain/entities/FormField';
import type { CollectionKey } from '@discocil/fv-domain-library/domain';

export class FormFieldCollection<T = FormField> extends Collection<T> {
  static override new<T = FormField>(key?: CollectionKey<T>): FormFieldCollection<T> {
    return new this<T>(key);
  }

  static fromPrimitives(primitives: FormField[]): FormFieldCollection<FormField> {
    const collection = new FormFieldCollection<FormField>('id');

    primitives.forEach(item => collection.add(item));

    return collection;
  }

  filterActive(): FormFieldCollection<FormField> {
    const fields = new FormFieldCollection<FormField>('id');

    for (const _field of this.items.values()) {
      const field = _field as FormField;

      if (!field.isActive) {
        continue;
      }

      fields.add(field);
    }

    return fields;
  }
}
