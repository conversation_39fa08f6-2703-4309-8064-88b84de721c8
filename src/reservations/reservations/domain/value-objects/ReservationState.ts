import {
  EReservationState,
  FvEnum,
  InvalidArgumentError,
  left,
  PostServiceStateTypes,
  PreServiceStateTypes,
  right,
  ServiceStateTypes,
} from '@discocil/fv-domain-library/domain';

import type { Either } from '@discocil/fv-domain-library/domain';

type ReservationStateEither = Either<InvalidArgumentError, ReservationState>;

export class ReservationState extends FvEnum<EReservationState> {
  static readonly values = Object.values(EReservationState);

  constructor(value: EReservationState) {
    super(value, ReservationState.values);
  }

  static build(value: EReservationState): ReservationStateEither {
    return this.values.includes(value)
      ? right(new ReservationState(value))
      : left(InvalidArgumentError.buildInvalidReservationState({
        context: this.constructor.name,
        target: value,
      }));
  }

  isPreService(): boolean {
    return Object.values(PreServiceStateTypes).some(enumValue => enumValue.toString() === this.toPrimitive());
  }

  isService(): boolean {
    return Object.values(ServiceStateTypes).some(enumValue => enumValue.toString() === this.toPrimitive());
  }

  isPostService(): boolean {
    return Object.values(PostServiceStateTypes).some(enumValue => enumValue.toString() === this.toPrimitive());
  }

  isAcceptPending(): boolean {
    return this.equalTo(EReservationState.ACCEPT_PENDING);
  }

  isAccepted(): boolean {
    return this.equalTo(EReservationState.ACCEPTED);
  }

  isRejected(): boolean {
    return this.equalTo(EReservationState.REJECTED);
  }

  isClientCanceled(): boolean {
    return this.equalTo(EReservationState.CLIENT_CANCELLED);
  }

  isToReview(): boolean {
    return this.equalTo(EReservationState.TO_REVIEW);
  }

  isNotCompleted(): boolean {
    return this.equalTo(EReservationState.NOT_COMPLETED);
  }

  isArrived(): boolean {
    return this.equalTo(EReservationState.ARRIVED);
  }

  isSeated(): boolean {
    return this.equalTo(EReservationState.SEATED);
  }

  isPaid(): boolean {
    return this.equalTo(EReservationState.PAID);
  }

  isFree(): boolean {
    return this.equalTo(EReservationState.FREE);
  }

  isNoShow(): boolean {
    return this.equalTo(EReservationState.NO_SHOW);
  }
}
