import {
  FvEnum,
  InvalidArgumentError,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import type { Either } from '@discocil/fv-domain-library/domain';

export enum EFormFieldSection {
  PROFESSIONALS = 'professionals',
  MICROSITES = 'microsites'
}

export type FormFieldSectionEither = Either<InvalidArgumentError, FormFieldSection>;

export class FormFieldSection extends FvEnum<EFormFieldSection> {
  static readonly values = Object.values(EFormFieldSection);

  private constructor(value: EFormFieldSection) {
    super(value, FormFieldSection.values);
  }

  static build(value: EFormFieldSection): FormFieldSectionEither {
    return this.values.includes(value)
      ? right(new FormFieldSection(value))
      : left(InvalidArgumentError.invalidValue({
        context: this.constructor.name,
        target: this.constructor.name,
        data: { value },
      }));
  }
}
