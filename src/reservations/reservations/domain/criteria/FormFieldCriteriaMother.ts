import { Criteria, Filters } from '@discocil/fv-criteria-converter-library/domain';

import { OrganizationIdFilter } from '@/reservations/reservations/domain/criteria/filters/OrganizationIdFilter';
import { SectionFilter } from '@/reservations/reservations/domain/criteria/filters/SectionFilter';

import type { EFormFieldSection } from '@/reservations/reservations/domain/value-objects/FormFieldSection';
import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';

export class FormFieldCriteriaMother {
  static organizationIdIs(organizationId: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(OrganizationIdFilter.buildEqual(organizationId));

    return Criteria.build(filters);
  }

  static sectionIs(section: EFormFieldSection): Criteria {
    const filters = Filters.build();

    filters.add(SectionFilter.buildEqual(section));

    return Criteria.build(filters);
  }
}
