import {
  Filter,
  FilterField,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { ReservationKeys } from '@/reservations/reservations/domain/entities/Reservation';

export class ActivationCodeFilter {
  private static readonly field: ReservationKeys = 'activationCode';

  static buildEqual(activationCode: string): Filter {
    const activationCodeField = new FilterField(this.field);
    const activationCodeOperator = FilterOperator.equal();
    const activationCodeFilterValue = FilterValue.build(activationCode);

    return new Filter(activationCodeField, activationCodeOperator, activationCodeFilterValue);
  }
}
