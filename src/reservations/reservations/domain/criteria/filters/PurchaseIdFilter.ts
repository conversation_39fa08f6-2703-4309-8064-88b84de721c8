import {
  Filter,
  FilterField,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { PaymentKeys } from '@/reservations/reservations/domain/entities/Payment';

export class PurchaseIdFilter {
  private static readonly field: PaymentKeys = 'purchaseId';

  static buildEqual(purchaseId: string): Filter {
    const purchaseIdField = new FilterField(this.field);
    const purchaseIdOperator = FilterOperator.equal();
    const purchaseIdFilterValue = FilterValue.build(purchaseId);

    return new Filter(purchaseIdField, purchaseIdOperator, purchaseIdFilterValue);
  }
}
