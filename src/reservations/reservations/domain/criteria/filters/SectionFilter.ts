import {
  FilterField, FilterOperator, FilterValue,
  Filter,
} from '@discocil/fv-criteria-converter-library/domain';

import type { EFormFieldSection } from '@/reservations/reservations/domain/value-objects/FormFieldSection';

export class SectionFilter {
  private static readonly field = 'section';

  static buildEqual(section: EFormFieldSection): Filter {
    const sectionField = new FilterField(this.field);
    const sectionOperator = FilterOperator.equal();
    const sectionFilterValue = FilterValue.build(section);

    return new Filter(sectionField, sectionOperator, sectionFilterValue);
  }
}
