import { Criteria, Filters } from '@discocil/fv-criteria-converter-library/domain';

import { ActivationCodeFilter } from '@/reservations/reservations/domain/criteria/filters/ActivationCodeFilter';
import { PurchaseIdFilter } from '@/reservations/reservations/domain/criteria/filters/PurchaseIdFilter';

export class ReservationCriteriaMother {
  static activationCodeToMatch(activationCode: string): Criteria {
    const filters = Filters.build();

    filters.add(ActivationCodeFilter.buildEqual(activationCode));

    return Criteria.build(filters);
  }

  static purchaseIdToMatch(purchaseId: string): Criteria {
    const filters = Filters.build();

    filters.add(PurchaseIdFilter.buildEqual(purchaseId));

    return Criteria.build(filters);
  }
}
