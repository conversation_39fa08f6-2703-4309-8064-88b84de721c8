import { left, right } from '@discocil/fv-domain-library/domain';

import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import type { FormFieldRepository } from '@/reservations/reservations/domain/contracts/FormFieldRepository';
import type { FormFieldsEither } from '@/reservations/reservations/domain/entities/FormField';

export class FormFieldSearcher {
  constructor(private readonly formFieldRepository: FormFieldRepository) {}

  async execute(organizationId: UniqueEntityID, criteria: Criteria): Promise<FormFieldsEither> {
    const formFieldsCollectionOrError = await this.formFieldRepository.search(organizationId, criteria);

    if (formFieldsCollectionOrError.isLeft()) {
      return left(formFieldsCollectionOrError.value);
    }

    return right(formFieldsCollectionOrError.value);
  }
}
