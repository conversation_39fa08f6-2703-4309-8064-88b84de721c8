import { left, right } from '@discocil/fv-domain-library/domain';


import { FormFieldCriteriaMother } from '@/reservations/reservations/domain/criteria/FormFieldCriteriaMother';
import { EFormFieldSection } from '@/reservations/reservations/domain/value-objects/FormFieldSection';

import type { SearchFormFieldsDto } from '@/reservations/reservations/application/contracts/SearchFormFieldsDto';
import type { FormFieldsEither } from '@/reservations/reservations/domain/entities/FormField';
import type { FormFieldSearcher } from '@/reservations/reservations/domain/services/FormFieldSearcher';
import type { UseCase } from '@discocil/fv-domain-library/application';

export class SearchFormFieldsUseCase implements UseCase<SearchFormFieldsDto, Promise<FormFieldsEither >> {
  constructor(private readonly formFieldSearcher: FormFieldSearcher) {}

  async execute(dto: SearchFormFieldsDto): Promise<FormFieldsEither> {
    const { organizationId } = dto;

    const criteria = FormFieldCriteriaMother.sectionIs(EFormFieldSection.MICROSITES);

    const formFieldsCollectionOrError = await this.formFieldSearcher.execute(organizationId, criteria);

    if (formFieldsCollectionOrError.isLeft()) {
      return left(formFieldsCollectionOrError.value);
    }

    const formFieldsCollection = formFieldsCollectionOrError.value;

    const activeFormFieldsCollection = formFieldsCollection.filterActive();

    return right(activeFormFieldsCollection);
  }
}

