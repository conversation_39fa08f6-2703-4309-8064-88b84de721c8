import {
  left, right, contextualizeError,
} from '@discocil/fv-domain-library/domain';


import type { InternalMessageBrokerClient } from '@/cross-cutting/domain/messageBroker/InternalMessageBrokerClientContracts';
import type { ReservationRepository } from '@/reservations/reservations/domain/contracts/ReservationRepository';
import type { PurchaseReservationRequestDto } from '@/reservations/reservations/domain/dtos/PurchaseReservationRequest';
import type { ReservationEither } from '@/reservations/reservations/domain/entities/Reservation';
import type { UseCase } from '@discocil/fv-domain-library/application';

export class PurchaseReservationUseCase implements UseCase<PurchaseReservationRequestDto, Promise<ReservationEither>> {
  constructor(
    private readonly reservationRepository: ReservationRepository,
    private readonly internalMessageBroker: InternalMessageBrokerClient,
  ) {}

  @contextualizeError()
  async execute(request: PurchaseReservationRequestDto): Promise<ReservationEither> {
    const purchaseResponse = await this.reservationRepository.purchase(request);

    if (purchaseResponse.isLeft()) {
      return left(purchaseResponse.value);
    }

    const reservation = purchaseResponse.value;

    void this.internalMessageBroker.publish(reservation.pullDomainEvents());

    return right(reservation);
  }
}
