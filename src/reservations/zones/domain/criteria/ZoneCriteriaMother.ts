import { Criteria, Filters } from '@discocil/fv-criteria-converter-library/domain';

import { EventIdFilter } from '@/reservations/zones/domain/criteria/filters/EventIdFilter';
import { OrganizationIdFilter } from '@/reservations/zones/domain/criteria/filters/OrganizationIdFilter';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';

export class ZoneCriteriaMother {
  static eventIdToMatch(eventId: UniqueEntityID, organizationId: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(EventIdFilter.buildEqual(eventId));
    filters.add(OrganizationIdFilter.buildEqual(organizationId));

    return Criteria.build(filters);
  }
}
