import {
  FilterField, FilterOperator, FilterValue,
  Filter,
} from '@discocil/fv-criteria-converter-library/domain';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';

export class EventIdFilter {
  private static readonly field = 'eventId';

  static buildEqual(eventId: UniqueEntityID): Filter {
    const eventIdField = new FilterField(this.field);
    const eventIdOperator = FilterOperator.equal();
    const eventIdFilterValue = FilterValue.build(eventId.toString());

    return new Filter(eventIdField, eventIdOperator, eventIdFilterValue);
  }
}
