import {
  FilterField, FilterOperator, FilterValue,
  Filter,
} from '@discocil/fv-criteria-converter-library/domain';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';

export class OrganizationIdFilter {
  private static readonly field = 'organizationId';

  static buildEqual(organizationId: UniqueEntityID): Filter {
    const organizationIdField = new FilterField(this.field);
    const organizationIdOperator = FilterOperator.equal();
    const organizationIdFilterValue = FilterValue.build(organizationId.toString());

    return new Filter(organizationIdField, organizationIdOperator, organizationIdFilterValue);
  }
}
