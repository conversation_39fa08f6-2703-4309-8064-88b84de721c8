import {
  FilterField, FilterOperator, FilterValue,
  Filter,
} from '@discocil/fv-criteria-converter-library/domain';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';

export class ZoneIdFilter {
  private static readonly field = 'zoneId';

  static buildEqual(zoneId: UniqueEntityID): Filter {
    const zoneIdField = new FilterField(this.field);
    const zoneIdOperator = FilterOperator.equal();
    const zoneIdFilterValue = FilterValue.build(zoneId.toString());

    return new Filter(zoneIdField, zoneIdOperator, zoneIdFilterValue);
  }
}
