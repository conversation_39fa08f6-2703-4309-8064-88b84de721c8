import { Collection } from '@discocil/fv-domain-library/domain';

import type { Zone } from '@/reservations/zones/domain/entities/Zone';
import type { CollectionKey } from '@discocil/fv-domain-library/domain';

export class ZoneCollection<T = Zone> extends Collection<T> {
  static override new<T = Zone>(key?: CollectionKey<T>): ZoneCollection<T> {
    return new this<T>(key);
  }

  static fromPrimitives(primitives: Zone[]): ZoneCollection<Zone> {
    const collection = new ZoneCollection<Zone>('id');

    primitives.forEach(item => collection.add(item));

    return collection;
  }

  filterByMicrosite(): ZoneCollection<Zone> {
    const zones = new ZoneCollection<Zone>('id');

    for (const _zone of this.items.values()) {
      const zone = _zone as Zone;

      if (zone.isHiddenInWeb) {
        continue;
      }

      zone.calculateRatesPricesPerPerson();

      zones.add(zone);
    }

    return zones;
  }
}
