import {
  left, right, contextualizeError,
} from '@discocil/fv-domain-library/domain';

import { ZoneCriteriaMother } from '@/reservations/zones/domain/criteria/ZoneCriteriaMother';

import type { FindZoneDto } from '@/reservations/zones/application/contracts/FindZoneDto';
import type { ZoneRepository } from '@/reservations/zones/domain/contracts/ZoneRepository';
import type { ZoneEither } from '@/reservations/zones/domain/entities/Zone';
import type { UseCase } from '@discocil/fv-domain-library/application';

export class FindZoneUseCase implements UseCase<FindZoneDto, Promise<ZoneEither>> {
  constructor(private readonly zoneRepository: ZoneRepository) {}

  @contextualizeError()
  async execute(dto: FindZoneDto): Promise<ZoneEither> {
    const {
      zoneId, eventId, organizationId,
    } = dto;

    const criteria = ZoneCriteriaMother.eventIdToMatch(eventId, organizationId);

    const zoneOrError = await this.zoneRepository.find(zoneId, criteria);

    if (zoneOrError.isLeft()) {
      return left(zoneOrError.value);
    }

    const zone = zoneOrError.value;

    zone.calculateRatesPricesPerPerson();

    return right(zone);
  }
}

