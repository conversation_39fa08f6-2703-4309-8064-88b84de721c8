import {
  left, right, contextualizeError,
} from '@discocil/fv-domain-library/domain';

import { ZoneCriteriaMother } from '@/reservations/zones/domain/criteria/ZoneCriteriaMother';

import type { SearchZonesDto } from '@/reservations/zones/application/contracts/SearchZonesDto';
import type { ZoneRepository } from '@/reservations/zones/domain/contracts/ZoneRepository';
import type { ZonesEither } from '@/reservations/zones/domain/entities/Zone';
import type { UseCase } from '@discocil/fv-domain-library/application';

export class SearchZonesUseCase implements UseCase<SearchZonesDto, Promise<ZonesEither>> {
  constructor(private readonly zoneRepository: ZoneRepository) {}

  @contextualizeError()
  async execute(dto: SearchZonesDto): Promise<ZonesEither> {
    const { eventId, organizationId } = dto;

    const criteria = ZoneCriteriaMother.eventIdToMatch(eventId, organizationId);

    const zonesCollectionOrError = await this.zoneRepository.search(criteria);

    if (zonesCollectionOrError.isLeft()) {
      return left(zonesCollectionOrError.value);
    }

    const zoneCollection = zonesCollectionOrError.value;

    const zones = zoneCollection.filterByMicrosite();

    return right(zones);
  }
}

