import type { RateSchemaType } from '@/reservations/rates/infrastructure/database/schemas/RateSchemaType';

export type ZonesSchemaType = {
  readonly zones: ZoneType[];
};

export type ZoneSchemaType = {
  readonly zone: ZoneType;
};

export type ZoneType = {
  readonly id: string;
  readonly name: string;
  readonly alias: string | null;
  readonly isFull: boolean;
  readonly isHiddenInWeb: boolean;
  readonly webImageUrl: string | null;
  readonly rates: RateSchemaType[];
  readonly spaces: SpaceType[];
};

export type SpaceType = {
  readonly id: string;
  readonly minimumCapacity: number;
  readonly maximumCapacity: number;
  readonly isHiddenInWeb: boolean;
  readonly isAvailable: boolean;
  readonly rates: RateSchemaType[];
};

export type ZoneWebImageSchemaType = {
  readonly zoneWebImageUrl: string;
};
