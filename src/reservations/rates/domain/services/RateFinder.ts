import {
  left, right, type UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { RateRepository } from '@/reservations/rates/domain/contracts/RateRepository';
import type { RateEither } from '@/reservations/rates/domain/entities/Rate';

export class RateFinder {
  constructor(private readonly rateRepository: RateRepository) {}

  async execute(rateId: UniqueEntityID, criteria: Criteria): Promise<RateEither> {
    const rateOrError = await this.rateRepository.find(rateId, criteria);

    if (rateOrError.isLeft()) {
      return left(rateOrError.value);
    }

    return right(rateOrError.value);
  }
}
