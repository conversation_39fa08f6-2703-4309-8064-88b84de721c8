import {
  FvNumber,
  InvalidArgumentError,
  Money,
  ValueObject,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { RateDepositType, type ERateDepositType } from './RateDepositType';

import type {
  ECurrency, Either, MoneyEither, Primitives,
} from '@discocil/fv-domain-library/domain';

export type RateDepositEither = Either<InvalidArgumentError, RateDeposit>;

export type RateDepositProps = {
  readonly type: RateDepositType;
  readonly amount: FvNumber | Money;
  readonly currency: ECurrency;
};

export type RateDepositPrimitives = Omit<Primitives<RateDeposit>, 'value'>;

export type RateDepositKeys = keyof RateDepositPrimitives;

export class RateDeposit extends ValueObject<RateDepositProps> {
  private constructor(
    private readonly _type: RateDepositType,
    private readonly _amount: FvNumber | Money,
    private readonly _currency: ECurrency,
  ) {
    super({
      type: _type,
      amount: _amount,
      currency: _currency,
    });
  }

  get amount(): number {
    if (this._amount instanceof Money) {
      return this._amount.toDecimal();
    }

    return this._amount.toPrimitive();
  }

  get type(): ERateDepositType {
    return this._type.toPrimitive();
  }

  get currency(): ECurrency {
    return this._currency;
  }

  static build(depositRaw: RateDepositPrimitives): RateDepositEither {
    const {
      type,
      amount,
      currency,
    } = depositRaw;

    const typeOrError = RateDepositType.build(type);

    if (typeOrError.isLeft()) {
      return left(typeOrError.value);
    }

    const typeValue = typeOrError.value;

    if (typeValue.isFixed()) {
      const amountOrError = Money.build({ amount, currency });

      if (amountOrError.isLeft()) {
        return left(amountOrError.value);
      }

      const moneyAmount = amountOrError.value;

      const rateDeposit = new RateDeposit(typeValue, moneyAmount, currency);

      return right(rateDeposit);
    }

    const percentageNumber = FvNumber.build(amount);

    if (!percentageNumber.isPercentageValid()) {
      return left(InvalidArgumentError.invalidValue({
        context: this.constructor.name,
        target: this.constructor.name,
        data: { amount },
      }));
    }

    const rateDeposit = new RateDeposit(typeValue, percentageNumber, currency);

    return right(rateDeposit);
  }

  typeIsFixed(): boolean {
    return this._type.isFixed();
  }

  typeIsPercentage(): boolean {
    return this._type.isPercentage();
  }

  calculateDeposit(baseAmount: Money): MoneyEither {
    if (baseAmount.isZero()) {
      return right(Money.buildZero(baseAmount.currency));
    }

    if (this.typeIsPercentage()) {
      return right(baseAmount.percentage(this.amount));
    }

    return Money.build({
      amount: this.amount,
      currency: baseAmount.currency,
    });
  }

  toPrimitives(): RateDepositPrimitives {
    return {
      type: this.type,
      amount: this.amount,
      currency: this.currency,
    };
  }
}
