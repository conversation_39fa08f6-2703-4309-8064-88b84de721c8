import {
  EFeeType,
  FvEnum,
  InvalidArgumentError,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import type { Either } from '@discocil/fv-domain-library/domain';

export type AdministrationFeeTypeEither = Either<InvalidArgumentError, AdministrationFeeType>;

export class AdministrationFeeType extends FvEnum<EFeeType> {
  static readonly values = Object.values(EFeeType);

  constructor(value: EFeeType) {
    super(value, AdministrationFeeType.values);
  }

  static build(value: EFeeType): AdministrationFeeTypeEither {
    return this.values.includes(value)
      ? right(new AdministrationFeeType(value))
      : left(InvalidArgumentError.buildInvalidAdministrationFeeType({
        context: this.constructor.name,
        target: value,
      }));
  }

  static buildPercentage(): AdministrationFeeType {
    return new AdministrationFeeType(EFeeType.PERCENTAGE);
  }

  isFixed(): boolean {
    return this.equalTo(EFeeType.FIXED);
  }

  isPercentage(): boolean {
    return this.equalTo(EFeeType.PERCENTAGE);
  }
}
