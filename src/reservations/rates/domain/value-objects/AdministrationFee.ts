import {
  FvNumber,
  InvalidArgumentError,
  Money,
  ValueObject,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { AdministrationFeeType } from './AdministrationFeeType';

import type {
  ECurrency,
  EFeeType,
  Either,
  MoneyEither,
  MoneyError,
  Primitives,
} from '@discocil/fv-domain-library/domain';

export type AdministrationFeeEither = Either<InvalidArgumentError | MoneyError, AdministrationFee>;

export type AdministrationFeeProps = {
  readonly type: AdministrationFeeType;
  readonly amount: FvNumber | Money;
};

export type AdministrationFeePrimitives = Omit<Primitives<AdministrationFee>, 'value'>;

export type AdAdministrationFeeKeys = keyof AdministrationFeePrimitives;

export class AdministrationFee extends ValueObject<AdministrationFeeProps> {
  private constructor(
    private readonly _type: AdministrationFeeType,
    private readonly _amount: FvNumber | Money,
  ) {
    super({
      type: _type,
      amount: _amount,
    });
  }

  get amount(): number {
    if (this._amount instanceof Money) {
      return this._amount.toDecimal();
    }

    return this._amount.value;
  }

  get type(): EFeeType {
    return this._type.toPrimitive();
  }

  static build(feeRaw: AdministrationFeePrimitives & { currency: ECurrency; }): AdministrationFeeEither {
    const {
      type,
      amount,
      currency,
    } = feeRaw;

    const typeOrError = AdministrationFeeType.build(type);

    if (typeOrError.isLeft()) {
      return left(typeOrError.value);
    }

    const typeValue = typeOrError.value;

    if (typeValue.isFixed()) {
      const amountOrError = Money.build({ amount, currency });

      if (amountOrError.isLeft()) {
        return left(amountOrError.value);
      }

      const moneyAmount = amountOrError.value;

      const administrationFee = new AdministrationFee(typeValue, moneyAmount);

      return right(administrationFee);
    }

    const percentageNumber = FvNumber.build(amount);

    if (!percentageNumber.isPercentageValid()) {
      return left(InvalidArgumentError.buildInvalidAdministrationFeeAmount({
        context: this.constructor.name,
        target: amount,
        data: { type: typeValue.toPrimitive() },
      }));
    }

    const administrationFee = new AdministrationFee(typeValue, percentageNumber);

    return right(administrationFee);
  }

  static buildDefault(): AdministrationFee {
    const type = AdministrationFeeType.buildPercentage();

    return new AdministrationFee(type, FvNumber.build(0));
  }

  typeIsFixed(): boolean {
    return this._type.isFixed();
  }

  typeIsPercentage(): boolean {
    return this._type.isPercentage();
  }

  calculateFee(baseAmount: Money, currency: ECurrency): MoneyEither {
    if (baseAmount.isZero()) {
      return right(Money.buildZero(currency));
    }

    if (this.typeIsPercentage()) {
      return right(baseAmount.percentage(this.amount));
    }

    return Money.build({
      amount: this.amount,
      currency,
    });
  }

  toPrimitives(): AdministrationFeePrimitives {
    return {
      type: this.type,
      amount: this.amount,
    };
  }
}
