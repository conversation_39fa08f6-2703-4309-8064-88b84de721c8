import {
  FvEnum,
  InvalidArgumentError,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import type { Either } from '@discocil/fv-domain-library/domain';

export type RateDepositTypeEither = Either<InvalidArgumentError, RateDepositType>;

export enum ERateDepositType {
  FIXED = 'fixed',
  PERCENTAGE = 'percentage'
}

export class RateDepositType extends FvEnum<ERateDepositType> {
  static readonly values = Object.values(ERateDepositType);

  constructor(value: ERateDepositType) {
    super(value, RateDepositType.values);
  }

  static build(value: ERateDepositType): RateDepositTypeEither {
    return this.values.includes(value)
      ? right(new RateDepositType(value))
      : left(InvalidArgumentError.invalidValue({
        context: this.constructor.name,
        target: this.constructor.name,
        data: { value },
      }));
  }

  static buildPercentage(): RateDepositType {
    return new RateDepositType(ERateDepositType.PERCENTAGE);
  }

  isFixed(): boolean {
    return this.equalTo(ERateDepositType.FIXED);
  }

  isPercentage(): boolean {
    return this.equalTo(ERateDepositType.PERCENTAGE);
  }
}
