export type RateSchemaType = {
  readonly id: string;
  readonly name: string;
  readonly maximumNumberOfPeople: number;
  readonly price: {
    readonly amount: number;
    readonly currency: string;
  };
  readonly extraPeopleAllowed: number | null;
  readonly pricePerExtraPerson: {
    readonly amount: number;
    readonly currency: string;
  } | null;
  readonly isFullPaymentAllowed: boolean;
  readonly administrationFee: {
    readonly amount: number;
    readonly type: string;
  };
  readonly deposit: {
    readonly amount: number;
    readonly type: string;
  };
  readonly includes: string | null;
  readonly conditions: string | null;
  readonly color: string;
};
