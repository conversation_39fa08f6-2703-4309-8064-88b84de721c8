import {
  Collection,
  EFeeType,
  left, Maybe, right,
} from '@discocil/fv-domain-library';

import { Rate } from '@/reservations/rates/domain/entities/Rate';
import { ERateDepositType } from '@/reservations/rates/domain/value-objects/RateDepositType';

import type { RateEither, RatesEither } from '@/reservations/rates/domain/entities/Rate';
import type { RateSchemaType } from '@/reservations/rates/infrastructure/database/schemas/RateSchemaType';
import type { ECurrency } from '@discocil/fv-domain-library';

export class RateHttpMapper {
  static toDomain(rateRaw: RateSchemaType): RateEither {
    const rateOrError = Rate.build({
      id: rateRaw.id,
      name: rateRaw.name,
      amount: {
        amount: rateRaw.price.amount,
        currency: rateRaw.price.currency as ECurrency,
      },
      maximumNumberOfPeople: rateRaw.maximumNumberOfPeople,
      extraPeopleAllowed: rateRaw.extraPeopleAllowed ?? 0,
      pricePerExtraPerson: {
        amount: rateRaw.pricePerExtraPerson?.amount ?? 0,
        currency: rateRaw.price.currency as ECurrency,
      },
      isFullPaymentAllowed: !!rateRaw.isFullPaymentAllowed,
      administrationFee: {
        amount: rateRaw.administrationFee?.amount ?? 0,
        type: rateRaw.administrationFee?.type as EFeeType ?? EFeeType.FIXED,
      },
      deposit: {
        amount: rateRaw.deposit?.amount ?? 0,
        type: rateRaw.deposit?.type as ERateDepositType ?? ERateDepositType.FIXED,
        currency: rateRaw.price.currency as ECurrency,
      },
      includes: Maybe.fromValue(rateRaw.includes),
      conditions: Maybe.fromValue(rateRaw.conditions),
      color: rateRaw.color,
    });

    if (rateOrError.isLeft()) {
      return left(rateOrError.value);
    }

    return right(rateOrError.value);
  }

  static toDomainFromArray(ratesRaw: RateSchemaType[]): RatesEither {
    const rates = new Array<Rate>();

    for (const rate of ratesRaw) {
      const rateOrError = this.toDomain(rate);

      if (rateOrError.isLeft()) {
        return left(rateOrError.value);
      }

      rates.push(rateOrError.value);
    }

    const ratesCollection = Collection.build(rates, 'id');

    return right(ratesCollection);
  }
}
