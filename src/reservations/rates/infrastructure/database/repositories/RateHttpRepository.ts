import {
  left, right, NotFoundError, UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import { HttpCriteriaConverter } from '@discocil/fv-criteria-converter-library/infrastructure';

import { BaseHttpRepository } from '@/cross-cutting/infrastructure/database/repositories/HttpRepository';
import { HTTP_CODES } from '@app/http/HttpCodes';
import { RateHttpMapper } from '@/reservations/rates/infrastructure/database/mappers/RateHttpMapper';
import { Rate, type RateEither } from '@/reservations/rates/domain/entities/Rate';

import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import type { RateSchemaType } from '@/reservations/rates/infrastructure/database/schemas/RateSchemaType';
import type { HttpRepository } from '@/cross-cutting/infrastructure/contracts/HttpRepository';
import type { RateRepository } from '@/reservations/rates/domain/contracts/RateRepository';

export class RateHttpRepository extends BaseHttpRepository implements RateRepository {
  constructor(
    protected readonly apiKey: string,
    baseUrl: string,
    httpRepository: HttpRepository,
  ) {
    super(baseUrl, httpRepository);
  }

  async find(rateId: UniqueEntityID, criteria: Criteria): Promise<RateEither> {
    const path = `rates/${rateId.value}`;
    const convertedParamsOrError = HttpCriteriaConverter.convert(criteria);

    if (convertedParamsOrError.isLeft()) {
      return left(convertedParamsOrError.value);
    }

    const convertedParams = convertedParamsOrError.value;

    const url = this.makeUrl({ queryParams: convertedParams, url: path });
    const headers = this.makeHeaders();

    const repositoryResponse = await this.httpRepository.get<RateSchemaType>({ url, headers });

    if (repositoryResponse.isLeft()) {
      if (repositoryResponse.value.status === HTTP_CODES.NOT_FOUND_404) {
        return left(NotFoundError.build({ context: this.constructor.name, target: Rate.name }));
      }

      return left(UnexpectedError.build({
        context: this.constructor.name,
        target: Rate.name,
        data: { criteria },
      }));
    }

    const rateRaw = repositoryResponse.value.data;

    const rateOrError = RateHttpMapper.toDomain(rateRaw);

    if (rateOrError.isLeft()) {
      return left(rateOrError.value);
    }

    return right(rateOrError.value);
  }

  protected makeHeaders(): Headers {
    const headers = new Headers();

    headers.set('Accept', 'application/json');
    headers.set('Content-Type', 'application/json');
    headers.set('x-api-key', this.apiKey);

    return headers;
  }
}
