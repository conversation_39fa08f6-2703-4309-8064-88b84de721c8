import { left, right } from '@discocil/fv-domain-library/domain';

import { RateCriteriaMother } from '@/reservations/rates/domain/criteria/RateCriteriaMother';

import type { RateEither } from '@/reservations/rates/domain/entities/Rate';
import type { RateFinder } from '@/reservations/rates/domain/services/RateFinder';
import type { FindRateDto } from '@/reservations/rates/application/contracts/FindRateDto';
import type { UseCase } from '@discocil/fv-domain-library/application';

export class FindRateUseCase implements UseCase<FindRateDto, Promise<RateEither>> {
  constructor(
    private readonly rateFinder: RateFinder,
  ) {}

  async execute(dto: FindRateDto): Promise<RateEither> {
    const {
      rateId, zoneId, eventId, organizationId, numberOfPeople,
    } = dto;

    const criteria = RateCriteriaMother.zoneIdToMatch(zoneId, eventId, organizationId);

    const rateEither = await this.rateFinder.execute(rateId, criteria);

    if (rateEither.isLeft()) {
      return left(rateEither.value);
    }

    const rate = rateEither.value;

    rate.calculatePrice(numberOfPeople);

    return right(rate);
  }
}

