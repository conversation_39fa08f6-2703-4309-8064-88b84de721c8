import { left, NotFoundError } from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { MicrositeJsonMapper } from '@/microsite/domain/mappers/MicrositeJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { MicrositeRepository } from '@/microsite/domain/contracts/MicrositeRepository';
import type { MicrositeEither } from '@/microsite/domain/entities/Microsite';
import type { MicrositeJsonPrimitives } from '@/microsite/domain/mappers/MicrositeJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export class MicrositeCacheRepository extends CacheRepository implements MicrositeRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: MicrositeRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<MicrositeEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: MicrositeJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = MicrositeJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        MicrositeJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }
}
