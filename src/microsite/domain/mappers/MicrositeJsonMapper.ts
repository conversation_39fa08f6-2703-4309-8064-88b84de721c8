import { Maybe } from '@discocil/fv-domain-library/domain';

import { Microsite } from '../entities/Microsite';

import type { EMicrositeServices, Nullable } from '@discocil/fv-domain-library/domain';
import type { MicrositeEither, MicrositePrimitives } from '../entities/Microsite';

export type MicrositeJsonPrimitives = Omit<MicrositePrimitives,
  'pageTheme'
  | 'thankYouPage'
  | 'cancelPage'
  | 'propertyIdGoogle'
  | 'facebookPixelId'
  | 'facebookAccessToken'
  | 'services'
> & Nullable<{
  readonly pageTheme: string;
  readonly thankYouPage: string;
  readonly cancelPage: string;
  readonly propertyIdGoogle: string;
  readonly facebookPixelId: string;
  readonly facebookAccessToken: string;
}> & {
  readonly services: EMicrositeServices[];
};

export class MicrositeJsonMapper {
  static toEntity(primitives: MicrositeJsonPrimitives): MicrositeEither {
    return Microsite.build({
      id: primitives.id,
      organizationId: primitives.organizationId,
      pageTheme: Maybe.fromValue(primitives.pageTheme),
      thankYouPage: Maybe.fromValue(primitives.thankYouPage),
      cancelPage: Maybe.fromValue(primitives.cancelPage),
      propertyIdGoogle: Maybe.fromValue(primitives.propertyIdGoogle),
      facebookPixelId: Maybe.fromValue(primitives.facebookPixelId),
      facebookAccessToken: Maybe.fromValue(primitives.facebookAccessToken),
      services: new Set(primitives.services),
      createdAt: primitives.createdAt,
      createdBy: primitives.createdBy,
      updatedAt: primitives.updatedAt,
      updatedBy: primitives.updatedBy,
      removedAt: primitives.removedAt,
      removedBy: primitives.removedBy,
    });
  }

  static toJson(microsite: Microsite): MicrositeJsonPrimitives {
    return {
      id: microsite.id,
      organizationId: microsite.organizationId,
      pageTheme: microsite.pageTheme.fold(() => null, item => item),
      thankYouPage: microsite.thankYouPage.fold(() => null, item => item),
      cancelPage: microsite.cancelPage.fold(() => null, item => item),
      propertyIdGoogle: microsite.propertyIdGoogle.fold(() => null, item => item),
      facebookPixelId: microsite.facebookPixelId.fold(() => null, item => item),
      facebookAccessToken: microsite.facebookAccessToken.fold(() => null, item => item),
      services: Array.from(microsite.services),
      createdAt: microsite.createdAt,
      createdBy: microsite.createdBy,
      updatedAt: microsite.updatedAt,
      updatedBy: microsite.updatedBy,
      removedAt: microsite.removedAt,
      removedBy: microsite.removedBy,
    };
  }
}
