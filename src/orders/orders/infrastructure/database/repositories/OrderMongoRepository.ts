import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { OrderEntity } from '@/orders/orders/domain/entities/OrderEntity';

import { OrderMapper } from '../mappers/OrderMapper';
import { OrderSchemaMapper } from '../mappers/OrderSchemaMapper';
import { OrderSchema } from '../schemas/OrderSchema';

import type { OrderRepository } from '@/orders/orders/domain/contracts/OrderRepository';
import type {
  OrderEither,
  OrderEntities,
  OrdersEither,
} from '@/orders/orders/domain/entities/OrderEntity';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { Schema } from 'mongoose';
import type { OrderSchemaType } from '../schemas/OrderSchemaType';

export class OrderMongoRepository extends MongoRepository implements OrderRepository {
  protected getSchema(): Schema {
    return OrderSchema;
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'orders';
  }

  async find(criteria: Criteria): Promise<OrderEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<OrderSchemaType>();

    return queryResponse
      ? OrderMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: OrderEntity.name }));
  }

  async save(order: OrderEntity): Promise<void> {
    const toSave = OrderSchemaMapper.execute(order);

    const filter = { _id: order.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(orders: OrderEntities): Promise<void> {
    const connection = await this.getConnection();
    const models: Array<Partial<OrderSchemaType>> = [];

    orders.forEach((order: OrderEntity) => models.push(OrderSchemaMapper.execute(order)));

    await connection.insertMany(models);
  }

  async search(criteria: Criteria): Promise<OrdersEither> {
    const response = new Map<IdPrimitive, OrderEntity>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<OrderSchemaType[]>();

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const orderResult = OrderMapper.execute(model);

      if (orderResult.isLeft()) {
        return left(orderResult.value);
      }

      const order = orderResult.value;

      response.set(order.id, order);
    }

    return right(response);
  }
}
