import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { OrderPayment } from '@/orders/orderPayments/domain/entities/OrderPaymentEntity';

import { OrderPaymentMapper } from '../mappers/OrderPaymentMapper';
import { OrderPaymentSchemaMapper } from '../mappers/OrderPaymentSchemaMapper';
import { OrderPaymentSchema } from '../schemas/OrderPaymentSchema';

import type { OrderPaymentRepository } from '@/orders/orderPayments/domain/contracts/OrderPaymentRepository';
import type {
  OrderPaymentEither,
  OrderPayments,
  OrderPaymentsEither,
} from '@/orders/orderPayments/domain/entities/OrderPaymentEntity';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { Schema } from 'mongoose';
import type { OrderPaymentSchemaType } from '../schemas/OrderPaymentSchemaType';

export class OrderPaymentMongoRepository extends MongoRepository implements OrderPaymentRepository {
  protected getSchema(): Schema {
    return OrderPaymentSchema;
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'order_payments';
  }

  async find(criteria: Criteria): Promise<OrderPaymentEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<OrderPaymentSchemaType>();

    return queryResponse
      ? OrderPaymentMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: OrderPayment.name }));
  }

  async save(order: OrderPayment): Promise<void> {
    const toSave = OrderPaymentSchemaMapper.execute(order);

    const filter = { _id: order.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(orders: OrderPayments): Promise<void> {
    const connection = await this.getConnection();
    const models: Array<Partial<OrderPaymentSchemaType>> = [];

    orders.forEach((order: OrderPayment) => models.push(OrderPaymentSchemaMapper.execute(order)));

    await connection.insertMany(models);
  }

  async search(criteria: Criteria): Promise<OrderPaymentsEither> {
    const response = new Map<IdPrimitive, OrderPayment>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<OrderPaymentSchemaType[]>();

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const orderResult = OrderPaymentMapper.execute(model);

      if (orderResult.isLeft()) {
        return left(orderResult.value);
      }

      const order = orderResult.value;

      response.set(order.id, order);
    }

    return right(response);
  }
}
