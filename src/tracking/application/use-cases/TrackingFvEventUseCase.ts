import {
  contextualizeError,
  left,
  Maybe,
  NotFoundError,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import { EventEntity } from '@/events/events/domain/entities/EventEntity';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';

import { TrackingRepository } from '../../domain/contracts/TrackingFvRepository';
import { EventTracking, EventTrackingCreatePrimitives } from '../../domain/entities/EventTracking';

import type { TrackingEventDto, TrackingEventEither } from '@/tracking/domain/contracts/TrackingEventUseCaseContracts';
import type { UseCase } from '@discocil/fv-domain-library/application';

export class TrackingFvEventUseCase implements UseCase<TrackingEventDto, Promise<TrackingEventEither>> {
  constructor(
    private readonly trackingRepository: TrackingRepository,
    private readonly eventRepository: EventRepository,
  ) {}

  @contextualizeError()
  async execute(dto: TrackingEventDto): Promise<TrackingEventEither> {
    const eventId = dto.eventId;

    if (eventId.isEmpty()) {
      return left(NotFoundError.build({
        context: this.constructor.name,
        target: EventEntity.name,
      }).notAutoContextualizable());
    }

    const eventCriteria = EventCriteriaMother.idToMatch(
      UniqueEntityID.build(eventId.get()),
    );

    const eventOrError = await this.eventRepository.find(eventCriteria);

    if (eventOrError.isLeft()) {
      return left(eventOrError.value);
    }

    const {
      id,
      name,
      organizationId,
      channel,
      ...eventTrackingData
    } = dto;

    const eventTrackingPrimitives: EventTrackingCreatePrimitives = {
      name,
      organizationId,
      eventId: Maybe.some(id),
      channel,
      data: eventTrackingData,
    };

    const eventTrackingOrError = EventTracking.create(eventTrackingPrimitives);

    if (eventTrackingOrError.isLeft()) {
      return left(eventTrackingOrError.value);
    }

    const trackingEvent = eventTrackingOrError.value;

    this.trackingRepository.save(trackingEvent);

    return right(true);
  }
}
