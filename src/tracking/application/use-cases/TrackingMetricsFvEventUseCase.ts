import { OrderTypes, type PaginationOption } from '@discocil/fv-criteria-converter-library/domain';
import {
  contextualizeError,
  left,
  Maybe,
  right,
  UnexpectedError,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { EventTrackingCalculateMetricsService } from '@/tracking/domain/services/EventTrackingCalculateMetricsService';

import { SearchMetricsDto } from '../../domain/contracts/SearchMetricsDto';
import { TrackingRepository } from '../../domain/contracts/TrackingFvRepository';
import {
  EventTrackingsEither,
  EventTrackingsPaginated,
  TrackingMetricsFvEventEither,
} from '../../domain/entities/EventTracking';
import { TrackingCriteriaMother } from '../../domain/filters/TrackingCriteriaMother';

import type { FetchAllPages } from '@/cross-cutting/domain/contracts/FetchAllPages';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { TrackingMetricsEventDto } from '../../domain/contracts/TrackingMetricsContracts';

export class TrackingMetricsFvEventUseCase implements UseCase<TrackingMetricsEventDto, Promise<TrackingMetricsFvEventEither>> {
  constructor(
    private readonly trackingRepository: TrackingRepository,
    private readonly eventRepository: EventRepository,
    private readonly fetchAllPages: FetchAllPages,
    private readonly eventTrackingCalculateMetricsService: EventTrackingCalculateMetricsService,
  ) {}

  @contextualizeError()
  async execute(dto: TrackingMetricsEventDto): Promise<TrackingMetricsFvEventEither> {
    const eventId = dto.eventId;

    const eventCriteria = EventCriteriaMother.idToMatch(
      UniqueEntityID.build(eventId),
    );

    const eventOrError = await this.eventRepository.find(eventCriteria);

    if (eventOrError.isLeft()) {
      return left(eventOrError.value);
    }

    const event = eventOrError.value;
    const organizationId = event.organizationId;

    const searchEventTicketTypesPagination: PaginationOption = {
      page: 1,
      perPage: 800,
      order: {
        type: OrderTypes.ASC,
        by: Maybe.some(TrackingCriteriaMother.defaultOrderKey),
      },
    };

    const searchEventTicketTypesDto: SearchMetricsDto = {
      eventId,
      organizationId,
      pagination: searchEventTicketTypesPagination,
    };

    const allTrackingEventsResponseOrError = await this.fetchAllPages.execute<
      EventTrackingsPaginated,
      EventTrackingsEither,
      UnexpectedError
    >(
      searchEventTicketTypesPagination,
      (pagination) => {
        const criteria = TrackingCriteriaMother.metricsToMatch({
          ...searchEventTicketTypesDto,
          pagination,
        });

        return this.trackingRepository.search(criteria);
      },
    );

    if (allTrackingEventsResponseOrError.isLeft()) {
      return left(allTrackingEventsResponseOrError.value);
    }

    const allTrackingEventsResponses = allTrackingEventsResponseOrError.value;

    const {
      allEventTrackings,
      eventTrackingsForTheLast15Minutes,
    } = this.eventTrackingCalculateMetricsService.execute(allTrackingEventsResponses);

    return right({
      allEventTrackings,
      eventTrackingsForTheLast15Minutes,
    });
  }
}
