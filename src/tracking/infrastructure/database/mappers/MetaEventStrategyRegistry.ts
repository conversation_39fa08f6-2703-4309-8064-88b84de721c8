

import { EEventTypes } from '@/tracking/domain/value-objects/EventType';

import { AddToCartMetaEvent } from './AddToCartMetaEvent';
import { InitiateCheckoutMetaEvent } from './InitiateCheckoutMetaEvent';
import { PageViewMetaEvent } from './PageViewMetaEvent';
import { PurchaseMetaEvent } from './PurchaseMetaEvent';
import { ViewContentMetaEvent } from './ViewContentMetaEvent';

import type { MetaEventStrategy, MetaEventStrategyType } from './MetaEvent';

export const metaEventStrategies: MetaEventStrategyType = new Map<EEventTypes, () => MetaEventStrategy>([
  [EEventTypes.PageView, (): MetaEventStrategy => new PageViewMetaEvent()],
  [EEventTypes.ViewContent, (): MetaEventStrategy => new ViewContentMetaEvent()],
  [EEventTypes.AddToCart, (): MetaEventStrategy => new AddToCartMetaEvent()],
  [EEventTypes.InitiateCheckout, (): MetaEventStrategy => new InitiateCheckoutMetaEvent()],
  [EEventTypes.Purchase, (): MetaEventStrategy => new PurchaseMetaEvent()],
]);
