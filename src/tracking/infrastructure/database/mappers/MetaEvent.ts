import {
  Collection,
  CryptoService,
  FvDate,
  left,
  right,
} from '@discocil/fv-domain-library/domain';
import {
  Content,
  CustomData,
  EventRequest,
  ServerEvent,
  UserData,
} from 'facebook-nodejs-business-sdk';

import { UnsupportedEventError } from '@/tracking/domain/errors/UnsupportedEventError';

import type { TrackingItemPrimitive } from '@/tracking/domain/contracts/EntityContracts';
import type { Credentials } from '@/tracking/domain/contracts/TrackingRepository';
import type { EEventTypes } from '@/tracking/domain/value-objects/EventType';
import type { Either } from '@discocil/fv-domain-library/domain';
import type { TrackingMetaUnfoldedData } from '../services/MetaEventParser';

export enum TrackingConstants {
  ActionSource = 'website',
  ContentType = 'product'
}

type MetaContent = {
  readonly items?: TrackingItemPrimitive[];
  readonly id?: string;
  readonly title?: string;
  readonly itemPrice?: number;
  readonly category?: string;
};

export type EitherResponse = Either<UnsupportedEventError, MetaEvent>;

type TrackResponse = {
  readonly id: string;
  readonly fbtraceId: string;
};

export type TrackBuildRequest = {
  readonly event: TrackingMetaUnfoldedData;
  readonly credentials: Credentials;
  readonly strategies: MetaEventStrategyType;
};

export interface MetaEventStrategy {
  execute(metaEvent: MetaEvent): void;
}

export type MetaEventStrategyType = Map<EEventTypes, () => MetaEventStrategy>;

export class MetaEvent {
  private readonly userData = new UserData();
  private readonly customData = new CustomData();
  private readonly contents = Collection.new<Content>();
  private readonly serverEvent = new ServerEvent();
  private readonly eventRequest: EventRequest;

  private constructor(
    readonly event: TrackingMetaUnfoldedData,
    private readonly credentials: Credentials,
    eventStrategy: (metaEvent: MetaEvent) => void,
  ) {
    this.eventRequest = new EventRequest(this.credentials.accessToken, this.credentials.pixelId);

    this.serverEvent.setEventId(this.event.id);
    this.serverEvent.setUserData(this.userData);

    if (this.event.urlPage) {
      this.serverEvent.setEventSourceUrl(this.event.urlPage);
    }

    eventStrategy(this);
  }

  setUserData(value: UserData): this {
    this.serverEvent.setUserData(value);

    return this;
  }

  static async build(request: TrackBuildRequest): Promise<EitherResponse> {
    const {
      event,
      credentials,
      strategies,
    } = request;

    const strategy = strategies.get(event.name);

    if (!strategy) {
      return left(UnsupportedEventError.build(
        {
          context: this.constructor.name,
          data: request,
        },
      ));
    }

    const metaEvent = new MetaEvent(event, credentials, strategy().execute.bind(strategy));

    metaEvent.setEventName(event.name);
    metaEvent.setEventTime(FvDate.create(event.date).toSeconds());
    metaEvent.setActionSource(TrackingConstants.ActionSource);

    await metaEvent.buildUserData(event);

    return right(metaEvent);
  }

  async buildUserData(event: TrackingMetaUnfoldedData): Promise<void> {
    this.userData.setClientIpAddress(event.remoteAddress);
    this.userData.setClientUserAgent(event.userAgent);

    if (event.externalId) {
      this.userData.setExternalId(event.externalId);
    }

    if (event.user) {
      const userData = event.user;
      const userName = userData.name;
      const userEmail = userData.email;
      const userPhone = userData.phone;

      if (userEmail) {
        const emailHashed = await CryptoService.sha256(userEmail.trim());

        this.userData.setEmail(emailHashed);
      }

      if (userPhone) {
        const phoneHashed = await CryptoService.sha256(userPhone.trim());

        this.userData.setPhone(phoneHashed);
      }

      if (userName) {
        const { firstName, lastName } = await this.getFirstAndLastName(userName);

        this.userData.setFirstName(firstName);

        if (lastName) {
          this.userData.setLastName(lastName);
        }
      }
    }

    if (event.fb) {
      const fbData = event.fb;
      const fbp = fbData.fbp;
      const fbc = fbData.fbc;

      if (fbp) {
        this.userData.setFbp(fbp);
      }

      if (fbc) {
        this.userData.setFbc(fbc);
      }
    }
  }

  private async getFirstAndLastName(fullname: string): Promise<{ firstName: string; lastName: string | null; }> {
    const nameTrimmed = fullname.trim();
    const indexBlankSpace = nameTrimmed.indexOf(' ');

    if (indexBlankSpace === -1) {
      const firstName = await CryptoService.sha256(nameTrimmed);

      return { firstName, lastName: null };
    }


    const firstName = await CryptoService.sha256(nameTrimmed.slice(0, indexBlankSpace));
    const lastName = await CryptoService.sha256(nameTrimmed.slice(indexBlankSpace + 1));

    return { firstName, lastName };
  }

  async track(): Promise<TrackResponse> {
    if (this.contents.isNotEmpty()) {
      this.customData.setContents(this.contents.toArray());
    }

    this.serverEvent.setCustomData(this.customData);
    this.eventRequest.setEvents([this.serverEvent]);

    const response = await this.eventRequest.execute();

    return {
      id: response.id,
      fbtraceId: response.fbtrace_id,
    };
  }

  addContent(content: MetaContent): this {
    for (const contentItem of (content.items ?? [])) {
      const itemContentData = new Content();

      if (content.id) {
        itemContentData.setId(content.id);
      }

      itemContentData.setQuantity(contentItem.quantity);
      itemContentData.setItemPrice(contentItem.price);

      if (content.title) {
        itemContentData.setTitle(content.title);
      }

      itemContentData.setCategory(contentItem.category);

      this.contents.add(itemContentData);
    }

    return this;
  }

  setCustomProperties(customProperties: Record<string, unknown>): this {
    this.customData.setCustomProperties(customProperties);

    return this;
  }

  setContentIds(value: string[]): this {
    this.customData.setContentIds(value);

    return this;
  }

  setValue(value: number): this {
    this.customData.setValue(value);

    return this;
  }

  setCurrency(value: string): this {
    this.customData.setCurrency(value);

    return this;
  }

  setNumItems(value: number): this {
    this.customData.setNumItems(value);

    return this;
  }

  setContentType(): this {
    this.customData.setContentType(TrackingConstants.ContentType);

    return this;
  }

  setEventName(value: string): this {
    this.serverEvent.setEventName(value);

    return this;
  }

  setEventTime(value: number): this {
    this.serverEvent.setEventTime(value);

    return this;
  }

  setEventSourceUrl(value: string): this {
    this.serverEvent.setEventSourceUrl(value);

    return this;
  }

  setActionSource(value: string): this {
    this.serverEvent.setActionSource(value);

    return this;
  }

  setEventId(value: string): this {
    this.serverEvent.setEventId(value);

    return this;
  }
}
