import {
  FvDate, left, Maybe,
  right,
} from '@discocil/fv-domain-library';

import { EventTracking } from '@/tracking/domain/entities/EventTracking';
import { EEventChannel } from '@/tracking/domain/value-objects/EventType';

import type { EventTrackingEither, EventTrackingPrimitives } from '@/tracking/domain/entities/EventTracking';
import type { UnknownObject } from '@discocil/fv-domain-library';
import type { FvTrackingSchemaType } from '../schemas/FvTrackingSchemaType';

export class FvTrackingMapper {
  static execute(payload: FvTrackingSchemaType): EventTrackingEither {
    const {
      _id, name, organization_id, event_id, created_at, ...restData
    } = payload;

    const eventTrackingPrimitives: EventTrackingPrimitives = {
      id: _id,
      name,
      channel: EEventChannel.fv,
      organizationId: organization_id,
      eventId: Maybe.fromValue(event_id),
      date: FvDate.createFromSeconds(created_at).toPrimitive(),
      data: this.makeData(restData),
    };

    const eventTrackingOrError = EventTracking.build(eventTrackingPrimitives);

    if (eventTrackingOrError.isLeft()) {
      return left(eventTrackingOrError.value);
    }

    return right(eventTrackingOrError.value);
  }

  private static makeData(eventTrackingData: UnknownObject): UnknownObject {
    const buildEventTrackingData: UnknownObject = {};

    if ('session_id' in eventTrackingData) {
      buildEventTrackingData.sessionId = Maybe.fromValue(eventTrackingData.session_id);
    }

    if ('external_id' in eventTrackingData) {
      buildEventTrackingData.externalId = Maybe.fromValue(eventTrackingData.external_id);
    }

    if ('url_page' in eventTrackingData) {
      buildEventTrackingData.urlPage = eventTrackingData.url_page;
    }

    if ('remote_address' in eventTrackingData) {
      buildEventTrackingData.remoteAddress = eventTrackingData.remote_address;
    }

    if ('user_agent' in eventTrackingData) {
      buildEventTrackingData.userAgent = eventTrackingData.user_agent;
    }

    if ('service_type' in eventTrackingData) {
      buildEventTrackingData.serviceType = Maybe.fromValue(eventTrackingData.service_type);
    }

    if ('container_type' in eventTrackingData) {
      buildEventTrackingData.containerType = Maybe.fromValue(eventTrackingData.container_type);
    }

    return buildEventTrackingData;
  }
}
