import { FvDate } from '@discocil/fv-domain-library/domain';

import type { EMicrositeContainerType } from '@/tracking/domain/contracts/EntityContracts';
import type { EventTracking } from '@/tracking/domain/entities/EventTracking';
import type {
  EMicrositeServices, Maybe,
  MoneyProps,
} from '@discocil/fv-domain-library/domain';
import type { FvTrackingSchemaType } from '../schemas/FvTrackingSchemaType';

export class FvTrackingSchemaMapper {
  static execute(eventTracking: EventTracking): FvTrackingSchemaType {
    const eventTrackingData = eventTracking.data;

    const price = (eventTrackingData.price as Maybe<MoneyProps>).fold(() => undefined, item => item);

    return {
      _id: eventTracking.id,
      name: eventTracking.name,
      event_id: eventTracking.eventId.fold(() => undefined, item => item),
      organization_id: eventTracking.organizationId,
      url_page: eventTrackingData.urlPage as string,
      price: price?.amount ?? 0,
      currency: price?.currency,
      remote_address: eventTrackingData.remoteAddress as string,
      user_agent: eventTrackingData.userAgent as string,
      session_id: eventTracking.sessionId.fold(() => undefined, item => item),
      service_type: (eventTrackingData.serviceType as Maybe<EMicrositeServices>).fold(() => undefined, item => item),
      container_type: (eventTrackingData.containerType as Maybe<EMicrositeContainerType>).fold(() => undefined, item => item),
      created_at: FvDate.create(eventTracking.date).toSeconds(),
    };
  }
}
