import { eventTrackingAddToCartSchema } from '../schemas/MetaEventAddToCartSchema';

import { type MetaEvent, type MetaEventStrategy } from './MetaEvent';

export class AddToCartMetaEvent implements MetaEventStrategy {
  execute(metaEvent: MetaEvent): void {
    const rawEvent = {
      ...metaEvent.event,
      content: {
        id: metaEvent.event.content?.id,
        ids: metaEvent.event.content?.ids,
        name: metaEvent.event.content?.name,
        date: metaEvent.event.content?.date,
        type: metaEvent.event.content?.type,
      },
    };
    const parsedEvent = eventTrackingAddToCartSchema.parse(rawEvent);

    metaEvent.setEventId(parsedEvent.id);

    let priceAmount;

    if (parsedEvent.price) {
      priceAmount = parsedEvent.price.amount;
    }

    if (parsedEvent.content?.ids) {
      metaEvent.setContentIds(parsedEvent.content.ids);
    }

    if (parsedEvent.totalPrice?.amount) {
      metaEvent.setValue(parsedEvent.totalPrice.amount);
    }

    if (parsedEvent.price?.currency) {
      metaEvent.setCurrency(parsedEvent.price.currency.toUpperCase());
    }

    metaEvent.setNumItems(parsedEvent.numItems ?? 0);

    metaEvent.addContent({
      items: parsedEvent.items,
      id: parsedEvent.content?.id,
      title: parsedEvent.content?.name,
      itemPrice: priceAmount,
      category: parsedEvent.content?.type,
    });
  }
}
