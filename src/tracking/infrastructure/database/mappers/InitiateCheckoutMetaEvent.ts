import { eventTrackingInitiateCheckoutSchema } from '../schemas/MetaEventInitiateCheckoutSchema';

import { type MetaEvent, type MetaEventStrategy } from './MetaEvent';

export class InitiateCheckoutMetaEvent implements MetaEventStrategy {
  execute(metaEvent: MetaEvent): void {
    const rawEvent = {
      ...metaEvent.event,
      content: {
        id: metaEvent.event.content?.id,
        name: metaEvent.event.content?.name,
        type: metaEvent.event.content?.type,
        ids: metaEvent.event.content?.ids,
      },
    };
    const parsedEvent = eventTrackingInitiateCheckoutSchema.parse(rawEvent);

    metaEvent.setEventId(parsedEvent.id);

    let priceAmount;

    if (parsedEvent.price) {
      priceAmount = parsedEvent.price.amount;
    }

    if (parsedEvent.content?.ids) {
      metaEvent.setContentIds(parsedEvent.content.ids);
    }

    if (parsedEvent.totalPrice?.amount) {
      metaEvent.setValue(parsedEvent.totalPrice.amount);
    }

    if (parsedEvent.price?.currency) {
      metaEvent.setCurrency(parsedEvent.price.currency.toUpperCase());
    }

    metaEvent.setNumItems(parsedEvent.numItems ?? 0);

    metaEvent.addContent({
      items: parsedEvent.items,
      id: parsedEvent.content?.id,
      title: parsedEvent.content?.name ?? '',
      itemPrice: priceAmount,
      category: parsedEvent.content?.type,
    });
  }
}
