import { eventTrackingViewContentSchema } from '../schemas/MetaEventViewContentSchema';

import type { MetaEvent, MetaEventStrategy } from './MetaEvent';

export class ViewContentMetaEvent implements MetaEventStrategy {
  execute(metaEvent: MetaEvent): void {
    const rawEvent = {
      ...metaEvent.event,
      content: {
        name: metaEvent.event.content?.name,
        date: metaEvent.event.content?.date,
      },
    };

    const event = eventTrackingViewContentSchema.parse(rawEvent);

    if (event.content?.name) {
      metaEvent.addContent({ title: event.content.name });
    }
  }
}
