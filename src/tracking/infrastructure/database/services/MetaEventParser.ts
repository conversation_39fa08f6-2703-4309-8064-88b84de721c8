import {
  FvDate,
  type Collection,
  type EMicrositeServices,
  type Maybe,
  type MoneyProps,
  type UniqueEntityID,
  type UnknownObject,
} from '@discocil/fv-domain-library';
import z from 'zod';

import { MoneyPropsSchema, type TrackingEventContentDto } from '../schemas/MetaEventBaseSchemas';

import type {
  EMicrositeContainerType,
  TrackingFb, TrackingItem,
  TrackingItemPrimitive,
  TrackingUser,
} from '@/tracking/domain/contracts/EntityContracts';
import type { EventTracking } from '@/tracking/domain/entities/EventTracking';
import type { EEventChannel, EEventTypes } from '@/tracking/domain/value-objects/EventType';

type TrackingDataCommon = {
  readonly id: UniqueEntityID;
  readonly name: EEventTypes;
  readonly channel: EEventChannel.meta | EEventChannel.fv;
  readonly urlPage: URL;
  readonly totalPrice: Maybe<MoneyProps>;
  readonly organizationId: UniqueEntityID;
  readonly userAgent: string;
  readonly remoteAddress: string;
};

export type TrackingDataMeta = TrackingDataCommon & {
  readonly externalId: UniqueEntityID;
  readonly route: Maybe<string>;
  readonly user: Maybe<TrackingUser>;
  readonly fb: Maybe<TrackingFb>;
  readonly content: Maybe<TrackingEventContentDto>;
  readonly items: Maybe<TrackingItem[]>;
  readonly price: Maybe<MoneyProps>;
  readonly numItems: Maybe<number>;
};

export type TrackingDataFv = TrackingDataCommon & {
  readonly eventId: UniqueEntityID;
  readonly sessionId: string;
  readonly serviceType: EMicrositeServices;
  readonly containerType: EMicrositeContainerType;
};

type TrackingContent = {
  readonly id: UniqueEntityID;
  readonly date: number;
  readonly name: string;
  readonly type: string;
  readonly ids: Collection<string>;
};

const eventTrackingUnfoldedData = z.object({
  eventId: z.string(),
  externalId: z.string(),
  remoteAddress: z.string(),
  route: z.optional(z.string()),
  urlPage: z.optional(z.string()),
  userAgent: z.string(),
  fb: z.optional(z.object({
    fbclid: z.optional(z.string()),
    fbc: z.optional(z.string()),
    fbp: z.optional(z.string()),
  })),
  user: z.optional(z.object({
    name: z.optional(z.string()),
    email: z.optional(z.string()),
    phone: z.optional(z.string()),
  })),
  content: z.optional(z.object({
    id: z.optional(z.string()),
    ids: z.optional(z.array(z.string())),
    name: z.optional(z.string()),
    type: z.optional(z.string()),
    date: z.optional(z.number()),
  })),
  price: z.optional(MoneyPropsSchema),
  totalPrice: z.optional(MoneyPropsSchema),
  items: z.optional(z.array(z.object({
    id: z.string(),
    price: z.number(),
    quantity: z.number(),
    category: z.string(),
  }))),
  numItems: z.optional(z.number()),
});

export type EventTrackingUnfoldedData = z.output<typeof eventTrackingUnfoldedData>;

export type TrackingMetaUnfoldedData = Pick<
  EventTracking, 'id' | 'name' | 'organizationId' | 'date' | 'data'
> & EventTrackingUnfoldedData;

export class MetaEventParser {
  static execute(eventTracking: EventTracking): TrackingMetaUnfoldedData {
    const rawTrackingEventData = this.makeData(eventTracking);

    const eventData = eventTrackingUnfoldedData.parse(rawTrackingEventData);

    const {
      externalId,
      remoteAddress,
      userAgent,
      fb,
      user,
      eventId,
      route,
      urlPage,
      content,
      items,
      numItems,
      price,
      totalPrice,
      ...restData
    } = eventData;

    return {
      id: eventTracking.id,
      name: eventTracking.name,
      organizationId: eventTracking.organizationId,
      eventId,
      date: eventTracking.date,
      externalId,
      route,
      urlPage,
      remoteAddress,
      userAgent,
      fb: fb
        ? {
          fbclid: fb.fbclid,
          fbc: fb.fbc,
          fbp: fb.fbp,
        }
        : undefined,
      user: user
        ? {
          name: user.name,
          email: user.email,
          phone: user.phone,
        }
        : undefined,
      content,
      items,
      numItems,
      price,
      totalPrice,
      data: restData,
    } satisfies TrackingMetaUnfoldedData;
  }

  private static makeData(eventTracking: EventTracking): UnknownObject {
    const trackingData = eventTracking.data;

    const user = (trackingData.user as Maybe<TrackingUser>).fold(() => undefined, (item) => {
      return {
        name: item.name.fold(() => undefined, item => item),
        email: item.email.fold(() => undefined, item => item),
        phone: item.phone.fold(() => undefined, item => item),
      };
    });

    const fb = (trackingData.fb as Maybe<TrackingFb>).fold(() => undefined, (item) => {
      return {
        fbclid: item.fbclid.fold(() => undefined, item => item),
        fbc: item.fbc.fold(() => undefined, item => item),
        fbp: item.fbp.fold(() => undefined, item => item),
      };
    });

    const items = (trackingData.items as Maybe<TrackingItemPrimitive[]>).fold(() => undefined, items => items);

    const content = (trackingData.content as Maybe<TrackingContent>).fold(() => undefined, (item) => {
      return {
        ...item,
        date: FvDate.create(eventTracking.date).toSeconds(),
      };
    });

    return {
      content,
      eventId: (trackingData.eventId as Maybe<string>).fold(() => undefined, item => item),
      externalId: (trackingData.externalId as Maybe<string>).fold(() => undefined, item => item),
      fb,
      price: (trackingData.price as Maybe<MoneyProps>).fold(() => undefined, item => item),
      route: (trackingData.route as Maybe<string>).fold(() => undefined, item => item),
      totalPrice: (trackingData.totalPrice as Maybe<MoneyProps>).fold(() => undefined, item => item),
      user,
      remoteAddress: trackingData.remoteAddress,
      urlPage: trackingData.urlPage,
      userAgent: trackingData.userAgent,
      items,
      numItems: (trackingData.numItems as Maybe<number>).fold(() => undefined, item => item),
    };
  }
}
