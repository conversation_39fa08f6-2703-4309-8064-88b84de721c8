import {
  left,
  right,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import { FacebookAdsApi } from 'facebook-nodejs-business-sdk';

import { MetaEvent } from '../mappers/MetaEvent';
import { metaEventStrategies } from '../mappers/MetaEventStrategyRegistry';
import { MetaEventParser } from '../services/MetaEventParser';

import type {
  Credentials,
  TrackingSaveEither,
  TrackingTrackRepository,
} from '@/tracking/domain/contracts/TrackingRepository';
import type { EventTracking } from '@/tracking/domain/entities/EventTracking';

export class TrackingMetaRepository implements TrackingTrackRepository {
  async track(event: EventTracking, credentials: Credentials): Promise<TrackingSaveEither> {
    const connection = FacebookAdsApi.init(credentials.accessToken);

    if (!connection) {
      return left(UnexpectedError.build({
        context: this.constructor.name,
        data: { id: event.id },
      }));
    }

    const metaTrackingEvent = MetaEventParser.execute(event);

    const metaEventOrError = await MetaEvent.build({
      event: metaTrackingEvent,
      credentials,
      strategies: metaEventStrategies,
    });

    if (metaEventOrError.isLeft()) {
      return left(metaEventOrError.value);
    }

    const metaEvent = metaEventOrError.value;

    try {
      await metaEvent.track();
    } catch (_error) {
      const parsedError = _error as Error;

      let metaExceptionData = parsedError.message;

      if ('response' in parsedError) {
        const parsedErrorResponse = parsedError.response as Record<'error_user_msg' | 'error_user_title', string>;

        metaExceptionData = `${parsedError.message} => ${parsedErrorResponse.error_user_title}: ${parsedErrorResponse.error_user_msg}`;
      }

      return left(UnexpectedError.build({
        context: this.constructor.name,
        error: parsedError,
        data: { id: event.id, metaExceptionData },
      }));
    }

    return right(true);
  }
}
