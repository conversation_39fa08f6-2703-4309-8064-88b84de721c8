import z from 'zod';

import { metaBaseCheckoutSchema, metaTrackingBaseSchema } from './MetaEventBaseSchemas';

const purchaseContentSchema = {
  id: z.optional(z.string()),
  name: z.optional(z.string()),
  type: z.optional(z.string()),
  ids: z.optional(z.array(z.string())),
};

export type PurchaseContent = z.output<typeof purchaseContentSchema>;

export const eventTrackingPurchaseSchema = z.object({
  ...metaTrackingBaseSchema,
  ...metaBaseCheckoutSchema,
  content: z.optional(z.object(purchaseContentSchema)),
});

export type EventTrackingPurchase = z.output<typeof eventTrackingPurchaseSchema>;
