import { ECurrency } from '@discocil/fv-domain-library';
import z from 'zod';

import type { MoneyProps } from '@discocil/fv-domain-library';
import type { AddToCartContent } from './MetaEventAddToCartSchema';
import type { InitiateCheckoutContent } from './MetaEventInitiateCheckoutSchema';
import type { PurchaseContent } from './MetaEventPurchaseSchema';
import type { ViewContentContent } from './MetaEventViewContentSchema';

export const MoneyPropsSchema = z.object({
  amount: z.number(),
  currency: z.enum(ECurrency),
}) satisfies z.ZodType<MoneyProps>;

export const metaTrackingBaseSchema = {
  id: z.string(),
  externalId: z.string(),
  organizationId: z.string(),
  urlPage: z.optional(z.url()),
  user: z.optional(z.object({
    name: z.optional(z.string()),
    email: z.optional(z.string()),
    phone: z.optional(z.string()),
  })),
  fb: z.optional(z.object({
    fbclid: z.optional(z.string()),
    fbc: z.optional(z.string()),
    fbp: z.optional(z.string()),
  })),
  remoteAddress: z.string(),
  userAgent: z.string(),
  type: z.optional(z.string()),
};

export const metaBaseCheckoutSchema = {
  price: z.optional(MoneyPropsSchema),
  totalPrice: z.optional(MoneyPropsSchema),
  items: z.optional(z.array(
    z.object({
      id: z.string(),
      price: z.number(),
      quantity: z.number(),
      category: z.string(),
    }),
  )),
  numItems: z.optional(z.number()),
};

export type TrackingEventContentDto =
  ViewContentContent
  | AddToCartContent
  | InitiateCheckoutContent
  | PurchaseContent;
