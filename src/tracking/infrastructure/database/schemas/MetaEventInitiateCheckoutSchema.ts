import z from 'zod';

import { metaBaseCheckoutSchema, metaTrackingBaseSchema } from './MetaEventBaseSchemas';

const initiateCheckoutContentSchema = {
  id: z.string(),
  name: z.string(),
  type: z.string(),
  ids: z.array(z.string()),
};

export type InitiateCheckoutContent = z.output<typeof initiateCheckoutContentSchema>;

export const eventTrackingInitiateCheckoutSchema = z.object({
  ...metaTrackingBaseSchema,
  ...metaBaseCheckoutSchema,
  content: z.optional(z.object(initiateCheckoutContentSchema)),
});

export type EventTrackingInitiateCheckout = z.output<typeof eventTrackingInitiateCheckoutSchema>;
