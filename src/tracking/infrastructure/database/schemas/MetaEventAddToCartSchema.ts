import z from 'zod';

import { metaBaseCheckoutSchema, metaTrackingBaseSchema } from './MetaEventBaseSchemas';

const addToCartContentSchema = {
  id: z.string(),
  ids: z.array(z.string()),
  name: z.string(),
  date: z.optional(z.number()),
  type: z.string(),
};

export type AddToCartContent = z.output<typeof addToCartContentSchema>;

export const eventTrackingAddToCartSchema = z.object({
  ...metaTrackingBaseSchema,
  ...metaBaseCheckoutSchema,
  content: z.optional(z.object(addToCartContentSchema)),
});

export type EventTrackingAddtoCart = z.output<typeof eventTrackingAddToCartSchema>;
