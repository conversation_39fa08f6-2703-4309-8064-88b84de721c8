import z from 'zod';

import { metaTrackingBaseSchema } from './MetaEventBaseSchemas';

const pageViewContentSchema = {
  name: z.string(),
  date: z.number(),
};

export type EventTrackingViewContent = z.output<typeof eventTrackingViewContentSchema>;

export const eventTrackingViewContentSchema = z.object({
  ...metaTrackingBaseSchema,
  content: z.optional(z.object(pageViewContentSchema)),
});

export type ViewContentContent = z.output<typeof pageViewContentSchema>;
