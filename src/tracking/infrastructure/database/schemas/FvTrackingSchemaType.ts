import type { EMicrositeContainerType } from '@/tracking/domain/contracts/EntityContracts';
import type { EEventTypes } from '@/tracking/domain/value-objects/EventType';
import type {
  ECurrency, EMicrositeServices, IdPrimitive,
} from '@discocil/fv-domain-library';

export type FvTrackingSchemaType = {
  _id: IdPrimitive;
  name: EEventTypes;
  event_id?: IdPrimitive;
  session_id?: string;
  organization_id: IdPrimitive;
  url_page: string;
  price?: number;
  currency?: ECurrency;
  remote_address: string;
  user_agent: string;
  service_type?: EMicrositeServices;
  container_type?: EMicrositeContainerType;
  created_at: number;
};
