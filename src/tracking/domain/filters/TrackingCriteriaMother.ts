import {
  Criteria,
  Filters,
  Order,
  Pagination,
} from '@discocil/fv-criteria-converter-library/domain';
import { UniqueEntityID } from '@discocil/fv-domain-library/domain';

import { EventIdFilter } from '@/cross-cutting/domain/filters/EventIdFilter';
import { OrganizationIdFilter } from '@/cross-cutting/domain/filters/OrganizationIdFilter';

import type { SearchMetricsDto } from '../contracts/SearchMetricsDto';
import type { TrackingEventKeys } from '../entities/EventTracking';

export class TrackingCriteriaMother {
  static readonly defaultOrderKey: TrackingEventKeys = 'date';

  static metricsToMatch(dto: SearchMetricsDto): Criteria {
    const filters = Filters.build();

    const eventId = UniqueEntityID.build(dto.eventId);

    filters.add(EventIdFilter.buildEqual(eventId));

    if (dto.organizationId) {
      const organizationId = UniqueEntityID.build(dto.organizationId);

      filters.add(OrganizationIdFilter.buildEqual(organizationId));
    }

    const pagination = Pagination.build(dto.pagination.page, dto.pagination.perPage);

    return Criteria.build(
      filters,
      Order.asc<TrackingEventKeys>(
        this.defaultOrderKey,
      ),
      pagination,
    );
  }
}
