import { DomainEvent } from '@discocil/fv-domain-library/domain';

import type { DomainEventRequest } from '@discocil/fv-domain-library/domain';

export class TrackedPurchaseDomainEvent extends DomainEvent {
  static readonly EVENT_NAME: string = 'fv.cli.1.ticket.tracking.purchase';

  static build(params: DomainEventRequest): TrackedPurchaseDomainEvent {
    return new this({
      ...params,
      type: this.EVENT_NAME,
    });
  }
}
