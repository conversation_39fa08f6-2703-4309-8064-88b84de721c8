import {
  FvEnum,
  left,
  right,
} from '@discocil/fv-domain-library/domain';

import { UnsupportedEventError } from '../errors/UnsupportedEventError';

import type { Either } from '@discocil/fv-domain-library/domain';

export enum EEventTypes {
  PageView = 'PageView',
  ViewContent = 'ViewContent',
  AddToCart = 'AddToCart',
  InitiateCheckout = 'InitiateCheckout',
  Purchase = 'Purchase'
}

export enum EEventChannel {
  meta = 'meta',
  fv = 'fv'
}

export class EventType extends FvEnum<EEventTypes> {
  static readonly values = Object.values(EEventTypes);

  private constructor(value: EEventTypes) {
    super(value, EventType.values);
  }

  static build(value: string): Either<UnsupportedEventError, EventType> {
    const isValueValid = this.values
      .map(item => item.toString())
      .includes(value);

    return isValueValid
      ? right(new EventType(value as EEventTypes))
      : left(UnsupportedEventError.build({ context: this.constructor.name, target: value }));
  }

  isPageView(): boolean {
    return this.equalTo(EEventTypes.PageView);
  }

  isViewContent(): boolean {
    return this.equalTo(EEventTypes.ViewContent);
  }

  isAddToCart(): boolean {
    return this.equalTo(EEventTypes.AddToCart);
  }

  isInitiateCheckout(): boolean {
    return this.equalTo(EEventTypes.InitiateCheckout);
  }

  isPurchase(): boolean {
    return this.equalTo(EEventTypes.Purchase);
  }

  static PageView(): EventType {
    return EventType.build(EEventTypes.PageView).value as EventType;
  }

  static ViewContent(): EventType {
    return EventType.build(EEventTypes.ViewContent).value as EventType;
  }

  static AddToCart(): EventType {
    return EventType.build(EEventTypes.AddToCart).value as EventType;
  }

  static InitiateCheckout(): EventType {
    return EventType.build(EEventTypes.InitiateCheckout).value as EventType;
  }

  static Purchase(): EventType {
    return EventType.build(EEventTypes.Purchase).value as EventType;
  }
}
