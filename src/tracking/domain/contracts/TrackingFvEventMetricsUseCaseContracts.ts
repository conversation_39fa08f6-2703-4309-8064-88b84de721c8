import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  Either,
  IdPrimitive,
  InvalidArgumentError,
  Maybe,
  MoneyError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import type { MissingCredentialsError } from '../errors/MissingCredentialsError';
import type { UnsupportedEventError } from '../errors/UnsupportedEventError';

export type TrackingEventMetricsDto = {
  readonly eventId: Maybe<IdPrimitive>;
};

export type TrackingEventEither = Either<
  InvalidArgumentError
  | MapperError
  | MissingCredentialsError
  | NotFoundError
  | UnexpectedError
  | UnsupportedEventError
  | MoneyError,
  true
>;
