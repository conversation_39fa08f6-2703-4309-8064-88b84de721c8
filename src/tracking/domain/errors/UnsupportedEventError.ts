import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { FvErrorRequest } from '@discocil/fv-domain-library/domain';

export class UnsupportedEventError extends FvError {
  static readonly defaultCause = EErrorKeys.UNSUPPORTED_EVENT;

  static build(request: FvErrorRequest): UnsupportedEventError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `Untrackable Event: ${target}`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
