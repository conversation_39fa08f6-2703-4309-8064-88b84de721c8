import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { FvErrorRequest } from '@discocil/fv-domain-library/domain';

export class MissingCredentialsError extends FvError {
  static readonly defaultCause = EErrorKeys.MISSING_CREDENTIALS;

  static build(request: FvErrorRequest): MissingCredentialsError {
    const exceptionMessage = `Proper META credentials are missing in this organization`;

    const {
      context, data, error, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
