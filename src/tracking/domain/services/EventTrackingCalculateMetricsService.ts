import {
  EMicrositeServices,
  FvDate,
} from '@discocil/fv-domain-library/domain';

import { EEventTypes } from '../value-objects/EventType';

import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type {
  EventTrackingsMetrics,
  EventTrackingsMetricsKeys,
  EventTrackingsPaginated,
  TrackingMetricsFvEventResponse,
  TrackingMetricsTypes,
} from '../entities/EventTracking';

export class EventTrackingCalculateMetricsService {
  execute(paginatedTrackings: EventTrackingsPaginated[]): TrackingMetricsFvEventResponse {
    const last15MinutesThreshold = FvDate.create().subtractMinutes(15);

    const allEventTrackings = EventTrackingCalculateMetricsService.makeEventTrackingMetricsGroup();
    const eventTrackingsForTheLast15Minutes = EventTrackingCalculateMetricsService.makeEventTrackingMetricsGroup();

    for (const _eventTracking of paginatedTrackings) {
      const { trackings } = _eventTracking;

      for (const _tracking of trackings.values()) {
        if (_tracking.getServiceType().isEmpty() || _tracking.sessionId.isEmpty()) {
          continue;
        }

        const trackingName = _tracking.name as unknown as TrackingMetricsTypes;
        const serviceType = _tracking.getServiceType().get() as unknown as EventTrackingsMetricsKeys;
        const sessionId = _tracking.sessionId.get();

        allEventTrackings[serviceType][trackingName].add(sessionId);
        allEventTrackings.all[trackingName].add(sessionId);

        const createdAtDate = FvDate.create(_tracking.date);

        if (createdAtDate.isGreaterThan(last15MinutesThreshold)) {
          eventTrackingsForTheLast15Minutes[serviceType][trackingName].add(sessionId);
          eventTrackingsForTheLast15Minutes.all[trackingName].add(sessionId);
        }
      }
    }

    return {
      allEventTrackings,
      eventTrackingsForTheLast15Minutes,
    };
  }

  static makeEventTrackingMetricsGroup(): EventTrackingsMetrics {
    return {
      [EMicrositeServices.TICKETS]: {
        [EEventTypes.InitiateCheckout]: new Set<IdPrimitive>(),
        [EEventTypes.AddToCart]: new Set<IdPrimitive>(),
        [EEventTypes.Purchase]: new Set<IdPrimitive>(),
      },
      [EMicrositeServices.GUESTLISTS]: {
        [EEventTypes.InitiateCheckout]: new Set<IdPrimitive>(),
        [EEventTypes.AddToCart]: new Set<IdPrimitive>(),
        [EEventTypes.Purchase]: new Set<IdPrimitive>(),
      },
      [EMicrositeServices.RESERVATIONS]: {
        [EEventTypes.InitiateCheckout]: new Set<IdPrimitive>(),
        [EEventTypes.AddToCart]: new Set<IdPrimitive>(),
        [EEventTypes.Purchase]: new Set<IdPrimitive>(),
      },
      [EMicrositeServices.PASSES]: {
        [EEventTypes.InitiateCheckout]: new Set<IdPrimitive>(),
        [EEventTypes.AddToCart]: new Set<IdPrimitive>(),
        [EEventTypes.Purchase]: new Set<IdPrimitive>(),
      },
      all: {
        [EEventTypes.InitiateCheckout]: new Set<IdPrimitive>(),
        [EEventTypes.AddToCart]: new Set<IdPrimitive>(),
        [EEventTypes.Purchase]: new Set<IdPrimitive>(),
      },
    };
  }
}
