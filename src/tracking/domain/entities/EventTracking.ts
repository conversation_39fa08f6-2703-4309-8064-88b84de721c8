import {
  AggregateRoot,
  FvDate,
  left,
  Maybe,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { EEventTypes, EventType } from '../value-objects/EventType';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { PaginationMetadataResponse } from '@discocil/fv-criteria-converter-library/domain';
import type {
  Either,
  EMicrositeServices,
  IdPrimitive,
  Primitives,
  UnexpectedError,
  UnknownObject,
} from '@discocil/fv-domain-library/domain';
import type { EEventChannel } from '../value-objects/EventType';

export type EventTrackingPrimitives = Omit<Primitives<EventTracking>, 'sessionId'>;

export type EventTrackingCreatePrimitives = Omit<EventTrackingPrimitives, 'id' | 'date'>;

export type SearchPaginatedTrackingEvents = PaginationMetadataResponse & {
  readonly trackings: EventTrackings;
};

export type EventTrackings = Map<IdPrimitive, EventTracking>;
export type EventTrackingsPaginated = Pick<SearchPaginatedTrackingEvents, 'trackings' | 'pagination'>;

export type EventTrackingEither = Either<UnexpectedError, EventTracking>;
export type EventTrackingsEither = Either<MapperError | UnexpectedError, EventTrackingsPaginated>;

export type TrackingEventKeys = keyof EventTracking;

export type TrackingMetricsFvEventResponse = {
  readonly allEventTrackings: EventTrackingsMetrics;
  readonly eventTrackingsForTheLast15Minutes: EventTrackingsMetrics;
};

export type TrackingMetricsFvEventEither = Either<UnexpectedError, TrackingMetricsFvEventResponse>;

export enum TrackingMetricsTypes {
  InitiateCheckout = EEventTypes.InitiateCheckout,
  AddToCart = EEventTypes.AddToCart,
  Purchase = EEventTypes.Purchase
}

export type EventTrackingMetricsByType = {
  [key in TrackingMetricsTypes]: Set<string>;
};

export type EventTrackingsMetricsKeys = `${EMicrositeServices}` | 'all';

export type EventTrackingsMetrics = {
  [key in EventTrackingsMetricsKeys]: EventTrackingMetricsByType;
};

export class EventTracking extends AggregateRoot {
  protected constructor(
    id: UniqueEntityID,
    private readonly _name: EventType,
    private readonly _organizationId: UniqueEntityID,
    private readonly _eventId: Maybe<UniqueEntityID>,
    private readonly _date: FvDate,
    private readonly _channel: EEventChannel,
    private _data: UnknownObject,
  ) {
    super(id);
  }

  static create(primitives: EventTrackingCreatePrimitives): EventTrackingEither {
    const id = UniqueEntityID.create();
    const date = FvDate.create();

    return this.build({
      ...primitives,
      id: id.toPrimitive(),
      date: date.toPrimitive(),
    });
  };

  static build(primitives: EventTrackingPrimitives): EventTrackingEither {
    const id = UniqueEntityID.build(primitives.id);

    const eventTypeOrError = EventType.build(primitives.name);

    if (eventTypeOrError.isLeft()) {
      return left(eventTypeOrError.value);
    }

    const eventType = eventTypeOrError.value;

    const organizationId = UniqueEntityID.build(primitives.organizationId);
    const eventId = primitives.eventId.map(item => UniqueEntityID.build(item));
    const date = FvDate.create(primitives.date);

    const entity = new EventTracking(
      id,
      eventType,
      organizationId,
      eventId,
      date,
      primitives.channel,
      primitives.data ?? {},
    );

    return right(entity);
  }

  get name(): EEventTypes {
    return this._name.toPrimitive();
  }

  get organizationId(): string {
    return this._organizationId.toPrimitive();
  }

  get eventId(): Maybe<string> {
    return this._eventId.map(item => item.toPrimitive());
  }

  get date(): Date {
    return this._date.toPrimitive();
  }

  get channel(): EEventChannel {
    return this._channel;
  }

  get sessionId(): Maybe<string> {
    if (!this.data.sessionId) {
      return Maybe.none<string>();
    }

    const sessionId = this.data.sessionId as Maybe<string>;

    if (sessionId.isEmpty()) {
      return Maybe.none<string>();
    }

    return sessionId;
  }

  get data(): UnknownObject {
    return this._data;
  }

  setSessionId(sessionId: string): this {
    this.data.sessionId = Maybe.some(sessionId);

    return this;
  }

  setData(data: UnknownObject): this {
    this._data = data;

    return this;
  }

  getServiceType(): Maybe<EMicrositeServices> {
    if (!this.data.serviceType) {
      return Maybe.none<EMicrositeServices>();
    }

    const serviceType = this.data.serviceType as Maybe<EMicrositeServices>;

    if (serviceType.isEmpty()) {
      return Maybe.none<EMicrositeServices>();
    }

    return serviceType;
  }
}
