import { Maybe } from '@discocil/fv-domain-library/domain';

import { Photo } from '../entities/Photo';

import type { Nullable } from '@discocil/fv-domain-library/domain';
import type {
  PhotoEither,
  PhotoPrimitives,
  PhotoSizeProps,
} from '../entities/Photo';

export type PhotoJsonPrimitives = Omit<PhotoPrimitives,
  'name'
  | 'sizes'
> & Nullable<{
  readonly name: string;
  readonly sizes: PhotoSizeProps;
}>;

export class PhotoJsonMapper {
  static toEntity(primitives: PhotoJsonPrimitives): PhotoEither {
    return Photo.build({
      ...primitives,
      name: Maybe.fromValue(primitives.name),
      sizes: Maybe.fromValue(primitives.sizes),
    });
  }

  static toJson(photo: Photo): PhotoJsonPrimitives {
    return {
      id: photo.id,
      organizationId: photo.organizationId,
      eventId: photo.eventId,
      oficial: photo.oficial,
      size: photo.size,
      url: photo.url,
      name: photo.name.fold(() => null, item => item),
      sizes: photo.sizes.fold(() => null, item => item),
      createdAt: photo.createdAt,
      createdBy: photo.createdBy,
      updatedAt: photo.updatedAt,
      updatedBy: photo.updatedBy,
      removedAt: photo.removedAt,
      removedBy: photo.removedBy,
    };
  }
}
