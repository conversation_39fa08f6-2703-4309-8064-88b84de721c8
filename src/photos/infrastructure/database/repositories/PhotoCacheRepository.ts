import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { PhotoJsonMapper } from '@/photos/domain/mappers/PhotoJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { PhotoRepository } from '@/photos/domain/contracts/PhotoRepository';
import type {
  Photo,
  PhotoEither,
  Photos,
  PhotosEither,
} from '@/photos/domain/entities/Photo';
import type { PhotoJsonPrimitives } from '@/photos/domain/mappers/PhotoJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class PhotoCacheRepository extends CacheRepository implements PhotoRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: PhotoRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<PhotoEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: PhotoJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = PhotoJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        PhotoJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async search(criteria: Criteria): Promise<PhotosEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: PhotoJsonPrimitives[] = JSON.parse(cacheHit);
      const photos: Photos = new Map<IdPrimitive, Photo>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = PhotoJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        photos.set(_primitive.id, entityOrError.value);
      }

      return right(photos);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const photos = repositoryResult.value;
      const eventLength = photos.size;
      let photosCacheIndex = 0;
      const jsonPhotos = new Array<PhotoJsonPrimitives>(eventLength);

      if (eventLength > 0) {
        for (const _event of photos.values()) {
          jsonPhotos[photosCacheIndex] = PhotoJsonMapper.toJson(_event);

          photosCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonPhotos, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}
