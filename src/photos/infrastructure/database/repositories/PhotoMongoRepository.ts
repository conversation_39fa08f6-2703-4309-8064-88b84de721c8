import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { Photo } from '@/photos/domain/entities/Photo';

import { PhotoMapper } from '../mappers/PhotoMapper';
import { PhotoSchemaMapper } from '../mappers/PhotoSchemaMapper';
import { photoSchema } from '../schemas/PhotoSchema';

import type { PhotoRepository } from '@/photos/domain/contracts/PhotoRepository';
import type {
  PhotoEither,
  PhotoKeys,
  Photos,
  PhotosEither,
} from '@/photos/domain/entities/Photo';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { PhotoSchemaType } from '../schemas/PhotoSchemaType';

type PropertiesMapper = Partial<Record<PhotoKeys, keyof PhotoSchemaType>>;

export class PhotoMongoRepository extends MongoRepository implements PhotoRepository {
  protected getSchema(): Schema {
    return new Schema(photoSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'fotos';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      eventId: 'evento_id',
      createdAt: 'created_at',
      removedAt: 'removed_at',
      organizationId: 'negocio_id',
    };
  }

  async find(criteria: Criteria): Promise<PhotoEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<PhotoSchemaType>();

    return queryResponse ? PhotoMapper.execute(queryResponse) : left(NotFoundError.build({ context: this.constructor.name, target: Photo.name }));
  }

  async search(criteria: Criteria): Promise<PhotosEither> {
    const response: Photos = new Map<IdPrimitive, Photo>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<PhotoSchemaType[]>();

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const photoResult = PhotoMapper.execute(model);

      if (photoResult.isLeft()) {
        return left(photoResult.value);
      }

      const photo = photoResult.value;

      response.set(photo.id, photo);
    }

    return right(response);
  }

  async save(photo: Photo): Promise<void> {
    const toSave = PhotoSchemaMapper.execute(photo);

    const filter = { _id: photo.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(photos: Photos): Promise<void> {
    const connection = await this.getConnection();
    const models: PhotoSchemaType[] = [];

    photos.forEach((photo: Photo) => models.push(PhotoSchemaMapper.execute(photo)));

    await connection.insertMany(models);
  }
}
