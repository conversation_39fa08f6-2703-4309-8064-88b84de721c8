import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { FvErrorRequest } from '@discocil/fv-domain-library/domain';

export class BookingNotActivableError extends FvError {
  static build(request: FvErrorRequest): BookingNotActivableError {
    const exceptionMessage = 'Not allowed to activate the booking';
    const cause = EErrorKeys.INVALID_FIELD;

    const {
      context, error, data, target,
    } = request;

    return new this(
      context,
      exceptionMessage,
      cause,
      error,
      data,
      target,
    );
  }
}
