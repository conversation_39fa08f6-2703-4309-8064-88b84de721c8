import {
  AggregateRoot,
  EBookingState,
  EPaymentMethods,
  FvDate,
  FvNumber,
  left,
  Maybe,
  Money,
  PersonalDocument,
  PhoneNumber,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { BookingCreatedDomainEvent } from '@/bookings/bookings/domain/events/BookingCreatedDomainEvent';
import { defaultStamps, stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import type { Device, TPersonalDocument } from '@/cross-cutting/domain/contracts/CommonContracts';
import type { CreateEntityPrimitives } from '@/cross-cutting/domain/contracts/CreateEntityPrimitives';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { Payment } from '@/payments/domain/entities/Payment';
import type {
  CreatedAt,
  CreatedBy,
  DatePrimitive,
  EBookingPaymentMethod,
  EBookingSaleType,
  ECountryCode,
  ECurrency,
  EGenderGroup,
  EGGDD,
  Either,
  ELanguagesCodes,
  EMerchantNames,
  ENotificationType,
  EPaymentsProvider,
  IdPrimitive,
  InvalidDateError,
  MoneyEither,
  MoneyError,
  MoneyProps,
  NotFoundError,
  Primitives,
  Properties,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';

export type ActivateBooking = {
  email: string;
  linkId: string;
  suscriptorId: string;
  referrerId: string;
  idx: string;
  purchaseId: string;
  remarketing: boolean;
  address: string;
  personalDocument: PersonalDocument;
  postalCode: string;
  country: ECountryCode;
  phone: PhoneNumber;
  clientId: string;
};

export type BookingCommissions = {
  business: number;
  discocil: number;
};

export type BookingRefund = {
  saleId: UniqueEntityID;
  userId: UniqueEntityID;
  amount: Money;
  date: FvDate;
};

type BookingRefundPrimitives = Primitives<BookingRefund>;

export type BookingReconfirmation = {
  isAccepted: boolean;
  reconfirmedAt: Maybe<FvDate>;
  sentAt: Maybe<FvDate>;
};

export type BookingCustomer = {
  name: string;
  email: Maybe<string>;
  phone: Maybe<PhoneNumber>;
  age: Maybe<number>;
  postalCode: Maybe<string>;
  address: Maybe<string>;
  personalDocument: Maybe<PersonalDocument>;
  language: Maybe<ELanguagesCodes>;
  country: Maybe<ECountryCode>;
};

export type BookingSignedUp = {
  signedUp: number;
  male: number;
  female: number;
  genderless: number;
};

export type BookingSignIn = {
  signIn: number;
  male: number;
  female: number;
  genderless: number;
};

export type BookingObservations = {
  client: Maybe<string>;
  pointer: Maybe<string>;
  reception: Maybe<string>;
};

export type BookingActivation = {
  code: string;
  date: Maybe<FvDate>;
};

export type BookingBail = {
  bail: Money;
  optional: Maybe<Money>;
  method: Maybe<EPaymentMethods>;
  paid: Money;
  notifiedAt: Maybe<FvDate>;
};

export type BookingPaymentData = {
  id: UniqueEntityID;
  currency: ECurrency;
  provider: EPaymentsProvider;
  merchantName: EMerchantNames;
};

export type BookingPaymentItem = {
  amount: Money;
  method: EPaymentMethods;
  saleId: Maybe<UniqueEntityID>;
  bail: Maybe<Money>;
  fees: Maybe<Money>;
  payment: Maybe<BookingPaymentData>;
};

export type BookingPayment = {
  amount: Money;
  method: Maybe<EBookingPaymentMethod>;
  isPaid: boolean;
  id: Maybe<UniqueEntityID>;
  isComplete: boolean;
  payments: BookingPaymentItem[];
  price: Maybe<Money>;
  totalImport: Maybe<Money>;
};

export type BookingSupplement = {
  customers: Maybe<number>;
  price: Maybe<Money>;
};

export type BookingGgdd = {
  type: EGGDD;
  amount: Money;
};

export type BookingGgddPrimitives = {
  type: EGGDD;
  amount: MoneyProps;
};

export type BookingSale = {
  id: Maybe<UniqueEntityID>;
  type: EBookingSaleType;
};

export type BookingStamp = {
  by: Maybe<UniqueEntityID>;
  date: Maybe<FvDate>;
};

export type Bookings = Map<IdPrimitive, Booking>;

export type BookingEither = Either<NotFoundError | MapperError | InvalidDateError | MoneyError, Booking>;
export type BookingsEither = Either<NotFoundError | MapperError | InvalidDateError | MoneyError, Bookings>;

export type BookingCustomerPrimitives = Properties<
  Primitives<Omit<BookingCustomer, 'phone' | 'personalDocument'>> & {
    phone: Maybe<string>;
    personalDocument: Maybe<TPersonalDocument>;
  }
>;

export type BookingBailPrimitives = Properties<
  Primitives<Omit<BookingBail, 'optional' | 'notifiedAt'>> & {
    optional: Maybe<number>;
    notifiedAt: Maybe<DatePrimitive>;
  }
>;

export type BookingPaymentDataPrimitives = Primitives<BookingPaymentData>;

export type BookingPaymentItemPrimitives = Properties<
  Primitives<Omit<BookingPaymentItem, 'saleId' | 'payment' | 'bail' | 'fees'>> & {
    saleId: Maybe<string>;
    bail: Maybe<number>;
    fees: Maybe<number>;
    payment: Maybe<BookingPaymentDataPrimitives>;
  }
>;

export type BookingPaymentPrimitives = Properties<
  Primitives<Omit<BookingPayment, 'id' | 'price' | 'totalImport' | 'payments'>> & {
    id: Maybe<string>;
    price: Maybe<number>;
    totalImport: Maybe<number>;
    payments: BookingPaymentItemPrimitives[];
  }
>;

export type BookingSalePrimitives = Properties<
  Primitives<Omit<BookingSale, 'id'>> & {
    id: Maybe<string>;
  }
>;

export type BookingStampPrimitives = {
  by: Maybe<string>;
  date: Maybe<DatePrimitive>;
};

export type BookingActivationPrimitives = Properties<
  Primitives<Omit<BookingActivation, 'date'>> & {
    date: Maybe<DatePrimitive>;
  }
>;

export type BookingReconfirmationPrimitives = Properties<
  Primitives<Omit<BookingReconfirmation, 'reconfirmedAt' | 'sentAt'>> & {
    reconfirmedAt: Maybe<DatePrimitive>;
    sentAt: Maybe<DatePrimitive>;
  }
>;

export type BookingSupplementPrimitives = Properties<
  Primitives<Omit<BookingSupplement, 'price'>> & {
    price: Maybe<MoneyProps>;
  }
>;

export type BookingPrimitives = Primitives<Booking>;

export type CreateBookingPrimitives = CreateEntityPrimitives<BookingPrimitives>;

export type BookingKeys = keyof Booking;

export class Booking extends AggregateRoot {
  constructor(
    id: UniqueEntityID,
    readonly applicationId: string,
    private readonly _organizationId: UniqueEntityID,
    private readonly _eventId: Maybe<UniqueEntityID>,
    private readonly _date: Maybe<FvDate>,
    private _referrerId: Maybe<UniqueEntityID>,
    private readonly _assignerId: Maybe<UniqueEntityID>,
    private readonly _organizationAssignedId: Maybe<UniqueEntityID>,
    private _linkId: Maybe<UniqueEntityID>,
    private _subscriberId: Maybe<UniqueEntityID>,
    readonly hard: boolean,
    private readonly _groups: UniqueEntityID[],
    readonly zoneSlug: Maybe<string>,
    readonly spaceId: Maybe<string>,
    private readonly _time: Maybe<FvDate>,
    private readonly _bookingTime: Maybe<FvDate>,
    private _clientId: Maybe<UniqueEntityID>,
    private _remarketing: boolean,
    private _customer: BookingCustomer,
    readonly signedUp: BookingSignedUp,
    readonly signIn: BookingSignIn,
    readonly observations: BookingObservations,
    readonly genderGroup: Maybe<EGenderGroup>,
    readonly quality: Maybe<number>,
    readonly code: string,
    private readonly _activation: BookingActivation,
    readonly commissions: Maybe<BookingCommissions>,
    private readonly _bail: BookingBail,
    private readonly _payment: BookingPayment,
    readonly slugType: Maybe<string>,
    readonly maxClients: Maybe<number>,
    private readonly _supplement: Maybe<BookingSupplement>,
    readonly content: Maybe<string>,
    readonly conditions: Maybe<string>,
    private readonly _collected: FvNumber,
    private readonly _ggdd: BookingGgdd,
    private readonly _sale: BookingSale,
    readonly state: EBookingState,
    readonly walkin: boolean,
    private readonly _rejected: BookingStamp,
    private readonly _accepted: BookingStamp,
    private readonly _received: BookingStamp,
    private readonly _released: BookingStamp,
    private _idx: Maybe<string>,
    private _purchaseId: Maybe<UniqueEntityID>,
    private readonly _refunded: Money,
    private readonly _refundedAt: Maybe<FvDate>,
    private readonly _refunds: BookingRefund[],
    readonly exit: number,
    private readonly _notificationsIds: UniqueEntityID[],
    readonly notificationTypes: ENotificationType[],
    readonly smsSent: number,
    private readonly _reconfirmation: BookingReconfirmation,
    readonly device: Device,
    readonly minimumSpend: Maybe<number>,
    readonly coverCharge: Maybe<number>,
    readonly cookiesAnalytics: boolean,
    readonly isFixedPrice: boolean,
    readonly currency: ECurrency,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);
  }

  static build(primitives: BookingPrimitives): BookingEither {
    const buildMoney = (amount: number, currency: ECurrency): MoneyEither => Money.build({ amount, currency });

    const id = UniqueEntityID.build(primitives.id);
    const organizationId = UniqueEntityID.build(primitives.organizationId);
    const eventId = primitives.eventId.map(item => UniqueEntityID.build(item));
    const date = primitives.date.map(item => FvDate.create(item));
    const referrerId = primitives.referrerId.map(item => UniqueEntityID.build(item));
    const assignerId = primitives.assignerId.map(item => UniqueEntityID.build(item));
    const organizationAssignedId = primitives.organizationAssignedId.map(item => UniqueEntityID.build(item));
    const linkId = primitives.linkId.map(item => UniqueEntityID.build(item));
    const subscriberId = primitives.subscriberId.map(item => UniqueEntityID.build(item));
    const groups = primitives.groups.map(item => UniqueEntityID.build(item));
    const time = primitives.time.map(item => FvDate.create(item));
    const bookingTime = primitives.bookingTime.map(item => FvDate.create(item));
    const clientId = primitives.clientId.map(item => UniqueEntityID.build(item));
    const collected = FvNumber.build(primitives.collected);

    let customerPersonalDocument = Maybe.none<PersonalDocument>();
    let customerPhone = Maybe.none<PhoneNumber>();

    if (primitives.customer.personalDocument.isDefined()) {
      const { type, number } = primitives.customer.personalDocument.get();

      if (type && number) {
        const personalDocumentResult = PersonalDocument.build(type, number);

        if (personalDocumentResult.isRight()) {
          customerPersonalDocument = Maybe.some(personalDocumentResult.value);
        }
      }
    }

    if (primitives.customer.phone.isDefined()) {
      const phoneOptions = primitives.customer.country.isDefined()
        ? { country: primitives.customer.country.get() }
        : undefined;

      const phoneOrError = PhoneNumber.build(primitives.customer.phone.get(), phoneOptions);

      if (phoneOrError.isRight()) {
        customerPhone = Maybe.some(phoneOrError.value);
      }
    }

    const customer: BookingCustomer = {
      ...primitives.customer,
      email: primitives.customer.email.map(item => item),
      age: primitives.customer.age.map(item => item),
      postalCode: primitives.customer.postalCode.map(item => item),
      address: primitives.customer.address.map(item => item),
      language: primitives.customer.language.map(item => item),
      country: primitives.customer.country.map(item => item),
      personalDocument: customerPersonalDocument,
      phone: customerPhone,
    };

    const activation: BookingActivation = {
      ...primitives.activation,
      date: primitives.activation.date.map(item => FvDate.create(item)),
    };

    const bailResult = buildMoney(primitives.bail.bail, primitives.currency);
    const paidResult = buildMoney(primitives.bail.paid, primitives.currency);

    if (bailResult.isLeft()) {
      return left(bailResult.value);
    }

    if (paidResult.isLeft()) {
      return left(paidResult.value);
    }

    let bailOptional = Maybe.none<Money>();

    if (primitives.bail.optional.isDefined()) {
      const optionalResult = buildMoney(primitives.bail.optional.get(), primitives.currency);

      if (optionalResult.isLeft()) {
        return left(optionalResult.value);
      }

      bailOptional = Maybe.some(optionalResult.value);
    }

    const bail: BookingBail = {
      ...primitives.bail,
      bail: bailResult.value,
      method: primitives.bail.method.map(item => item),
      optional: bailOptional,
      paid: paidResult.value,
      notifiedAt: primitives.bail.notifiedAt.map(item => FvDate.create(item)),
    };

    const paymentAmountResult = buildMoney(primitives.payment.amount, primitives.currency);
    let paymentPrice = Maybe.none<Money>();
    let paymentTotalImport = Maybe.none<Money>();

    if (paymentAmountResult.isLeft()) {
      return left(paymentAmountResult.value);
    }

    if (primitives.payment.price.isDefined()) {
      const paymentPriceResult = buildMoney(primitives.payment.price.get(), primitives.currency);

      if (paymentPriceResult.isLeft()) {
        return left(paymentPriceResult.value);
      }

      paymentPrice = Maybe.some(paymentPriceResult.value);
    }

    if (primitives.payment.totalImport.isDefined()) {
      const paymentTotalImportResult = buildMoney(primitives.payment.totalImport.get(), primitives.currency);

      if (paymentTotalImportResult.isLeft()) {
        return left(paymentTotalImportResult.value);
      }

      paymentTotalImport = Maybe.some(paymentTotalImportResult.value);
    }

    const payment: BookingPayment = {
      ...primitives.payment,
      method: primitives.payment.method.map(item => item),
      id: primitives.payment.id.map(item => UniqueEntityID.build(item)),
      amount: paymentAmountResult.value,
      price: paymentPrice,
      totalImport: paymentTotalImport,
      payments: primitives.payment.payments.map((item) => {
        return {
          ...item,
          saleId: item.saleId.map(item => UniqueEntityID.build(item)),
          amount: buildMoney(item.amount, primitives.currency).value as Money,
          bail: item.bail.map(item => buildMoney(item, primitives.currency).value as Money),
          fees: item.fees.map(item => buildMoney(item, primitives.currency).value as Money),
          payment: item.payment.map(item => ({
            ...item,
            id: UniqueEntityID.build(item.id),
          })),
        };
      }),
    };

    const sale: BookingSale = {
      ...primitives.sale,
      id: primitives.sale.id.map(item => UniqueEntityID.build(item)),
    };

    const rejected: BookingStamp = {
      by: primitives.rejected.by.map(item => UniqueEntityID.build(item)),
      date: primitives.rejected.date.map(item => FvDate.create(item)),
    };

    const accepted: BookingStamp = {
      by: primitives.accepted.by.map(item => UniqueEntityID.build(item)),
      date: primitives.accepted.date.map(item => FvDate.create(item)),
    };

    const received: BookingStamp = {
      by: primitives.received.by.map(item => UniqueEntityID.build(item)),
      date: primitives.received.date.map(item => FvDate.create(item)),
    };

    const released: BookingStamp = {
      by: primitives.released.by.map(item => UniqueEntityID.build(item)),
      date: primitives.released.date.map(item => FvDate.create(item)),
    };

    const purchaseId = primitives.purchaseId.map(item => UniqueEntityID.build(item));
    const refundedAt = primitives.refundedAt.map(item => FvDate.create(item));

    const notificationsIds = primitives.notificationsIds.map(id => UniqueEntityID.build(id));

    const reconfirmation: BookingReconfirmation = {
      ...primitives.reconfirmation,
      reconfirmedAt: primitives.reconfirmation.reconfirmedAt.map(item => FvDate.create(item)),
      sentAt: primitives.reconfirmation.sentAt.map(item => FvDate.create(item)),
    };

    const refunds: BookingRefund[] = [];

    for (const refund of primitives.refunds) {
      const amountResult = buildMoney(refund.amount, primitives.currency);

      if (amountResult.isLeft()) {
        return left(amountResult.value);
      }

      refunds.push({
        ...refund,
        saleId: UniqueEntityID.build(refund.saleId),
        userId: UniqueEntityID.build(refund.userId),
        date: FvDate.create(refund.date),
        amount: amountResult.value,
      });
    }

    let supplement = Maybe.none<BookingSupplement>();

    if (primitives.supplement.isDefined()) {
      const { price, customers } = primitives.supplement.get();

      if (price.isDefined()) {
        const { amount, currency } = price.get();
        const priceResult = buildMoney(amount, currency);

        if (priceResult.isLeft()) {
          return left(priceResult.value);
        }

        supplement = Maybe.some({
          ...primitives.supplement,
          price: Maybe.some(priceResult.value),
          customers: customers.map(item => item),
        });
      }
    }

    const ggddAmountResult = buildMoney(primitives.ggdd.amount.amount, primitives.ggdd.amount.currency);

    if (ggddAmountResult.isLeft()) {
      return left(ggddAmountResult.value);
    }

    const ggdd: BookingGgdd = {
      ...primitives.ggdd,
      amount: ggddAmountResult.value,
    };

    const refundedResult = buildMoney(primitives.refunded.amount, primitives.refunded.currency);

    if (refundedResult.isLeft()) {
      return left(refundedResult.value);
    }

    const refunded = refundedResult.value;

    const zoneSlug = primitives.zoneSlug.map(item => item);
    const spaceId = primitives.spaceId.map(item => item);
    const maxClients = primitives.maxClients.map(item => item);

    const observations: BookingObservations = {
      client: primitives.observations.client.map(item => item),
      pointer: primitives.observations.pointer.map(item => item),
      reception: primitives.observations.reception.map(item => item),
    };

    const genderGroup = primitives.genderGroup.map(item => item);
    const quality = primitives.quality.map(item => item);
    const commissions = primitives.commissions.map(item => item);
    const slugType = primitives.slugType.map(item => item);
    const content = primitives.content.map(item => item);
    const conditions = primitives.conditions.map(item => item);
    // eslint-disable-next-line no-restricted-syntax
    const idx = primitives.idx.map(item => item);
    const minimumSpend = primitives.minimumSpend.map(item => item);
    const coverCharge = primitives.coverCharge.map(item => item);

    const stamps = stampValueObjects(primitives);

    const entity = new Booking(
      id,
      primitives.applicationId,
      organizationId,
      eventId,
      date,
      referrerId,
      assignerId,
      organizationAssignedId,
      linkId,
      subscriberId,
      primitives.hard,
      groups,
      zoneSlug,
      spaceId,
      time,
      bookingTime,
      clientId,
      primitives.remarketing,
      customer,
      primitives.signedUp,
      primitives.signIn,
      observations,
      genderGroup,
      quality,
      primitives.code,
      activation,
      commissions,
      bail,
      payment,
      slugType,
      maxClients,
      supplement,
      content,
      conditions,
      collected,
      ggdd,
      sale,
      primitives.state,
      primitives.walkin,
      rejected,
      accepted,
      received,
      released,
      idx,
      purchaseId,
      refunded,
      refundedAt,
      refunds,
      primitives.exit,
      notificationsIds,
      primitives.notificationTypes,
      primitives.smsSent,
      reconfirmation,
      primitives.device,
      minimumSpend,
      coverCharge,
      primitives.cookiesAnalytics,
      primitives.isFixedPrice,
      primitives.currency,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return right(entity);
  }

  static create(primitives: CreateBookingPrimitives): BookingEither {
    const id = UniqueEntityID.create().toPrimitive();

    const entityResult = Booking.build({
      ...primitives,
      id,
      ...defaultStamps(),
    });

    if (entityResult.isLeft()) {
      return left(entityResult.value);
    }

    const entity = entityResult.value;

    entity.addDomainEvent(
      BookingCreatedDomainEvent.build({
        aggregateId: entity._id,
        attributes: entity.toPrimitives(),
      }),
    );

    return right(entity);
  }

  get organizationId(): IdPrimitive {
    return this._organizationId.toPrimitive();
  }

  get eventId(): Maybe<IdPrimitive> {
    return this._eventId.map(item => item.toPrimitive());
  }

  get date(): Maybe<DatePrimitive> {
    return this._date.map(item => item.toPrimitive());
  }

  get referrerId(): Maybe<IdPrimitive> {
    return this._referrerId.map(item => item.toPrimitive());
  }

  get assignerId(): Maybe<IdPrimitive> {
    return this._assignerId.map(item => item.toPrimitive());
  }

  get organizationAssignedId(): Maybe<IdPrimitive> {
    return this._organizationAssignedId.map(item => item.toPrimitive());
  }

  get linkId(): Maybe<IdPrimitive> {
    return this._linkId.map(item => item.toPrimitive());
  }

  get subscriberId(): Maybe<IdPrimitive> {
    return this._subscriberId.map(item => item.toPrimitive());
  }

  get groups(): IdPrimitive[] {
    return this._groups.map(item => item.toPrimitive());
  }

  get time(): Maybe<DatePrimitive> {
    return this._time.map(item => item.toPrimitive());
  }

  get bookingTime(): Maybe<DatePrimitive> {
    return this._bookingTime.map(item => item.toPrimitive());
  }

  get clientId(): Maybe<IdPrimitive> {
    return this._clientId.map(item => item.toPrimitive());
  }

  get remarketing(): boolean {
    return this._remarketing;
  }

  get customer(): BookingCustomerPrimitives {
    return {
      ...this._customer,
      phone: this._customer.phone.map(item => item.value.number),
      personalDocument: this._customer.personalDocument.map((item) => {
        return {
          type: item.type,
          number: item.number,
        };
      }),
    };
  }

  get activation(): BookingActivationPrimitives {
    return {
      ...this._activation,
      date: this._activation.date.map(item => item.toPrimitive()),
    };
  }

  getActivationDateInSeconds(): Maybe<number> {
    return this._activation.date.map(item => item.toSeconds());
  }

  getRejectedDateInSeconds(): Maybe<number> {
    return this._rejected.date.map(item => item.toSeconds());
  }

  getAcceptedDateInSeconds(): Maybe<number> {
    return this._accepted.date.map(item => item.toSeconds());
  }

  getReceivedDateInSeconds(): Maybe<number> {
    return this._received.date.map(item => item.toSeconds());
  }

  getReleasedDateInSeconds(): Maybe<number> {
    return this._released.date.map(item => item.toSeconds());
  }

  getRefundedAtInSeconds(): Maybe<number> {
    return this._refundedAt.map(item => item.toSeconds());
  }

  getReconfirmationReconfirmedAtInSeconds(): Maybe<number> {
    return this._reconfirmation.reconfirmedAt.map(item => item.toSeconds());
  }

  getReconfirmationSentAtInSeconds(): Maybe<number> {
    return this._reconfirmation.sentAt.map(item => item.toSeconds());
  }

  getBailNotifiedAtInSeconds(): Maybe<number> {
    return this._bail.notifiedAt.map(item => item.toSeconds());
  }

  getBookingTimeOrElse(fallback: DatePrimitive): DatePrimitive {
    return this.bookingTime.getOrElse(fallback);
  }

  hasTime(): boolean {
    return this._time.isDefined();
  }

  hasBookingTime(): boolean {
    return this.bookingTime.isDefined();
  }

  getBookingTime(): FvDate {
    return FvDate.create(this.time.get());
  }

  get bail(): BookingBailPrimitives {
    return {
      ...this._bail,
      notifiedAt: this._bail.notifiedAt.map(item => item.toPrimitive()),
      bail: this._bail.bail.toDecimal(),
      optional: this._bail.optional.map(item => item.toDecimal()),
      paid: this._bail.paid.toDecimal(),
    };
  }

  get payment(): BookingPaymentPrimitives {
    return {
      ...this._payment,
      id: this._payment.id.map(item => item.value),
      amount: this._payment.amount.toDecimal(),
      price: this._payment.price.map(item => item.toDecimal()),
      totalImport: this._payment.totalImport.map(item => item.toDecimal()),
      payments: this._payment.payments.map((paymentItem) => {
        return {
          ...paymentItem,
          saleId: paymentItem.saleId.map(item => item.value),
          amount: paymentItem.amount.toDecimal(),
          bail: paymentItem.bail.map(item => item.toDecimal()),
          fees: paymentItem.fees.map(item => item.toDecimal()),
          payment: paymentItem.payment.map(item => ({
            ...item,
            id: item.id.value,
          })),
        };
      }),
    };
  }

  get collected(): number {
    return this._collected.toPrimitive();
  }

  get rejected(): BookingStampPrimitives {
    const { by, date } = this._rejected;

    return {
      by: by.map(item => item.value),
      date: date.map(item => item.toPrimitive()),
    };
  }

  get accepted(): BookingStampPrimitives {
    const { by, date } = this._accepted;

    return {
      by: by.map(item => item.value),
      date: date.map(item => item.toPrimitive()),
    };
  }

  get received(): BookingStampPrimitives {
    const { by, date } = this._received;

    return {
      by: by.map(item => item.value),
      date: date.map(item => item.toPrimitive()),
    };
  }

  get released(): BookingStampPrimitives {
    const { by, date } = this._released;

    return {
      by: by.map(item => item.value),
      date: date.map(item => item.toPrimitive()),
    };
  }

  get idx(): Maybe<string> {
    return this._idx;
  }

  get purchaseId(): Maybe<IdPrimitive> {
    return this._purchaseId.map(item => item.value);
  }

  get refunded(): MoneyProps {
    return this._refunded.toPrimitive();
  }

  get refundedAt(): Maybe<DatePrimitive> {
    return this._refundedAt.map(item => item.toPrimitive());
  }

  get notificationsIds(): IdPrimitive[] {
    return this._notificationsIds.map(item => item.value);
  }

  get supplement(): Maybe<BookingSupplementPrimitives> {
    return this._supplement.map(supplement => ({
      ...supplement,
      price: supplement.price.map(item => item.toPrimitive()),
    }));
  }

  get ggdd(): BookingGgddPrimitives {
    return {
      ...this._ggdd,
      amount: this._ggdd.amount.toPrimitive(),
    };
  }

  get sale(): BookingSalePrimitives {
    return {
      ...this._sale,
      id: this._sale.id.map(item => item.value),
    };
  }

  get refunds(): BookingRefundPrimitives[] {
    return this._refunds.map((item) => {
      return {
        ...item,
        saleId: item.saleId.value,
        userId: item.userId.value,
        date: item.date.value,
        amount: item.amount.toDecimal(),
      };
    });
  }

  get reconfirmation(): BookingReconfirmationPrimitives {
    return {
      ...this._reconfirmation,
      reconfirmedAt: this._reconfirmation.reconfirmedAt.map(item => item.toPrimitive()),
      sentAt: this._reconfirmation.sentAt.map(item => item.toPrimitive()),
    };
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }

  hasSpaceId(spaceId: string): boolean {
    return this.spaceId.is(spaceId);
  }

  isPaid(): boolean {
    return this._bail.paid.isPositive();
  }

  hasBail(): boolean {
    return this._bail.bail.isPositive() && this._bail.optional.isDefined();
  }

  isActivable(): boolean {
    if (this._bail.bail.isZero() && this._bail.optional.isEmpty()) {
      return false;
    }

    if (this.isPaid()) {
      return false;
    }

    if (this.isCanceledByClient() || this.isRejected()) {
      return false;
    }

    if (this._bail.method.is(EPaymentMethods.PAYMENT_PLATFORM)) {
      return false;
    }

    return true;
  }

  isRejected(): boolean {
    return this.state === EBookingState.REJECTED;
  }

  isCanceledByClient(): boolean {
    return this.state === EBookingState.CANCELED_BY_CLIENT;
  }

  setCustomer(customer: BookingCustomerPrimitives): this {
    let phone = Maybe.none<PhoneNumber>();
    let personalDocument = Maybe.none<PersonalDocument>();

    if (customer.phone.isDefined()) {
      const phoneOptions = customer.country.isDefined()
        ? { country: customer.country.get() }
        : undefined;

      const phoneOrError = PhoneNumber.build(customer.phone.get(), phoneOptions);

      if (phoneOrError.isRight()) {
        phone = Maybe.some(phoneOrError.value);
      }
    }

    if (customer.personalDocument.isDefined()) {
      const { type, number } = customer.personalDocument.get();

      if (type && number) {
        const personalDocumentResult = PersonalDocument.build(type, number);

        if (personalDocumentResult.isRight()) {
          personalDocument = Maybe.some(personalDocumentResult.value);
        }
      }
    }

    this._customer = {
      ...customer,
      email: customer.email.map(item => item),
      age: customer.age.map(item => item),
      postalCode: customer.postalCode.map(item => item),
      address: customer.address.map(item => item),
      language: customer.language.map(item => item),
      country: customer.country.map(item => item),
      phone,
      personalDocument,
    };

    return this;
  }

  activate(data: ActivateBooking): this {
    const bookingCustomer = this.customer;

    bookingCustomer.email = Maybe.fromValue(data.email);
    bookingCustomer.address = Maybe.fromValue(data.address);
    bookingCustomer.postalCode = Maybe.fromValue(data.postalCode);
    bookingCustomer.country = Maybe.fromValue(data.country);
    bookingCustomer.phone = Maybe.fromValue(data.phone.number);
    bookingCustomer.personalDocument = Maybe.fromValue(data.personalDocument.toPrimitive());

    this.setCustomer(bookingCustomer);

    this._linkId = Maybe.some(UniqueEntityID.build(data.linkId));
    this._subscriberId = Maybe.some(UniqueEntityID.build(data.suscriptorId));
    this._referrerId = Maybe.some(UniqueEntityID.build(data.referrerId));
    this._idx = Maybe.some(data.idx);
    this._purchaseId = Maybe.some(UniqueEntityID.build(data.purchaseId));
    this._remarketing = data.remarketing;
    this._clientId = Maybe.some(UniqueEntityID.build(data.clientId));

    return this;
  }

  setPayment(paymentEnitity: Payment): this {
    this._payment.id = Maybe.fromValue(UniqueEntityID.build(paymentEnitity.id));

    this._payment.amount = Money.build({
      amount: paymentEnitity.totalPrice,
      currency: paymentEnitity.currency,
    }).value as Money;

    return this;
  }

  getActivationCode(): string {
    return this._activation.code;
  }

  getDateInDateFormat(): Maybe<string> {
    return this._date.map(item => item.toFormat({ style: 'date' }));
  }

  getTimeInSeconds(): Maybe<number> {
    return this._time.map(item => item.toSeconds());
  }

  getBookingTimeInSeconds(): Maybe<number> {
    return this._bookingTime.map(item => item.toSeconds());
  }

  havePaymentCompleted(): boolean {
    return this._payment.isComplete;
  }

  toPrimitives(): BookingPrimitives {
    return {
      id: this.id,
      applicationId: this.applicationId,
      organizationId: this.organizationId,
      eventId: this.eventId,
      date: this.date,
      referrerId: this.referrerId,
      assignerId: this.assignerId,
      organizationAssignedId: this.organizationAssignedId,
      linkId: this.linkId,
      subscriberId: this.subscriberId,
      hard: this.hard,
      groups: this.groups,
      zoneSlug: this.zoneSlug,
      spaceId: this.spaceId,
      time: this.time,
      bookingTime: this.bookingTime,
      clientId: this.clientId,
      remarketing: this.remarketing,
      customer: this.customer,
      signedUp: this.signedUp,
      signIn: this.signIn,
      observations: this.observations,
      genderGroup: this.genderGroup,
      quality: this.quality,
      code: this.code,
      activation: this.activation,
      commissions: this.commissions,
      bail: this.bail,
      payment: this.payment,
      slugType: this.slugType,
      maxClients: this.maxClients,
      supplement: this.supplement,
      content: this.content,
      conditions: this.conditions,
      collected: this.collected,
      ggdd: this.ggdd,
      sale: this.sale,
      state: this.state,
      walkin: this.walkin,
      rejected: this.rejected,
      accepted: this.accepted,
      received: this.received,
      released: this.released,
      idx: this.idx,
      purchaseId: this.purchaseId,
      refunded: this.refunded,
      refundedAt: this.refundedAt,
      refunds: this.refunds,
      exit: this.exit,
      notificationsIds: this.notificationsIds,
      smsSent: this.smsSent,
      reconfirmation: this.reconfirmation,
      notificationTypes: this.notificationTypes,
      device: this.device,
      minimumSpend: this.minimumSpend,
      coverCharge: this.coverCharge,
      cookiesAnalytics: this.cookiesAnalytics,
      isFixedPrice: this.isFixedPrice,
      currency: this.currency,
      createdAt: this.createdAt,
      createdBy: this.createdBy,
      updatedAt: this.updatedAt,
      updatedBy: this.updatedBy,
      removedAt: this.removedAt,
      removedBy: this.removedBy,
    };
  }
}
