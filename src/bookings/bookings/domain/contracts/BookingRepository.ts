import type {
  Booking,
  BookingEither,
  Bookings,
  BookingsEither,
} from '@/bookings/bookings/domain/entities/BookingEntity';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export interface BookingRepository {
  find: (criteria: Criteria) => Promise<BookingEither>;
  save: (booking: Booking) => Promise<void>;
  saveMany: (booking: Bookings) => Promise<void>;
  search: (criteria: Criteria) => Promise<BookingsEither>;
}
