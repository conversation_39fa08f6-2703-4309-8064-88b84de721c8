import type { BookingType } from '@/bookings/bookingTypes/domain/entities/BookingTypeEntity';
import type { BookingZone } from '@/bookings/bookingZones/domain/entities/BookingZoneEntity';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { EventEntity } from '@/events/events/domain/entities/EventEntity';
import type { PaymentGatewayBadConfigurationError } from '@/gatewayAccount/domain/errors/PaymentGatewayBadConfigurationError';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { Payment, PaymentEither } from '@/payments/domain/entities/Payment';
import type {
  ECountryCode,
  EDocumentType,
  Either,
  InvalidArgumentError,
  InvalidDateError,
  Maybe,
  Money,
  MoneyError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import type { Booking } from '../entities/BookingEntity';
import type { BookingNotActivableError } from '../errors/RateNotAllowedToSellError';

export type ActivateBookingDto = {
  bookingId: string;
  userId: string;
  userObservations: string;
  email: string;
  linkId: string;
  suscriptorId: string;
  referrerId: string;
  idx: string;
  purchaseId: string;
  remarketing: boolean;
  metadata: object;
  redirectUrl: string;
  errorUrl: string;
  fullPayment: boolean;
  address: string;
  personalDocumentNumber: string;
  personalDocumentType: EDocumentType;
  postalCode: string;
  country: ECountryCode;
  phone: string;
};

export type ActivateBookingPayment = {
  booking: Booking;
  bookingType: BookingType;
  zone: BookingZone;
  organization: Organization;
  event: Maybe<EventEntity>;
};

export interface ICheckBookingIsActivable {
  execute: (booking: Booking, zone: BookingZone) => Promise<Either<BookingNotActivableError, boolean>>;
}

export interface IActivateBookingPaymentService {
  execute: (activateBookingPayment: ActivateBookingPayment) => Promise<PaymentEither>;
}

export type ActivateBookingResponseDto = {
  booking: Booking;
  redirectUrl: string;
  errorUrl: string;
  payment: Payment;
};

export type ActivateBookingEither = Either<
  InvalidArgumentError
  | InvalidDateError
  | MapperError
  | NotFoundError
  | PaymentGatewayBadConfigurationError
  | UnexpectedError
  | MoneyError,
  ActivateBookingResponseDto
>;

export type Totals = {
  price: Money;
  import: Money;
  fees: Money;
};
