import type { RuleTemplate, TemplatePrimitives } from '@/organizations/organizations/domain/entities/RuleTemplate';
import type { Money } from '@discocil/fv-domain-library/domain';

type PaymentGGDD = {
  readonly discocilTotal: Money;
  readonly ggddTemplate: TemplatePrimitives;
};

export class BookingPaymentGGDDCalculate {
  constructor(
    private readonly ruleTemplate: RuleTemplate,
    private readonly totalBookingAmountToPay: Money,
  ) { }

  execute(): PaymentGGDD {
    const ggddTemplate = this.ruleTemplate.getGGDDBookings();

    const percentageComission = this.totalBookingAmountToPay.percentage(ggddTemplate.percentage);
    const totalFee = percentageComission.add(ggddTemplate.fixed);
    const boundedComission = this.ruleTemplate.getBookingBoundedCommissionFee(totalFee);

    const discocilTotal = this.totalBookingAmountToPay.lt(boundedComission)
      ? this.totalBookingAmountToPay
      : boundedComission;

    return {
      discocilTotal,
      ggddTemplate,
    };
  }
}
