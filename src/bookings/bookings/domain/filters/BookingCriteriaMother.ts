import { Criteria, Filters } from '@discocil/fv-criteria-converter-library/domain';

import { EventIdFilter } from '@/cross-cutting/domain/filters/EventIdFilter';
import { IdFilter } from '@/cross-cutting/domain/filters/IdFilter';
import { OrganizationIdFilter } from '@/cross-cutting/domain/filters/OrganizationIdFilter';
import { RemovedAtFilter } from '@/cross-cutting/domain/filters/RemovedAtFilter';

import { BookingStateFilter } from './BookingStateFilter';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';

export class BookingCriteriaMother {
  static idToMatch(id: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(id));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static availablesToMatch(eventId: UniqueEntityID, organizationId: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(OrganizationIdFilter.buildEqual(organizationId));
    filters.add(RemovedAtFilter.buildActive());
    filters.add(EventIdFilter.buildEqual(eventId));
    filters.add(BookingStateFilter.buildIn());

    return Criteria.build(filters);
  }
}
