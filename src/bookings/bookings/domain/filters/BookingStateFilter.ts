import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';
import { EBookingState } from '@discocil/fv-domain-library/domain';

import type { BookingKeys } from '../entities/BookingEntity';

class FilterField extends FilterFieldBase<BookingKeys> {}

export class BookingStateFilter {
  private static readonly field: BookingKeys = 'state';

  static buildIn(): Filter {
    const states = [
      EBookingState.PENDING,
      EBookingState.ACCEPTED,
      EBookingState.ARRIVAL,
      EBookingState.SEATED,
      EBookingState.PARTIAL_SEATED,
    ];

    const stateField = new FilterField(this.field);
    const stateOperator = FilterOperator.in();
    const stateValue = states.map((value: string) => FilterValue.build(value));

    return new Filter(stateField, stateOperator, stateValue);
  }
}
