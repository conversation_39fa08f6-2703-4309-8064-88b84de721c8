import { DomainEvent } from '@discocil/fv-domain-library/domain';

import type { DomainEventRequest } from '@discocil/fv-domain-library/domain';

export class BookingCreatedDomainEvent extends DomainEvent {
  static readonly EVENT_NAME: string = 'cli.booking.created';

  static build(params: DomainEventRequest): BookingCreatedDomainEvent {
    return new this({
      ...params,
      type: this.EVENT_NAME,
    });
  }
}
