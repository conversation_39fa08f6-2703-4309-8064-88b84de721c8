import { container, instanceCachingFactory } from 'tsyringe';

import { BookingDependencyIdentifier } from '@/bookings/bookings/domain/dependencyIdentifier/BookingDependencyIdentifier';
import { BookingMongoRepository } from '@/bookings/bookings/infrastructure/database/repositories/BookingMongoRepository';
import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';

import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';

export const BookingContainer = {
  register: (): void => {
    container.register(BookingDependencyIdentifier.BookingRepository, {
      useFactory: instanceCachingFactory((container) => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

        return new BookingMongoRepository(dbConnection);
      }),
    });
  },
};
