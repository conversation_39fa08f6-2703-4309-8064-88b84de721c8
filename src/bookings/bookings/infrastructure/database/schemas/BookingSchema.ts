import {
  EBookingPaymentMethod,
  EBookingSaleType,
  EBookingState,
  EGGDD,
  ENotificationType,
  EPaymentMethods,
  Gender,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import config from '@config/index';

export const bookingSchema = {
  _id: {
    type: String,
    required: true,
  },
  aplicacion_id: {
    type: String,
    required: true,
    default: config.fv.apps.professionals.applicationId,
  },
  negocio_id: {
    type: String,
    required: true,
    index: true,
  },
  // Si no se dispone de evento_id significa que la reserva está pendiente de asignar evento
  evento_id: {
    type: String,
    index: true,
    default: '',
  },
  // La fecha de la reserva en formato YYYY/MM/DD
  fecha: {
    type: String,
    index: true,
    default: '',
  },
  referente_id: {
    type: String,
    index: true,
    default: '',
  },
  apuntador_id: {
    type: String,
    index: true,
    default: '',
  },
  negocio_apuntando: {
    type: String,
    index: true,
    default: '',
  },
  link_id: {
    // Custom link desde el que se hace la venta
    type: String,
    index: true,
    default: '',
  },
  suscriptor_id: {
    type: String,
    index: true,
    default: '',
  },
  hard: {
    type: Boolean,
    default: false,
  },
  grupos: { type: [String] },
  zona_slug: {
    type: String,
    default: '',
  },
  espacio_id: {
    type: String,
    default: '',
  },
  hora: {
    type: Number,
    default: 0,
  },
  tiempo_reserva: {
    type: Number,
    default: 0,
  },
  cliente_id: {
    type: String,
    index: true,
    default: '',
  },
  remarketing: {
    type: Boolean,
    default: false,
  },
  nombre: {
    type: String,
    default: 'Sin nombre',
  },
  apuntados: {
    type: Number,
    default: 1,
  },
  apuntados_chicos: { type: Number },
  apuntados_chicas: { type: Number },
  apuntados_singen: { type: Number },
  entran: {
    type: Number,
    default: 0,
  },
  entran_chicos: { type: Number },
  entran_chicas: { type: Number },
  entran_singen: { type: Number },
  observaciones_cliente: { type: String },
  observaciones_apuntador: { type: String },
  dni: { type: String },
  email: {
    type: String,
    index: true,
  },
  edad: { type: Number },
  telefono: { type: String },
  codigo_postal: { type: String },
  genero_grupo: {
    type: String,
    enum: Gender.groups(),
  },
  calidad: { type: Number },
  codigo: {
    type: String,
    index: true,
  },
  codigo_activacion: {
    type: String,
    index: true,
  },
  activated_at: {
    type: Number,
    default: 0,
  },
  comisiones: {
    type: Object,
    default: {},
  },
  fianza: {
    // Indica el precio de la fianza según la tarifa seleccionada
    type: Number,
    default: 0,
  },
  fianza_opcional: {
    type: Number,
    default: null,
  },
  fianza_metodo: {
    type: String,
    enum: Object.values(EPaymentMethods),
  },
  fianza_pagada: {
    // Vamos a utilizar una cache para saber los pagos de las fianzas
    type: Number,
    default: 0,
  },
  fianza_notificada_at: {
    type: Number,
    default: 0,
  },
  pago_importe: {
    type: Number,
    default: 0,
  },
  pago_metodo: {
    type: String,
    enum: Object.values(EBookingPaymentMethod),
  },
  pago_efectuado: {
    type: Boolean,
    default: false,
  },
  pagos: {
    // Se guardará un array con todos los pagos realizados en la reserva. Por ahora tendremos solo uno
    type: Array,
    default: [],
  },
  precio: { type: Number },
  importe_total: { type: Number },
  // Identificad or del tipo en el momento de hacer la reserva
  tipo_slug: {
    type: String,
    default: '',
  },
  max_personas: { type: Number },
  suplemento_personas: { type: Number },
  suplemento_precio: { type: Number },
  contenido: { type: String },
  condiciones: { type: String },
  recaudado: {
    type: Number,
    default: 0,
  },
  gastos_gestion_tipo: {
    type: String,
    enum: Object.values(EGGDD),
    default: EGGDD.FIX,
  },
  gastos_gestion_cantidad: {
    type: Number,
    default: 0,
  },
  tipo_venta: {
    type: String,
    enum: Object.values(EBookingSaleType),
    default: EBookingSaleType.ONLINE,
  },
  estado: {
    type: String,
    enum: Object.values(EBookingState),
    default: EBookingState.PENDING,
  },
  walkin: {
    type: Boolean,
    default: false,
  },
  rechazada_by: {
    type: String,
    default: '',
  },
  rechazada_at: {
    type: Number,
    default: 0,
  },
  aceptada_by: {
    type: String,
    default: '',
  },
  aceptada_at: {
    type: Number,
    default: 0,
  },
  recepcionada_by: {
    type: String,
    default: '',
  },
  recepcionada_at: {
    type: Number,
    default: 0,
  },
  liberada_by: {
    type: String,
    default: '',
  },
  liberada_at: {
    type: Number,
    default: 0,
  },
  observaciones_recepcion: { type: String },
  language: { type: String },
  country: { type: String },
  browser: { type: String },
  device: { type: String },
  os: { type: String },
  sale_id: { type: String },
  payment_id: { type: String },
  idx: {
    type: String,
    index: true,
  },
  purchase_id: {
    type: String,
    index: true,
  },
  refunded: {
    // Cantidad devuelta
    type: Number,
    default: 0,
  },
  refunded_at: {
    type: Number,
    default: 0,
  },
  refunds: {
    type: Array,
    default: [],
  },
  salida: {
    type: Number,
    default: 0,
  },
  notificaciones_ids: {
    type: Array,
    default: [],
  },
  sms_sent: {
    type: Number,
    default: 0,
  },
  reconfirmacion: {
    aceptada: {
      // El usuario ha aceptado o no
      type: Boolean,
    },
    reconfirmada_at: {
      // Momento en el que se reconfirma por el usuario
      type: Number,
      default: 0,
    },
    sent_at: {
      // Momento en el que se manda la notificacion
      type: Number,
      default: 0,
    },
  },
  full_payment: {
    type: Boolean,
    default: false,
  },
  notification_type: {
    type: [String],
    enum: Object.values(ENotificationType),
  },
  address: { type: String },
  personal_document_number: { type: String },
  personal_document_type: { type: String },
  minimum_spend: { type: Number },
  cover_charge: { type: Number },
  cookies_analytics: { type: Boolean },
  is_fixed_price: { type: Boolean },
  created_at: { type: Number },
  created_by: { type: String },
  updated_at: { type: Number },
  updated_by: { type: String },
  removed_at: {
    type: Number,
    index: true,
  },
  removed_by: { type: String },
};

export const BookingSchema = new Schema(bookingSchema);

BookingSchema.index({
  removed_at: 1,
  activated_at: 1,
  fianza_metodo: 1,
  fianza_pagada: 1,
  evento_id: 1,
});

BookingSchema.index({
  removed_at: 1,
  evento_id: 1,
  estado: 1,
  negocio_apuntando: 1,
});

BookingSchema.index({
  negocio_id: 1,
  removed_at: 1,
  fianza_metodo: 1,
  fianza_pagada: 1,
});
