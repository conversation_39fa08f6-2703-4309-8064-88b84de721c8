import type {
  EBookingPaymentMethod,
  EBookingSaleType,
  EBookingState,
  ECountryCode,
  ECurrency,
  EDocumentType,
  EGenderGroup,
  EGGDD, ELanguagesCodes,
  EMerchantNames,
  ENotificationType,
  EPaymentMethods,
  EPaymentsProvider,
} from '@discocil/fv-domain-library/domain';

export type BookingCommissionsSchema = {
  negocio: number;
  discocil: number;
};

export type BookingRefundSchema = {
  sale_id: string;
  usuario_id: string;
  importe: number;
  at: number;
};

export type BookingPaymentSchema = {
  importe: number;
  metodo: EPaymentMethods;
  fianza?: number;
  fees?: number;
  sale_id?: string;
  payment_id?: string;
  payment_currency?: ECurrency;
  payment_provider?: EPaymentsProvider;
  payment_merchant_name?: EMerchantNames;
};

export type BoolingReconfirmationSchema = {
  aceptada?: boolean;
  reconfirmada_at: number;
  sent_at: number;
};

export type BookingSchemaType = {
  _id: string;
  aplicacion_id: string;
  negocio_id: string;
  evento_id: string;
  fecha: string;
  referente_id: string;
  apuntador_id: string;
  negocio_apuntando: string;
  link_id: string;
  suscriptor_id: string;
  hard: boolean;
  grupos: string[];
  zona_slug: string;
  espacio_id: string;
  hora: number;
  tiempo_reserva: number;
  cliente_id: string;
  remarketing: boolean;
  nombre: string;
  apuntados: number;
  apuntados_chicos?: number;
  apuntados_chicas?: number;
  apuntados_singen?: number;
  entran: number;
  entran_chicos?: number;
  entran_chicas?: number;
  entran_singen?: number;
  observaciones_cliente?: string;
  observaciones_apuntador?: string;
  dni?: string;
  email?: string;
  edad?: number;
  telefono?: string;
  codigo_postal?: string;
  genero_grupo?: EGenderGroup;
  calidad?: number;
  codigo: string;
  codigo_activacion: string;
  activated_at: number;
  comisiones?: BookingCommissionsSchema;
  fianza: number;
  fianza_opcional?: number | null;
  fianza_metodo?: EPaymentMethods;
  fianza_pagada: number;
  fianza_notificada_at: number;
  pago_importe: number;
  pago_metodo?: EBookingPaymentMethod;
  pago_efectuado: boolean;
  pagos: BookingPaymentSchema[];
  precio?: number;
  importe_total?: number;
  tipo_slug: string;
  max_personas?: number;
  suplemento_personas?: number;
  suplemento_precio?: number;
  contenido?: string;
  condiciones?: string;
  recaudado: number;
  gastos_gestion_tipo: EGGDD;
  gastos_gestion_cantidad: number;
  tipo_venta: EBookingSaleType;
  estado: EBookingState;
  walkin: boolean;
  rechazada_by: string;
  rechazada_at: number;
  aceptada_by: string;
  aceptada_at: number;
  recepcionada_at: number;
  recepcionada_by: string;
  liberada_at: number;
  liberada_by: string;
  observaciones_recepcion?: string;
  language?: ELanguagesCodes;
  country?: ECountryCode;
  browser?: string;
  device?: string;
  os?: string;
  sale_id?: string;
  payment_id?: string;
  idx?: string;
  purchase_id?: string;
  refunded: number;
  refunded_at: number;
  refunds: BookingRefundSchema[];
  salida: number;
  notificaciones_ids: string[];
  sms_sent: number;
  reconfirmacion: BoolingReconfirmationSchema;
  full_payment: boolean;
  notification_type: ENotificationType[];
  address?: string;
  personal_document_number?: string;
  personal_document_type?: EDocumentType;
  minimum_spend?: number;
  cover_charge?: number;
  cookies_analytics?: boolean;
  is_fixed_price?: boolean;
  event: {
    currency: ECurrency;
  };
  organization: {
    currency: ECurrency;
  };
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
  removed_at: number;
  removed_by: string;
};

export type BookingOriginalSchemaType = Omit<BookingSchemaType, 'event' | 'organization'>;
