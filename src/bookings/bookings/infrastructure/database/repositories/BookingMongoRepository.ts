import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { Booking } from '@/bookings/bookings/domain/entities/BookingEntity';
import { BookingMapper } from '@/bookings/bookings/infrastructure/database/mappers/BookingMapper';
import { BookingSchemaMapper } from '@/bookings/bookings/infrastructure/database/mappers/BookingSchemaMapper';
import { BookingSchema } from '@/bookings/bookings/infrastructure/database/schemas/BookingSchema';
import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';

import type { BookingRepository } from '@/bookings/bookings/domain/contracts/BookingRepository';
import type {
  BookingEither,
  BookingKeys,
  Bookings,
  BookingsEither,
} from '@/bookings/bookings/domain/entities/BookingEntity';
import type { BookingSchemaType } from '@/bookings/bookings/infrastructure/database/schemas/BookingSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { Schema } from 'mongoose';

type PropertiesMapper = Partial<Record<BookingKeys, keyof BookingSchemaType>>;

export class BookingMongoRepository extends MongoRepository implements BookingRepository {
  protected getSchema(): Schema {
    return BookingSchema;
  }

  protected getDBName(): string {
    return EDBNames.BOOKING;
  }

  protected getModel(): string {
    return 'reservas';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      eventId: 'evento_id',
      state: 'estado',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<BookingEither> {
    criteria.setPage(1).setPerPage(1);

    const queryResponse = (await this.customQueryFinder<BookingSchemaType[]>(criteria)).shift();

    return queryResponse ? BookingMapper.execute(queryResponse) : left(NotFoundError.build({ context: this.constructor.name, target: Booking.name }));
  }

  async save(booking: Booking): Promise<void> {
    const toSave = BookingSchemaMapper.execute(booking);

    const filter = { _id: booking.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(bookings: Bookings): Promise<void> {
    const connection = await this.getConnection();
    const models: Array<Partial<BookingSchemaType>> = [];

    bookings.forEach((booking: Booking) => models.push(BookingSchemaMapper.execute(booking)));

    await connection.insertMany(models);
  }

  async search(criteria: Criteria): Promise<BookingsEither> {
    const response = new Map<IdPrimitive, Booking>();

    const queryResponse = await this.customQueryFinder<BookingSchemaType[]>(criteria);

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const bookingResult = BookingMapper.execute(model);

      if (bookingResult.isLeft()) {
        return left(bookingResult.value);
      }

      const booking = bookingResult.value;

      response.set(booking.id, booking);
    }

    return right(response);
  }

  private async customQueryFinder<T>(criteria: Criteria): Promise<T> {
    const connection = await this.getConnection();
    const pipeline = this.criteriaConverter.toPipeline(criteria);

    return connection.aggregate([
      ...pipeline,
      {
        $lookup: {
          from: 'eventos',
          let: { eventId: '$evento_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$eventId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'event',
        },
      },
      {
        $lookup: {
          from: 'negocios',
          let: { organizationId: '$negocio_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$organizationId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'organization',
        },
      },
      {
        $project: {
          main: '$$ROOT',
          eventObj: { $arrayElemAt: ['$event', 0] },
          organizationObj: { $arrayElemAt: ['$organization', 0] },
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              '$main',
              {
                event: { currency: { $ifNull: ['$eventObj.currency', null] } },
                organization: { currency: { $ifNull: ['$organizationObj.currency', null] } },
              },
            ],
          },
        },
      },
    ]) as unknown as T;
  }
}
