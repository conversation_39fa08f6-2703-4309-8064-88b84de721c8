import {
  EBookingSaleType,
  EBookingState,
  ECurrency,
  EGGDD,
  FvDate,
  left,
  Maybe,
} from '@discocil/fv-domain-library/domain';
import { container } from 'tsyringe';

import { Booking } from '@/bookings/bookings/domain/entities/BookingEntity';
import { bookingValidationSchema } from '@/bookings/bookings/infrastructure/database/mappers/BookingSchemaValidation';
import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import { isObjectEmpty } from '@/cross-cutting/domain/helpers/objects';
import config from '@config/index';

import type {
  BookingCommissions,
  BookingEither,
  BookingPrimitives,
} from '@/bookings/bookings/domain/entities/BookingEntity';
import type { BookingSchemaType } from '@/bookings/bookings/infrastructure/database/schemas/BookingSchemaType';
import type { TPersonalDocument } from '@/cross-cutting/domain/contracts/CommonContracts';
import type {
  DatePrimitive,
  EMerchantNames,
  EPaymentsProvider,
} from '@discocil/fv-domain-library/domain';
import type Ajv from 'ajv';

export class BookingMapper {
  static execute(data: BookingSchemaType): BookingEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(bookingValidationSchema);

    if (!validate(data) || validate.errors) {
      MapperError.logger({
        context: BookingMapper.name,
        data,
        target: validate.errors,
      });

      return BookingSoftMapper.execute(data);
    }

    return this.buildEntity(data);
  }

  static buildEntity(data: BookingSchemaType): BookingEither {
    let date = Maybe.none<DatePrimitive>();

    if (data.fecha) {
      const dateResult = FvDate.createFromFormat(data.fecha);

      if (dateResult.isLeft()) {
        return left(dateResult.value);
      }

      date = Maybe.some(dateResult.value.value);
    }

    const currency = data.event?.currency ?? data.organization?.currency ?? ECurrency.EUR;

    return Booking.build({
      id: data._id,
      applicationId: data.aplicacion_id,
      organizationId: data.negocio_id,
      eventId: Maybe.fromValue(data.evento_id),
      date,
      referrerId: Maybe.fromValue(data.referente_id),
      assignerId: Maybe.fromValue(data.apuntador_id),
      organizationAssignedId: Maybe.fromValue(data.negocio_apuntando),
      linkId: Maybe.fromValue(data.link_id),
      subscriberId: Maybe.fromValue(data.suscriptor_id),
      hard: data.hard ?? false,
      groups: data.grupos,
      zoneSlug: Maybe.fromValue(data.zona_slug),
      spaceId: Maybe.fromValue(data.espacio_id),
      time: data.hora > 0 ? Maybe.some(this.makeDate(data.hora)) : Maybe.none(),
      bookingTime: data.tiempo_reserva > 0 ? Maybe.some(this.makeDate(data.tiempo_reserva)) : Maybe.none(),
      clientId: Maybe.fromValue(data.cliente_id),
      remarketing: data.remarketing ?? false,
      customer: this.makeCustomer(data),
      signedUp: this.makeSignedUp(data),
      signIn: this.makeSignIn(data),
      observations: this.makeObservations(data),
      genderGroup: Maybe.fromValue(data.genero_grupo),
      quality: Maybe.fromValue(data.calidad),
      code: data.codigo,
      activation: this.makeActivation(data),
      commissions: isObjectEmpty(data.comisiones ?? {}) ? Maybe.none() : Maybe.some(this.makeCommissions(data)),
      bail: this.makeBail(data),
      payment: this.makePayment(data),
      slugType: Maybe.fromValue(data.tipo_slug),
      maxClients: Maybe.fromValue(data.max_personas),
      supplement: this.makeSupplement(data, currency),
      content: Maybe.fromValue(data.contenido),
      conditions: Maybe.fromValue(data.condiciones),
      collected: data.recaudado ?? 0,
      ggdd: this.makeGgdd(data, currency),
      sale: this.makeSale(data),
      state: data.estado ?? EBookingState.PENDING,
      walkin: data.walkin ?? false,
      rejected: this.makeRejected(data),
      accepted: this.makeAccepted(data),
      received: this.makeReceived(data),
      released: this.makeReleased(data),
      idx: Maybe.fromValue(data.idx),
      purchaseId: Maybe.fromValue(data.purchase_id),
      refunded: {
        amount: data.refunded ?? 0,
        currency,
      },
      refundedAt: data.refunded_at > 0 ? Maybe.some(this.makeDate(data.refunded_at)) : Maybe.none(),
      refunds: data.refunds ? this.makeRefunds(data) : [],
      exit: data.salida ?? 0,
      notificationsIds: data.notificaciones_ids ?? [],
      smsSent: data.sms_sent ?? 0,
      reconfirmation: this.makeReconfirmation(data),
      notificationTypes: data.notification_type ?? [],
      device: this.makeDevice(data),
      minimumSpend: Maybe.fromValue(data.minimum_spend),
      coverCharge: Maybe.fromValue(data.cover_charge),
      cookiesAnalytics: data.cookies_analytics ?? false,
      isFixedPrice: data.is_fixed_price ?? false,
      currency,
      createdAt: data.created_at,
      createdBy: data.created_by,
      updatedAt: data.updated_at,
      updatedBy: data.updated_by,
      removedAt: data.removed_at,
      removedBy: data.removed_by,
    });
  }

  protected static makeDate(value: number): DatePrimitive {
    return FvDate.createFromSeconds(value).toPrimitive();
  }

  protected static makePersonalDocument(data: BookingSchemaType): Maybe<TPersonalDocument> {
    const personalDocumentNumber = data.dni || data.personal_document_number || null;
    const personalDocumentType = data.personal_document_type || null;

    if (personalDocumentNumber && personalDocumentType) {
      return Maybe.some({
        number: personalDocumentNumber,
        type: personalDocumentType,
      });
    }

    return Maybe.none();
  }

  protected static makeCustomer(data: BookingSchemaType): BookingPrimitives['customer'] {
    return {
      name: data.nombre ?? 'Sin nombre',
      email: Maybe.fromValue(data.email),
      phone: Maybe.fromValue(data.telefono),
      age: Maybe.fromValue(data.edad),
      postalCode: Maybe.fromValue(data.codigo_postal),
      address: Maybe.fromValue(data.address),
      personalDocument: this.makePersonalDocument(data),
      language: Maybe.fromValue(data.language),
      country: Maybe.fromValue(data.country),
    };
  }

  protected static makeSignedUp(data: BookingSchemaType): BookingPrimitives['signedUp'] {
    return {
      signedUp: data.apuntados ?? 1,
      male: data.apuntados_chicos ?? 0,
      female: data.apuntados_chicas ?? 0,
      genderless: data.apuntados_singen ?? 0,
    };
  }

  protected static makeSignIn(data: BookingSchemaType): BookingPrimitives['signIn'] {
    return {
      signIn: data.entran ?? 1,
      male: data.entran_chicos ?? 0,
      female: data.entran_chicas ?? 0,
      genderless: data.entran_singen ?? 0,
    };
  }

  protected static makeObservations(data: BookingSchemaType): BookingPrimitives['observations'] {
    return {
      client: Maybe.fromValue(data.observaciones_cliente),
      pointer: Maybe.fromValue(data.observaciones_apuntador),
      reception: Maybe.fromValue(data.observaciones_recepcion),
    };
  }

  protected static makeActivation(data: BookingSchemaType): BookingPrimitives['activation'] {
    return {
      code: data.codigo_activacion,
      date: data.activated_at > 0 ? Maybe.some(this.makeDate(data.activated_at)) : Maybe.none(),
    };
  }

  protected static makeCommissions(data: BookingSchemaType): BookingCommissions {
    return {
      business: data.comisiones?.negocio ?? 0,
      discocil: data.comisiones?.discocil ?? 0,
    };
  }

  protected static makeBail(data: BookingSchemaType): BookingPrimitives['bail'] {
    return {
      bail: data.fianza ?? 0,
      optional: Maybe.fromValue(data.fianza_opcional),
      method: Maybe.fromValue(data.fianza_metodo),
      paid: data.fianza_pagada ?? 0,
      notifiedAt: data.fianza_notificada_at > 0 ? Maybe.some(this.makeDate(data.fianza_notificada_at)) : Maybe.none(),
    };
  }

  protected static makePayment(data: BookingSchemaType): BookingPrimitives['payment'] {
    return {
      amount: data.pago_importe ?? 0,
      method: Maybe.fromValue(data.pago_metodo),
      isPaid: data.pago_efectuado ?? false,
      id: Maybe.fromValue(data.payment_id),
      isComplete: data.full_payment ?? false,
      payments: data.pagos.map((item) => {
        return {
          amount: item.importe,
          method: item.metodo,
          saleId: Maybe.fromValue(item.sale_id),
          bail: Maybe.fromValue(item.fianza),
          fees: Maybe.fromValue(item.fees),
          payment: item.payment_id
            ? Maybe.some({
              id: item.payment_id,
              currency: item.payment_currency as ECurrency,
              provider: item.payment_provider as EPaymentsProvider,
              merchantName: item.payment_merchant_name as EMerchantNames,
            })
            : Maybe.none(),
        };
      }),
      price: Maybe.fromValue(data.precio),
      totalImport: Maybe.fromValue(data.importe_total),
    };
  }

  protected static makeSupplement(data: BookingSchemaType, currency: ECurrency): BookingPrimitives['supplement'] {
    return Maybe.fromValue({
      customers: Maybe.fromValue(data.suplemento_personas),
      price: data.suplemento_precio
        ? Maybe.fromValue({
          amount: data.suplemento_precio,
          currency,
        })
        : Maybe.none(),
    });
  }

  protected static makeGgdd(data: BookingSchemaType, currency: ECurrency): BookingPrimitives['ggdd'] {
    return {
      type: data.gastos_gestion_tipo ?? EGGDD.FIX,
      amount: {
        amount: data.gastos_gestion_cantidad ?? 0,
        currency,
      },
    };
  }

  protected static makeSale(data: BookingSchemaType): BookingPrimitives['sale'] {
    return {
      id: Maybe.fromValue(data.sale_id),
      type: data.tipo_venta ?? EBookingSaleType.ONLINE,
    };
  }

  protected static makeRejected(data: BookingSchemaType): BookingPrimitives['rejected'] {
    return {
      by: Maybe.fromValue(data.rechazada_by),
      date: data.rechazada_at > 0 ? Maybe.some(this.makeDate(data.rechazada_at)) : Maybe.none(),
    };
  }

  protected static makeAccepted(data: BookingSchemaType): BookingPrimitives['accepted'] {
    return {
      by: Maybe.fromValue(data.aceptada_by),
      date: data.aceptada_at > 0 ? Maybe.some(this.makeDate(data.aceptada_at)) : Maybe.none(),
    };
  }

  protected static makeReceived(data: BookingSchemaType): BookingPrimitives['received'] {
    return {
      by: Maybe.fromValue(data.recepcionada_by),
      date: data.recepcionada_at > 0 ? Maybe.some(this.makeDate(data.recepcionada_at)) : Maybe.none(),
    };
  }

  protected static makeReleased(data: BookingSchemaType): BookingPrimitives['released'] {
    return {
      by: Maybe.fromValue(data.liberada_by),
      date: data.liberada_at > 0 ? Maybe.some(this.makeDate(data.liberada_at)) : Maybe.none(),
    };
  }

  protected static makeRefunds(data: BookingSchemaType): BookingPrimitives['refunds'] {
    return data.refunds.map((item) => {
      return {
        saleId: item.sale_id,
        userId: item.usuario_id,
        amount: item.importe,
        date: this.makeDate(item.at),
      };
    });
  }

  protected static makeReconfirmation(data: BookingSchemaType): BookingPrimitives['reconfirmation'] {
    return {
      isAccepted: data.reconfirmacion.aceptada ?? false,
      reconfirmedAt: data.reconfirmacion.reconfirmada_at > 0 ? Maybe.some(this.makeDate(data.reconfirmacion.reconfirmada_at)) : Maybe.none(),
      sentAt: data.reconfirmacion.sent_at > 0 ? Maybe.some(this.makeDate(data.reconfirmacion.sent_at)) : Maybe.none(),
    };
  }

  protected static makeDevice(data: BookingSchemaType): BookingPrimitives['device'] {
    return {
      browser: data.browser ?? null,
      device: data.device ?? null,
      os: data.os ?? null,
    };
  }
}

export class BookingSoftMapper extends BookingMapper {
  static execute(data: BookingSchemaType): BookingEither {
    return this.buildEntity({
      ...data,
      _id: data._id,
      aplicacion_id: data.aplicacion_id ?? config.fv.apps.professionals.applicationId,
      negocio_id: data.negocio_id ?? null,
      grupos: data.grupos ?? [],
      codigo: data.codigo ?? null,
    });
  }
}
