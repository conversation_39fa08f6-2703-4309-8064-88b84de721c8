import {
  EBookingSaleType,
  EBookingState,
  EGGDD,
  FvDate,
} from '@discocil/fv-domain-library/domain';

import config from '@config/index';

import type { Booking } from '@/bookings/bookings/domain/entities/BookingEntity';
import type { BookingOriginalSchemaType } from '@/bookings/bookings/infrastructure/database/schemas/BookingSchemaType';

export class BookingSchemaMapper {
  static execute(booking: Booking): BookingOriginalSchemaType {
    return {
      _id: booking.id,
      aplicacion_id: booking.applicationId ?? config.fv.apps.professionals.applicationId,
      negocio_id: booking.organizationId,
      evento_id: booking.eventId.fold(() => '', item => item),
      fecha: booking.getDateInDateFormat().fold(() => '', item => item),
      referente_id: booking.referrerId.fold(() => '', item => item),
      apuntador_id: booking.assignerId.fold(() => '', item => item),
      negocio_apuntando: booking.organizationAssignedId.fold(() => '', item => item),
      link_id: booking.linkId.fold(() => '', item => item),
      suscriptor_id: booking.subscriberId.fold(() => '', item => item),
      hard: booking.hard ?? false,
      grupos: booking.groups,
      zona_slug: booking.zoneSlug.fold(() => '', item => item),
      espacio_id: booking.spaceId.fold(() => '', item => item),
      hora: booking.getTimeInSeconds().fold(() => 0, item => item),
      tiempo_reserva: booking.getBookingTimeInSeconds().fold(() => 0, item => item),
      cliente_id: booking.clientId.fold(() => '', item => item),
      remarketing: booking.remarketing ?? false,
      nombre: booking.customer.name ?? 'Sin nombre',
      apuntados: booking.signedUp.signedUp ?? 1,
      apuntados_chicos: booking.signedUp.male,
      apuntados_chicas: booking.signedUp.female,
      apuntados_singen: booking.signedUp.genderless,
      entran: booking.signIn.signIn ?? 0,
      entran_chicos: booking.signIn.male,
      entran_chicas: booking.signIn.female,
      entran_singen: booking.signIn.genderless,
      observaciones_cliente: booking.observations.client.fold(() => undefined, item => item),
      observaciones_apuntador: booking.observations.pointer.fold(() => undefined, item => item),
      dni: booking.customer.personalDocument.fold(() => undefined, item => item.number ?? undefined),
      email: booking.customer.email.fold(() => undefined, item => item),
      edad: booking.customer.age.fold(() => undefined, item => item),
      telefono: booking.customer.phone.fold(() => undefined, item => item),
      codigo_postal: booking.customer.postalCode.fold(() => undefined, item => item),
      genero_grupo: booking.genderGroup.fold(() => undefined, item => item),
      calidad: booking.quality.fold(() => undefined, item => item),
      codigo: booking.code,
      codigo_activacion: booking.activation.code,
      activated_at: booking.getActivationDateInSeconds().fold(() => 0, item => item),
      comisiones: booking.commissions.fold(
        () => undefined,
        (commissions) => {
          return {
            negocio: commissions.business,
            discocil: commissions.discocil,
          };
        },
      ),
      fianza: booking.bail.bail ?? 0,
      fianza_opcional: booking.bail.optional.fold(() => undefined, item => item),
      fianza_metodo: booking.bail.method.fold(() => undefined, item => item),
      fianza_pagada: booking.bail.paid ?? 0,
      fianza_notificada_at: booking.getBailNotifiedAtInSeconds().fold(() => 0, item => item),
      pago_importe: booking.payment.amount ?? 0,
      pago_metodo: booking.payment.method.fold(() => undefined, item => item),
      pago_efectuado: booking.payment.isPaid ?? false,
      pagos: booking?.payment?.payments
        ? booking.payment.payments.map((item) => {
          return {
            importe: item.amount,
            metodo: item.method,
            sale_id: item.saleId.fold(() => undefined, item => item),
            fianza: item.bail.fold(() => undefined, item => item),
            fees: item.fees.fold(() => undefined, item => item),
            payment_id: item.payment.fold(() => undefined, item => item.id),
            payment_currency: item.payment.fold(() => undefined, item => item.currency),
            payment_provider: item.payment.fold(() => undefined, item => item.provider),
            payment_merchant_name: item.payment.fold(() => undefined, item => item.merchantName),
          };
        })
        : [],
      precio: booking.payment.price.fold(() => undefined, item => item),
      importe_total: booking.payment.totalImport.fold(() => undefined, item => item),
      tipo_slug: booking.slugType.fold(() => '', item => item),
      max_personas: booking.maxClients.fold(() => undefined, item => item),
      suplemento_personas: booking.supplement.fold(
        () => undefined,
        supplement => supplement.customers.fold(
          () => undefined,
          item => item,
        ),
      ),
      suplemento_precio: booking.supplement.fold(
        () => undefined,
        supplement => supplement.price.fold(
          () => undefined,
          item => item.amount,
        ),
      ),
      contenido: booking.content.fold(() => undefined, item => item),
      condiciones: booking.conditions.fold(() => undefined, item => item),
      recaudado: booking.collected ?? 0,
      gastos_gestion_tipo: booking.ggdd.type ?? EGGDD.FIX,
      gastos_gestion_cantidad: booking.ggdd.amount.amount ?? 0,
      sale_id: booking.sale.id.fold(() => undefined, item => item),
      tipo_venta: booking.sale.type ?? EBookingSaleType.ONLINE,
      estado: booking.state ?? EBookingState.PENDING,
      walkin: booking.walkin ?? false,
      rechazada_by: booking.rejected.by.fold(() => '', item => item),
      rechazada_at: booking.getRejectedDateInSeconds().fold(() => 0, item => item),
      aceptada_by: booking.accepted.by.fold(() => '', item => item),
      aceptada_at: booking.getAcceptedDateInSeconds().fold(() => 0, item => item),
      recepcionada_by: booking.received.by.fold(() => '', item => item),
      recepcionada_at: booking.getReceivedDateInSeconds().fold(() => 0, item => item),
      liberada_by: booking.released.by.fold(() => '', item => item),
      liberada_at: booking.getReleasedDateInSeconds().fold(() => 0, item => item),
      observaciones_recepcion: booking.observations.reception.fold(() => undefined, item => item),
      language: booking.customer.language.fold(() => undefined, item => item),
      country: booking.customer.country.fold(() => undefined, item => item),
      browser: booking.device.browser ?? undefined,
      device: booking.device.device ?? undefined,
      os: booking.device.os ?? undefined,
      payment_id: booking.payment.id.fold(() => undefined, item => item),
      idx: booking.idx.fold(() => undefined, item => item),
      purchase_id: booking.purchaseId.fold(() => undefined, item => item),
      refunded: booking.refunded.amount ?? 0,
      refunded_at: booking.getRefundedAtInSeconds().fold(() => 0, item => item),
      refunds: booking.refunds.map((item) => {
        return {
          sale_id: item.saleId,
          usuario_id: item.userId,
          importe: item.amount,
          at: FvDate.create(item.date).toSeconds(),
        };
      }),
      salida: booking.exit ?? 0,
      notificaciones_ids: booking.notificationsIds ?? [],
      sms_sent: booking.smsSent ?? 0,
      reconfirmacion: {
        aceptada: booking.reconfirmation.isAccepted,
        reconfirmada_at: booking.getReconfirmationReconfirmedAtInSeconds().fold(() => 0, item => item),
        sent_at: booking.getReconfirmationSentAtInSeconds().fold(() => 0, item => item),
      },
      full_payment: booking.payment.isComplete ?? false,
      notification_type: booking.notificationTypes,
      address: booking.customer.address.fold(() => undefined, item => item),
      personal_document_number: booking.customer.personalDocument.fold(() => undefined, item => item.number ?? undefined),
      personal_document_type: booking.customer.personalDocument.fold(() => undefined, item => item.type ?? undefined),
      minimum_spend: booking.minimumSpend.fold(() => undefined, item => item),
      cover_charge: booking.coverCharge.fold(() => undefined, item => item),
      cookies_analytics: booking.cookiesAnalytics,
      is_fixed_price: booking.isFixedPrice,
      created_at: booking.createdAt,
      created_by: booking.createdBy,
      updated_at: booking.updatedAt,
      updated_by: booking.updatedBy,
      removed_at: booking.removedAt,
      removed_by: booking.removedBy,
    };
  }
}
