import type { JSONSchemaType } from 'ajv';
import type { BookingOriginalSchemaType } from '../schemas/BookingSchemaType';

export const bookingValidationSchema: JSONSchemaType<BookingOriginalSchemaType> = {
  title: 'Booking Json Schema Validation',
  required: ['_id', 'aplicacion_id', 'negocio_id'],
  type: 'object',
  properties: {
    _id: { type: 'string' },
    aplicacion_id: { type: 'string' },
    negocio_id: { type: 'string' },
    evento_id: { type: 'string' },
    fecha: { type: 'string' },
    referente_id: { type: 'string' },
    apuntador_id: { type: 'string' },
    negocio_apuntando: { type: 'string' },
    link_id: { type: 'string' },
    suscriptor_id: { type: 'string' },
    hard: { type: 'boolean' },
    grupos: {
      type: 'array',
      items: { type: 'string' },
    },
    zona_slug: { type: 'string' },
    espacio_id: { type: 'string' },
    hora: { type: 'number' },
    tiempo_reserva: { type: 'number' },
    cliente_id: { type: 'string' },
    remarketing: { type: 'boolean' },
    nombre: { type: 'string' },
    apuntados: { type: 'number' },
    apuntados_chicos: { type: 'number', nullable: true },
    apuntados_chicas: { type: 'number', nullable: true },
    apuntados_singen: { type: 'number', nullable: true },
    entran: { type: 'number' },
    entran_chicos: { type: 'number', nullable: true },
    entran_chicas: { type: 'number', nullable: true },
    entran_singen: { type: 'number', nullable: true },
    observaciones_cliente: { type: 'string', nullable: true },
    observaciones_apuntador: { type: 'string', nullable: true },
    dni: { type: 'string', nullable: true },
    email: { type: 'string', nullable: true },
    edad: { type: 'number', nullable: true },
    telefono: { type: 'string', nullable: true },
    codigo_postal: { type: 'string', nullable: true },
    genero_grupo: { type: 'string', nullable: true },
    calidad: { type: 'number', nullable: true },
    codigo: { type: 'string' },
    codigo_activacion: { type: 'string' },
    activated_at: { type: 'number' },
    comisiones: {
      type: 'object',
      nullable: true,
      required: [],
      properties: {
        negocio: { type: 'number' },
        discocil: { type: 'number' },
      },
    },
    fianza: { type: 'number' },
    fianza_opcional: { type: 'number', nullable: true },
    fianza_metodo: { type: 'string', nullable: true },
    fianza_pagada: { type: 'number' },
    fianza_notificada_at: { type: 'number' },
    pago_importe: { type: 'number' },
    pago_metodo: { type: 'string', nullable: true },
    pago_efectuado: { type: 'boolean' },
    pagos: {
      type: 'array',
      items: {
        type: 'object',
        required: ['importe', 'metodo'],
        properties: {
          importe: { type: 'number' },
          metodo: { type: 'string' },
          fianza: { type: 'number', nullable: true },
          fees: { type: 'number', nullable: true },
          sale_id: { type: 'string', nullable: true },
          payment_id: { type: 'string', nullable: true },
          payment_currency: { type: 'string', nullable: true },
          payment_provider: { type: 'string', nullable: true },
          payment_merchant_name: { type: 'string', nullable: true },
        },
      },
    },
    precio: { type: 'number', nullable: true },
    importe_total: { type: 'number', nullable: true },
    tipo_slug: { type: 'string' },
    max_personas: { type: 'number', nullable: true },
    suplemento_personas: { type: 'number', nullable: true },
    suplemento_precio: { type: 'number', nullable: true },
    contenido: { type: 'string', nullable: true },
    condiciones: { type: 'string', nullable: true },
    recaudado: { type: 'number' },
    gastos_gestion_tipo: { type: 'string' },
    gastos_gestion_cantidad: { type: 'number' },
    tipo_venta: { type: 'string' },
    estado: { type: 'string' },
    walkin: { type: 'boolean' },
    rechazada_by: { type: 'string' },
    rechazada_at: { type: 'number' },
    aceptada_by: { type: 'string' },
    aceptada_at: { type: 'number' },
    recepcionada_at: { type: 'number' },
    recepcionada_by: { type: 'string' },
    liberada_at: { type: 'number' },
    liberada_by: { type: 'string' },
    observaciones_recepcion: { type: 'string', nullable: true },
    language: { type: 'string', nullable: true },
    country: { type: 'string', nullable: true },
    browser: { type: 'string', nullable: true },
    device: { type: 'string', nullable: true },
    os: { type: 'string', nullable: true },
    sale_id: { type: 'string', nullable: true },
    payment_id: { type: 'string', nullable: true },
    idx: { type: 'string', nullable: true },
    purchase_id: { type: 'string', nullable: true },
    refunded: { type: 'number' },
    refunded_at: { type: 'number' },
    refunds: {
      type: 'array',
      items: {
        type: 'object',
        required: ['sale_id', 'usuario_id', 'importe', 'at'],
        properties: {
          sale_id: { type: 'string' },
          usuario_id: { type: 'string' },
          importe: { type: 'number' },
          at: { type: 'number' },
        },
      },
    },
    salida: { type: 'number' },
    notificaciones_ids: {
      type: 'array',
      items: { type: 'string' },
    },
    sms_sent: { type: 'number' },
    reconfirmacion: {
      type: 'object',
      required: ['reconfirmada_at', 'sent_at'],
      properties: {
        aceptada: { type: 'boolean', nullable: true },
        reconfirmada_at: { type: 'number' },
        sent_at: { type: 'number' },
      },
    },
    full_payment: { type: 'boolean' },
    notification_type: {
      type: 'array',
      items: { type: 'string' },
    },
    address: { type: 'string', nullable: true },
    personal_document_number: { type: 'string', nullable: true },
    personal_document_type: { type: 'string', nullable: true },
    minimum_spend: { type: 'number', nullable: true },
    cover_charge: { type: 'number', nullable: true },
    cookies_analytics: { type: 'boolean', nullable: true },
    is_fixed_price: { type: 'boolean', nullable: true },
    created_at: { type: 'number' },
    created_by: { type: 'string' },
    updated_at: { type: 'number' },
    updated_by: { type: 'string' },
    removed_at: { type: 'number' },
    removed_by: { type: 'string' },
  },
};
