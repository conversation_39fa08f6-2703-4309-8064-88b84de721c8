import { CryptoService } from '@discocil/fv-domain-library/domain';

import config from '@config/index';

import type { Booking } from '../../domain/entities/BookingEntity';

export class UrlsBooking {
  private readonly hash: string;

  private readonly urls = {
    cli: config.fv.apps.clients.url,
    connector: config.fv.services.connector.url,
  };

  constructor(private readonly booking: Booking) {
    this.hash = CryptoService.createHash(`${this.booking.id}-${this.booking.code}`, 'SHA-256');
  }

  getSuccess(): string {
    return `${this.urls.cli}/reservado-thanks/${this.booking.code}/${this.hash}`;
  }

  getDownload(): string {
    return `${this.urls.connector}/reservados/${this.booking.id}/reservados-${this.booking.code}.pdf`;
  }

  getActivate(): string {
    return `${this.urls.cli}/activar/${this.booking.getActivationCode()}/${this.hash}`;
  }

  getReconfirm(): string {
    return `${this.urls.cli}/confirm-booking/${this.booking.code}/${this.hash}`;
  }

  getCancel(): string {
    return `${this.urls.cli}/cancel-booking/${this.booking.code}/${this.hash}`;
  }

  getStatus(): string {
    return `${this.urls.cli}/status/${this.booking.code}/${this.hash}`;
  }
}
