import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type {
  BookingType,
  BookingTypeEither,
  BookingTypes,
  BookingTypesEither,
} from '../entities/BookingTypeEntity';

export interface BookingTypeRepository {
  find: (criteria: Criteria) => Promise<BookingTypeEither>;
  save: (bookingType: BookingType) => Promise<void>;
  saveMany: (bookingTypes: BookingTypes) => Promise<void>;
  search: (criteria: Criteria) => Promise<BookingTypesEither>;
}
