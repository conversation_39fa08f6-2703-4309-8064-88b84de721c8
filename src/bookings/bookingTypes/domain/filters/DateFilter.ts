import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { FvDate } from '@discocil/fv-domain-library/domain';
import type { BookingTypeKeys } from '../entities/BookingTypeEntity';

class FilterField extends FilterFieldBase<BookingTypeKeys> {}

export class DateFilter {
  private static readonly field: BookingTypeKeys = 'date';

  static buildEquals(value: FvDate): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(value.toSeconds());

    return new Filter(field, operator, filterValue);
  }

  static notExists(): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.exists();
    const filterValue = FilterValue.buildFalse();

    return new Filter(field, operator, filterValue);
  }
}
