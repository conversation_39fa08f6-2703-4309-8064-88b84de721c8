import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { BookingTypeKeys } from '../entities/BookingTypeEntity';

class FilterField extends FilterFieldBase<BookingTypeKeys> {}

export class SlugFilter {
  private static readonly field: BookingTypeKeys = 'slug';

  static buildEquals(value: string): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(value);

    return new Filter(field, operator, filterValue);
  }

  static buildIn(values: string[]): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.in();

    const filterValues: FilterValue[] = values.map(value => FilterValue.build(value));

    return new Filter(field, operator, filterValues);
  }
}
