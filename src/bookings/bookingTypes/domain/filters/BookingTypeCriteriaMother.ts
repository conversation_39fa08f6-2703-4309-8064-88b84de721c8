import {
  Criteria,
  Filters,
  Order,
} from '@discocil/fv-criteria-converter-library/domain';
import { UniqueEntityID } from '@discocil/fv-domain-library/domain';

import { EventIdFilter } from '@/cross-cutting/domain/filters/EventIdFilter';
import { IdFilter } from '@/cross-cutting/domain/filters/IdFilter';
import { OrganizationIdFilter } from '@/cross-cutting/domain/filters/OrganizationIdFilter';
import { RemovedAtFilter } from '@/cross-cutting/domain/filters/RemovedAtFilter';

import { DateFilter } from './DateFilter';
import { SlugFilter } from './SlugFilter';

import type { FvDate, IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { BookingTypeKeys } from '../entities/BookingTypeEntity';

export class BookingTypeCriteriaMother {
  private static readonly defaultOrderKey: BookingTypeKeys = 'order';

  static idToMatch(id: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(id));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static availablesToMatch(eventId: UniqueEntityID, organizationId: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(OrganizationIdFilter.buildEqual(organizationId));
    filters.add(RemovedAtFilter.buildActive());
    filters.add(EventIdFilter.buildEqual(eventId));

    return Criteria.build(filters, Order.asc<BookingTypeKeys>(this.defaultOrderKey));
  }

  static rateBookingMatchByEvent(organizationId: UniqueEntityID, eventId: UniqueEntityID, slug: string): Criteria {
    const filters = Filters.build();

    filters.add(OrganizationIdFilter.buildEqual(organizationId));
    filters.add(RemovedAtFilter.buildActive());
    filters.add(EventIdFilter.buildEqual(eventId));
    filters.add(SlugFilter.buildEquals(slug));

    return Criteria.build(filters, Order.asc<BookingTypeKeys>(this.defaultOrderKey));
  }

  static rateBookingMatchByDate(organizationId: UniqueEntityID, date: FvDate, slug: string): Criteria {
    const filters = Filters.build();
    const orFilter = Filters.buildOr();
    const andFilter = Filters.buildAnd();

    andFilter.add(DateFilter.notExists());
    andFilter.add(EventIdFilter.notExists());

    orFilter.add(DateFilter.buildEquals(date));
    orFilter.add(andFilter);

    filters.add(OrganizationIdFilter.buildEqual(organizationId));
    filters.add(RemovedAtFilter.buildActive());
    filters.add(SlugFilter.buildEquals(slug));
    filters.add(orFilter);

    return Criteria.build(filters, Order.asc<BookingTypeKeys>(this.defaultOrderKey));
  }

  static slugToMatch(slug: string): Criteria {
    const filters = Filters.build();

    filters.add(SlugFilter.buildEquals(slug));

    return Criteria.build(filters, Order.asc<BookingTypeKeys>(this.defaultOrderKey));
  }

  static slugsToMatchByEvent(slugsTypes: Set<IdPrimitive>, eventId: IdPrimitive): Criteria {
    const filters = Filters.build();

    filters.add(SlugFilter.buildIn(Array.from(slugsTypes)));
    filters.add(EventIdFilter.buildEqual(UniqueEntityID.build(eventId)));

    return Criteria.build(filters, Order.asc<BookingTypeKeys>(this.defaultOrderKey));
  }
}
