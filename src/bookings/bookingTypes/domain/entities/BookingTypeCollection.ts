import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { BookingType } from './BookingTypeEntity';

export class BookingTypeCollection {
  constructor(
    private readonly bookingTypes = new Map<IdPrimitive, BookingType[]>(),
  ) { }

  isEmpty(): boolean {
    return this.bookingTypes.size === 0;
  }

  isFull(): boolean {
    return this.bookingTypes.size > 0;
  }

  getAll(): Map<IdPrimitive, BookingType[]> {
    return this.bookingTypes;
  }

  find(key: string): BookingType[] {
    return this.bookingTypes.get(key) ?? [];
  }

  exists(key: string): boolean {
    return this.bookingTypes.has(key);
  }

  add(key: string, bookingTypes: BookingType[]): this {
    this.bookingTypes.set(key, bookingTypes);

    return this;
  }

  delete(key: string): this {
    this.bookingTypes.delete(key);

    return this;
  }

  getKeys(): MapIterator<IdPrimitive> {
    return this.bookingTypes.keys();
  }

  getValues(): MapIterator<BookingType[]> {
    return this.bookingTypes.values();
  }

  getArrayKeys(): IdPrimitive[] {
    return [...this.getKeys()];
  }

  getArrayValues(): BookingType[] {
    return [...this.getValues()].flat();
  }

  getMaxPrice(key: string): number {
    let maxPrice = -Infinity;

    const types = this.find(key);

    if (!types) {
      return maxPrice;
    }

    for (const type of types) {
      if (type.price.isEmpty()) {
        continue;
      }

      if (type.hasPriceGreaterThan(maxPrice)) {
        maxPrice = type.price.fold(() => maxPrice, item => item.amount);
      }
    }

    return maxPrice;
  }
}
