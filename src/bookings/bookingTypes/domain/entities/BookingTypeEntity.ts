import {
  AggregateRoot,
  FvDate,
  FvN<PERSON>ber,
  Maybe,
  Money,
  UniqueEntityID,
  left, right,
} from '@discocil/fv-domain-library/domain';

import { stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import type {
  BookingGgdd,
  BookingGgddPrimitives,
  BookingSupplement,
  BookingSupplementPrimitives,
} from '@/bookings/bookings/domain/entities/BookingEntity';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  CreatedAt,
  CreatedBy,
  DatePrimitive,
  EBookingTypeBailTypes,
  EBookingTypeCoverChargeTypes,
  ECurrency,
  EPaymentMethods,
  Either,
  IdPrimitive,
  InvalidDateError,
  MoneyEither,
  MoneyError,
  MoneyProps,
  NotFoundError,
  Primitives,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';

type BookingTypeBail = {
  bails: Maybe<number>;
  type: EBookingTypeBailTypes;
  methods: EPaymentMethods[];
  isCustomizable: boolean;
};

type BookingTypeCoverCharge = {
  type: EBookingTypeCoverChargeTypes;
  val: number;
};

export type BookingTypes = Map<IdPrimitive, BookingType>;

export type BookingTypeEither = Either<NotFoundError | MapperError | InvalidDateError | MoneyError, BookingType>;
export type BookingTypesEither = Either<NotFoundError | MapperError | InvalidDateError | MoneyError, BookingTypes>;

export type BookingTypePrimitives = Primitives<BookingType>;

export type BookingTypeKeys = keyof BookingType;

export class BookingType extends AggregateRoot {
  private constructor(
    id: UniqueEntityID,
    private readonly _organizationId: UniqueEntityID,
    readonly slug: string,
    private readonly _eventId: Maybe<UniqueEntityID>,
    private readonly _date: Maybe<FvDate>,
    readonly order: number,
    readonly name: string,
    readonly description: Maybe<string>,
    readonly colors: number[],
    private readonly _price: Maybe<Money>,
    readonly maxClients: number,
    private readonly _supplement: BookingSupplement,
    readonly content: Maybe<string>,
    readonly conditions: Maybe<string>,
    private readonly _ggdd: BookingGgdd,
    readonly bail: BookingTypeBail,
    readonly summary: Maybe<string>,
    readonly isComplete: boolean,
    readonly coverCharge: BookingTypeCoverCharge,
    readonly currency: ECurrency,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);
  }

  static build(primitives: BookingTypePrimitives): BookingTypeEither {
    const buildMoney = (amount: number, currency: ECurrency): MoneyEither => Money.build({ amount, currency });

    const id = UniqueEntityID.build(primitives.id);
    const organizationId = UniqueEntityID.build(primitives.organizationId);
    const eventId = primitives.eventId.map(item => UniqueEntityID.build(item));
    const date = primitives.date.map(item => FvDate.create(item));

    const description = primitives.description.map(item => item);
    const content = primitives.content.map(item => item);
    const conditions = primitives.conditions.map(item => item);
    const summary = primitives.summary.map(item => item);

    let price = Maybe.none<Money>();

    if (primitives.price.isDefined()) {
      const { amount, currency } = primitives.price.get();
      const priceResult = buildMoney(amount, currency);

      if (priceResult.isLeft()) {
        return left(priceResult.value);
      }

      price = Maybe.some(priceResult.value);
    }

    const supplement: BookingSupplement = {
      ...primitives.supplement,
      price: Maybe.none(),
      customers: primitives.supplement.customers.map(item => item),
    };

    if (primitives.supplement.price.isDefined()) {
      const { amount, currency } = primitives.supplement.price.get();
      const priceResult = buildMoney(amount, currency);

      if (priceResult.isLeft()) {
        return left(priceResult.value);
      }

      supplement.price = Maybe.some(priceResult.value);
    }

    const ggddAmountResult = buildMoney(primitives.ggdd.amount.amount, primitives.ggdd.amount.currency);

    if (ggddAmountResult.isLeft()) {
      return left(ggddAmountResult.value);
    }

    const ggdd: BookingGgdd = {
      ...primitives.ggdd,
      amount: ggddAmountResult.value,
    };

    const bail: BookingTypeBail = {
      ...primitives.bail,
      bails: primitives.bail.bails.map(item => item),
    };

    const stamps = stampValueObjects(primitives);

    const entity = new BookingType(
      id,
      organizationId,
      primitives.slug,
      eventId,
      date,
      primitives.order,
      primitives.name,
      description,
      primitives.colors,
      price,
      primitives.maxClients,
      supplement,
      content,
      conditions,
      ggdd,
      bail,
      summary,
      primitives.isComplete,
      primitives.coverCharge,
      primitives.currency,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return right(entity);
  }

  get organizationId(): IdPrimitive {
    return this._organizationId.toPrimitive();
  }

  get eventId(): Maybe<IdPrimitive> {
    return this._eventId.map(item => item.toPrimitive());
  }

  get date(): Maybe<DatePrimitive> {
    return this._date.map(item => item.toPrimitive());
  }

  get supplement(): BookingSupplementPrimitives {
    return {
      ...this._supplement,
      price: this._supplement.price.map(item => item.toPrimitive()),
    };
  }

  get ggdd(): BookingGgddPrimitives {
    return {
      ...this._ggdd,
      amount: this._ggdd.amount.toPrimitive(),
    };
  }

  get price(): Maybe<MoneyProps> {
    return this._price.map(item => item.toPrimitive());
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }

  hasPriceGreaterThan(otherPrice: number): boolean {
    if (this.price.isEmpty()) {
      return false;
    }

    return FvNumber.build(this._price.get().amount).isGreaterThan(otherPrice);
  }

  getDateInDateFormat(): Maybe<string> {
    return this._date.map(item => item.toFormat({ style: 'date' }));
  }

  hasPrice(): boolean {
    return this._price.isDefined();
  }

  getPriceAmount(): number {
    return this._price.fold(
      () => 0,
      item => item.toDecimal(),
    );
  }

  toPrimitives(): BookingTypePrimitives {
    return {
      id: this.id,
      organizationId: this.organizationId,
      eventId: this.eventId,
      date: this.date,
      slug: this.slug,
      order: this.order,
      name: this.name,
      description: this.description,
      colors: this.colors,
      price: this.price,
      maxClients: this.maxClients,
      supplement: this.supplement,
      content: this.content,
      conditions: this.conditions,
      ggdd: this.ggdd,
      bail: this.bail,
      summary: this.summary,
      isComplete: this.isComplete,
      coverCharge: this.coverCharge,
      currency: this.currency,
      createdAt: this.createdAt,
      createdBy: this.createdBy,
      updatedAt: this.updatedAt,
      updatedBy: this.updatedBy,
      removedAt: this.removedAt,
      removedBy: this.removedBy,
    };
  }
}
