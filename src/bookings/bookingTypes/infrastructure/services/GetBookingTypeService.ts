import {
  contextualizeError,
  FvDate,
  left,
  NotFoundError,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { BookingType } from '@/bookings/bookingTypes/domain/entities/BookingTypeEntity';
import { BookingTypeCriteriaMother } from '@/bookings/bookingTypes/domain/filters/BookingTypeCriteriaMother';

import type { Booking } from '@/bookings/bookings/domain/entities/BookingEntity';
import type { BookingTypeRepository } from '@/bookings/bookingTypes/domain/contracts/BookingTypeRepository';
import type { BookingTypeEither } from '@/bookings/bookingTypes/domain/entities/BookingTypeEntity';
import type { IGetBookingTypeService } from '../../domain/contracts/GetBookingTypeContract';

export class GetBookingTypeService implements IGetBookingTypeService {
  constructor(private readonly bookingTypeRepository: BookingTypeRepository) { }

  @contextualizeError()
  async execute(booking: Booking): Promise<BookingTypeEither> {
    if (booking.slugType.isEmpty()) {
      return left(NotFoundError.build({ context: BookingType.name }));
    }

    const organizationId = UniqueEntityID.build(booking.organizationId);
    const slugType = booking.slugType.get();

    if (booking.eventId.isDefined()) {
      const eventId = UniqueEntityID.build(booking.eventId.get());
      const criteria = BookingTypeCriteriaMother.rateBookingMatchByEvent(organizationId, eventId, slugType);

      return await this.bookingTypeRepository.find(criteria);
    }

    if (booking.date.isDefined()) {
      const date = FvDate.create(booking.date.get());
      const criteria = BookingTypeCriteriaMother.rateBookingMatchByDate(organizationId, date, slugType);

      return await this.bookingTypeRepository.find(criteria);
    }

    return left(NotFoundError.build({ context: this.constructor.name, target: BookingType.name }));
  }
}
