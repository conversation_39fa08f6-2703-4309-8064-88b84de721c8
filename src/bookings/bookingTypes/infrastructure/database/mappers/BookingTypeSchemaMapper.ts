

import type { BookingType } from '@/bookings/bookingTypes/domain/entities/BookingTypeEntity';
import type { BookingTypeOriginalSchemaType } from '../schemas/BookingTypeSchemaType';

export class BookingTypeSchemaMapper {
  static execute(bookingType: BookingType): BookingTypeOriginalSchemaType {
    return {
      _id: bookingType.id,
      negocio_id: bookingType.organizationId,
      slug: bookingType.slug,
      evento_id: bookingType.eventId.fold(() => undefined, item => item),
      fecha: bookingType.getDateInDateFormat().fold(() => '', item => item),
      orden: bookingType.order,
      nombre: bookingType.name,
      descripcion: bookingType.description.fold(() => undefined, item => item),
      color: bookingType.colors,
      precio: bookingType.price.fold(() => undefined, item => item.amount),
      max_personas: bookingType.maxClients,
      suplemento_personas: bookingType.supplement.customers.fold(() => undefined, item => item),
      suplemento_precio: bookingType.supplement.price.fold(() => undefined, item => item.amount),
      contenido: bookingType.content.fold(() => undefined, item => item),
      condiciones: bookingType.conditions.fold(() => undefined, item => item),
      gastos_gestion_tipo: bookingType.ggdd.type,
      gastos_gestion_cantidad: bookingType.ggdd.amount.amount,
      fianza: bookingType.bail.bails.fold(() => undefined, item => item),
      fianza_tipo: bookingType.bail.type,
      fianza_metodos: bookingType.bail.methods,
      fianza_personalizable: bookingType.bail.isCustomizable,
      resumen: bookingType.summary.fold(() => undefined, item => item),
      full_payment: bookingType.isComplete,
      cover_charge_type: bookingType.coverCharge.type,
      cover_charge_value: bookingType.coverCharge.val,
      created_at: bookingType.createdAt,
      created_by: bookingType.createdBy,
      updated_at: bookingType.updatedAt,
      updated_by: bookingType.updatedBy,
      removed_at: bookingType.removedAt,
      removed_by: bookingType.removedBy,
    };
  }
}
