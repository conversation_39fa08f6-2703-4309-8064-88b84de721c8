import type { JSONSchemaType } from 'ajv';
import type { BookingTypeOriginalSchemaType } from '../schemas/BookingTypeSchemaType';

export const bookingTypeValidationSchema: JSONSchemaType<BookingTypeOriginalSchemaType> = {
  title: 'Booking Type Json Schema Validation',
  required: ['_id', 'negocio_id', 'slug', 'nombre'],
  type: 'object',
  properties: {
    _id: { type: 'string' },
    negocio_id: { type: 'string' },
    slug: { type: 'string' },
    evento_id: { type: 'string', nullable: true },
    fecha: { type: 'string', nullable: true },
    orden: { type: 'number' },
    nombre: { type: 'string' },
    descripcion: { type: 'string', nullable: true },
    color: {
      type: 'array',
      nullable: true,
      items: { type: 'number' },
    },
    precio: { type: 'number', nullable: true },
    max_personas: { type: 'number' },
    suplemento_personas: { type: 'number', nullable: true },
    suplemento_precio: { type: 'number', nullable: true },
    contenido: { type: 'string', nullable: true },
    condiciones: { type: 'string', nullable: true },
    gastos_gestion_tipo: { type: 'string' },
    gastos_gestion_cantidad: { type: 'number' },
    fianza: { type: 'number', nullable: true },
    fianza_tipo: { type: 'string' },
    fianza_metodos: {
      type: 'array',
      items: { type: 'string' },
    },
    fianza_personalizable: { type: 'boolean' },
    resumen: { type: 'string', nullable: true },
    full_payment: { type: 'boolean' },
    cover_charge_type: { type: 'string' },
    cover_charge_value: { type: 'number' },
    created_at: { type: 'number' },
    created_by: { type: 'string' },
    updated_at: { type: 'number' },
    updated_by: { type: 'string' },
    removed_at: { type: 'number' },
    removed_by: { type: 'string' },
  },
};
