import {
  EBookingTypeBailTypes,
  EBookingTypeCoverChargeTypes,
  ECurrency,
  EGGDD,
  EPaymentMethods,
  FvDate,
  FvN<PERSON>ber,
  left, Maybe,
} from '@discocil/fv-domain-library/domain';
import { container } from 'tsyringe';

import { BookingType } from '@/bookings/bookingTypes/domain/entities/BookingTypeEntity';
import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { MapperError } from '@/cross-cutting/domain/errors/MapperError';

import { bookingTypeValidationSchema } from './BookingTypeSchemaValidation';

import type { BookingTypeEither, BookingTypePrimitives } from '@/bookings/bookingTypes/domain/entities/BookingTypeEntity';
import type { DatePrimitive } from '@discocil/fv-domain-library/domain';
import type Ajv from 'ajv';
import type { BookingTypeSchemaType } from '../schemas/BookingTypeSchemaType';

export class BookingTypeMapper {
  static execute(data: BookingTypeSchemaType): BookingTypeEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(bookingTypeValidationSchema);

    if (!validate(data) || validate.errors) {
      MapperError.logger({
        context: BookingTypeMapper.name,
        data,
        target: validate.errors,
      });

      return BookingTypeSoftMapper.execute(data);
    }


    return this.buildEntity(data);
  }

  static buildEntity(data: BookingTypeSchemaType): BookingTypeEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(bookingTypeValidationSchema);

    if (!validate(data) || validate.errors) {
      return left(MapperError.build({
        context: BookingTypeMapper.name,
        data,
        target: validate.errors,
      }));
    }

    let date = Maybe.none<DatePrimitive>();

    if (data.fecha) {
      const dateResult = FvDate.createFromFormat(data.fecha);

      if (dateResult.isLeft()) {
        return left(dateResult.value);
      }

      date = Maybe.some(dateResult.value.value);
    }

    const currency = data.event?.currency ?? data.organization?.currency ?? ECurrency.EUR;

    return BookingType.build({
      id: data._id,
      organizationId: data.negocio_id,
      slug: data.slug,
      eventId: Maybe.fromValue(data.evento_id),
      date,
      order: data.orden ?? 0,
      name: data.nombre,
      description: Maybe.fromValue(data.descripcion),
      colors: data.color ?? [],
      price: FvNumber.is(data.precio)
        ? Maybe.fromValue({
          amount: data.precio,
          currency,
        })
        : Maybe.none(),
      maxClients: data.max_personas ?? 1,
      supplement: this.makeSupplement(data, currency),
      content: Maybe.fromValue(data.contenido),
      conditions: Maybe.fromValue(data.condiciones),
      ggdd: this.makeGgdd(data, currency),
      bail: this.makeBail(data),
      summary: Maybe.fromValue(data.resumen),
      isComplete: data.full_payment ?? false,
      coverCharge: this.makeCoverCharge(data),
      currency,
      createdAt: data.created_at,
      createdBy: data.created_by,
      updatedAt: data.updated_at,
      updatedBy: data.updated_by,
      removedAt: data.removed_at,
      removedBy: data.removed_by,
    });
  }

  protected static makeSupplement(data: BookingTypeSchemaType, currency: ECurrency): BookingTypePrimitives['supplement'] {
    return {
      customers: Maybe.fromValue(data.suplemento_personas),
      price: data.suplemento_precio
        ? Maybe.fromValue({
          amount: data.suplemento_precio,
          currency,
        })
        : Maybe.none(),
    };
  }

  protected static makeGgdd(data: BookingTypeSchemaType, currency: ECurrency): BookingTypePrimitives['ggdd'] {
    return {
      type: data.gastos_gestion_tipo ?? EGGDD.FIX,
      amount: {
        amount: data.gastos_gestion_cantidad ?? 0,
        currency,
      },
    };
  }

  protected static makeBail(data: BookingTypeSchemaType): BookingTypePrimitives['bail'] {
    return {
      bails: Maybe.fromValue(data.fianza),
      type: data.fianza_tipo ?? EBookingTypeBailTypes.THERE_IS_NOT,
      methods: data.fianza_metodos ?? [EPaymentMethods.PAYMENT_PLATFORM],
      isCustomizable: data.fianza_personalizable ?? false,
    };
  }

  protected static makeCoverCharge(data: BookingTypeSchemaType): BookingTypePrimitives['coverCharge'] {
    return {
      type: data.cover_charge_type ?? EBookingTypeCoverChargeTypes.NO_COVER_CHARGE,
      val: data.cover_charge_value ?? 0,
    };
  }
}

export class BookingTypeSoftMapper extends BookingTypeMapper {
  static execute(data: BookingTypeSchemaType): BookingTypeEither {
    return this.buildEntity({
      ...data,
      _id: data._id,
      negocio_id: data.negocio_id ?? null,
      slug: data.slug ?? null,
      nombre: data.nombre ?? null,
    });
  }
}
