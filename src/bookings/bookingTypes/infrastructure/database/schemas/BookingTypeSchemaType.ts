import type {
  EBookingTypeBailTypes,
  EBookingTypeCoverChargeTypes,
  ECurrency,
  EGGDD,
  EPaymentMethods,
} from '@discocil/fv-domain-library/domain';

export type BookingTypeSchemaType = {
  _id: string;
  negocio_id: string;
  slug: string;
  evento_id?: string;
  fecha?: string;
  orden: number;
  nombre: string;
  descripcion?: string;
  color?: number[];
  precio?: number;
  max_personas: number;
  suplemento_personas?: number;
  suplemento_precio?: number;
  contenido?: string;
  condiciones?: string;
  gastos_gestion_tipo: EGGDD;
  gastos_gestion_cantidad: number;
  fianza?: number;
  fianza_tipo: EBookingTypeBailTypes;
  fianza_metodos: EPaymentMethods[];
  fianza_personalizable: boolean;
  resumen?: string;
  full_payment: boolean;
  cover_charge_type: EBookingTypeCoverChargeTypes;
  cover_charge_value: number;
  event: {
    currency: ECurrency;
  };
  organization: {
    currency: ECurrency;
  };
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
  removed_at: number;
  removed_by: string;
};

export type BookingTypeOriginalSchemaType = Omit<BookingTypeSchemaType, 'event' | 'organization'>;
