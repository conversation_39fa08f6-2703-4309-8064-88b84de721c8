import {
  EBookingTypeBailTypes,
  EBookingTypeCoverChargeTypes,
  EGGDD,
  EPaymentMethods,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

export const bookingTypeSchema = {
  _id: {
    type: String,
    required: true,
  },
  negocio_id: {
    type: String,
    required: true,
    index: true,
  },
  slug: {
    type: String,
    required: true,
    index: true,
  },
  evento_id: {
    type: String,
    index: true,
  },
  fecha: {
    type: String,
    index: true,
  },
  orden: {
    type: Number,
    default: 0,
  },
  nombre: {
    type: String,
    required: true,
  },
  descripcion: { type: String },
  color: { type: [Number] },
  precio: { type: Number },
  max_personas: {
    type: Number,
    default: 1,
  },
  suplemento_personas: { type: Number },
  suplemento_precio: { type: Number },
  contenido: { type: String },
  condiciones: { type: String },
  gastos_gestion_tipo: {
    type: String,
    enum: Object.values(EGGDD),
    default: EGGDD.FIX,
  },
  gastos_gestion_cantidad: {
    type: Number,
    default: 0,
  },
  fianza: { type: Number },
  fianza_tipo: {
    type: String,
    default: EBookingTypeBailTypes.THERE_IS_NOT,
    enum: Object.values(EBookingTypeBailTypes),
  },
  fianza_metodos: {
    type: [String],
    default: [EPaymentMethods.PAYMENT_PLATFORM],
    enum: Object.values(EPaymentMethods),
  },
  fianza_personalizable: {
    type: Boolean,
    default: false,
  },
  resumen: { type: String },
  full_payment: {
    type: Boolean,
    default: false,
  },
  cover_charge_type: {
    type: String,
    default: EBookingTypeCoverChargeTypes.NO_COVER_CHARGE,
    enum: Object.values(EBookingTypeCoverChargeTypes),
  },
  cover_charge_value: {
    type: Number,
    default: 0,
  },
  created_at: { type: Number },
  created_by: { type: String },
  updated_at: { type: Number },
  updated_by: { type: String },
  removed_at: {
    type: Number,
    index: true,
  },
  removed_by: { type: String },
};

export const BookingTypeSchema = new Schema(bookingTypeSchema);
