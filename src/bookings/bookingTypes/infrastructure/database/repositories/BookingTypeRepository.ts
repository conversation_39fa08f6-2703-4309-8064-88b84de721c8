import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { BookingType } from '@/bookings/bookingTypes/domain/entities/BookingTypeEntity';
import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';

import { BookingTypeMapper } from '../mappers/BookingTypeMapper';
import { BookingTypeSchemaMapper } from '../mappers/BookingTypeSchemaMapper';
import { BookingTypeSchema } from '../schemas/BookingTypeSchema';

import type { BookingTypeRepository } from '@/bookings/bookingTypes/domain/contracts/BookingTypeRepository';
import type {
  BookingTypeEither,
  BookingTypeKeys,
  BookingTypes,
  BookingTypesEither,
} from '@/bookings/bookingTypes/domain/entities/BookingTypeEntity';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { Schema } from 'mongoose';
import type { BookingTypeSchemaType } from '../schemas/BookingTypeSchemaType';

type PropertiesMapper = Partial<Record<BookingTypeKeys, keyof BookingTypeSchemaType>>;

export class BookingTypeMongoRepository extends MongoRepository implements BookingTypeRepository {
  protected getSchema(): Schema {
    return BookingTypeSchema;
  }

  protected getDBName(): string {
    return EDBNames.BOOKING;
  }

  protected getModel(): string {
    return 'tipos';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      slug: 'slug',
      organizationId: 'negocio_id',
      eventId: 'evento_id',
      order: 'orden',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<BookingTypeEither> {
    criteria.setPage(1).setPerPage(1);

    const queryResponse = (await this.customQueryFinder<BookingTypeSchemaType[]>(criteria)).shift();

    return queryResponse
      ? BookingTypeMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: BookingType.name }));
  }

  async save(bookingType: BookingType): Promise<void> {
    const toSave = BookingTypeSchemaMapper.execute(bookingType);

    const filter = { _id: bookingType.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(bookingTypes: BookingTypes): Promise<void> {
    const connection = await this.getConnection();
    const models: Array<Partial<BookingTypeSchemaType>> = [];

    bookingTypes.forEach((bookingType: BookingType) => models.push(BookingTypeSchemaMapper.execute(bookingType)));

    await connection.insertMany(models);
  }

  async search(criteria: Criteria): Promise<BookingTypesEither> {
    const response = new Map<IdPrimitive, BookingType>();

    const queryResponse = await this.customQueryFinder<BookingTypeSchemaType[]>(criteria);

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const bookingTypeResult = BookingTypeMapper.execute(model);

      if (bookingTypeResult.isLeft()) {
        return left(bookingTypeResult.value);
      }

      const bookingType = bookingTypeResult.value;

      response.set(bookingType.id, bookingType);
    }

    return right(response);
  }

  private async customQueryFinder<T>(criteria: Criteria): Promise<T> {
    const connection = await this.getConnection();
    const pipeline = this.criteriaConverter.toPipeline(criteria);

    return connection.aggregate([
      ...pipeline,
      {
        $lookup: {
          from: 'eventos',
          let: { eventId: '$evento_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$eventId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'event',
        },
      },
      {
        $lookup: {
          from: 'negocios',
          let: { organizationId: '$negocio_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$organizationId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'organization',
        },
      },
      {
        $project: {
          main: '$$ROOT',
          eventObj: { $arrayElemAt: ['$event', 0] },
          organizationObj: { $arrayElemAt: ['$organization', 0] },
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              '$main',
              {
                event: { currency: { $ifNull: ['$eventObj.currency', null] } },
                organization: { currency: { $ifNull: ['$organizationObj.currency', null] } },
              },
            ],
          },
        },
      },
    ]) as unknown as T;
  }
}
