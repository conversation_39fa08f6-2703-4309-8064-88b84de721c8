import { container, instanceCachingFactory } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';

import { BookingTypeDependencyIdentifier } from '../../domain/dependencyIdentifier/BookingTypeDependencyIdentifier';
import { BookingTypeMongoRepository } from '../database/repositories/BookingTypeRepository';
import { GetBookingTypeService } from '../services/GetBookingTypeService';

import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';

export const BookingTypeContainer = {
  register: (): void => {
    container.register(BookingTypeDependencyIdentifier.BookingTypeRepository, {
      useFactory: instanceCachingFactory((container) => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

        return new BookingTypeMongoRepository(dbConnection);
      }),
    });

    container.register(BookingTypeDependencyIdentifier.IGetBookingTypeFromSaleService, {
      useFactory: instanceCachingFactory((container) => {
        return new GetBookingTypeService(container.resolve(BookingTypeDependencyIdentifier.BookingTypeRepository));
      }),
    });
  },
};
