import { Paginator } from '@discocil/fv-criteria-converter-library/domain';
import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { BookingZone } from '@/bookings/bookingZones/domain/entities/BookingZoneEntity';
import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';

import { BookingZoneMapper } from '../mappers/BookingZoneMapper';
import { BookingZoneSchemaMapper } from '../mappers/BookingZoneSchemaMapper';
import { BookingZoneSchema } from '../schemas/BookingZoneSchema';

import type { BookingZoneRepository } from '@/bookings/bookingZones/domain/contracts/BookingZoneRepository';
import type {
  BookingZoneEither,
  BookingZoneKeys,
  BookingZones,
  BookingZonesEither,
} from '@/bookings/bookingZones/domain/entities/BookingZoneEntity';
import type { Criteria, RequiredCriteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { Schema } from 'mongoose';
import type { BookingZoneSchemaType } from '../schemas/BookingZoneSchemaType';

type PropertiesMapper = Partial<Record< BookingZoneKeys, keyof BookingZoneSchemaType>>;

export class BookingZoneMongoRepository extends MongoRepository implements BookingZoneRepository {
  protected getSchema(): Schema {
    return BookingZoneSchema;
  }

  protected getDBName(): string {
    return EDBNames.BOOKING;
  }

  protected getModel(): string {
    return 'zonas';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      eventId: 'evento_id',
      isActive: 'activa',
      canBooking: 'puede_reservar',
      removedAt: 'removed_at',
      order: 'orden',
    };
  }

  async find(criteria: Criteria): Promise<BookingZoneEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<BookingZoneSchemaType>();

    return queryResponse
      ? BookingZoneMapper.execute(queryResponse)
      : left(NotFoundError.build({ context: this.constructor.name, target: BookingZone.name }));
  }

  async save(bookingZone: BookingZone): Promise<void> {
    const toSave = BookingZoneSchemaMapper.execute(bookingZone);

    const filter = { _id: bookingZone.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }

  async saveMany(bookingZones: BookingZones): Promise<void> {
    const connection = await this.getConnection();
    const models: Array<Partial<BookingZoneSchemaType>> = [];

    bookingZones.forEach((bookingZone: BookingZone) => models.push(BookingZoneSchemaMapper.execute(bookingZone)));

    await connection.insertMany(models);
  }

  async search(criteria: Criteria): Promise<BookingZonesEither> {
    const bookingZones = new Map<IdPrimitive, BookingZone>();

    const connection = await this.getConnection();
    const pipeline = this.criteriaConverter.toPipeline(criteria);

    const queryResponse = await connection.aggregate<BookingZoneSchemaType>([
      ...pipeline,
      {
        $lookup: {
          from: 'cache_zonas',
          let: {
            organizationId: '$negocio_id',
            eventId: '$evento_id',
            zoneSlug: '$slug',
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$$organizationId', '$negocio_id'] },
                    { $eq: ['$$eventId', '$evento_id'] },
                    { $eq: ['$$zoneSlug', '$zona_slug'] },
                    { $eq: ['$removed_at', 0] },
                  ],
                },
              },
            },
            { $project: { completa: 1 } },
          ],
          as: 'cacheInfo',
        },
      },
    ]);

    if (queryResponse.length === 0) {
      return right({ bookingZones });
    }

    for (const model of queryResponse) {
      const bookingZoneResult = BookingZoneMapper.execute(model);

      if (bookingZoneResult.isLeft()) {
        return left(bookingZoneResult.value);
      }

      const bookingZone = bookingZoneResult.value;

      bookingZones.set(bookingZone.id, bookingZone);
    }

    if (criteria.pagination) {
      const connection = await this.getConnection();
      const filterQuery = this.criteriaConverter.convert(criteria);
      const total = await connection.countDocuments(filterQuery.filter);
      const requiredCriteria = criteria as RequiredCriteria;

      return right({
        bookingZones,
        paginationMetadata: Paginator.execute(total, requiredCriteria, queryResponse.length),
      });
    }

    return right({ bookingZones });
  }
}
