import type { JSONSchemaType } from 'ajv';
import type { BookingZoneOriginalSchemaType } from '../schemas/BookingZoneSchemaType';

export const bookingZoneValidationSchema: JSONSchemaType<BookingZoneOriginalSchemaType> = {
  title: 'Booking Zone Json Schema Validation',
  required: ['_id', 'negocio_id', 'nombre', 'slug'],
  type: 'object',
  properties: {
    _id: { type: 'string' },
    negocio_id: { type: 'string' },
    evento_id: { type: 'string', nullable: true },
    fecha: { type: 'string', nullable: true },
    slug: { type: 'string' },
    orden: { type: 'number' },
    nombre: { type: 'string' },
    descripcion: { type: 'string', nullable: true },
    imagen: { type: 'string', nullable: true },
    imagen_plano: { type: 'string', nullable: true },
    puede_reservar: {
      type: 'array',
      items: { type: 'string' },
    },
    puede_seleccionar_espacio: {
      type: 'array',
      items: { type: 'string' },
    },
    mostrar_plano: { type: 'boolean' },
    mostrar_espacios_plano: { type: 'boolean' },
    autoaceptar: {
      type: 'array',
      items: { type: 'string' },
    },
    activa: { type: 'boolean' },
    dias: {
      type: 'array',
      items: { type: 'number' },
    },
    limite: { type: 'number' },
    limite_campo: { type: 'string' },
    horas: {
      type: 'array',
      items: {
        type: 'object',
        required: ['format', 'selected'],
        properties: {
          format: { type: 'string' },
          selected: { type: 'boolean' },
        },
      },
    },
    tiempo_reserva: { type: 'number' },
    espacios: {
      type: 'array',
      items: {
        type: 'object',
        required: ['_id', 'tipo', 'nombre'],
        properties: {
          _id: { type: 'string' },
          tipo: { type: 'string' },
          nombre: { type: 'string' },
          nombre_integracion: { type: 'string', nullable: true },
          capacidad: { type: 'number' },
          minimo: { type: 'number' },
          imagen: { type: 'string', nullable: true },
          bloqueado: { type: 'boolean' },
          bloqueado_web: { type: 'boolean' },
          codigo_integracion: { type: 'string', nullable: true },
          top: { type: 'number', nullable: true },
          left: { type: 'number', nullable: true },
          width: { type: 'number', nullable: true },
          height: { type: 'number', nullable: true },
          radius: { type: 'number', nullable: true },
          scale: { type: 'number', nullable: true },
          rotate: { type: 'number', nullable: true },
          tipos_slugs: {
            type: 'array',
            items: { type: 'string' },
          },
          combinaciones_ids: {
            type: 'array',
            items: { type: 'string' },
          },
          removed_at: { type: 'number' },
          removed_by: { type: 'string' },
        },
      },
    },
    tipos_slugs: {
      type: 'array',
      items: { type: 'string' },
    },
    created_at: { type: 'number' },
    created_by: { type: 'string' },
    updated_at: { type: 'number' },
    updated_by: { type: 'string' },
    removed_at: { type: 'number' },
    removed_by: { type: 'string' },
  },
};
