import { FvDate } from '@discocil/fv-domain-library/domain';

import type { BookingZone } from '@/bookings/bookingZones/domain/entities/BookingZoneEntity';
import type { BookingZoneOriginalSchemaType } from '../schemas/BookingZoneSchemaType';

export class BookingZoneSchemaMapper {
  static execute(bookingZone: BookingZone): BookingZoneOriginalSchemaType {
    return {
      _id: bookingZone.id,
      negocio_id: bookingZone.organizationId,
      evento_id: bookingZone.eventId.fold(() => undefined, item => item),
      fecha: bookingZone.getDateInIso8601WithoutSeparatorFormat().fold(() => '', item => item),
      slug: bookingZone.slug,
      orden: bookingZone.order,
      nombre: bookingZone.name,
      descripcion: bookingZone.description.fold(() => undefined, item => item),
      imagen: bookingZone.image.fold(() => undefined, item => item),
      imagen_plano: bookingZone.imageMap.fold(() => undefined, item => item),
      puede_reservar: bookingZone.canBooking,
      puede_seleccionar_espacio: bookingZone.canSelectSpace,
      mostrar_plano: bookingZone.showMap,
      mostrar_espacios_plano: bookingZone.showSpaceMap,
      autoaceptar: bookingZone.autoaccept,
      activa: bookingZone.isActive,
      dias: bookingZone.days,
      limite: bookingZone.limit,
      limite_campo: bookingZone.fieldLimit,
      horas: bookingZone.hours.map((item) => {
        return {
          format: FvDate.create(item.time).toFormat({ style: 'timeShort' }) ?? '',
          selected: item.selected,
        };
      }),
      tiempo_reserva: bookingZone.getBookingTimeInSeconds().fold(() => 0, item => item),
      espacios: [...bookingZone.spaces.values()].map((space) => {
        return {
          _id: space.id,
          tipo: space.type,
          nombre: space.name,
          nombre_integracion: space.integration.fold(() => undefined, item => item.name),
          capacidad: space.capacity,
          minimo: space.minimum,
          imagen: space.image.fold(() => undefined, item => item),
          bloqueado: space.blocked,
          bloqueado_web: space.blockedWeb,
          codigo_integracion: space.integration.fold(() => undefined, item => item.code),
          top: space.top.fold(() => undefined, item => item),
          left: space.left.fold(() => undefined, item => item),
          width: space.width.fold(() => undefined, item => item),
          height: space.height.fold(() => undefined, item => item),
          radius: space.radius.fold(() => undefined, item => item),
          scale: space.scale.fold(() => undefined, item => item),
          rotate: space.rotate.fold(() => undefined, item => item),
          tipos_slugs: space.slugsTypes,
          combinaciones_ids: space.combinationsIds,
          removed_at: space.removedAt,
          removed_by: space.removedBy,
        };
      }),
      tipos_slugs: bookingZone.slugsTypes,
      created_at: bookingZone.createdAt,
      created_by: bookingZone.createdBy,
      updated_at: bookingZone.updatedAt,
      updated_by: bookingZone.updatedBy,
      removed_at: bookingZone.removedAt,
      removed_by: bookingZone.removedBy,
    };
  }
}
