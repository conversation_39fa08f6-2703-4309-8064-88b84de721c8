import {
  EBookingCriteriaCompleteZone,
  EBookingPeople,
  EBookingSpaceTypes,
  EFormats,
  FvDate,
  left,
  Maybe,
} from '@discocil/fv-domain-library/domain';
import { container } from 'tsyringe';

import { BookingZone } from '@/bookings/bookingZones/domain/entities/BookingZoneEntity';
import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { MapperError } from '@/cross-cutting/domain/errors/MapperError';

import { bookingZoneValidationSchema } from './BookingZoneSchemaValidation';

import type {
  BookingSpacePrimitives,
  BookingSpacesPrimitives,
  BookingZoneEither,
  BookingZonePrimitives,
} from '@/bookings/bookingZones/domain/entities/BookingZoneEntity';
import type { DatePrimitive } from '@discocil/fv-domain-library/domain';
import type Ajv from 'ajv';
import type {
  BookingSpaceSchemaType,
  BookingZoneSchemaType,
} from '../schemas/BookingZoneSchemaType';

export class BookingZoneMapper {
  static execute(data: BookingZoneSchemaType): BookingZoneEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(bookingZoneValidationSchema);

    if (!validate(data) || validate.errors) {
      MapperError.logger({
        context: BookingZoneMapper.name,
        data,
        target: validate.errors,
      });

      return BookingZoneSoftMapper.execute(data);
    }

    return this.buildEntity(data);
  }

  static buildEntity(data: BookingZoneSchemaType): BookingZoneEither {
    const cacheInfo = data.cacheInfo?.slice().shift();
    const isComplete = cacheInfo?.completa ?? false;

    let date = Maybe.none<DatePrimitive>();

    if (data.fecha) {
      const dateResult = FvDate.createFromFormat(data.fecha, EFormats.ISO8601_WITHOUT_SEPARATOR);

      if (dateResult.isLeft()) {
        return left(dateResult.value);
      }

      date = Maybe.some(dateResult.value.value);
    }

    const hours: BookingZonePrimitives['hours'] = [];

    for (const hora of data.horas) {
      const dateResult = FvDate.createFromFormat(hora.format, EFormats.TIME_SHORT);

      if (dateResult.isLeft()) {
        return left(dateResult.value);
      }

      hours.push({
        time: dateResult.value.toPrimitive(),
        selected: hora.selected,
      });
    }

    const spaces: BookingSpacesPrimitives = new Map<string, BookingSpacePrimitives>();

    (data.espacios ?? []).forEach((space) => {
      const spacePrimitive = this.makeSpace(space);

      spaces.set(spacePrimitive.id, spacePrimitive);
    });

    return BookingZone.build({
      id: data._id,
      organizationId: data.negocio_id,
      eventId: Maybe.fromValue(data.evento_id),
      date,
      slug: data.slug,
      order: data.orden,
      name: data.nombre,
      description: Maybe.fromValue(data.descripcion),
      image: Maybe.fromValue(data.imagen),
      imageMap: Maybe.fromValue(data.imagen_plano),
      canBooking: data.puede_reservar,
      canSelectSpace: data.puede_seleccionar_espacio,
      showMap: data.mostrar_plano ?? false,
      showSpaceMap: data.mostrar_espacios_plano ?? false,
      autoaccept: data.autoaceptar ?? [],
      isActive: data.activa ?? true,
      days: data.dias,
      limit: data.limite ?? 0,
      fieldLimit: data.limite_campo ?? EBookingCriteriaCompleteZone.NEVER,
      hours,
      bookingTime: data.tiempo_reserva
        ? Maybe.some(FvDate.createFromSeconds(data.tiempo_reserva).toPrimitive())
        : Maybe.none<Date>(),
      spaces,
      slugsTypes: data.tipos_slugs ?? [],
      isComplete,
      createdAt: data.created_at,
      createdBy: data.created_by,
      updatedAt: data.updated_at,
      updatedBy: data.updated_by,
      removedAt: data.removed_at,
      removedBy: data.removed_by,
    });
  }

  protected static makeSpace(space: BookingSpaceSchemaType): BookingSpacePrimitives {
    const makeIntegration = (name: string | undefined, code: string | undefined): BookingSpacePrimitives['integration'] => {
      if (name && code) {
        return Maybe.some({ name, code });
      }

      return Maybe.none();
    };

    return {
      id: space._id,
      type: space.tipo ?? EBookingSpaceTypes.TABLE,
      name: space.nombre,
      integration: makeIntegration(space.nombre_integracion, space.codigo_integracion),
      capacity: space.capacidad ?? 0,
      minimum: space.minimo ?? '',
      image: Maybe.fromValue(space.imagen),
      blocked: space.bloqueado ?? false,
      blockedWeb: space.bloqueado_web ?? false,
      top: Maybe.fromValue(space.top),
      left: Maybe.fromValue(space.left),
      width: Maybe.fromValue(space.width),
      height: Maybe.fromValue(space.height),
      radius: Maybe.fromValue(space.radius),
      scale: Maybe.fromValue(space.scale),
      rotate: Maybe.fromValue(space.rotate),
      slugsTypes: space.tipos_slugs ?? [],
      combinationsIds: space.combinaciones_ids ?? [],
      removedAt: space.removed_at ?? 0,
      removedBy: space.removed_by,
    };
  }
}

export class BookingZoneSoftMapper extends BookingZoneMapper {
  static execute(data: BookingZoneSchemaType): BookingZoneEither {
    return this.buildEntity({
      ...data,
      _id: data._id,
      negocio_id: data.negocio_id ?? null,
      slug: data.slug ?? null,
      orden: data.orden ?? 0,
      nombre: data.nombre ?? null,
      puede_reservar: data.puede_reservar ?? Object.values(EBookingPeople),
      puede_seleccionar_espacio: data.puede_seleccionar_espacio ?? [],
      autoaceptar: data.autoaceptar ?? [],
      dias: data.dias ?? [],
      limite: data.limite ?? 0,
      limite_campo: data.limite_campo ?? EBookingCriteriaCompleteZone.NEVER,
      tipos_slugs: data.tipos_slugs ?? [],
    });
  }
}
