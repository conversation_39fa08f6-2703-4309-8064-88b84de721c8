import {
  EBookingCriteriaCompleteZone,
  EBookingPeople,
  EBookingSpaceTypes,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

const bookingZoneTimeSchema = {
  format: {
    type: String,
    required: true,
  },
  selected: {
    type: Boolean,
    required: true,
  },
};

const bookingSpaceSchema = {
  _id: {
    type: String,
    required: true,
  },
  tipo: {
    type: String,
    required: true,
    default: EBookingSpaceTypes.TABLE,
  },
  nombre: {
    type: String,
    required: true,
  },
  // Nombre del espacio en el POS
  nombre_integracion: { type: String },
  capacidad: {
    type: Number,
    required: true,
    default: 0,
  },
  minimo: {
    type: Number,
    required: true,
    default: 0,
  },
  imagen: { type: String },
  bloqueado: {
    type: Boolean,
    default: false,
  },
  bloqueado_web: {
    type: Boolean,
    default: false,
  },
  codigo_integracion: { type: String },
  top: { type: Number },
  left: { type: Number },
  width: { type: Number },
  height: { type: Number },
  radius: { type: Number },
  scale: { type: Number },
  rotate: { type: Number },
  tipos_slugs: {
    type: [String],
    default: [],
  },
  // Si es un espacio combinado aquí irán los ids de los espacios que se están combinando
  combinaciones_ids: {
    type: [String],
    default: [],
  },
  removed_at: {
    type: Number,
    default: 0,
  },
  removed_by: {
    type: String,
    default: '',
  },
};

export const bookingZoneSchema = {
  _id: {
    type: String,
    required: true,
  },
  negocio_id: {
    type: String,
    required: true,
    index: true,
  },
  evento_id: {
    type: String,
    index: true,
  },
  fecha: {
    type: String,
    index: true,
  },
  slug: {
    type: String,
    required: true,
    index: true,
  },
  orden: {
    type: Number,
    default: 0,
  },
  nombre: {
    type: String,
    required: true,
  },
  // Descripción que no verá el cliente final
  descripcion: { type: String },
  // Imagen promocional del reservado (ofertas, carta, etc)
  imagen: { type: String },
  // Imagen del reservado
  imagen_plano: { type: String },
  puede_reservar: {
    type: [String],
    default: Object.values(EBookingPeople),
    enum: Object.values(EBookingPeople),
  },
  puede_seleccionar_espacio: {
    type: [String],
    default: [],
    enum: Object.values(EBookingPeople),
  },
  mostrar_plano: {
    type: Boolean,
    default: false,
  },
  mostrar_espacios_plano: {
    type: Boolean,
    default: false,
  },
  autoaceptar: {
    type: [String],
    default: [],
    enum: Object.values(EBookingPeople).filter(item => item !== EBookingPeople.COLLABORATORS),
  },
  activa: {
    type: Boolean,
    default: true,
  },
  dias: { type: [Number] },
  // Limites
  limite: {
    type: Number,
    default: 0,
  },
  limite_campo: {
    type: String,
    enum: Object.values(EBookingCriteriaCompleteZone),
    default: EBookingCriteriaCompleteZone.NEVER,
  },
  // Horas disponibles para hacer la reserva
  horas: { type: [bookingZoneTimeSchema] },
  // Duracion media de una reserva en esta zona en segundos
  tiempo_reserva: {
    type: Number,
    default: 0, // Inicializamos la zona sin tiempos de reserva
  },
  espacios: {
    type: [bookingSpaceSchema],
    optional: true,
    default: [],
  },
  tipos_slugs: {
    type: [String],
    default: [],
  },
  created_at: { type: Number },
  created_by: { type: String },
  updated_at: { type: Number },
  updated_by: { type: String },
  removed_at: {
    type: Number,
    index: true,
  },
  removed_by: { type: String },
};

export const BookingZoneSchema = new Schema(bookingZoneSchema);
