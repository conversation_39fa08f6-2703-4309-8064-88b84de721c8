import type { EBookingCriteriaCompleteZone, EBookingPeople } from '@discocil/fv-domain-library/domain';

export type BookingZoneCacheInfoSchemaType = {
  _id: string;
  completa: boolean;
};

export type BookingZoneTimeSchemaType = {
  format: string;
  selected: boolean;
};

export type BookingSpaceSchemaType = {
  _id: string;
  tipo: string;
  nombre: string;
  nombre_integracion?: string;
  capacidad: number;
  minimo: number;
  imagen?: string;
  bloqueado: boolean;
  bloqueado_web: boolean;
  codigo_integracion?: string;
  top?: number;
  left?: number;
  width?: number;
  height?: number;
  radius?: number;
  scale?: number;
  rotate?: number;
  tipos_slugs: string[];
  combinaciones_ids: string[];
  removed_at: number;
  removed_by: string;
};

export type BookingZoneSchemaType = {
  _id: string;
  negocio_id: string;
  evento_id?: string;
  fecha?: string;
  slug: string;
  orden: number;
  nombre: string;
  descripcion?: string;
  imagen?: string;
  imagen_plano?: string;
  puede_reservar: EBookingPeople[];
  puede_seleccionar_espacio: EBookingPeople[];
  mostrar_plano: boolean;
  mostrar_espacios_plano: boolean;
  autoaceptar: EBookingPeople[];
  activa: boolean;
  dias: number[];
  limite: number;
  limite_campo: EBookingCriteriaCompleteZone;
  horas: BookingZoneTimeSchemaType[];
  tiempo_reserva: number;
  espacios: BookingSpaceSchemaType[];
  tipos_slugs: string[];
  cacheInfo?: BookingZoneCacheInfoSchemaType[];
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
  removed_at: number;
  removed_by: string;
};

export type BookingZoneOriginalSchemaType = Omit<BookingZoneSchemaType, 'cacheInfo'>;
