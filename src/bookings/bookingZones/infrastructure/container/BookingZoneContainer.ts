import { container, instanceCachingFactory } from 'tsyringe';

import { BookingDependencyIdentifier } from '@/bookings/bookings/domain/dependencyIdentifier/BookingDependencyIdentifier';
import { BookingTypeDependencyIdentifier } from '@/bookings/bookingTypes/domain/dependencyIdentifier/BookingTypeDependencyIdentifier';
import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { GetBookingZonesServices } from '@/events/events/infrastructure/services/bookings/GetBookingZonesServices';

import { BookingZoneDependencyIdentifier } from '../../domain/dependencyIdentifier/BookingZoneDependencyIdentifier';
import { BookingZoneMongoRepository } from '../database/repositories/BookingZoneRepository';

import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';

export const BookingZoneContainer = {
  register: (): void => {
    container.register(BookingZoneDependencyIdentifier.BookingZoneRepository, {
      useFactory: instanceCachingFactory((container) => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

        return new BookingZoneMongoRepository(dbConnection);
      }),
    });

    container.register(BookingDependencyIdentifier.IGetBookingsServices, {
      useFactory: instanceCachingFactory((container) => {
        return new GetBookingZonesServices(
          container.resolve(BookingZoneDependencyIdentifier.BookingZoneRepository),
          container.resolve(BookingTypeDependencyIdentifier.BookingTypeRepository),
          container.resolve(BookingDependencyIdentifier.BookingRepository),
        );
      }),
    });
  },
};
