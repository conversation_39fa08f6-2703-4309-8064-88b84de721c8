import {
  FvDate,
  left,
  NotFoundError,
  UniqueEntityID,
  contextualizeError,
} from '@discocil/fv-domain-library/domain';


import { BookingZone } from '../../domain/entities/BookingZoneEntity';
import { BookingZoneCriteriaMother } from '../../domain/filters/BookingZoneCriteriaMother';

import type { Booking } from '@/bookings/bookings/domain/entities/BookingEntity';
import type { BookingZoneRepository } from '../../domain/contracts/BookingZoneRepository';
import type { IGetBookingZoneService } from '../../domain/contracts/GetBookingZoneContract';
import type { BookingZoneEither } from '../../domain/entities/BookingZoneEntity';

export class GetBookingZoneService implements IGetBookingZoneService {
  constructor(private readonly bookingZoneRepository: BookingZoneRepository) {}

  @contextualizeError()
  async execute(booking: Booking): Promise<BookingZoneEither> {
    if (booking.slugType.isEmpty()) {
      return left(NotFoundError.build({ context: BookingZone.name }));
    }

    const organizationId = UniqueEntityID.build(booking.organizationId);
    const slugType = booking.slugType.get();

    if (booking.eventId.isDefined()) {
      const eventId = UniqueEntityID.build(booking.eventId.get());
      const criteria = BookingZoneCriteriaMother.matchByEvent(organizationId, eventId, slugType);

      return await this.bookingZoneRepository.find(criteria);
    }

    if (booking.date.isDefined()) {
      const date = FvDate.create(booking.date.get());
      const criteria = BookingZoneCriteriaMother.matchByDate(organizationId, date, slugType);

      return await this.bookingZoneRepository.find(criteria);
    }

    return left(NotFoundError.build({ context: BookingZone.name }));
  }
}
