import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';
import { EBookingPeople } from '@discocil/fv-domain-library/domain';

import type { BookingZoneKeys } from '../entities/BookingZoneEntity';

class FilterField extends FilterFieldBase<BookingZoneKeys> {}

export class BookignZoneStateFilter {
  private static readonly field: BookingZoneKeys = 'canBooking';

  static buildCanMakeBooking(): Filter {
    const states = [EBookingPeople.CLIENTS];

    const canBookingField = new FilterField(this.field);
    const canBookingOperator = FilterOperator.in();
    const canBookingValue = states.map((value: string) => FilterValue.build(value));

    return new Filter(canBookingField, canBookingOperator, canBookingValue);
  }
}
