import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { BookingZoneKeys } from '../entities/BookingZoneEntity';

class FilterField extends FilterFieldBase<BookingZoneKeys> {}

export class SlugFilter {
  private static readonly field: BookingZoneKeys = 'slug';

  static buildEquals(value: string): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(value);

    return new Filter(field, operator, filterValue);
  }
}
