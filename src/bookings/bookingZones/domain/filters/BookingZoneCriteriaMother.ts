import {
  Criteria,
  Filters,
  Order,
  OrderTypes,
  Pagination,
} from '@discocil/fv-criteria-converter-library/domain';

import { EventIdFilter } from '@/cross-cutting/domain/filters/EventIdFilter';
import { IdFilter } from '@/cross-cutting/domain/filters/IdFilter';
import { OrganizationIdFilter } from '@/cross-cutting/domain/filters/OrganizationIdFilter';
import { RemovedAtFilter } from '@/cross-cutting/domain/filters/RemovedAtFilter';

import { BookignZoneActiveFilter } from './BookingZoneActiveFilter';
import { BookignZoneStateFilter } from './BookingZoneStateFilter';
import { DateFilter } from './DateFilter';
import { SlugFilter } from './SlugFilter';

import type { PaginationOption } from '@discocil/fv-criteria-converter-library/domain';
import type { FvDate, UniqueEntityID } from '@discocil/fv-domain-library/domain';
import type { BookingZoneKeys } from '../entities/BookingZoneEntity';

export class BookingZoneCriteriaMother {
  private static readonly defaultOrderKey: BookingZoneKeys = 'order';

  static idToMatch(id: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(id));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static availablesToMatch(eventId: UniqueEntityID, organizationId: UniqueEntityID, paginationOption?: PaginationOption): Criteria {
    const filters = Filters.build();

    filters.add(OrganizationIdFilter.buildEqual(organizationId));
    filters.add(RemovedAtFilter.buildActive());
    filters.add(EventIdFilter.buildEqual(eventId));
    filters.add(BookignZoneActiveFilter.buildTrue());
    filters.add(BookignZoneStateFilter.buildCanMakeBooking());

    if (!paginationOption) {
      return Criteria.build(filters, Order.asc<BookingZoneKeys>(this.defaultOrderKey));
    }

    const {
      order: orderOption, page, perPage,
    } = paginationOption as PaginationOption<BookingZoneKeys>;

    const orderBy = orderOption.by.getOrElse(this.defaultOrderKey);
    const orderType = orderOption.type ?? OrderTypes.ASC;

    const order = Order.fromValues<BookingZoneKeys>(orderBy, orderType);
    const pagination = Pagination.build(page, perPage);

    return Criteria.build(filters, order, pagination);
  }

  static matchByEvent(organizationId: UniqueEntityID, eventId: UniqueEntityID, slug: string): Criteria {
    const filters = Filters.build();

    filters.add(OrganizationIdFilter.buildEqual(organizationId));
    filters.add(EventIdFilter.buildEqual(eventId));
    filters.add(RemovedAtFilter.buildActive());
    filters.add(SlugFilter.buildEquals(slug));

    return Criteria.build(filters, Order.asc<BookingZoneKeys>(this.defaultOrderKey));
  }

  static matchByDate(organizationId: UniqueEntityID, date: FvDate, slug: string): Criteria {
    const filters = Filters.build();
    const orFilter = Filters.buildOr();
    const andFilter = Filters.buildAnd();

    andFilter.add(DateFilter.notExists());
    andFilter.add(EventIdFilter.notExists());

    orFilter.add(DateFilter.buildEquals(date));
    orFilter.add(andFilter);

    filters.add(OrganizationIdFilter.buildEqual(organizationId));
    filters.add(RemovedAtFilter.buildActive());
    filters.add(SlugFilter.buildEquals(slug));
    filters.add(orFilter);

    return Criteria.build(filters, Order.asc<BookingZoneKeys>(this.defaultOrderKey));
  }
}
