import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';

import type { BookingZoneKeys } from '../entities/BookingZoneEntity';

class FilterField extends FilterFieldBase<BookingZoneKeys> {}

export class BookignZoneActiveFilter {
  private static readonly field: BookingZoneKeys = 'isActive';

  static buildTrue(): Filter {
    const stateActiveField = new FilterField(this.field);
    const stateActiveOperator = FilterOperator.equal();
    const stateActiveValue = FilterValue.buildTrue();

    return new Filter(stateActiveField, stateActiveOperator, stateActiveValue);
  }
}
