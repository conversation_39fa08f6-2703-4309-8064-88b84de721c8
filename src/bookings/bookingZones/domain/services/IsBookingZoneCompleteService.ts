import { Maybe } from '@discocil/fv-domain-library/domain';

import type { Booking } from '@/bookings/bookings/domain/entities/BookingEntity';
import type { FvDate } from '@discocil/fv-domain-library/domain';
import type {
  BookingSpace,
  BookingSpacePrimitives,
  BookingSpaces,
  BookingZone,
} from '../entities/BookingZoneEntity';

export class IsBookingZoneCompleteService {
  constructor(
    private readonly bookingZone: BookingZone,
  ) { }

  execute(): boolean {
    if (this.bookingZone.zoneHasNoRates()) {
      return true;
    }

    if (!this.bookingZone.fieldLimitIsSpaces()) {
      if (this.bookingZone.allSpacesHaveNoRate()) {
        return true;
      }
    }

    const hasSpaces = this.bookingZone.hasSpaces();
    const hasBookingTime = this.bookingZone.hasBookingTime();

    if (hasBookingTime) {
      const availableHours = this.bookingZone.getSelectedHours();

      if (availableHours.length === 0) {
        return true;
      }

      const atLeastOneBookingIsAvailable = this.getIsAtLeastOneSpaceAvailableForBookingTime();

      if (hasSpaces) {
        return !atLeastOneBookingIsAvailable;
      }
    }

    if (this.bookingZone.allSpacesHaveNoRate()) {
      return true;
    }

    if (hasSpaces) {
      const freeSpaces = this.getFreeSpacesWithoutBookingTime();

      return freeSpaces.size === 0;
    }

    return false;
  }

  private getIsAtLeastOneSpaceAvailableForBookingTime(): boolean {
    const availableHours = this.bookingZone.getSelectedHours();

    for (const _availableHour of availableHours) {
      const spaceIdsAffectedByCombinations = new Set();

      for (const _space of this.bookingZone.getSpacesWithCombinations().values()) {
        const firstBookingResult = this.getFirstBookingFromSpace(_space, _availableHour.time);

        const isSpaceCombinationBooked = firstBookingResult.isDefined();

        if (isSpaceCombinationBooked) {
          for (const _id of _space.combinationsIds) {
            spaceIdsAffectedByCombinations.add(_id);
          }
        }
      }

      let hourIsAvailable = false;

      for (const _space of this.bookingZone.getSpacesWithoutCombinations().values()) {
        const firstBookingResult = this.getFirstBookingFromSpace(_space, _availableHour.time);

        const isHourAvailable = firstBookingResult.isEmpty();
        const spaceIsBookedFromParentCombination = spaceIdsAffectedByCombinations.has(_space.id);

        if (isHourAvailable && !spaceIsBookedFromParentCombination) {
          hourIsAvailable = true;

          break;
        }
      }

      if (hourIsAvailable) {
        return true;
      }
    }

    return false;
  }

  private getFreeSpacesWithoutBookingTime(): BookingSpaces {
    const freeSpaces: BookingSpaces = new Map<string, BookingSpace>();

    for (const _space of this.bookingZone.getSpaces().values()) {
      const isCombination = _space.combinationsIds.length > 0;
      const isSpaceBlocked = this.bookingZone.thisSpaceIsBlocked(_space.id);
      const hasSpaceRate = this.bookingZone.thisSpaceHasRate(_space.id);

      if (!isCombination && !isSpaceBlocked && hasSpaceRate) {
        freeSpaces.set(_space.id, _space);
      }
    }

    for (const _booking of this.bookingZone.getBookings()) {
      if (_booking.spaceId.isEmpty()) {
        continue;
      }

      const bookedSpaceId = _booking.spaceId.get();
      const spaceResult = this.bookingZone.getSpaceById(bookedSpaceId);

      if (spaceResult.isEmpty()) {
        continue;
      }

      const space = spaceResult.get();

      freeSpaces.delete(bookedSpaceId);

      space.combinationsIds.forEach((_id) => {
        freeSpaces.delete(_id);
      });
    }

    return freeSpaces;
  }

  private getFirstBookingFromSpace(space: BookingSpace | BookingSpacePrimitives, time: FvDate): Maybe<Booking> {
    const bookingsForThisSpace = this.getBookingsForThisSpace(space.id);

    if (bookingsForThisSpace.length === 0) {
      return Maybe.none();
    }

    const bookedHour = bookingsForThisSpace.find((_booking) => {
      if (!_booking.hasTime()) {
        return false;
      }

      if (!_booking.hasBookingTime() && this.bookingZone.hasBookingTime()) {
        return false;
      }

      const bookingDuration = _booking.getBookingTimeOrElse(this.bookingZone.bookingTime.get());

      const bookingTime = _booking.getBookingTime();

      const bookingTimeStart = this.bookingZone.shiftToNextDayIfEarlyMorning(bookingTime);
      const bookingTimeEnd = bookingTimeStart.addEpoch(bookingDuration);

      let availableHourTimeStart = bookingTime.set({
        hour: time.getHours(),
        minute: time.getMinutes(),
        second: time.getSeconds(),
      });

      availableHourTimeStart = this.bookingZone.shiftToNextDayIfEarlyMorning(availableHourTimeStart);
      const availableHourTimeEnd = availableHourTimeStart.addEpoch(bookingDuration);

      const sameStart = bookingTimeStart.isEqualTo(availableHourTimeStart);
      const startInside = bookingTimeStart.isBetween(availableHourTimeStart, availableHourTimeEnd);
      const endInside = bookingTimeEnd.isBetween(availableHourTimeStart, availableHourTimeEnd);

      return (sameStart || startInside || endInside);
    });

    return Maybe.fromValue(bookedHour);
  }

  private getBookingsForThisSpace(spaceId: string): Booking[] {
    const bookingsForThisSpace: Booking[] = [];

    for (const _booking of this.bookingZone.getBookings()) {
      const spaceIdsToCheck = [spaceId];
      const spaceIsCombination = this.bookingZone.thisSpaceHaveCombinations(spaceId);

      if (!spaceIsCombination) {
        const parentCombinations = this.bookingZone.getSpacesWithCombinations().get(spaceId);

        if (parentCombinations) {
          for (const _combinationIds of parentCombinations.combinationsIds) {
            spaceIdsToCheck.push(..._combinationIds);
          }
        }
      }

      for (const _spaceId of spaceIdsToCheck) {
        if (_booking.hasSpaceId(_spaceId)) {
          bookingsForThisSpace.push(_booking);
        }
      }
    }

    return bookingsForThisSpace;
  }
}
