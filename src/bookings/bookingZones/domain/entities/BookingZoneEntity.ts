import {
  AggregateRoot,
  EBookingCriteriaCompleteZone,
  EBookingPeople,
  FvDate,
  FvNumber,
  Maybe,
  RemovedAt,
  RemovedBy,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import { IsBookingZoneCompleteService } from '../services/IsBookingZoneCompleteService';

import type { Booking } from '@/bookings/bookings/domain/entities/BookingEntity';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { StampsPrimitive } from '@/cross-cutting/domain/services/Stamps';
import type { PaginationMetadata } from '@discocil/fv-criteria-converter-library/domain';
import type {
  CreatedAt,
  CreatedBy,
  DatePrimitive,
  Either,
  IdPrimitive,
  InvalidDateError,
  NotFoundError,
  Primitives,
  Properties,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';

export type BookingZoneTime = {
  time: FvDate;
  selected: boolean;
};

type BookingZoneTimePrimitives = Primitives<BookingZoneTime>;

type BookingSpaceIntegration = {
  name: string;
  code: string;
};

export type BookingSpace = {
  id: string;
  type: string;
  name: string;
  integration: Maybe<BookingSpaceIntegration>;
  capacity: number;
  minimum: number;
  image: Maybe<string>;
  blocked: boolean;
  blockedWeb: boolean;
  top: Maybe<number>;
  left: Maybe<number>;
  width: Maybe<number>;
  height: Maybe<number>;
  radius: Maybe<number>;
  scale: Maybe<number>;
  rotate: Maybe<number>;
  slugsTypes: string[];
  combinationsIds: string[];
  removedAt: RemovedAt;
  removedBy: RemovedBy;
};

export type BookingSpaces = Map<string, BookingSpace>;

export type BookingZones = Map<IdPrimitive, BookingZone>;

export type BookingZoneEither = Either<NotFoundError | MapperError | InvalidDateError, BookingZone>;

export type BookingZonesResponse = {
  bookingZones: BookingZones;
  paginationMetadata?: PaginationMetadata;
};

export type BookingZonesEither = Either<NotFoundError | MapperError | InvalidDateError, BookingZonesResponse>;

export type BookingSpacePrimitives = Properties<
  Primitives<
    Omit<BookingSpace, 'removedAt' | 'removedBy'>> &
    Pick<StampsPrimitive, 'removedAt' | 'removedBy'>
>;

export type BookingSpacesPrimitives = Map<string, BookingSpacePrimitives>;

export type BookingZonePrimitives = Primitives<BookingZone>;

export type BookingZoneKeys = keyof BookingZone;

export class BookingZone extends AggregateRoot {
  private readonly nextDayHourCutoff = 8;

  private readonly occupiedSpaces = new Set<string>();
  private readonly _bookings: Booking[] = [];
  private readonly _spacesForMicrosite: BookingSpaces = new Map<string, BookingSpace>();
  private readonly _blockedSpaces: BookingSpaces = new Map<string, BookingSpace>();
  private readonly _spacesWithCombinations: BookingSpaces = new Map<string, BookingSpace>();
  private readonly _spacesWithoutCombinations: BookingSpaces = new Map<string, BookingSpace>();

  private constructor(
    id: UniqueEntityID,
    private readonly _organizationId: UniqueEntityID,
    private readonly _eventId: Maybe<UniqueEntityID>,
    private readonly _date: Maybe<FvDate>,
    readonly slug: string,
    readonly order: number,
    readonly name: string,
    readonly description: Maybe<string>,
    readonly image: Maybe<string>,
    readonly imageMap: Maybe<string>,
    readonly canBooking: EBookingPeople[],
    readonly canSelectSpace: EBookingPeople[],
    readonly showMap: boolean,
    readonly showSpaceMap: boolean,
    readonly autoaccept: EBookingPeople[],
    readonly isActive: boolean,
    readonly days: number[],
    readonly limit: number,
    readonly fieldLimit: EBookingCriteriaCompleteZone,
    private readonly _hours: BookingZoneTime[],
    private readonly _bookingTime: Maybe<FvDate>,
    private _spaces: BookingSpaces,
    readonly slugsTypes: string[],
    private readonly _isComplete: boolean,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);

    this.buildSpaces();
  }

  static build(primitives: BookingZonePrimitives): BookingZoneEither {
    const id = UniqueEntityID.build(primitives.id);
    const organizationId = UniqueEntityID.build(primitives.organizationId);

    const eventId = primitives.eventId.map(item => UniqueEntityID.build(item));
    const date = primitives.date.map(item => FvDate.create(item));
    const bookingTime = primitives.bookingTime.map(item => FvDate.create(item));

    const description = primitives.description.map(item => item);
    const image = primitives.image.map(item => item);
    const imageMap = primitives.imageMap.map(item => item);

    const spaces: BookingSpaces = new Map<string, BookingSpace>();

    for (const space of primitives.spaces.values()) {
      spaces.set(space.id, {
        ...space,
        integration: space.integration.map(item => item),
        image: space.image.map(item => item),
        top: space.top.map(item => item),
        left: space.left.map(item => item),
        width: space.width.map(item => item),
        height: space.height.map(item => item),
        radius: space.radius.map(item => item),
        scale: space.scale.map(item => item),
        rotate: space.rotate.map(item => item),
        removedAt: RemovedAt.build(space.removedAt),
        removedBy: RemovedBy.build(space.removedBy),
      });
    }

    const hours = primitives.hours.map((item) => {
      return {
        time: FvDate.create(item.time),
        selected: item.selected,
      };
    });

    const stamps = stampValueObjects(primitives);

    const entity = new BookingZone(
      id,
      organizationId,
      eventId,
      date,
      primitives.slug,
      primitives.order,
      primitives.name,
      description,
      image,
      imageMap,
      primitives.canBooking,
      primitives.canSelectSpace,
      primitives.showMap,
      primitives.showSpaceMap,
      primitives.autoaccept,
      primitives.isActive,
      primitives.days,
      primitives.limit,
      primitives.fieldLimit,
      hours,
      bookingTime,
      spaces,
      primitives.slugsTypes,
      primitives.isComplete,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return right(entity);
  }

  get organizationId(): IdPrimitive {
    return this._organizationId.toPrimitive();
  }

  get eventId(): Maybe<IdPrimitive> {
    return this._eventId.map(item => item.toPrimitive());
  }

  get date(): Maybe<DatePrimitive> {
    return this._date.map(item => item.toPrimitive());
  }

  get bookingTime(): Maybe<DatePrimitive> {
    return this._bookingTime.map(item => item.toPrimitive());
  }

  get hours(): BookingZoneTimePrimitives[] {
    return this._hours.map((hour) => {
      const time = this.shiftToNextDayIfEarlyMorning(hour.time);

      return {
        ...hour,
        time: time.toPrimitive(),
      };
    });
  }

  get spaces(): BookingSpacesPrimitives {
    return this.makePrimitivesSpaces(this._spacesForMicrosite);
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }

  get isComplete(): boolean {
    if (this._isComplete) {
      return true;
    }

    const isCompleteService = new IsBookingZoneCompleteService(this);

    return isCompleteService.execute();
  }

  setSpaces(values: BookingSpaces): this {
    this._spaces = values;

    this.buildSpaces();

    return this;
  }

  addSpace(space: BookingSpace): this {
    this._spaces.set(space.id, space);

    this.buildSpaces();

    return this;
  }

  addBookings(bookings: Booking[]): this {
    this._bookings.push(...bookings);

    return this;
  }

  clientCanSelectSpace(): boolean {
    return this.canSelectSpace.includes(EBookingPeople.CLIENTS);
  }

  setOccupiedSpace(spaceId: string): this {
    this.occupiedSpaces.add(spaceId);

    return this;
  }

  setFreeSpace(spaceId: string): this {
    this.occupiedSpaces.delete(spaceId);

    return this;
  }

  hasBookingTime(): boolean {
    if (this._bookingTime.isEmpty()) {
      return false;
    }

    return this._bookingTime.get().toSeconds() > 0;
  }

  hasSpaces(): boolean {
    return this.spaces.size > 0;
  }

  blockSpace(spaceId: string): this {
    const space = this.spaces.get(spaceId);

    if (space) {
      space.blocked = true;
    }

    return this;
  }

  getBookings(): Booking[] {
    return this._bookings;
  }

  getSpaces(): BookingSpaces {
    return this._spaces;
  }

  getSpaceById(spaceId: string): Maybe<BookingSpacePrimitives> {
    const foundSpace = this.spaces.get(spaceId);

    return Maybe.fromValue(foundSpace);
  }

  getHours(): BookingZoneTime[] {
    return this._hours;
  }

  getSelectedHours(): BookingZoneTime[] {
    return this._hours.filter(hour => hour.selected);
  }

  getBlockedSpaces(): BookingSpacesPrimitives {
    return this.makePrimitivesSpaces(this._blockedSpaces);
  }

  getSpacesWithCombinations(): BookingSpacesPrimitives {
    return this.makePrimitivesSpaces(this._spacesWithCombinations);
  }

  getSpacesWithoutCombinations(): BookingSpaces {
    return this._spacesWithoutCombinations;
  }

  getOccupiedSpaces(): Set<string> {
    return this.occupiedSpaces;
  }

  isAvailableSpace(spaceId: string): boolean {
    return !this.isOccupiedSpace(spaceId);
  }

  isOccupiedSpace(spaceId: string): boolean {
    return this.occupiedSpaces.has(spaceId);
  }

  thisSpaceHaveCombinations(spaceId: string): boolean {
    const space = this._spaces.get(spaceId);

    return space ? space.combinationsIds.length > 0 : false;
  }

  thisSpaceIsBlocked(spaceId: string): boolean {
    const space = this._spaces.get(spaceId);

    if (!space) {
      return true;
    }

    const blockedSpace = this._blockedSpaces.get(spaceId);

    return Boolean(blockedSpace);
  }

  thisSpaceHasRate(spaceId: string): boolean {
    const space = this._spaces.get(spaceId);

    if (!space) {
      return false;
    }

    return space.slugsTypes.length > 0;
  }

  getDateInIso8601WithoutSeparatorFormat(): Maybe<string> {
    return this._date.map(item => item.toFormat({ style: 'iso8601WithoutSeparator' }));
  }

  getBookingTimeInSeconds(): Maybe<number> {
    return this._bookingTime.map(item => item.toSeconds());
  }

  shiftToNextDayIfEarlyMorning(date: FvDate): FvDate {
    let newDate = FvDate.create(date.toPrimitive());
    const isNextDay = FvNumber.build(newDate.getHours()).isLessThan(this.nextDayHourCutoff);

    if (isNextDay) {
      newDate = newDate.addHours(24);
    }

    return newDate;
  }

  zoneHasNoRates(): boolean {
    return this.slugsTypes.length === 0;
  }

  allSpacesHaveNoRate(): boolean {
    if (this.spaces.size === 0) {
      return false;
    }

    return Array.from(this.spaces.values()).every(space => space.slugsTypes.length === 0);
  }

  fieldLimitIsSpaces(): boolean {
    return this.fieldLimit === EBookingCriteriaCompleteZone.SPACES;
  }

  private makePrimitivesSpaces(_spaces: BookingSpaces): BookingSpacesPrimitives {
    const spaces = new Map<string, BookingSpacePrimitives>();

    for (const space of _spaces.values()) {
      spaces.set(space.id, {
        ...space,
        removedAt: space.removedAt.toMilliseconds(),
        removedBy: space.removedBy.toPrimitive(),
      });
    }

    return spaces;
  }

  private buildSpaces(): void {
    this._spacesForMicrosite.clear();
    this._blockedSpaces.clear();
    this._spacesWithCombinations.clear();
    this._spacesWithoutCombinations.clear();

    for (const _space of this._spaces.values()) {
      // const hasSpaceRate = this.thisSpaceHasRate(_space.id);

      if (_space.blocked || _space.blockedWeb /* || !hasSpaceRate */) {
        this._blockedSpaces.set(_space.id, _space);

        continue;
      }

      this._spacesForMicrosite.set(_space.id, _space);

      if (_space.combinationsIds.length > 0) {
        this._spacesWithCombinations.set(_space.id, _space);
      } else {
        this._spacesWithoutCombinations.set(_space.id, _space);
      }
    }
  }

  toPrimitives(): BookingZonePrimitives {
    return {
      id: this.id,
      organizationId: this.organizationId,
      eventId: this.eventId,
      date: this.date,
      slug: this.slug,
      order: this.order,
      name: this.name,
      description: this.description,
      image: this.image,
      imageMap: this.imageMap,
      canBooking: this.canBooking,
      canSelectSpace: this.canSelectSpace,
      showMap: this.showMap,
      showSpaceMap: this.showSpaceMap,
      autoaccept: this.autoaccept,
      isActive: this.isActive,
      days: this.days,
      limit: this.limit,
      fieldLimit: this.fieldLimit,
      hours: this.hours,
      bookingTime: this.bookingTime,
      spaces: this.spaces,
      slugsTypes: this.slugsTypes,
      isComplete: this.isComplete,
      createdAt: this.createdAt,
      createdBy: this.createdBy,
      updatedAt: this.updatedAt,
      updatedBy: this.updatedBy,
      removedAt: this.removedAt,
      removedBy: this.removedBy,
    };
  }
}
