import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type {
  BookingZone,
  BookingZoneEither,
  BookingZones,
  BookingZonesEither,
} from '../entities/BookingZoneEntity';

export interface BookingZoneRepository {
  find: (criteria: Criteria) => Promise<BookingZoneEither>;
  save: (bookingZone: BookingZone) => Promise<void>;
  saveMany: (bookingZones: BookingZones) => Promise<void>;
  search: (criteria: Criteria) => Promise<BookingZonesEither>;
}
