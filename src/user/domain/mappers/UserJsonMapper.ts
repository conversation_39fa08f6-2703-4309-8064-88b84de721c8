import { Maybe } from '@discocil/fv-domain-library/domain';

import { User } from '../entities/User';

import type { EGender, Nullable } from '@discocil/fv-domain-library/domain';
import type {
  UserEither,
  UserPrimitives,
  UserProfile,
} from '../entities/User';

type UserProfileJson = Nullable<{
  readonly name: string;
  readonly lastname: string;
  readonly gender: EG<PERSON>;
  readonly photo: string;
  readonly birthDate: number;
}>;

export type UserJsonPrimitives = Omit<UserPrimitives,
  'email'
  | 'phone'
  | 'username'
  | 'profile'
> & Nullable<{
  readonly email: string;
  readonly phone: string;
  readonly username: string;
  readonly profile: UserProfile<PERSON><PERSON>;
}>;

export class UserJsonMapper {
  static toEntity(primitives: UserJsonPrimitives): UserEither {
    const buildProfile = (profile: UserProfileJson | null | undefined): Maybe<UserProfile> => {
      if (!profile) {
        return Maybe.none<UserProfile>();
      }

      return Maybe.some({
        name: Maybe.fromValue(profile.name),
        lastname: Maybe.fromValue(profile.lastname),
        gender: Maybe.fromValue(profile.gender),
        photo: Maybe.fromValue(profile.photo),
        birthDate: Maybe.fromValue(profile.birthDate),
      });
    };

    return User.build({
      ...primitives,
      email: Maybe.fromValue(primitives.email),
      phone: Maybe.fromValue(primitives.phone),
      username: Maybe.fromValue(primitives.username),
      profile: buildProfile(primitives.profile),
    });
  }

  static toJson(user: User): UserJsonPrimitives {
    return {
      id: user.id,
      email: user.email.fold(() => null, item => item),
      phone: user.phone.fold(() => null, item => item),
      username: user.username.fold(() => null, item => item),
      organizations: user.organizations,
      hosts: user.hosts,
      profile: user.profile.fold(() => null, (item) => {
        return {
          name: item.name.fold(() => null, item => item),
          lastname: item.lastname.fold(() => null, item => item),
          gender: item.gender.fold(() => null, item => item),
          photo: item.photo.fold(() => null, item => item),
          birthDate: item.birthDate.fold(() => null, item => item),
        };
      }),
      createdAt: user.createdAt,
      createdBy: user.createdBy,
      updatedAt: user.updatedAt,
      updatedBy: user.updatedBy,
      removedAt: user.removedAt,
      removedBy: user.removedBy,
    };
  }
}
