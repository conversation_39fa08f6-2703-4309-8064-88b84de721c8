import { left, NotFoundError } from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { User } from '@/user/domain/entities/User';

import { UserMapper } from '../mappers/UserMapper';
import { userSchema } from '../schemas/UserSchema';

import type { UserRepository } from '@/user/domain/contracts/UserRepository';
import type { UserEither, UserKeys } from '@/user/domain/entities/User';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { UserSchemaType } from '../schemas/UserSchemaType';

type PropertiesMapper = Partial<Record<UserKeys, keyof UserSchemaType>>;

export class UserMongoRepository extends MongoRepository implements UserRepository {
  protected getSchema(): Schema {
    return new Schema(userSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'usuarios';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      username: 'username',
      email: 'email',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<UserEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<UserSchemaType>();

    return queryResponse ? UserMapper.execute(queryResponse) : left(NotFoundError.build({ context: this.constructor.name, target: User.name }));
  }
}
