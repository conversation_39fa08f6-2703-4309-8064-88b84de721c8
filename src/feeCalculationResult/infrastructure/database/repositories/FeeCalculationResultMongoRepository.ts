import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';

import { FeeCalculationResultSchemaMapper } from '../mappers/FeeCalculationResultSchemaMapper';
import { feeCalculationResultSchema } from '../schemas/FeeCalculationResultSchema';

import type { FeeCalculationResultRepository } from '@/feeCalculationResult/domain/contracts/FeeCalculationResultRepository';
import type { FeeCalculationResult, FeeCalculationResultKeys } from '@/feeCalculationResult/domain/entities/FeeCalculationResult';
import type { FeeCalculationResultSchemaType } from '../schemas/FeeCalculationResultSchemaType';

type PropertiesMapper = Partial<Record<FeeCalculationResultKeys, keyof FeeCalculationResultSchemaType>>;

export class FeeCalculationResultMongoRepository extends MongoRepository implements FeeCalculationResultRepository {
  protected getSchema(): Schema {
    return new Schema(feeCalculationResultSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'customer_custom_fee_calculation';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      paymentId: 'payment_id',
      orderId: 'order_id',
      removedAt: 'removed_at',
    };
  }

  async save(feeCalculationResult: FeeCalculationResult): Promise<void> {
    const toSave = FeeCalculationResultSchemaMapper.execute(feeCalculationResult);

    const filter = { _id: feeCalculationResult.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }
}
