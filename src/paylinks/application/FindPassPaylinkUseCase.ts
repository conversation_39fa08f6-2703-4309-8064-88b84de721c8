import {
  left,
  right,
  UniqueEntityID,
  contextualizeError,
} from '@discocil/fv-domain-library/domain';

import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';

import { PaylinkCriteriaMother } from '../domain/filters/PaylinkCriteriaMother';

import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { FindPassPaylinkResponse } from '../domain/contracts/FindPassPaylinkResponse';
import type { FindPaylinkDTO } from '../domain/contracts/FindPaylinkDtoContract';
import type { PaylinkRepository } from '../domain/contracts/PaylinkRepository';

export class FindPassPaylinkUseCase implements UseCase<FindPaylinkDTO, Promise<FindPassPaylinkResponse>> {
  constructor(
    private readonly paylinkRepository: PaylinkRepository,
    private readonly organizationRepository: OrganizationRepository,
  ) { }

  @contextualizeError()
  async execute(dto: FindPaylinkDTO): Promise<FindPassPaylinkResponse> {
    const { activateCode } = dto;

    const paylinkCriteria = PaylinkCriteriaMother.activateCodeMatch(activateCode);
    const paylinkOrError = await this.paylinkRepository.find(paylinkCriteria);

    if (paylinkOrError.isLeft()) {
      return left(paylinkOrError.value);
    }

    const paylink = paylinkOrError.value;

    const organizationCriteria = OrganizationCriteriaMother.idToMatch(UniqueEntityID.build(paylink.organizationId));
    const organizationOrError = await this.organizationRepository.find(organizationCriteria);

    if (organizationOrError.isLeft()) {
      return left(organizationOrError.value);
    }

    const organization = organizationOrError.value;

    return right({ paylink, organization });
  }
}
