import {
  contextualizeError,
  IdPrimitive,
  left,
  Maybe,
  NotFoundError,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { EventConfigurations } from '@/events/eventConfigurations/domain/entities/EventConfiguration';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { LocationCriteriaMother } from '@/locations/domain/filters/LocationCriteriaMother';
import { IMicrositeChannelService } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import { OrganizationConfigurationRepository } from '@/organizations/organizationConfigurations/domain/contracts/OrganizationConfigurationRepository';
import { OrganizationConfigurationCriteriaMother } from '@/organizations/organizationConfigurations/domain/filters/OrganizationConfigurationCriteriaMother';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';
import { IGetTicketTypesExtrasService } from '@/tickets/ticketsTypes/domain/contracts/GetTicketTypesExtrasContracts';
import { TicketTypeExtras } from '@/tickets/ticketsTypes/domain/contracts/TicketsTypesContracts';
import { TicketTypeRepository } from '@/tickets/ticketsTypes/domain/contracts/TicketTypeRepository';
import { TicketType, TicketTypes } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import { TicketTypeCriteriaMother } from '@/tickets/ticketsTypes/domain/filters/TicketTypeCriteriaMother';
import { UserRepository } from '@/user/domain/contracts/UserRepository';
import { UserCriteriaMother } from '@/user/domain/filters/UserCriteriaMother';

import { PaylinkCriteriaMother } from '../domain/filters/PaylinkCriteriaMother';

import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type { LocationFinder } from '@/locations/domain/services/LocationFinder';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { FindPaylinkDTO } from '../domain/contracts/FindPaylinkDtoContract';
import type { FindTicketPaylinkResponse } from '../domain/contracts/FindTicketPaylinkResponse';
import type { PaylinkRepository } from '../domain/contracts/PaylinkRepository';

export class FindTicketPaylinkUseCase implements UseCase<FindPaylinkDTO, Promise<FindTicketPaylinkResponse>> {
  constructor(
    private readonly paylinkRepository: PaylinkRepository,
    private readonly organizationRepository: OrganizationRepository,
    private readonly eventRepository: EventRepository,
    private readonly locationFinder: LocationFinder,
    private readonly organizationConfigurationRepository: OrganizationConfigurationRepository,
    private readonly ticketTypeRepository: TicketTypeRepository,
    private readonly micrositeChannelService: IMicrositeChannelService,
    private readonly getTicketTypesExtrasService: IGetTicketTypesExtrasService,
    private readonly userRepository: UserRepository,
  ) { }

  @contextualizeError()
  async execute(dto: FindPaylinkDTO): Promise<FindTicketPaylinkResponse> {
    const { activateCode } = dto;

    const paylinkCriteria = PaylinkCriteriaMother.activatePendingCodeMatch(activateCode);
    const paylinkOrError = await this.paylinkRepository.find(paylinkCriteria);

    if (paylinkOrError.isLeft()) {
      return left(paylinkOrError.value);
    }

    const paylink = paylinkOrError.value;

    const paylinkIsPending = paylink.ensureIsPending();

    if (paylinkIsPending.isLeft()) {
      return left(paylinkIsPending.value);
    }

    const organizationId = UniqueEntityID.build(paylink.organizationId);

    const organizationCriteria = OrganizationCriteriaMother.idToMatch(organizationId);
    const eventCriteria = EventCriteriaMother.idToMatch(UniqueEntityID.build(paylink.eventId));
    const ticketTypeCriteria = TicketTypeCriteriaMother.idToMatch(UniqueEntityID.build(paylink.rateId));
    const organizationConfigurationCriteria = OrganizationConfigurationCriteriaMother.organizationToMatch(organizationId);

    const [organizationOrError, eventOrError, organizationConfigurationOrError, ticketTypeOrError] = await Promise.all([
      this.organizationRepository.find(organizationCriteria),
      this.eventRepository.find(eventCriteria),
      this.organizationConfigurationRepository.find(organizationConfigurationCriteria),
      this.ticketTypeRepository.find(ticketTypeCriteria),
    ]);

    if (organizationOrError.isLeft()) {
      return left(organizationOrError.value);
    }

    if (eventOrError.isLeft()) {
      return left(eventOrError.value);
    }

    if (ticketTypeOrError.isLeft()) {
      return left(ticketTypeOrError.value);
    }

    const organization = organizationOrError.value;
    const event = eventOrError.value;
    const ticketType = ticketTypeOrError.value;


    if (organizationConfigurationOrError.isRight()) {
      event.setConfiguration({
        organizationConfiguration: Maybe.some(organizationConfigurationOrError.value),
        eventConfigurations: Maybe.none<EventConfigurations>(),
      });
    }

    const location = LocationCriteriaMother.idToMatch(UniqueEntityID.build(event.locationId));

    const userCriteria = UserCriteriaMother.idToMatch(UniqueEntityID.build(paylink.referrerId));
    const userOrError = await this.userRepository.find(userCriteria);

    if (userOrError.isLeft()) {
      return left(userOrError.value);
    }

    const user = userOrError.value;

    if (user.username.isEmpty()) {
      return left(NotFoundError.build({
        context: this.constructor.name,
        data: {
          dto,
          paylink,
          user,
        },
      }));
    }

    const micrositeChannelDto = {
      slug: user.username.get(),
      organizationSlugs: new Set<string>(),
    };

    const [locationOrError, micrositeChannelOrError] = await Promise.all([
      this.locationFinder.execute(location),
      this.micrositeChannelService.execute(micrositeChannelDto),
    ]);

    if (locationOrError.isDefined()) {
      event.setLocation(locationOrError.get());
    }

    if (micrositeChannelOrError.isLeft()) {
      return left(micrositeChannelOrError.value);
    }

    const channel = micrositeChannelOrError.value;

    const ticketTypes: TicketTypes = new Map<IdPrimitive, TicketType>();

    ticketTypes.set(ticketType.id, ticketType);

    const ticketTypesExtrasResult = await this.getTicketTypesExtrasService.execute({
      ticketTypes, channel, event, paylink: Maybe.some(paylink),
    });

    if (ticketTypesExtrasResult.isLeft()) {
      return left(ticketTypesExtrasResult.value);
    }

    const ticketTypesExtras = ticketTypesExtrasResult.value.get(ticketType.id) as TicketTypeExtras;

    const { quantitySelector } = ticketTypesExtras;

    return right({
      paylink,
      organization,
      event,
      quantitySelector,
    });
  }
}
