import { FvError } from '@discocil/fv-domain-library/domain';

import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';

import type { ErrorMethodRequest } from '@/cross-cutting/domain/errors/ErrorRequest';

export class PaylinkAlreadyUsed extends FvError {
  static readonly defaultCause = EErrorKeys.PAYLINK_ALREADY_USED;

  static build(request: ErrorMethodRequest): PaylinkAlreadyUsed {
    const {
      context, error, data, target,
    } = request;

    const exceptionMessage = 'Paylink is already used';

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
