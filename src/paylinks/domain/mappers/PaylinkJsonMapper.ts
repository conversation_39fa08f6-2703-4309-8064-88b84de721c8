import { FvDate, Maybe } from '@discocil/fv-domain-library/domain';

import { Paylink } from '../entities/Paylink';

import type { DatePrimitive, Nullable } from '@discocil/fv-domain-library/domain';
import type { PaylinkEither, PaylinkPrimitives } from '../entities/Paylink';

export type PaylinkJsonPrimitives = Omit<PaylinkPrimitives,
  'expiresAt'
  | 'paymentId'
  | 'phone'
  | 'email'
  | 'activationDate'
> & Nullable<{
  readonly expiresAt: string;
  readonly paymentId: string;
  readonly phone: string;
  readonly email: string;
  readonly activationDate: string;
}>;

export class PaylinkJsonMapper {
  static toEntity(primitives: PaylinkJsonPrimitives): PaylinkEither {
    const isoToDate = (value: string): FvDate => FvDate.createFromISO(value).value as FvDate;

    return Paylink.build({
      ...primitives,
      paymentId: Maybe.fromValue(primitives.paymentId),
      phone: Maybe.fromValue(primitives.phone),
      email: Maybe.fromValue(primitives.email),
      activationDate: primitives.activationDate ? Maybe.some(isoToDate(primitives.activationDate).toPrimitive()) : Maybe.none<DatePrimitive>(),
      expiresAt: primitives.expiresAt ? Maybe.some(isoToDate(primitives.expiresAt).toPrimitive()) : Maybe.none<DatePrimitive>(),
    });
  }

  static toJson(paylink: Paylink): PaylinkJsonPrimitives {
    return {
      id: paylink.id,
      organizationId: paylink.organizationId,
      organizationAssigned: paylink.organizationAssigned,
      eventId: paylink.eventId,
      rateId: paylink.rateId,
      referrerId: paylink.referrerId,
      assignerId: paylink.assignerId,
      resourceIds: paylink.resourceIds,
      resourceType: paylink.resourceType,
      paymentId: paylink.paymentId.fold(() => null, item => item),
      nRequested: paylink.nRequested,
      nCompleted: paylink.nCompleted,
      phone: paylink.phone.fold(() => null, item => item),
      email: paylink.email.fold(() => null, item => item),
      activateCode: paylink.activateCode,
      status: paylink.status,
      activationDate: paylink.getActivationDateInISO().fold(() => null, item => item),
      language: paylink.language,
      notificationType: paylink.notificationType,
      resentSms: paylink.resentSms,
      expiresAt: paylink.getExpiresAtInISO().fold(() => null, item => item),
      createdAt: paylink.createdAt,
      createdBy: paylink.createdBy,
      updatedAt: paylink.updatedAt,
      updatedBy: paylink.updatedBy,
      removedAt: paylink.removedAt,
      removedBy: paylink.removedBy,
    };
  }
}
