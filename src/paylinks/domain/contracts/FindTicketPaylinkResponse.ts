import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { EventEntity } from '@/events/events/domain/entities/EventEntity';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { QuantitySelector } from '@/tickets/ticketsTypes/domain/services/QuantitySelector';
import type {
  Either,
  InvalidArgumentError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import type { Paylink } from '../entities/Paylink';

export type ResponseFindTicketPaylinkSchemaDto = Readonly<{
  organization: Organization;
  paylink: Paylink;
  event: EventEntity;
  quantitySelector: QuantitySelector;
}>;


export type FindTicketPaylinkResponse = Either<
  NotFoundError
  | MapperError
  | InvalidArgumentError
  | UnexpectedError,
  ResponseFindTicketPaylinkSchemaDto
>;
