import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type {
  Either,
  InvalidArgumentError,
  NotFoundError,
} from '@discocil/fv-domain-library/domain';
import type { Paylink } from '../entities/Paylink';

export type ResponseFindPassPaylinkSchemaDto = {
  readonly organization: Organization;
  readonly paylink: Paylink;
};

export type FindPassPaylinkResponse = Either<NotFoundError | MapperError | InvalidArgumentError, ResponseFindPassPaylinkSchemaDto>;
