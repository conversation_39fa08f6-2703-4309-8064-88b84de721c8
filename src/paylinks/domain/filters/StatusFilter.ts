import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';
import { EPaylinkStatus } from '@discocil/fv-domain-library/domain';

import type { PaylinkKeys } from '../entities/Paylink';

class FilterField extends FilterFieldBase<PaylinkKeys> {}

export class StatusFilter {
  private static readonly field: PaylinkKeys = 'status';

  static buildPending(): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.equal();
    const filterValue = FilterValue.build(EPaylinkStatus.PENDING);

    return new Filter(field, operator, filterValue);
  }
}
