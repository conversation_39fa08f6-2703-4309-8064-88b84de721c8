import { left, NotFoundError } from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { Paylink } from '@/paylinks/domain/entities/Paylink';
import { PaylinkMapper } from '@/paylinks/infrastructure/database/mappers/PaylinkMapper';
import { PaylinkSchemaMapper } from '@/paylinks/infrastructure/database/mappers/PaylinkSchemaMapper';
import { paylinkSchema } from '@/paylinks/infrastructure/database/schemas/PaylinkSchema';

import type { PaylinkRepository } from '@/paylinks/domain/contracts/PaylinkRepository';
import type { PaylinkEither, PaylinkKeys } from '@/paylinks/domain/entities/Paylink';
import type { PaylinkSchemaType } from '@/paylinks/infrastructure/database/schemas/PaylinkSchemaType';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

type PropertiesMapper = Partial<Record<PaylinkKeys, keyof PaylinkSchemaType>>;

export class PaylinkMongoRepository extends MongoRepository implements PaylinkRepository {
  protected getSchema(): Schema {
    return new Schema(paylinkSchema);
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'paylinks';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      eventId: 'evento_id',
      activateCode: 'activate_code',
      status: 'status',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<PaylinkEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<PaylinkSchemaType>();

    return queryResponse ? PaylinkMapper.execute(queryResponse) : left(NotFoundError.build({ context: this.constructor.name, target: Paylink.name }));
  }

  async save(paylink: Paylink): Promise<void> {
    const toSave = PaylinkSchemaMapper.execute(paylink);

    const filter = { _id: paylink.id };
    const update = { $set: toSave };
    const options = { upsert: true };

    const connection = await this.getConnection();

    await connection.updateOne(filter, update, options);
  }
}
