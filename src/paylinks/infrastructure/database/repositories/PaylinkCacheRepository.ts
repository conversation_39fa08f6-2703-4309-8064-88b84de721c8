import { left, NotFoundError } from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { PaylinkJsonMapper } from '@/paylinks/domain/mappers/PaylinkJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { PaylinkRepository } from '@/paylinks/domain/contracts/PaylinkRepository';
import type { Paylink, PaylinkEither } from '@/paylinks/domain/entities/Paylink';
import type { PaylinkJsonPrimitives } from '@/paylinks/domain/mappers/PaylinkJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';

export class PaylinkCacheRepository extends CacheRepository implements PaylinkRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: PaylinkRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<PaylinkEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: PaylinkJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = PaylinkJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        PaylinkJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async save(paylink: Paylink): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.save(paylink);
  }
}
