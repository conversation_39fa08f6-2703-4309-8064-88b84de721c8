import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';
import { Sale } from '@/sale/domain/entities/Sale';

import { SaleMapper } from '../mappers/SaleMapper';
import { SaleSchemaMapper } from '../mappers/SaleSchemaMapper';
import { SaleSchema } from '../schemas/SaleMongoSchema';

import type { SaleRepository } from '@/sale/domain/contracts/SaleRepository';
import type {
  SaleEither, SaleKeys, Sales, SalesEither,
} from '@/sale/domain/entities/Sale';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { Schema } from 'mongoose';
import type { SaleSchemaType } from '../schemas/SaleMongoSchemaType';

type PropertiesMapper = Partial<Record<SaleKeys, keyof SaleSchemaType>>;

export class SaleMongoRepository extends MongoRepository implements SaleRepository {
  protected getSchema(): Schema {
    return SaleSchema;
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'ventas';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<SaleEither> {
    criteria.setPage(1).setPerPage(1);

    const queryResponse = (await this.customQueryFinder<SaleSchemaType[]>(criteria)).shift();

    return queryResponse ? SaleMapper.execute(queryResponse) : left(NotFoundError.build({ context: this.constructor.name, target: Sale.name }));
  }

  async remove(criteria: Criteria): Promise<void> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    await connection.deleteOne(filterQuery.filter);
  }

  async save(sale: Sale): Promise<void> {
    const modelQuery = await this.getConnection();

    const filter = { _id: sale.id };
    const options = { upsert: true };
    const update = { $set: SaleSchemaMapper.execute(sale) };

    await modelQuery.updateOne(filter, update, options);
  }

  async saveMany(sales: Sales): Promise<void> {
    const connection = await this.getConnection();
    const models: Array<Partial<SaleSchemaType>> = [];

    sales.forEach((sale: Sale) => models.push(SaleSchemaMapper.execute(sale)));

    await connection.insertMany(models);
  }

  async search(criteria: Criteria): Promise<SalesEither> {
    const response: Sales = new Map<IdPrimitive, Sale>();

    const queryResponse = await this.customQueryFinder<SaleSchemaType[]>(criteria);

    if (queryResponse.length === 0) {
      return right(response);
    }

    for (const model of queryResponse) {
      const queryResponse = SaleMapper.execute(model);

      if (queryResponse.isLeft()) {
        return left(queryResponse.value);
      }

      const sale = queryResponse.value;

      response.set(sale.id, sale);
    }

    return right(response);
  }

  private async customQueryFinder<T>(criteria: Criteria): Promise<T> {
    const connection = await this.getConnection();
    const pipeline = this.criteriaConverter.toPipeline(criteria);

    return connection.aggregate([
      ...pipeline,
      {
        $lookup: {
          from: 'eventos',
          let: { eventId: '$evento_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$eventId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'event',
        },
      },
      {
        $lookup: {
          from: 'negocios',
          let: { organizationId: '$negocio_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$$organizationId', '$_id'] } } },
            { $project: { currency: 1 } },
          ],
          as: 'organization',
        },
      },
      {
        $project: {
          main: '$$ROOT',
          eventObj: { $arrayElemAt: ['$event', 0] },
          organizationObj: { $arrayElemAt: ['$organization', 0] },
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              '$main',
              {
                event: { currency: { $ifNull: ['$eventObj.currency', null] } },
                organization: { currency: { $ifNull: ['$organizationObj.currency', null] } },
              },
            ],
          },
        },
      },
    ]) as unknown as T;
  }
}
