import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';
import { SaleJsonMapper } from '@/sale/domain/mappers/SaleJsonMapper';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { SaleRepository } from '@/sale/domain/contracts/SaleRepository';
import type {
  Sale,
  SaleEither,
  Sales,
  SalesEither,
} from '@/sale/domain/entities/Sale';
import type { SaleJsonPrimitives } from '@/sale/domain/mappers/SaleJsonMapper';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class SaleCacheRepository extends CacheRepository implements SaleRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: SaleRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<SaleEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: SaleJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = SaleJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        SaleJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async remove(criteria: Criteria): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    this.otherRepository.remove(criteria);
  }

  async save(sale: Sale): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.save(sale);
  }

  async saveMany(sales: Sales): Promise<void> {
    if (!this.otherRepository) {
      return;
    }

    await this.otherRepository.saveMany(sales);
  }

  async search(criteria: Criteria): Promise<SalesEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: SaleJsonPrimitives[] = JSON.parse(cacheHit);
      const sales: Sales = new Map<IdPrimitive, Sale>();

      for (const _primitive of jsonPrimitives) {
        const entityOrError = SaleJsonMapper.toEntity(_primitive);

        if (entityOrError.isLeft()) {
          continue;
        }

        sales.set(_primitive.id, entityOrError.value);
      }

      return right(sales);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const sales = repositoryResult.value;
      const salesLength = sales.size;
      let salesCacheIndex = 0;
      const jsonSales = new Array<SaleJsonPrimitives>(salesLength);

      if (salesLength > 0) {
        for (const _sale of sales.values()) {
          jsonSales[salesCacheIndex] = SaleJsonMapper.toJson(_sale);

          salesCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonSales, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}
