import { FvDate, Maybe } from '@discocil/fv-domain-library/domain';

import { Sale } from '../entities/Sale';

import type { Coordinates } from '@/cross-cutting/domain/contracts/CommonContracts';
import type {
  DatePrimitive,
  IdPrimitive,
  Nullable,
} from '@discocil/fv-domain-library/domain';
import type { SaleEither, SalePrimitives } from '../entities/Sale';

export type SaleJsonPrimitives = Omit<SalePrimitives,
  'organizationAssignedId'
  | 'paymentId'
  | 'code'
  | 'referrerId'
  | 'slugZone'
  | 'slugType'
  | 'date'
  | 'hour'
  | 'spaceId'
  | 'discocilCommissions'
  | 'idx'
  | 'receivedAt'
  | 'observationsReception'
  | 'coordinates'
> & Nullable<{
  readonly organizationAssignedId: IdPrimitive;
  readonly paymentId: IdPrimitive;
  readonly code: string;
  readonly referrerId: IdPrimitive;
  readonly slugZone: string;
  readonly slugType: string;
  readonly date: string;
  readonly hour: string;
  readonly spaceId: string;
  readonly discocilCommissions: number;
  readonly idx: string;
  readonly receivedAt: number;
  readonly observationsReception: string;
  readonly coordinates: Coordinates;
}>;

export class SaleJsonMapper {
  static toEntity(primitives: SaleJsonPrimitives): SaleEither {
    const isoToDate = (value: string): FvDate => FvDate.createFromISO(value).value as FvDate;

    return Sale.build({
      ...primitives,
      organizationAssignedId: Maybe.fromValue(primitives.organizationAssignedId),
      paymentId: Maybe.fromValue(primitives.paymentId),
      code: Maybe.fromValue(primitives.code),
      referrerId: Maybe.fromValue(primitives.referrerId),
      slugZone: Maybe.fromValue(primitives.slugZone),
      slugType: Maybe.fromValue(primitives.slugType),
      date: primitives.date ? Maybe.fromValue(isoToDate(primitives.date).toPrimitive()) : Maybe.none<DatePrimitive>(),
      hour: Maybe.fromValue(primitives.hour),
      spaceId: Maybe.fromValue(primitives.spaceId),
      discocilCommissions: Maybe.fromValue(primitives.discocilCommissions),
      idx: Maybe.fromValue(primitives.idx),
      receivedAt: Maybe.fromValue(primitives.receivedAt),
      observationsReception: Maybe.fromValue(primitives.observationsReception),
      coordinates: Maybe.fromValue(primitives.coordinates),
    });
  }

  static toJson(sale: Sale): SaleJsonPrimitives {
    return {
      id: sale.id,
      organizationId: sale.organizationId,
      organizationAssignedId: sale.organizationAssignedId.fold(() => null, item => item),
      event: sale.event,
      paymentId: sale.paymentId.fold(() => null, item => item),
      applicationId: sale.applicationId,
      code: sale.code.fold(() => null, item => item),
      type: sale.type,
      saleType: sale.saleType,
      state: sale.state,
      rate: sale.rate,
      saleId: sale.saleId,
      customer: sale.customer,
      referrerId: sale.referrerId.fold(() => null, item => item),
      saleDate: sale.saleDate,
      slugZone: sale.slugZone.fold(() => null, item => item),
      slugType: sale.slugType.fold(() => null, item => item),
      date: sale.getDateInISO().fold(() => null, item => item),
      hour: sale.hour.fold(() => null, item => item),
      spaceId: sale.spaceId.fold(() => null, item => item),
      hard: sale.hard,
      discocilCommissions: sale.discocilCommissions.fold(() => null, item => item),
      collected: sale.collected,
      refunded: sale.refunded,
      refundedAt: sale.refundedAt,
      idx: sale.idx.fold(() => null, item => item),
      targeted: sale.targeted,
      receivedAt: sale.receivedAt.fold(() => null, item => item),
      quality: sale.quality,
      remarketing: sale.remarketing,
      observationsReception: sale.observationsReception.fold(() => null, item => item),
      environment: sale.environment,
      musicalGenres: sale.musicalGenres,
      saleDevice: sale.saleDevice,
      deposit: sale.deposit,
      comeIn: sale.comeIn,
      coordinates: sale.coordinates.fold(() => null, item => item),
      currency: sale.currency,
      createdAt: sale.createdAt,
      createdBy: sale.createdBy,
      updatedAt: sale.updatedAt,
      updatedBy: sale.updatedBy,
      removedAt: sale.removedAt,
      removedBy: sale.removedBy,
    };
  }
}
