import { Criteria, Filters } from '@discocil/fv-criteria-converter-library/domain';

import { IdFilter } from '@/cross-cutting/domain/filters/IdFilter';
import { OrganizationIdFilter } from '@/cross-cutting/domain/filters/OrganizationIdFilter';
import { RemovedAtFilter } from '@/cross-cutting/domain/filters/RemovedAtFilter';

import type { UniqueEntityID } from '@discocil/fv-domain-library/domain';

export class BillingAddressCriteriaMother {
  static idToMatch(value: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(value));

    return Criteria.build(filters);
  }

  static organizationsToMatch(values: UniqueEntityID[]): Criteria {
    const filters = Filters.build();

    filters.add(OrganizationIdFilter.buildIn(values));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }

  static organizationToMatch(value: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(OrganizationIdFilter.buildEqual(value));
    filters.add(RemovedAtFilter.buildActive());

    return Criteria.build(filters);
  }
}
