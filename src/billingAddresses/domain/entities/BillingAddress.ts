import {
  AggregateRoot, UniqueEntityID, right,
} from '@discocil/fv-domain-library/domain';

import { stampValueObjects } from '@/cross-cutting/domain/services/Stamps';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type {
  CreatedAt,
  CreatedBy,
  EBillingAddressType,
  Either,
  IdPrimitive,
  Maybe,
  NotFoundError, Primitives,
  RemovedAt,
  RemovedBy,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';

export type BillingAddresses = Map<IdPrimitive, BillingAddress>;

export type BillingAddressEither = Either<NotFoundError | MapperError, BillingAddress>;
export type BillingAddressesEither = Either<NotFoundError | MapperError, BillingAddresses>;

export type BillingAddressPrimitives = Primitives<BillingAddress>;

export class BillingAddress extends AggregateRoot {
  private constructor(
    id: UniqueEntityID,
    private readonly _organizationId: UniqueEntityID,
    readonly type: EBillingAddressType,
    readonly companyName: string,
    readonly cif: Maybe<string>,
    readonly address: string,
    readonly postalCode: string,
    readonly number: string,
    readonly municipality: Maybe<string>,
    readonly province: string,
    readonly country: Maybe<string>,
    readonly iban: Maybe<string>,
    readonly activeDebit: boolean,
    readonly vatApplicable: Maybe<number>,
    readonly countriesTaxesId: string,
    readonly countriesTaxOptionsId: string,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
  ) {
    super(id);
  }

  static build(primitives: BillingAddressPrimitives): BillingAddressEither {
    const id = UniqueEntityID.build(primitives.id);
    const organizationId = UniqueEntityID.build(primitives.organizationId);
    const cif = primitives.cif.map(item => item);
    const municipality = primitives.municipality.map(item => item);
    const country = primitives.country.map(item => item);
    const iban = primitives.iban.map(item => item);
    const vatApplicable = primitives.vatApplicable.map(item => item);

    const stamps = stampValueObjects(primitives);

    const entity = new BillingAddress(
      id,
      organizationId,
      primitives.type,
      primitives.companyName,
      cif,
      primitives.address,
      primitives.postalCode,
      primitives.number,
      municipality,
      primitives.province,
      country,
      iban,
      primitives.activeDebit,
      vatApplicable,
      primitives.countriesTaxesId,
      primitives.countriesTaxOptionsId,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
    );

    return right(entity);
  }

  get organizationId(): string {
    return this._organizationId.value;
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }
}
