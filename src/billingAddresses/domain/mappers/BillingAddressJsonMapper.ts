import { Maybe } from '@discocil/fv-domain-library/domain';

import { BillingAddress } from '../entities/BillingAddress';

import type { BillingAddressEither, BillingAddressPrimitives } from '../entities/BillingAddress';

export type BillingAddressJsonPrimitives = Omit<BillingAddressPrimitives,
  'cif'
  | 'municipality'
  | 'country'
  | 'iban'
  | 'vatApplicable'
> & {
  readonly cif: string | null;
  readonly municipality: string | null;
  readonly country: string | null;
  readonly iban: string | null;
  readonly vatApplicable: number | null;
};

export class BillingAddressJsonMapper {
  static toEntity(data: BillingAddressJsonPrimitives): BillingAddressEither {
    return BillingAddress.build({
      ...data,
      cif: Maybe.fromValue(data.cif),
      municipality: Maybe.fromValue(data.municipality),
      country: Maybe.fromValue(data.country),
      iban: Maybe.fromValue(data.iban),
      vatApplicable: Maybe.fromValue(data.vatApplicable),
    });
  }

  static toJson(billingAddress: BillingAddress): BillingAddressJsonPrimitives {
    return {
      id: billingAddress.id,
      organizationId: billingAddress.organizationId,
      type: billingAddress.type,
      companyName: billingAddress.companyName,
      cif: billingAddress.cif.fold(() => null, item => item),
      address: billingAddress.address,
      postalCode: billingAddress.postalCode,
      number: billingAddress.number,
      municipality: billingAddress.municipality.fold(() => null, item => item),
      province: billingAddress.province,
      country: billingAddress.country.fold(() => null, item => item),
      iban: billingAddress.iban.fold(() => null, item => item),
      activeDebit: billingAddress.activeDebit,
      vatApplicable: billingAddress.vatApplicable.fold(() => null, item => item),
      countriesTaxesId: billingAddress.countriesTaxesId,
      countriesTaxOptionsId: billingAddress.countriesTaxOptionsId,
      createdAt: billingAddress.createdAt,
      createdBy: billingAddress.createdBy,
      updatedAt: billingAddress.updatedAt,
      updatedBy: billingAddress.updatedBy,
      removedAt: billingAddress.removedAt,
      removedBy: billingAddress.removedBy,
    };
  }
}
