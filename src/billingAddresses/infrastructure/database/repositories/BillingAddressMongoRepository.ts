import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';
import { Schema } from 'mongoose';

import { BillingAddress } from '@/billingAddresses/domain/entities/BillingAddress';
import { MongoRepository } from '@/cross-cutting/infrastructure/database/repositories/MongoRepository';
import { EDBNames } from '@/cross-cutting/infrastructure/enums/DBNames';

import { BillingAddressMapper } from '../mappers/BillingAddressMapper';
import { billingAddressSchema } from '../schemas/BillingAddressSchema';

import type { BillingAddressRepository } from '@/billingAddresses/domain/contracts/BillingAddressRepository';
import type {
  BillingAddressEither,
  BillingAddressesEither,
} from '@/billingAddresses/domain/entities/BillingAddress';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { BillingAddressSchemaType } from '../schemas/BillingAddressSchemaType';

type PropertiesMapper = Partial<Record<keyof BillingAddress, keyof BillingAddressSchemaType>>;

export class BillingAddressMongoRepository extends MongoRepository implements BillingAddressRepository {
  protected getSchema(): Schema {
    return new Schema(billingAddressSchema, { collection: this.getModel() });
  }

  protected getDBName(): string {
    return EDBNames.API;
  }

  protected getModel(): string {
    return 'direcciones_facturacion';
  }

  protected propertiesMapper(): PropertiesMapper {
    return {
      id: '_id',
      organizationId: 'negocio_id',
      removedAt: 'removed_at',
    };
  }

  async find(criteria: Criteria): Promise<BillingAddressEither> {
    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .findOne(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<BillingAddressSchemaType>();

    return queryResponse
      ? BillingAddressMapper.execute(queryResponse)
      : left(NotFoundError.build({
        context: this.constructor.name,
        target: BillingAddress.name,
      }));
  }

  async search(criteria: Criteria): Promise<BillingAddressesEither> {
    const billingAddresses = new Map<IdPrimitive, BillingAddress>();

    const connection = await this.getConnection();
    const filterQuery = this.criteriaConverter.convert(criteria);

    const queryResponse = await connection
      .find(filterQuery.filter, {}, filterQuery.pagination)
      .sort(filterQuery.sort)
      .lean<BillingAddressSchemaType[]>();

    if (queryResponse.length === 0) {
      return right(billingAddresses);
    }

    for (const model of queryResponse) {
      const billingAddressesResult = BillingAddressMapper.execute(model);

      if (billingAddressesResult.isLeft()) {
        return left(billingAddressesResult.value);
      }

      const billingAddress = billingAddressesResult.value;

      billingAddresses.set(billingAddress.organizationId, billingAddress);
    }

    return right(billingAddresses);
  }
}
