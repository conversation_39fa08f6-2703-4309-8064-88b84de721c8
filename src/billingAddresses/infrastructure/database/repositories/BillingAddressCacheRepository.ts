import {
  left,
  NotFoundError,
  right,
} from '@discocil/fv-domain-library/domain';

import { BillingAddressJsonMapper } from '@/billingAddresses/domain/mappers/BillingAddressJsonMapper';
import { CacheRepository } from '@/cross-cutting/infrastructure/database/repositories/CacheRepository';

import type { BillingAddressRepository } from '@/billingAddresses/domain/contracts/BillingAddressRepository';
import type {
  BillingAddress,
  BillingAddressEither,
  BillingAddresses,
  BillingAddressesEither,
} from '@/billingAddresses/domain/entities/BillingAddress';
import type { BillingAddressJsonPrimitives } from '@/billingAddresses/domain/mappers/BillingAddressJsonMapper';
import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';

export class BillingAddressCacheRepository extends CacheRepository implements BillingAddressRepository {
  constructor(
    private readonly cacheHandler: ICacheRepository,
    private readonly cacheConfig: CacheConfig,
    private readonly otherRepository?: BillingAddressRepository,
  ) {
    super();
  }

  protected getConfig(): CacheConfig {
    return this.cacheConfig;
  }

  protected getHandler(): ICacheRepository {
    return this.cacheHandler;
  }

  async find(criteria: Criteria): Promise<BillingAddressEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: BillingAddressJsonPrimitives = JSON.parse(cacheHit);
      const entityOrError = BillingAddressJsonMapper.toEntity(jsonPrimitives);

      if (entityOrError.isRight()) {
        return entityOrError;
      }
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.find(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      await this.cacheHandler.set(
        cacheKey,
        BillingAddressJsonMapper.toJson(repositoryResult.value),
        this.getConfig().ttl,
      );
    }

    return repositoryResult;
  }

  async search(criteria: Criteria): Promise<BillingAddressesEither> {
    const { cacheKey, cacheHit } = await this.getCache(criteria);

    if (cacheHit) {
      const jsonPrimitives: BillingAddressJsonPrimitives[] = JSON.parse(cacheHit);
      const billingAddresses: BillingAddresses = new Map<IdPrimitive, BillingAddress>();

      for (const _primitive of jsonPrimitives) {
        const eventOrError = BillingAddressJsonMapper.toEntity(_primitive);

        if (eventOrError.isLeft()) {
          continue;
        }

        billingAddresses.set(_primitive.organizationId, eventOrError.value);
      }

      return right(billingAddresses);
    }

    if (!this.otherRepository) {
      return left(NotFoundError.build({ context: this.constructor.name }));
    }

    const repositoryResult = await this.otherRepository.search(criteria);

    if (repositoryResult.isLeft()) {
      return left(repositoryResult.value);
    }

    if (this.getConfig().isEnabled()) {
      const billingAddresses = repositoryResult.value;
      const billingAddressesLength = billingAddresses.size;
      let eventCacheIndex = 0;
      const jsonBillingAddresses = new Array<BillingAddressJsonPrimitives>(billingAddressesLength);

      if (billingAddressesLength > 0) {
        for (const _event of billingAddresses.values()) {
          jsonBillingAddresses[eventCacheIndex] = BillingAddressJsonMapper.toJson(_event);

          eventCacheIndex++;
        }
      }

      await this.cacheHandler.set(cacheKey, jsonBillingAddresses, this.getConfig().ttl);
    }

    return repositoryResult;
  }
}
