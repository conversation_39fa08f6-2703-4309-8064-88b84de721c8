import { EType } from '@/cross-cutting/domain/enums/CommonEnum';

export const billingAddressSchema = {
  _id: {
    type: String,
    required: true,
  },
  negocio_id: {
    type: String,
    required: true,
    index: true,
  },
  tipo: {
    type: String,
    enum: Object.values(EType),
  },
  razon: { type: String },
  cif: { type: String },
  direccion: { type: String },
  codigo_postal: { type: String },
  numero: { type: String },
  municipio: { type: String },
  provincia: { type: String },
  pais: { type: String },
  iban: { type: String },
  domiciliacion_activa: {
    type: Boolean,
    default: false,
  },
  iva_aplicable: {
    type: Number,
    default: 21,
  },
  countries_taxes_id: { type: String },
  countries_tax_options_id: { type: String },
  created_at: { type: Number },
  created_by: { type: String },
  updated_at: { type: Number },
  updated_by: { type: String },
  removed_at: {
    type: Number,
    index: true,
  },
  removed_by: { type: String },
};
