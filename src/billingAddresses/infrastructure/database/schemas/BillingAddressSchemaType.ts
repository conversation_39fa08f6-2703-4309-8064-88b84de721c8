import type { EBillingAddressType } from '@discocil/fv-domain-library/domain';

export type BillingAddressSchemaType = {
  _id: string;
  negocio_id: string;
  tipo: EBillingAddressType;
  razon: string;
  cif?: string | null;
  direccion: string;
  codigo_postal: string;
  numero: string;
  municipio?: string | null;
  provincia: string;
  pais?: string | null;
  iban?: string | null;
  domiciliacion_activa: boolean;
  iva_aplicable?: number | null;
  countries_taxes_id: string;
  countries_tax_options_id: string;
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
  removed_at: number;
  removed_by: string;
};
