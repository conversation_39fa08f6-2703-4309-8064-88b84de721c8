

import type { BillingAddress } from '@/billingAddresses/domain/entities/BillingAddress';
import type { BillingAddressSchemaType } from '../schemas/BillingAddressSchemaType';

export class BillingAddressSchemaMapper {
  static execute(billingAddress: BillingAddress): BillingAddressSchemaType {
    return {
      _id: billingAddress.id,
      negocio_id: billingAddress.organizationId,
      tipo: billingAddress.type,
      razon: billingAddress.companyName,
      cif: billingAddress.cif.fold(() => undefined, item => item),
      direccion: billingAddress.address,
      codigo_postal: billingAddress.postalCode,
      numero: billingAddress.number,
      municipio: billingAddress.municipality.fold(() => undefined, item => item),
      provincia: billingAddress.province,
      pais: billingAddress.country.fold(() => undefined, item => item),
      iban: billingAddress.iban.fold(() => undefined, item => item),
      domiciliacion_activa: billingAddress.activeDebit,
      iva_aplicable: billingAddress.vatApplicable.fold(() => undefined, item => item),
      countries_taxes_id: billingAddress.countriesTaxesId,
      countries_tax_options_id: billingAddress.countriesTaxesId,
      created_at: billingAddress.createdAt,
      created_by: billingAddress.createdBy,
      updated_at: billingAddress.updatedAt,
      updated_by: billingAddress.updatedBy,
      removed_at: billingAddress.removedAt,
      removed_by: billingAddress.removedBy,
    };
  }
}
