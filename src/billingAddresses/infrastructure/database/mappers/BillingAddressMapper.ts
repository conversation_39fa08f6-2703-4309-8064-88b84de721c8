import { Maybe } from '@discocil/fv-domain-library/domain';
import { container } from 'tsyringe';

import { BillingAddress } from '@/billingAddresses/domain/entities/BillingAddress';
import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { MapperError } from '@/cross-cutting/domain/errors/MapperError';

import { billingAddressValidationSchema } from './BillingAddressSchemaValidation';

import type { BillingAddressEither } from '@/billingAddresses/domain/entities/BillingAddress';
import type Ajv from 'ajv';
import type { BillingAddressSchemaType } from '../schemas/BillingAddressSchemaType';

export class BillingAddressMapper {
  static execute(data: BillingAddressSchemaType): BillingAddressEither {
    const validator = container.resolve<Ajv>(DependencyIdentifier.Ajv);
    const validate = validator.compile<typeof data>(billingAddressValidationSchema);

    if (!validate(data) || validate.errors) {
      MapperError.logger({
        context: BillingAddressMapper.name,
        data,
        target: validate.errors,
      });

      return BillingAddressSoftMapper.execute(data);
    }

    return this.buildEntity(data);
  }

  static buildEntity(data: BillingAddressSchemaType): BillingAddressEither {
    return BillingAddress.build({
      id: data._id,
      organizationId: data.negocio_id,
      type: data.tipo,
      companyName: data.razon,
      cif: Maybe.fromValue(data.cif),
      address: data.direccion,
      postalCode: data.codigo_postal,
      number: data.numero,
      municipality: Maybe.fromValue(data.municipio),
      province: data.provincia,
      country: Maybe.fromValue(data.pais),
      iban: Maybe.fromValue(data.iban),
      activeDebit: data.domiciliacion_activa ?? false,
      vatApplicable: Maybe.fromValue(data.iva_aplicable),
      countriesTaxesId: data.countries_taxes_id,
      countriesTaxOptionsId: data.countries_tax_options_id,
      createdAt: data.created_at,
      createdBy: data.created_by,
      updatedAt: data.updated_at,
      updatedBy: data.updated_by,
      removedAt: data.removed_at,
      removedBy: data.removed_by,
    });
  }
}

export class BillingAddressSoftMapper extends BillingAddressMapper {
  static execute(data: BillingAddressSchemaType): BillingAddressEither {
    return this.buildEntity({
      ...data,
      _id: data._id,
      razon: data.razon ?? null,
      direccion: data.direccion ?? null,
      codigo_postal: data.codigo_postal ?? null,
      numero: data.numero ?? null,
      provincia: data.provincia ?? null,
      countries_taxes_id: data.countries_taxes_id ?? null,
      countries_tax_options_id: data.countries_tax_options_id ?? null,
    });
  }
}
