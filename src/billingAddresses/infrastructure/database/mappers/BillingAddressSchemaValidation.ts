import type { BillingAddressSchemaType } from '@/billingAddresses/infrastructure/database/schemas/BillingAddressSchemaType';
import type { JSONSchemaType } from 'ajv';

export const billingAddressValidationSchema: JSONSchemaType<BillingAddressSchemaType> = {
  title: 'billingAddress Json Schema Validation',
  required: ['_id', 'negocio_id'],
  type: 'object',
  properties: {
    _id: { type: 'string' },
    negocio_id: { type: 'string' },
    tipo: { type: 'string' },
    razon: { type: 'string' },
    cif: { type: 'string', nullable: true },
    direccion: { type: 'string' },
    codigo_postal: { type: 'string' },
    numero: { type: 'string' },
    municipio: { type: 'string', nullable: true },
    provincia: { type: 'string' },
    pais: { type: 'string', nullable: true },
    iban: { type: 'string', nullable: true },
    domiciliacion_activa: { type: 'boolean' },
    iva_aplicable: { type: 'number', nullable: true },
    countries_taxes_id: { type: 'string' },
    countries_tax_options_id: { type: 'string' },
    created_at: { type: 'number' },
    created_by: { type: 'string' },
    updated_at: { type: 'number' },
    updated_by: { type: 'string' },
    removed_at: { type: 'number' },
    removed_by: { type: 'string' },
  },
};
