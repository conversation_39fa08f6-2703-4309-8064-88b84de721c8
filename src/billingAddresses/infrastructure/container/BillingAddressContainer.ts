import { container, instanceCachingFactory } from 'tsyringe';

import { BillingAddressDependencyIdentifier } from '@/billingAddresses/domain/dependencyIdentifier/BillingAddressDependencyIdentifier';
import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';

import { BillingAddressCacheRepository } from '../database/repositories/BillingAddressCacheRepository';
import { BillingAddressMongoRepository } from '../database/repositories/BillingAddressMongoRepository';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';

export const BillingAddressContainer = {
  register: (): void => {
    container.register(BillingAddressDependencyIdentifier.BillingAddressRepository, {
      useFactory: instanceCachingFactory((container) => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);
        const mongoRepository = new BillingAddressMongoRepository(dbConnection);

        const cacheHander = container.resolve<ICacheRepository>(DependencyIdentifier.ICacheHandler);
        const cacheConfig = container.resolve<CacheConfig>(DependencyIdentifier.CacheConfig);

        return new BillingAddressCacheRepository(cacheHander, cacheConfig, mongoRepository);
      }),
    });
  },
};
