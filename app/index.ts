import 'reflect-metadata';

import { UnexpectedError, type Logger } from '@discocil/fv-domain-library/domain';
import { container } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { initializeContainer } from '@/cross-cutting/infrastructure/container/Container';
import config, { isDeveloperEnv } from '@config/index';

import { Server } from './http/Server';

const bootstrap = async (): Promise<void> => {
  const {
    domain,
    env,
    port,
  } = config;

  await initializeContainer();

  const logger = container.resolve<Logger>(DependencyIdentifier.Logger);
  const isLocal = isDeveloperEnv(env);

  process.on('uncaughtException', (error: Error) => {
    if (isLocal) {
      return;
    }

    logger.error(`👎🏽 | uncaughtException: ${error.toString()}`);
  });

  process.on('unhandledRejection', (error: Error) => {
    if (isLocal) {
      return;
    }

    logger.error(`👎🏽 | unhandledRejection: ${error.toString()}`);
  });

  try {
    const app = container.resolve(Server);

    await app.start(domain, port);
  } catch (error) {
    const catchError = UnexpectedError.build({
      context: 'index',
      error: error as Error,
    });

    logger.error(catchError);
  }
};

void bootstrap();
