import { singleton } from 'tsyringe';

import { VerifyAuth } from '@/cross-cutting/infrastructure/middlewares/VerifyAuth';

import { HTTP_CODES } from '../HttpCodes';

import type { FastifyReply, FastifyRequest } from 'fastify';

@singleton()
export class VerifyAuthOnRequest {
  async handle(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    try {
      const currentUrl = request.raw.url ?? '';

      const urlWithoutAuth = [
        '/generateGuestToken',
        '/liveness',
        '/readiness',
        '/docs',
      ];

      const isUrlWithoutAuth = urlWithoutAuth.some(url => currentUrl.includes(url));

      if (
        request.routeOptions.method === 'OPTIONS'
        || isUrlWithoutAuth
      ) {
        return;
      }

      const verifyAuthResult = await VerifyAuth.handle(request);

      if (verifyAuthResult.isLeft()) {
        reply.status(HTTP_CODES.UNAUTHORIZED_401);
        reply.send({ message: verifyAuthResult.value.message });
      }
    } catch (error) {
      void reply.send(error);
    }
  }
}
