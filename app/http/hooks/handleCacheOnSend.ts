import { FvString } from '@discocil/fv-domain-library/domain';

import { HTTP_CODES } from '../HttpCodes';

import type {
  DoneFuncWithErrOrRes,
  FastifyReply,
  FastifyRequest,
} from 'fastify';

export const handleCacheOnSend = (
  request: FastifyRequest,
  reply: FastifyReply,
  payload: unknown,
  done: DoneFuncWithErrOrRes,
): void => {
  const isSuccess = reply.statusCode >= HTTP_CODES.OK_200 && reply.statusCode < HTTP_CODES.MULTIPLE_CHOICES_300;

  if (isSuccess && FvString.is(payload)) {
    const tempPayload = JSON.parse(payload);

    request.cache.setItem('metadata' in tempPayload
      ? payload
      : tempPayload.data);
  }

  done(null, payload);
};
