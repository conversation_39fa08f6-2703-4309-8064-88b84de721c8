import { FvString } from '@discocil/fv-domain-library/domain';

import { HTTP_CODES } from '../HttpCodes';

import type {
  DoneFuncWithErrOrRes,
  FastifyReply,
  FastifyRequest,
} from 'fastify';

export const transformPayloadOnSend = (
  request: FastifyRequest,
  reply: FastifyReply,
  payload: unknown,
  done: DoneFuncWithErrOrRes,
): void => {
  const isRoutesEnabled = (): boolean => {
    const isSuccess = reply.statusCode >= HTTP_CODES.OK_200 && reply.statusCode < HTTP_CODES.MULTIPLE_CHOICES_300;
    const isExcluded = ['/liveness', '/readiness'].includes(request.url) || request.url.includes('/docs');

    return isSuccess && !isExcluded;
  };

  if (isRoutesEnabled() && FvString.is(payload)) {
    const tempPayload = JSON.parse(payload);

    if ('metadata' in tempPayload) {
      const { data, metadata } = tempPayload;

      payload = { data, metadata };
    } else {
      payload = { data: tempPayload };
    }

    payload = JSON.stringify(payload);
  }

  done(null, payload);
};
