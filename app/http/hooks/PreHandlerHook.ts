import type { Logger } from '@/cross-cutting/infrastructure/loggers/Logger';
import type {
  FastifyReply,
  FastifyRequest,
  HookHandlerDoneFunction,
} from 'fastify';

export const preHandler = (request: FastifyRequest, _reply: FastifyReply, done: HookHandlerDoneFunction, logger: Logger): void => {
  const context = {
    requestId: request.id,
    params: request.params,
    query: request.query,
    url: request.url,
    body: request.body,
  };

  logger.addNameSpace(context, done);
};
