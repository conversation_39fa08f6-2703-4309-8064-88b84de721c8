import { singleton } from 'tsyringe';
import { FastifyInstance } from 'fastify';

import { SearchTicketController } from '../controllers/upselling/controllers/SearchTicketController';
import { searchTicketSchema } from '../@types/cli-api/upselling/search/schema';
import { GetSupplementsController } from '../controllers/upselling/controllers/GetSupplementsController';
import { getSupplementsSchema } from '../@types/cli-api/upselling/getSupplements/schema';
import { AddSupplementsController } from '../controllers/upselling/controllers/AddSupplementsController';
import { addSupplementsSchema } from '../@types/cli-api/upselling/addSupplements/schema';

@singleton()
export class UpsellingRoutes {
  constructor(
    private readonly searchController: SearchTicketController,
    private readonly supplementsController: GetSupplementsController,
    private readonly addSupplementsController: AddSupplementsController,
  ) {}

  async setup(server: FastifyInstance): Promise<void> {
    server.get('/', {
      schema: {
        tags: ['Upselling'],
        description: 'Endpoint where can find a ticket',
        ...searchTicketSchema,
      },
      handler: this.searchController.handler.bind(this.searchController),
    });

    server.get('/supplements/:ticketTypeId', {
      schema: {
        tags: ['Upselling'],
        description: 'Endpoint where can find a ticket supplements',
        ...getSupplementsSchema,
      },
      handler: this.supplementsController.handler.bind(this.supplementsController),
    });

    server.post('/supplements', {
      schema: {
        tags: ['Upselling'],
        description: 'Endpoint where can add a ticket supplements',
        ...addSupplementsSchema,
      },
      handler: this.addSupplementsController.handler.bind(this.addSupplementsController),
    });
  }
}
