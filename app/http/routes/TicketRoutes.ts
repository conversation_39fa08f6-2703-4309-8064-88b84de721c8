import { singleton } from 'tsyringe';

import { activateTicketSchema } from '../@types/cli-api/tickets/activate/schema';
import { findTicketSchema } from '../@types/cli-api/tickets/find/schema';
import { purchaseTicketsSchema } from '../@types/cli-api/tickets/purchase/schema';
import { searchTicketSchema } from '../@types/cli-api/tickets/search/schema';
import { searchActivationTicketsSchema } from '../@types/cli-api/tickets/searchActivation/schema';
import { FindTicketController } from '../controllers/tickets/controllers/FindTicketController';
import { PartialModifyTicketsController } from '../controllers/tickets/controllers/PartialModifyTicketsController';
import { PurchaseTicketController } from '../controllers/tickets/controllers/PurchaseTicketController';
import { SearchActivationTicketsController } from '../controllers/tickets/controllers/SearchActivationTicketsController';
import { SearchTicketsController } from '../controllers/tickets/controllers/SearchTicketsController';
import { PurchaseTicketsRequest } from '../controllers/tickets/requests/PurchaseTicketsRequest';

import type { FastifyInstance } from 'fastify';

@singleton()
export class TicketRoutes {
  constructor(
    private readonly findController: FindTicketController,
    private readonly partialModifyController: PartialModifyTicketsController,
    private readonly purchaseController: PurchaseTicketController,
    private readonly purchaseRequest: PurchaseTicketsRequest,
    private readonly searchController: SearchTicketsController,
    private readonly searchActivationTicketsController: SearchActivationTicketsController,
  ) {}

  async setup(server: FastifyInstance): Promise<void> {
    server.post('/', {
      schema: {
        tags: ['Tickets'],
        description: 'Endpoint where you can buy tickets',
        ...purchaseTicketsSchema,
      },
      preValidation: this.purchaseRequest.preValidation.bind(this.purchaseRequest),
      handler: this.purchaseController.handler.bind(this.purchaseController),
    });

    server.get('/:idOrActivationCode', {
      schema: {
        tags: ['Tickets'],
        description: 'Endpoint where can find a ticket',
        ...findTicketSchema,
      },
      handler: this.findController.handler.bind(this.findController),
    });

    server.get('/', {
      schema: {
        tags: ['Tickets'],
        description: 'Endpoint where can search tickets',
        ...searchTicketSchema,
      },
      handler: this.searchController.handler.bind(this.searchController),
    });

    server.get('/activation/:ticketOrPurchaseId', {
      schema: {
        tags: ['Tickets'],
        description: 'Endpoint where you can find or search tickets for activation',
        ...searchActivationTicketsSchema,
      },
      handler: this.searchActivationTicketsController.handler.bind(this.searchActivationTicketsController),
    });

    server.patch('/:ticketOrPurchaseId', {
      schema: {
        tags: ['Tickets'],
        description: 'Endpoint where you can activate a ticket',
        ...activateTicketSchema,
      },
      handler: this.partialModifyController.handler.bind(this.partialModifyController),
    });
  }
}
