import { singleton } from 'tsyringe';

import { getPassTypeSchema } from '../@types/cli-api/passType/find/schema';
import { searchPassTypeSchema } from '../@types/cli-api/passType/search/schema';
import { FindPassTypeController } from '../controllers/passType/FindPassTypeController';
import { SearchPassTypesController } from '../controllers/passType/SearchPassTypesController';

import type { FastifyInstance } from 'fastify';

@singleton()
export class PassTypeRoutes {
  constructor(
    private readonly findPassTypeController: FindPassTypeController,
    private readonly searchPassTypesController: SearchPassTypesController,
  ) {}

  async setup(server: FastifyInstance): Promise<void> {
    server.get('/:id', {
      schema: {
        tags: ['PassType'],
        description: 'Endpoint where can find pass type by specific id',
        ...getPassTypeSchema,
      },
      handler: this.findPassTypeController.handler.bind(this.findPassTypeController),
    });

    server.get('', {
      schema: {
        tags: ['PassType'],
        description: 'Endpoint where can find pass types by specific filters',
        ...searchPassTypeSchema,
      },
      handler: this.searchPassTypesController.handler.bind(this.searchPassTypesController),
    });
  }
}
