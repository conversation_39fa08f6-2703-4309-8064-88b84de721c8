import { type FastifyInstance } from 'fastify';
import { singleton } from 'tsyringe';

import { searchFormFieldsSchema } from '@app/http/@types/cli-api/reservations/form-fields/search/schema';
import { findRateSchema } from '@app/http/@types/cli-api/reservations/rates/find/schema';
import { purchaseReservationSchema } from '@app/http/@types/cli-api/reservations/reservations/purchase/schema';
import { findZoneSchema } from '@app/http/@types/cli-api/reservations/zones/find/schema';
import { searchZonesSchema } from '@app/http/@types/cli-api/reservations/zones/search/schema';
import { SearchFormFieldsController } from '@app/http/controllers/reservations/reservations/controllers/SearchFormFieldsController';
import { FindZoneController } from '@app/http/controllers/reservations/zones/controllers/FindZoneController';
import { SearchZonesController } from '@app/http/controllers/reservations/zones/controllers/SearchZonesController';

import { findReservationSchema } from '../@types/cli-api/reservations/reservations/find/schema';
import { modifyReservationSchema } from '../@types/cli-api/reservations/reservations/modify/schema';
import { FindRateController } from '../controllers/reservations/rates/controllers/FindRateController';
import { FindReservationController } from '../controllers/reservations/reservations/controllers/FindReservationController';
import { ModifyReservationController } from '../controllers/reservations/reservations/controllers/ModifyReservationController';
import { PurchaseReservationController } from '../controllers/reservations/reservations/controllers/PurchaseReservationController';

@singleton()
export class ReservationRoutes {
  constructor(
    private readonly findReservationController: FindReservationController,
    private readonly modifyReservationController: ModifyReservationController,
    private readonly purchaseReservationController: PurchaseReservationController,
    private readonly searchZonesController: SearchZonesController,
    private readonly findZoneController: FindZoneController,
    private readonly findRateController: FindRateController,
    private readonly searchFormFieldsController: SearchFormFieldsController,
  ) {}

  async setup(server: FastifyInstance): Promise<void> {
    server.get('', {
      schema: {
        tags: ['Reservations'],
        description: 'Endpoint to get a reservation by activation code and return form fields',
        ...findReservationSchema,
      },
      handler: this.findReservationController.handler.bind(this.findReservationController),
    });

    server.patch('/', {
      schema: {
        tags: ['Reservations'],
        description: 'Endpoint to patch a reservation and return a payment URL',
        ...modifyReservationSchema,
      },
      handler: this.modifyReservationController.handler.bind(this.modifyReservationController),
    });

    server.post('/', {
      schema: {
        tags: ['Reservations'],
        description: 'Endpoint to create a reservation and return a payment URL',
        ...purchaseReservationSchema,
      },
      handler: this.purchaseReservationController.handler.bind(this.purchaseReservationController),
    });

    server.get('/zones', {
      schema: {
        tags: ['Zones'],
        description: 'Endpoint to get all zones',
        ...searchZonesSchema,
      },
      handler: this.searchZonesController.handler.bind(this.searchZonesController),
    });

    server.get('/zones/:zoneId', {
      schema: {
        tags: ['Zones'],
        description: 'Endpoint to get a zone by id',
        ...findZoneSchema,
      },
      handler: this.findZoneController.handler.bind(this.findZoneController),
    });

    server.get('/rates/:rateId', {
      schema: {
        tags: ['Rates'],
        description: 'Endpoint to get a rate by id',
        ...findRateSchema,
      },
      handler: this.findRateController.handler.bind(this.findRateController),
    });

    server.get('/form-fields/:organizationId', {
      schema: {
        tags: ['Form Fields'],
        description: 'Endpoint to get form fields by organization id',
        ...searchFormFieldsSchema,
      },
      handler: this.searchFormFieldsController.handler.bind(this.searchFormFieldsController),
    });
  }
}
