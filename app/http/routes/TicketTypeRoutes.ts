import { FastifyInstance } from 'fastify';
import { singleton } from 'tsyringe';

import { findTicketTypeSchema } from '../@types/cli-api/ticketsTypes/find/schema';
import { getPricingTicketTypeSchema } from '../@types/cli-api/ticketsTypes/pricing/schema';
import { FindTicketTypeController } from '../controllers/ticketsTypes/controllers/FindTicketTypeController';
import { GetPricingTicketTypeController } from '../controllers/ticketsTypes/controllers/GetPricingTicketTypeController';

@singleton()
export class TicketTypeRoutes {
  constructor(
    private readonly findController: FindTicketTypeController,
    private readonly pricingController: GetPricingTicketTypeController,
  ) {}

  async setup(server: FastifyInstance): Promise<void> {
    server.get('/:id/pricing', {
      schema: {
        tags: ['Tickets-types'],
        description: 'Endpoint where can find a pricing for a ticket',
        ...getPricingTicketTypeSchema,
      },
      handler: this.pricingController.handler.bind(this.pricingController),
    });

    server.get('/:id', {
      schema: {
        tags: ['Tickets-types'],
        description: 'Endpoint where can find a ticket type',
        ...findTicketTypeSchema,
      },
      handler: this.findController.handler.bind(this.findController),
    });
  }
}
