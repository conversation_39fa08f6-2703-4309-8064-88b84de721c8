import { singleton } from 'tsyringe';

import { getPassSchema } from '../@types/cli-api/pass/find/schema';
import { purchasePassSchema } from '../@types/cli-api/pass/purchase/schema';
import { FindPassController } from '../controllers/pass/FindPassController';
import { PurchasePassController } from '../controllers/pass/PurchasePassController';

import type { FastifyInstance } from 'fastify';

@singleton()
export class PassRoutes {
  constructor(
    private readonly purchasePassController: PurchasePassController,
    private readonly findPassController: FindPassController,
  ) {}

  async setup(server: FastifyInstance): Promise<void> {
    server.post('/', {
      schema: {
        tags: ['Pass'],
        description: 'Endpoint to purchase a pass',
        ...purchasePassSchema,
      },
      handler: this.purchasePassController.handler.bind(this.purchasePassController),
    });

    server.get('/:id', {
      schema: {
        tags: ['Pass'],
        description: 'Endpoint where can find pass by specific id',
        ...getPassSchema,
      },
      handler: this.findPassController.handler.bind(this.findPassController),
    });
  }
}
