import { singleton } from 'tsyringe';

import { deletePaymentSchema } from '../@types/cli-api/payments/deletePayment/schema';
import { searchPaymentsSchema } from '../@types/cli-api/payments/searchPayments/schema';
import { DeletePaymentController } from '../controllers/payments/DeletePaymentController';
import { SearchPaymentsController } from '../controllers/payments/SearchPaymentsController';

import type { FastifyInstance } from 'fastify';

@singleton()
export class PaymentRoutes {
  constructor(
    private readonly searchPaymentsController: SearchPaymentsController,
    private readonly deletePaymentController: DeletePaymentController,
  ) { }

  async setup(server: FastifyInstance): Promise<void> {
    server.get('/', {
      schema: {
        tags: ['Payments'],
        description: 'Endpoint to search payments',
        ...searchPaymentsSchema,
      },
      handler: this.searchPaymentsController.handler.bind(this.searchPaymentsController),
    });

    server.delete('/:id', {
      schema: {
        tags: ['Payments'],
        description: 'Endpoint to delete a payment',
        ...deletePaymentSchema,
      },
      handler: this.deletePaymentController.handler.bind(this.deletePaymentController),
    });
  }
}
