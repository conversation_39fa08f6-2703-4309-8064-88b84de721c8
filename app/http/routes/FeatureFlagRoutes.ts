import { singleton } from 'tsyringe';

import { findFeatureFlagSchema } from '../@types/cli-api/featureFlag/find/schema';
import { FindFeatureFlagController } from '../controllers/featureFlag/controllers/FindFeatureFlagController';

import type { FastifyInstance } from 'fastify';

@singleton()
export class FeatureFlagRoutes {
  constructor(
    private readonly findController: FindFeatureFlagController,
  ) {}

  async setup(server: FastifyInstance): Promise<void> {
    server.get('/:key', {
      schema: {
        tags: ['FeatureFlag'],
        description: 'Find a feature flag by key and slug',
        ...findFeatureFlagSchema,
      },
      handler: this.findController.handler.bind(this.findController),
    });
  }
}
