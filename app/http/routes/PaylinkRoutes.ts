import { singleton } from 'tsyringe';

import { findPaylinkSchema as findPassPaylinkSchema } from '../@types/cli-api/paylinks/passes/find/schema';
import { findPaylinkSchema as findTicketPaylinkSchema } from '../@types/cli-api/paylinks/tickets/find/schema';
import { FindPassPaylinkController } from '../controllers/paylink/FindPassPaylinkController';
import { FindTicketPaylinkController } from '../controllers/paylink/FindTicketPaylinkController';

import type { FastifyInstance } from 'fastify';

@singleton()
export class PaylinkRoutes {
  constructor(
    private readonly findPassPaylinkController: FindPassPaylinkController,
    private readonly findTicketPaylinkController: FindTicketPaylinkController,
  ) { }

  async setup(server: FastifyInstance): Promise<void> {
    server.get('/passes/:activateCode', {
      schema: {
        tags: ['Paylinks'],
        description: 'Endpoint where can find a paylink for a pass',
        ...findPassPaylinkSchema,
      },
      handler: this.findPassPaylinkController.handler.bind(this.findPassPaylinkController),
    });

    server.get('/tickets/:activateCode', {
      schema: {
        tags: ['Paylinks'],
        description: 'Endpoint where can find a paylink for a ticket',
        ...findTicketPaylinkSchema,
      },
      handler: this.findTicketPaylinkController.handler.bind(this.findTicketPaylinkController),
    });
  }
}
