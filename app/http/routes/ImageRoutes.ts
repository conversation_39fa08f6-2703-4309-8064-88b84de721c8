import { singleton } from 'tsyringe';

import { UploadImageController } from '../controllers/image/UploadImageController';

import type { FastifyInstance } from 'fastify';

@singleton()
export class ImageRoutes {
  constructor(private readonly uploadImageController: UploadImageController) {}

  async setup(server: FastifyInstance): Promise<void> {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    void server.register(require('@fastify/multipart'));

    server.post('/', {}, this.uploadImageController.handler.bind(this.uploadImageController));
  }
}
