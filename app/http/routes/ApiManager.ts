import { singleton } from 'tsyringe';

import { EventRoutes } from './EventRoutes';
import { FeatureFlagRoutes } from './FeatureFlagRoutes';
import { FeeRoutes } from './FeeRoutes';
import { ImageRoutes } from './ImageRoutes';
import { MicrositeRoutes } from './MicrositesRoutes';
import { PassRoutes } from './PassRoutes';
import { PassTypeRoutes } from './PassTypeRoutes';
import { PaylinkRoutes } from './PaylinkRoutes';
import { PaymentRoutes } from './PaymentRoutes';
import { ReservationRoutes } from './ReservationRoutes';
import { TicketRoutes } from './TicketRoutes';
import { TicketTypeRoutes } from './TicketTypeRoutes';
import { TrackingRoutes } from './TrackingRoutes';
import { UpsellingRoutes } from './UpsellingRoutes';

import type { FastifyInstance } from 'fastify';

@singleton()
export class ApiManager {
  constructor(
    private readonly events: EventRoutes,
    private readonly microsites: MicrositeRoutes,
    private readonly passes: PassRoutes,
    private readonly passTypes: PassTypeRoutes,
    private readonly paylinks: PaylinkRoutes,
    private readonly reservations: ReservationRoutes,
    private readonly tickets: TicketRoutes,
    private readonly ticketTypes: TicketTypeRoutes,
    private readonly uploadImages: ImageRoutes,
    private readonly upselling: UpsellingRoutes,
    private readonly featureFlags: FeatureFlagRoutes,
    private readonly fees: FeeRoutes,
    private readonly tracking: TrackingRoutes,
    private readonly payments: PaymentRoutes,
  ) { }

  async run(server: FastifyInstance): Promise<void> {
    void server.register(this.events.setup.bind(this.events), { prefix: '/events' });
    void server.register(this.microsites.setup.bind(this.microsites), { prefix: '/microsites' });
    void server.register(this.passes.setup.bind(this.passes), { prefix: '/pass' });
    void server.register(this.passTypes.setup.bind(this.passTypes), { prefix: '/pass-type' });
    void server.register(this.paylinks.setup.bind(this.paylinks), { prefix: '/paylinks' });
    void server.register(this.reservations.setup.bind(this.reservations), { prefix: '/reservation' });
    void server.register(this.tickets.setup.bind(this.tickets), { prefix: '/tickets' });
    void server.register(this.ticketTypes.setup.bind(this.ticketTypes), { prefix: '/tickets-types' });
    void server.register(this.uploadImages.setup.bind(this.uploadImages), { prefix: '/file' });
    void server.register(this.upselling.setup.bind(this.upselling), { prefix: '/upselling' });
    void server.register(this.featureFlags.setup.bind(this.featureFlags), { prefix: '/feature-flag' });
    void server.register(this.fees.setup.bind(this.fees), { prefix: '/fees' });
    void server.register(this.tracking.setup.bind(this.tracking), { prefix: '/trackings' });
    void server.register(this.payments.setup.bind(this.payments), { prefix: '/payments' });
  }
}
