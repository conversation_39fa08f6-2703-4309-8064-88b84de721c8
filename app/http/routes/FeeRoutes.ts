import { FastifyInstance } from 'fastify';
import { singleton } from 'tsyringe';

import { searchFeesSchema } from '../@types/cli-api/fees/search/schema';
import { SearchFeesController } from '../controllers/fees/controllers/SearchFeesController';

@singleton()
export class FeeRoutes {
  constructor(
    private readonly searchController: SearchFeesController,
  ) {}

  async setup(server: FastifyInstance): Promise<void> {
    server.get('/', {
      schema: {
        tags: ['Fees'],
        description: 'Endpoint where can search custom fees',
        ...searchFeesSchema,
      },
      handler: this.searchController.handler.bind(this.searchController),
    });
  }
}
