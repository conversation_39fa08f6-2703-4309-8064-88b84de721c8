import { singleton } from 'tsyringe';

import { findMicrositeTypeSchema } from '../@types/cli-api/microsite/find/schema';
import { FindMicrositeController } from '../controllers/microsite/FindMicrositeController';

import type { FastifyInstance } from 'fastify';

@singleton()
export class MicrositeRoutes {
  constructor(private readonly findMicrositeController: FindMicrositeController) {}

  async setup(server: FastifyInstance): Promise<void> {
    server.get('/:slug', {
      schema: {
        tags: ['Microsites'],
        description: 'Endpoint where can find data about Microsite',
        ...findMicrositeTypeSchema,
      },
      handler: this.findMicrositeController.handler.bind(this.findMicrositeController),
    });
  }
}
