import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { FindPassPaylinkUseCase } from '@/paylinks/application/FindPassPaylinkUseCase';
import { FindPaylinkDTO } from '@/paylinks/domain/contracts/FindPaylinkDtoContract';

import { FindPassPaylinkResponse } from './responses/passes/FindPassPaylinkResponse';

import type {
  FindPaylinkReply,
  FindPaylinkRequest,
  FindPaylinkReplyResponse as Response,
} from '@app/http/@types/cli-api/paylinks/passes/find/schema';

@singleton()
export class FindPassPaylinkController {
  constructor(private readonly useCase: FindPassPaylinkUseCase) { }

  @cacheController()
  async handler(request: FindPaylinkRequest, reply: FindPaylinkReply): Promise<Response> {
    const dto: FindPaylinkDTO = { activateCode: request.params.activateCode };

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    const response = FindPassPaylinkResponse();
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
