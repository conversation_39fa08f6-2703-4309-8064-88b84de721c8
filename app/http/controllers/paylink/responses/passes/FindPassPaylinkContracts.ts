import type { ResponseFindPassPaylinkSchemaDto } from '@/paylinks/domain/contracts/FindPassPaylinkResponse';
import type { successResponseSchema } from '@app/http/@types/cli-api/paylinks/passes/find/successResponseSchema';
import type { HTTP_CODES } from '@app/http/HttpCodes';
import type { Static } from '@sinclair/typebox';

export interface IFindPassPaylinkResource {
  execute: (findPaylink: ResponseFindPassPaylinkSchemaDto) => FindPassPaylinkResponseType;
  status: () => HTTP_CODES;
}

export type FindPassPaylinkResponseType = Static<typeof successResponseSchema>;
