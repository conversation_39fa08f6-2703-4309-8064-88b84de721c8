import { HTTP_CODES } from '@app/http/HttpCodes';

import type { ResponseFindPassPaylinkSchemaDto } from '@/paylinks/domain/contracts/FindPassPaylinkResponse';
import type { FindPassPaylinkResponseType, IFindPassPaylinkResource } from './FindPassPaylinkContracts';

export const FindPassPaylinkResponse = (): IFindPassPaylinkResource => {
  const execute = (findPaylink: ResponseFindPassPaylinkSchemaDto): FindPassPaylinkResponseType => {
    const { organization, paylink } = findPaylink;

    return {
      id: paylink.id,
      organization: {
        id: organization.id,
        name: organization.name,
        slug: organization.slug.fold(() => null, item => item),
        image: organization.image.fold(() => null, item => item),
      },
      resource: {
        ids: paylink.resourceIds,
        type: paylink.resourceType,
      },
      referrer: { id: paylink.referrerId },
      rate: { id: paylink.rateId },
      phone: paylink.phone.fold(() => null, item => item),
      email: paylink.email.fold(() => null, item => item),
      activateCode: paylink.activateCode,
      status: paylink.status,
      language: paylink.language,
    };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
