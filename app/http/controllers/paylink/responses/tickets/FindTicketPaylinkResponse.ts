import { HTTP_CODES } from '@app/http/HttpCodes';

import type { ResponseFindTicketPaylinkSchemaDto } from '@/paylinks/domain/contracts/FindTicketPaylinkResponse';
import type { FindTicketPaylinkResponseType, IFindTicketPaylinkResource } from '../tickets/FindTicketPaylinkContracts';

export const FindTicketPaylinkResponse = (): IFindTicketPaylinkResource => {
  const execute = (findPaylink: ResponseFindTicketPaylinkSchemaDto): FindTicketPaylinkResponseType => {
    const {
      organization, paylink, event, quantitySelector,
    } = findPaylink;

    return {
      id: paylink.id,
      organization: {
        id: organization.id,
        slug: organization.slug.fold(() => null, item => item),
      },
      event: { id: event.id },
      referrer: { id: paylink.referrerId },
      ticketType: {
        id: paylink.rateId,
        customers: {
          max: quantitySelector.maximum,
          min: quantitySelector.minimum,
          quantity: quantitySelector.quantity,
        },
      },
      phone: paylink.phone.fold(() => null, item => item),
      email: paylink.email.fold(() => null, item => item),
      activateCode: paylink.activateCode,
      status: paylink.status,
      language: paylink.language,
      nCompleted: paylink.nCompleted,
      nRequested: paylink.nRequested,
    };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
