import type { ResponseFindTicketPaylinkSchemaDto } from '@/paylinks/domain/contracts/FindTicketPaylinkResponse';
import type { successResponseSchema } from '@app/http/@types/cli-api/paylinks/tickets/find/successResponseSchema';
import type { HTTP_CODES } from '@app/http/HttpCodes';
import type { Static } from '@sinclair/typebox';

export interface IFindTicketPaylinkResource {
  execute: (findPaylink: ResponseFindTicketPaylinkSchemaDto) => FindTicketPaylinkResponseType;
  status: () => HTTP_CODES;
}

export type FindTicketPaylinkResponseType = Static<typeof successResponseSchema>;
