import type { FindMicrositeResponseType } from '@/microsite/domain/contracts/FindMicrositeResponse';
import type { featureFlagSuccessResponseSchema } from '@app/http/@types/cli-api/featureFlag/find/successResponseSchema';
import type { HTTP_CODES } from '@app/http/HttpCodes';
import type { Static } from '@sinclair/typebox';

export type FindFeatureFlagResponseDto = Static<typeof featureFlagSuccessResponseSchema>;

export interface IFindFeatureFlagResource {
  execute: (findMicrosite: FindMicrositeResponseType) => FindFeatureFlagResponseDto;
  status: () => HTTP_CODES;
}
