import { HTTP_CODES } from '@app/http/HttpCodes';

import type { featureFlagSuccessResponseSchema } from '@app/http/@types/cli-api/featureFlag/find/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof featureFlagSuccessResponseSchema>;

type FindFeatureFlagResponseType = {
  isEnabledForSlugs: boolean;
};

export const FindFeatureFlagResponse = (): IResponse<FindFeatureFlagResponseType, Response> => {
  const execute = (dto: FindFeatureFlagResponseType): Response => {
    const { isEnabledForSlugs } = dto;

    return { isEnabled: isEnabledForSlugs };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
