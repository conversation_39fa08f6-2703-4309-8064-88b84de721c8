import { Collection } from '@discocil/fv-domain-library/domain';

import { EFeatureFlagEnvironments } from '@/featureFlags/domain/entities/FeatureFlag';
import { Enviroments } from '@config/enviroments';
import config from '@config/index';

import type { FindFeatureFlagDto } from '@/featureFlags/domain/contracts/FindFeatureFlagDto';
import type { FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { findFeatureFlagSchema } from '@app/http/@types/cli-api/featureFlag/find/schema';

type Request = FastifyRequestTypebox<typeof findFeatureFlagSchema>;

export const parseFindFeatureFlagRequest = (request: Request): FindFeatureFlagDto => {
  return {
    slugChannel: request.query.slug,
    featureFlagKey: request.params.key,
    environment: parseConfigEnvironment(config.env),
  };
};

const parseConfigEnvironment = (env: Enviroments): EFeatureFlagEnvironments => {
  const configEnvToFeatureFlagEnv = Collection.build([
    { key: Enviroments.DEVELOP, value: EFeatureFlagEnvironments.LOCAL },
    { key: Enviroments.TEST, value: EFeatureFlagEnvironments.LOCAL },
    { key: Enviroments.ALPHA, value: EFeatureFlagEnvironments.ALPHA },
    { key: Enviroments.LAB1, value: EFeatureFlagEnvironments.PRODUCTION },
  ], 'key');

  const featureFlagEnvResult = configEnvToFeatureFlagEnv.getByIndex(env);

  return featureFlagEnvResult.fold(() => EFeatureFlagEnvironments.PRODUCTION, item => item.value);
};
