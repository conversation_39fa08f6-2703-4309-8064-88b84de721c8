import { NotFoundError } from '@discocil/fv-domain-library/domain';
import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { FindFeatureFlagUseCase } from '@/featureFlags/application/FindFeatureFlagUseCase';

import { parseFindFeatureFlagRequest } from '../requests/ParseFindFeatureFlagRequest';
import { FindFeatureFlagResponse } from '../responses/FindFeatureFlagResponse';

import type { FindEventResponse as Response } from '@app/http/@types/cli-api/events/find/schema';
import type { FindFeatureFlagReply, FindFeatureFlagRequest } from '@app/http/@types/cli-api/featureFlag/find/schema';

@singleton()
export class FindFeatureFlagController {
  constructor(private readonly useCase: FindFeatureFlagUseCase) {}

  @cacheController()
  async handler(request: FindFeatureFlagRequest, reply: FindFeatureFlagReply): Promise<Response> {
    const dto = parseFindFeatureFlagRequest(request);
    const useCaseResultOrError = await this.useCase.execute(dto);

    if (useCaseResultOrError.isLeft() && !(useCaseResultOrError.value instanceof NotFoundError)) {
      const { code } = getErrorInfo(useCaseResultOrError.value);

      const errorInstance = useCaseResultOrError.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      return reply.code(code).send(errorInstance);
    }

    let isEnabledForSlugs = false;

    if (useCaseResultOrError.isRight()) {
      isEnabledForSlugs = useCaseResultOrError.value.isEnabledForSlugs;
    }

    const response = FindFeatureFlagResponse();
    const responseData = response.execute({ isEnabledForSlugs });

    return reply.code(response.status()).send(responseData);
  }
}
