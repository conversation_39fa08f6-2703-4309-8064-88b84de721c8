import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { SearchPaymentsUseCase } from '@/payments/application/SearchPaymentsUseCase';

import { parseSearchPaymentsRequest } from './requests/ParseSearchPaymentsRequest';
import { SearchPaymentsResponse } from './responses/SearchPaymentsResponse';

import type { SearchPaymentsReply, SearchPaymentsRequest } from '@app/http/@types/cli-api/payments/searchPayments/schema';
import type { successResponseSchema } from '@app/http/@types/cli-api/payments/searchPayments/successResponseSchema';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

@singleton()
export class SearchPaymentsController {
  constructor(private readonly useCase: SearchPaymentsUseCase) { }

  @cacheController()
  async handler(request: SearchPaymentsRequest, reply: SearchPaymentsReply): Promise<Response> {
    const dto = parseSearchPaymentsRequest(request);

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.get().contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    const response = SearchPaymentsResponse();
    const responseData = response.execute(useCaseResult.get());

    return reply.code(response.status()).send(responseData);
  }
}
