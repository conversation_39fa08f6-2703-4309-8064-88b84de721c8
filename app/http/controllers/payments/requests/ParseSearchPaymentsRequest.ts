import { paginationOptionRequest } from '@discocil/fv-criteria-converter-library/infrastructure';
import { UniqueEntityID } from '@discocil/fv-domain-library/domain';

import type { SearchPaymentsDto } from '@/payments/domain/contracts/SearchPaymentsDtoContract';
import type { SearchPaymentsRequest } from '@app/http/@types/cli-api/payments/searchPayments/schema';

export const parseSearchPaymentsRequest = (request: SearchPaymentsRequest): SearchPaymentsDto => {
  const { query } = request;

  const eventId = UniqueEntityID.build(query.eventId);

  return {
    eventId,
    idx: query.idx,
    status: query.status,
    pagination: paginationOptionRequest(query),
  };
};
