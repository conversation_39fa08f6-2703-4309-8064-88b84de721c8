import { UniqueEntityID } from '@discocil/fv-domain-library/domain';

import type { DeletePaymentDto } from '@/payments/domain/contracts/DeletePaymentDtoContract';
import type { DeletePaymentRequest } from '@app/http/@types/cli-api/payments/deletePayment/schema';

export const parseDeletePaymentRequest = (request: DeletePaymentRequest): DeletePaymentDto => {
  const { params } = request;

  return { paymentId: UniqueEntityID.build(params.id) };
};
