
import { HTTP_CODES } from '@app/http/HttpCodes';
import { paginationResponse } from '@app/http/responses/PaginationResponse';

import type { SearchPaginatedPaymentsResponse } from '@/payments/domain/entities/Payment';
import type { successResponseSchema } from '@app/http/@types/cli-api/payments/searchPayments/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

export const SearchPaymentsResponse = (): IResponse<SearchPaginatedPaymentsResponse, Response> => {
  const execute = (dto: SearchPaginatedPaymentsResponse): Response['data'] => {
    const { paymentsWithTickets } = dto;

    return [...paymentsWithTickets.values()].map((paymentWithTickets) => {
      const { payment, tickets } = paymentWithTickets;

      const userNames: string[] = [];

      for (const _ticket of tickets.values()) {
        if (_ticket.name.isDefined()) {
          userNames.push(_ticket.name.get());
        }
      }

      return {
        id: payment.id,
        userNames,
        expiresAt: payment.getExpiresAtInMilliseconds(),
      };
    });
  };

  return {
    execute: paginationResponse(execute),
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
