import { UniqueEntityID } from '@discocil/fv-domain-library/domain';

import type { ECustomFeeApplyType } from '@discocil/fv-domain-library/domain';
import type { SearchFeesRequestDto } from '@/fees/domain/contracts/SearchFeesContracts';
import type { SearchFeesRequest } from '@app/http/@types/cli-api/fees/search/schema';

export const parseSearchFeesRequest = (request: SearchFeesRequest): SearchFeesRequestDto => {
  return {
    applyTo: request.query.applyTo as ECustomFeeApplyType,
    organizationId: UniqueEntityID.build(request.query.organizationId),
  };
};
