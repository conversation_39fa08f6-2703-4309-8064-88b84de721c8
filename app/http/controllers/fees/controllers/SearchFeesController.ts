import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { SearchFeesUseCase } from '@/fees/application/SearchFeesUseCase';

import { parseSearchFeesRequest } from '../requests/SearchFeesRequest';
import { SearchFeesResponse } from '../responses/SearchFeesResponse';

import type {
  SearchFeesReply,
  SearchFeesReplyResponse,
  SearchFeesRequest,
} from '@app/http/@types/cli-api/fees/search/schema';

@singleton()
export class SearchFeesController {
  constructor(private readonly useCase: SearchFeesUseCase) {}

  @cacheController()
  async handler(request: SearchFeesRequest, reply: SearchFeesReply): Promise<SearchFeesReplyResponse> {
    const dto = parseSearchFeesRequest(request);

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return await reply.code(code).send(errorInstance);
    }

    const response = SearchFeesResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
