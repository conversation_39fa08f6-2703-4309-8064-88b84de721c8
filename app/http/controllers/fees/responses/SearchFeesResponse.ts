import { HTTP_CODES } from '@app/http/HttpCodes';

import type { successResponseSchema } from '@app/http/@types/cli-api/fees/search/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';
import type { Fees } from '@/fees/domain/entities/Fee';

export type Response = Static<typeof successResponseSchema>;

export const SearchFeesResponse = (): IResponse<Fees, Response> => {
  const execute = (dto: Fees): Response =>
    dto.toArray().map(fee => ({
      id: fee.id,
      name: fee.name,
      applyTo: fee.applyTo.map(String),
      feesToApply: fee.feesToApply,
      type: String(fee.type),
      calculation: fee.calculation,
      state: String(fee.state),
    }));

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
