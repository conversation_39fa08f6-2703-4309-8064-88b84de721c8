import { UrlsPayment } from '@/payments/infrastructure/decorators/UrlsPayment';
import { HTTP_CODES } from '@app/http/HttpCodes';

import type { PurchaseResponse } from '@/tickets/tickets/domain/contracts/PurchaseContracts';
import type { successResponseSchema } from '@app/http/@types/cli-api/tickets/purchase/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

export const PurchaseTicketsResponse = (): IResponse<PurchaseResponse, Response> => {
  const execute = (dto: PurchaseResponse): Response => {
    const { purchase } = dto;

    const urls = purchase.hasPayment()
      ? new UrlsPayment(purchase.getPayment().get())
      : null;

    return {
      ids: [...purchase.getTicketIds()],
      payment: { url: urls?.getUrl() ?? null },
    };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.CREATED_201,
  };
};
