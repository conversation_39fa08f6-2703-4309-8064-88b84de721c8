import { UrlsTicket } from '@/tickets/tickets/infrastructure/decorators/UrlsTicket';
import { HTTP_CODES } from '@app/http/HttpCodes';

import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { FindTicketResponseType } from '@/tickets/tickets/domain/contracts/FindTicketContracts';
import type {
  Ticket,
  TicketCommissionsPrimitives,
  TicketWarrantyPrimitives,
} from '@/tickets/tickets/domain/entities/TicketEntity';
import type { successResponseSchema } from '@app/http/@types/cli-api/tickets/find/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { IdPrimitive, Maybe } from '@discocil/fv-domain-library/domain';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

type PersonalDocument = {
  readonly number: string | null;
  readonly type: string | null;
};

type Payment = {
  readonly id: string;
};

type Assistant = {
  readonly name: string | null;
  readonly email: string;
  readonly phone: string | null;
  readonly personalDocument: PersonalDocument | null;
};

type TicketCustomer = {
  readonly fullname: string | null;
  readonly email: string;
  readonly phone: string | null;
};

type Supplement = {
  readonly id: string;
  readonly label: string;
  readonly price: number;
  readonly fakePrice: number | null;
  readonly purchaseQuantity: number | null;
  readonly description: string | null;
};

const buildPersonalDocument = (ticket: Ticket): PersonalDocument | null => {
  const {
    personalDocumentNumber: number,
    personalDocumentType: type,
    dni,
  } = ticket;

  const exists = !!(number ?? type ?? dni);

  if (!exists) {
    return null;
  }

  return {
    number: number.fold(() => dni.fold(() => null, item => item), item => item),
    type: type.fold(() => null, item => item),
  };
};

const buildPayment = (paymentId: IdPrimitive): Payment => {
  return { id: paymentId };
};

const buildAssistant = (ticket: Ticket): Assistant | null => {
  const {
    name,
    email,
    phone,
  } = ticket;

  if (email.isEmpty()) {
    return null;
  }

  const personalDocument = buildPersonalDocument(ticket);

  return {
    email: email.get(),
    name: name.fold(() => null, item => item),
    phone: phone.fold(() => null, item => item),
    personalDocument,
  };
};

export const buildCustomer = (ticket: Ticket): TicketCustomer | null => {
  const {
    name,
    email,
    phone,
  } = ticket;

  if (email.isEmpty()) {
    return null;
  }

  return {
    email: email.get(),
    fullname: name.fold(() => null, item => item),
    phone: phone.fold(() => null, item => item),
  };
};

const buildSupplement = (supplement: Ticket['supplements'][0]): Supplement => {
  return {
    id: supplement.id,
    label: supplement.label,
    price: supplement.price,
    fakePrice: supplement.fakePrice ?? null,
    purchaseQuantity: supplement.purchaseQuantity.fold(() => null, item => item.quantity),
    description: supplement.description,
  };
};

const buildWarranty = (_warranty: Maybe<TicketWarrantyPrimitives>): { total: number; } | null => {
  if (_warranty.isEmpty()) {
    return null;
  }

  const warranty = _warranty.get();

  if (warranty.total.isDefined()) {
    return { total: warranty.total.get() };
  }

  return null;
};

const buildCommissions = (commissions: TicketCommissionsPrimitives): Pick<TicketCommissionsPrimitives, 'business'> => {
  return { business: commissions.business };
};

export const FindTicketResponse = (): IResponse<FindTicketResponseType, Response> => {
  const execute = (dto: FindTicketResponseType): Response => {
    const { ticket, organization } = dto;

    return mapSingleTicketResponse(ticket, organization);
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};

export const mapSingleTicketResponse = (ticket: Ticket, organization: Organization): Response => {
  return {
    id: ticket.id,
    organization: {
      id: organization.id,
      slug: organization.slug.fold(() => null, item => item),
    },
    eventId: ticket.eventId,
    type: {
      id: ticket.typeId,
      type: ticket.type,
    },
    referrerId: ticket.referrerId.fold(() => null, item => item),
    nominative: ticket.nominative,
    purchaseId: ticket.purchaseId.fold(() => null, item => item),
    paymentId: ticket.paymentId.fold(() => null, item => item),
    organizationAssignedId: ticket.organizationAssignedId,
    code: ticket.code,
    assistant: buildAssistant(ticket),
    payment: ticket.paymentId.isDefined() ? buildPayment(ticket.paymentId.get()) : null,
    urls: urlsTicketResponse(ticket),
    customer: buildCustomer(ticket),
    price: ticket.price,
    totalPrice: ticket.importTotal,
    remarketing: ticket.remarketing,
    warranty: buildWarranty(ticket.warranty),
    supplements: ticket.supplements.map(buildSupplement),
    commissions: ticket.commissions.isDefined() ? buildCommissions(ticket.commissions.get()) : null,
    option: {
      name: ticket.option.name.fold(() => null, value => value),
      price: ticket.option.price,
    },
    state: ticket.state,
  };
};

export const urlsTicketResponse = (ticket: Ticket): Response['urls'] => {
  const urls = new UrlsTicket(ticket);

  return {
    download: urls.getDownload(),
    export: urls.getExport(),
    payment: urls.getPayment(),
    source: ticket.userWebData.fold(
      () => null,
      item => item.sourceUrl.fold(() => null, item => item),
    ),
  };
};
