import { HTTP_CODES } from '@app/http/HttpCodes';
import { paginationResponse } from '@app/http/responses/PaginationResponse';

import { mapSingleTicketResponse } from './FindTicketResponse';

import type { SearchActivationTicketsResponse as SearchActivationTicketsUseCaseResponse } from '@/tickets/tickets/domain/contracts/SearchActivationTicketsContracts';
import type { successResponseSchema } from '@app/http/@types/cli-api/tickets/searchActivation/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

export const SearchActivationTicketsResponse = (): IResponse<SearchActivationTicketsUseCaseResponse, Response> => {
  const execute = (dto: SearchActivationTicketsUseCaseResponse): Response['data'] => {
    const { tickets, organization } = dto;

    return [...tickets.values()].map(ticket =>
      mapSingleTicketResponse(ticket, organization));
  };

  return {
    execute: paginationResponse(execute),
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
