import { singleton } from 'tsyringe';

import { MaintenanceError } from '@/cross-cutting/infrastructure/errors/MaintenanceError';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { PurchaseTicketsOrderUseCase } from '@/tickets/tickets/application/use-cases/PurchaseTicketsOrderUseCase';
import { PurchaseTicketsUseCase } from '@/tickets/tickets/application/use-cases/PurchaseTicketsUseCase';
import { EPurchaseTicketsFlow } from '@/tickets/tickets/domain/enums/TicketEnum';
import { HTTP_CODES } from '@app/http/HttpCodes';
import config from '@config/index';

import { PurchaseTicketsRequest } from '../requests/PurchaseTicketsRequest';
import { PurchaseTicketsResponse } from '../responses/PurchaseTicketsResponse';

import type {
  PurchaseTicketsReply,
  PurchaseTicketsRequest as Request,
  PurchaseTicketsResponse as Response,
} from '@app/http/@types/cli-api/tickets/purchase/schema';

@singleton()
export class PurchaseTicketController {
  constructor(
    private readonly singleUseCase: PurchaseTicketsUseCase,
    private readonly orderUseCase: PurchaseTicketsOrderUseCase,
  ) {}

  async handler(request: Request, reply: PurchaseTicketsReply): Promise<Response> {
    if (config.maintenance.enabled) {
      const maintenanceError = MaintenanceError.build({ context: this.constructor.name });

      return await reply.code(HTTP_CODES.SERVICE_UNAVAILABLE_503).send({ message: maintenanceError.message });
    }

    const dto = PurchaseTicketsRequest.parser(request);

    const useCaseResult = await (dto.flow === EPurchaseTicketsFlow.ONE_TYPE ? this.singleUseCase.execute(dto) : this.orderUseCase.execute(dto));

    if (useCaseResult.isLeft()) {
      request.error = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code, message } = getErrorInfo(request.error);

      return await reply.code(code).send({ message });
    }

    const response = PurchaseTicketsResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
