import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { SearchTicketsUseCase } from '@/tickets/tickets/application/use-cases/SearchTicketsUseCase';

import { parseSearchTicketsRequest } from '../requests/SearchTicketRequest';
import { SearchTicketResponse } from '../responses/SearchTicketResponse';

import type { SearchTicketsDto } from '@/tickets/tickets/domain/contracts/SearchTicketsDtoContract';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { searchTicketSchema } from '@app/http/@types/cli-api/tickets/search/schema';

type Response = ReturnType<ReturnType<typeof SearchTicketResponse>['execute']>;

type Schema = typeof searchTicketSchema;

@singleton()
export class SearchTicketsController {
  constructor(private readonly useCase: SearchTicketsUseCase) {}

  @cacheController()
  async handler(request: FastifyRequestTypebox<Schema>, reply: FastifyReplyTypebox<Schema>): Promise<Response> {
    const dto: SearchTicketsDto = parseSearchTicketsRequest(request);

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return await reply.code(code).send(errorInstance);
    }

    const response = SearchTicketResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
