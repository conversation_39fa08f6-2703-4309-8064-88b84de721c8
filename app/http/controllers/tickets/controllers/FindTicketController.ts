import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { FindTicketUseCase } from '@/tickets/tickets/application/use-cases/FindTicketUseCase';

import { FindTicketResponse } from '../responses/FindTicketResponse';

import type { FindTicketDto } from '@/tickets/tickets/domain/contracts/FindTicketContracts';
import type {
  FindTicketReply,
  FindTicketRequest,
  FindTicketResponse as Response,
} from '@app/http/@types/cli-api/tickets/find/schema';

@singleton()
export class FindTicketController {
  constructor(private readonly useCase: FindTicketUseCase) {}

  @cacheController()
  async handler(request: FindTicketRequest, reply: FindTicketReply): Promise<Response> {
    const dto: FindTicketDto = { idOrActivationCode: request.params.idOrActivationCode };

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return await reply.code(code).send(errorInstance);
    }

    const response = FindTicketResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
