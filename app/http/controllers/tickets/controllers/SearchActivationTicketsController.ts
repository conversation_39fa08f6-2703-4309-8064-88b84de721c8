import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { SearchActivationTicketsUseCase } from '@/tickets/tickets/application/use-cases/SearchActivationTicketsUseCase';
import { SearchActivationTicketsDto } from '@/tickets/tickets/domain/contracts/SearchActivationTicketsContracts';

import { SearchActivationTicketsResponse } from '../responses/SearchActivationTicketsResponse';

import type {
  SearchActivationTicketsResponse as Response,
  SearchActivationTicketsReply,
  SearchActivationTicketsRequest,
} from '@app/http/@types/cli-api/tickets/searchActivation/schema';

@singleton()
export class SearchActivationTicketsController {
  constructor(private readonly useCase: SearchActivationTicketsUseCase) {}

  @cacheController()
  async handler(request: SearchActivationTicketsRequest, reply: SearchActivationTicketsReply): Promise<Response> {
    const dto: SearchActivationTicketsDto = { ticketOrPurchaseId: request.params.ticketOrPurchaseId };

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return await reply.code(code).send(errorInstance);
    }

    const response = SearchActivationTicketsResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
