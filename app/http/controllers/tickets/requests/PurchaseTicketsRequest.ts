import { ESaleTypes } from '@discocil/fv-domain-library/domain';
import { singleton } from 'tsyringe';

import { purchaseTicketsOrderParser } from './PurchaseTicketsOrderParser';
import { purchaseTicketsParser } from './PurchaseTicketsParser';

import type { PurchaseTicketsDto, PurchaseTicketsOrderDto } from '@/tickets/tickets/domain/contracts/PurchaseTicketsDto';
import type {
  PurchaseTicketsReply,
  PurchaseTicketsRequest as Request,
} from '@app/http/@types/cli-api/tickets/purchase/schema';
import type { HookHandlerDoneFunction } from 'fastify/types/hooks';

@singleton()
export class PurchaseTicketsRequest {
  preValidation(request: Request, _reply: PurchaseTicketsReply, done: HookHandlerDoneFunction): void {
    if (!request.body.saleType) {
      request.body.saleType = ESaleTypes.ONLINE;
    }

    if (!request.body.warrantySelected) {
      request.body.warrantySelected = false;
    }

    done();
  }

  static parser(request: Request): PurchaseTicketsDto | PurchaseTicketsOrderDto {
    return 'typeId' in request.body ? purchaseTicketsParser(request) : purchaseTicketsOrderParser(request);
  }
}
