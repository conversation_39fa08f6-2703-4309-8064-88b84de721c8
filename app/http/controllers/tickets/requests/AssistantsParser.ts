import { FvDate, Maybe } from '@discocil/fv-domain-library/domain';

import type { AssistantRequestPrimitives } from '@/tickets/tickets/domain/entities/Assistant';
import type { OrderPurchaseAssistants } from '@app/http/@types/cli-api/tickets/purchase/bodySchema';

export const assistantsParser = (assistant: OrderPurchaseAssistants): AssistantRequestPrimitives => {
  return {
    supplements: assistant.supplements ?? [],
    fullname: Maybe.from<PERSON><PERSON><PERSON>(assistant.fullname),
    email: assistant.email,
    emailConfirmation: Maybe.from<PERSON><PERSON><PERSON>(assistant.emailConfirmation),
    phone: Maybe.fromValue(assistant.phone),
    birthDate: assistant.birthDate ? Maybe.some(FvDate.createFromSeconds(assistant.birthDate)) : Maybe.none<FvDate>(),
    gender: Maybe.fromValue(assistant.gender),
    personalDocument: Maybe.from<PERSON><PERSON><PERSON>(assistant.personalDocument),
    address: Maybe.fromValue(assistant.address),
    country: Maybe.fromVal<PERSON>(assistant.country),
    zipCode: Maybe.from<PERSON><PERSON><PERSON>(assistant.zipCode),
    answers: assistant.answers ?? [],
    optionId: Maybe.none<string>(),
  };
};
