import { paginationOptionRequest } from '@discocil/fv-criteria-converter-library/infrastructure';

import type { SearchTicketsDto } from '@/tickets/tickets/domain/contracts/SearchTicketsDtoContract';
import type { FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { searchTicketSchema } from '@app/http/@types/cli-api/tickets/search/schema';

type Request = FastifyRequestTypebox<typeof searchTicketSchema>;

export const parseSearchTicketsRequest = (request: Request): SearchTicketsDto => {
  return {
    ids: request.query.ids,
    idx: request.query.idx,
    purchaseId: request.query.purchaseId,
    pagination: paginationOptionRequest(request.query),
  };
};
