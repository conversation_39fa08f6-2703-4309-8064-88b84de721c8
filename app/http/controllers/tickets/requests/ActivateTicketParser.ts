import { assistantsParser } from './AssistantsParser';

import type { ActivateTicketDto } from '@/tickets/tickets/domain/contracts/ActivateTicketContracts';
import type { ActivateTicketRequest } from '@app/http/@types/cli-api/tickets/activate/schema';

export const activateTicketParser = (request: ActivateTicketRequest): ActivateTicketDto => {
  const { params, body } = request;

  return {
    identifier: params.ticketOrPurchaseId,
    assistants: body.assistants.map((assistant) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { supplements, ...restOfAssistant } = assistantsParser(assistant);

      return {
        ...restOfAssistant,
        ticketId: assistant.ticketId,
      };
    }),
    remarketing: body.remarketing,
    idx: body.idx,
    ip: request.fingerPrint.fingerPrint.ip,
    os: request.fingerPrint.fingerPrint.os.name,
    language: request.fingerPrint.fingerPrint.language.veryShort,
    browser: request.fingerPrint.fingerPrint.browser.name,
    device: request.fingerPrint.fingerPrint.device.name,
  };
};
