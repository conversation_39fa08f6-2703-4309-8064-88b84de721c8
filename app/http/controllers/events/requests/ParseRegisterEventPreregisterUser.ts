import { Maybe, UniqueEntityID } from '@discocil/fv-domain-library/domain';

import type { RegisterPreregisterDto } from '@/events/events/domain/contracts/RegisterPreregisterDtoContract';
import type { FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { registerEventPreregisterUserSchema } from '@app/http/@types/cli-api/events/registerPreregisterUser/schema';
import type { ELanguagesCodes } from '@discocil/fv-domain-library/domain';

type Request = FastifyRequestTypebox<typeof registerEventPreregisterUserSchema>;

export const parseRegisterEventPreregisterUser = (request: Request): RegisterPreregisterDto => {
  return {
    eventId: UniqueEntityID.build(request.params.id),
    fields: request.body.fields,
    remarketing: request.body.remarketing,
    newsletter: request.body.newsletter,
    language: Maybe.fromValue<ELanguagesCodes>(request.fingerPrint.fingerPrint.language.veryShort as ELanguagesCodes),
  };
};
