import { paginationOptionRequest } from '@discocil/fv-criteria-converter-library/infrastructure';

import type { SearchEventPassTypesDto } from '@/events/events/domain/contracts/passType/SearchEventPassTypesContract';
import type { FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { searchPassTypesSchema } from '@app/http/@types/cli-api/events/searchPassTypes/schema';

type Request = FastifyRequestTypebox<typeof searchPassTypesSchema>;

export const parseSearchEventPassesRequest = (request: Request): SearchEventPassTypesDto => {
  const organizationSlugs = new Set(request.query.organizations);

  return {
    idOrCode: request.params.idOrCode,
    slug: request.query.slug,
    organizationSlugs,
    language: request.fingerPrint.fingerPrint.language.short,
    smsSale: request.query.smsSale ?? false,
    pagination: paginationOptionRequest(request.query),
  };
};
