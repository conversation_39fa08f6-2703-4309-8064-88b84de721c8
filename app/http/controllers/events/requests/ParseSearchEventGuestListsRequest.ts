import { paginationOptionRequest } from '@discocil/fv-criteria-converter-library/infrastructure';

import type { SearchEventGuestListTypesDto } from '@/events/events/domain/contracts/guestListType/SearchEventGuestListTypesContract';
import type { FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { searchGuestListsSchema } from '@app/http/@types/cli-api/events/searchGuestLists/schema';

type Request = FastifyRequestTypebox<typeof searchGuestListsSchema>;

export const parseSearchEventGuestListsRequest = (request: Request): SearchEventGuestListTypesDto => {
  const organizationSlugs = new Set(request.query.organizations);

  return {
    id: request.params.id,
    slug: request.query.slug,
    organizationSlugs,
    language: request.fingerPrint.fingerPrint.language.short,
    smsSale: request.query.smsSale ?? false,
    pagination: paginationOptionRequest(request.query),
  };
};
