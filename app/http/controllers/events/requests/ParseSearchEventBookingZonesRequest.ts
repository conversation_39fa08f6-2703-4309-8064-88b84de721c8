import { paginationOptionRequest } from '@discocil/fv-criteria-converter-library/infrastructure';

import type { SearchEventBookingZonesDto } from '@/events/events/domain/contracts/bookingZones/SearchEventBookingZonesContract';
import type { FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { searchBookingsSchema } from '@app/http/@types/cli-api/events/searchBookingZones/schema';

type Request = FastifyRequestTypebox<typeof searchBookingsSchema>;

export const parseSearchEventBookingZonesRequest = (request: Request): SearchEventBookingZonesDto => {
  const organizationSlugs = new Set(request.query.organizations);

  return {
    id: request.params.id,
    slug: request.query.slug,
    organizationSlugs,
    language: request.fingerPrint.fingerPrint.language.short,
    smsSale: request.query.smsSale ?? false,
    pagination: paginationOptionRequest(request.query),
  };
};
