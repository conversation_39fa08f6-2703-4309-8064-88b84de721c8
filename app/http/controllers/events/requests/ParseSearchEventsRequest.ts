import { paginationOptionRequest } from '@discocil/fv-criteria-converter-library/infrastructure';
import { FvDate, Maybe } from '@discocil/fv-domain-library/domain';

import type { SearchEventDto } from '@/microsite/domain/contracts/SearchEventDtoContract';
import type { SearchEventsRequest } from '@app/http/@types/cli-api/events/searchEvents/schema';

export const parseSearchEventsRequest = (request: SearchEventsRequest): SearchEventDto => {
  const { query } = request;

  const endDate = query.endDate ? Maybe.some(FvDate.createFromSeconds(query.endDate)) : Maybe.none<FvDate>();
  const startDate = query.startDate ? Maybe.some(FvDate.createFromSeconds(query.startDate)) : Maybe.none<FvDate>();
  const slug = Maybe.fromValue(query.slug);
  const organizationSlugs = new Set(request.query.organizations);
  const eventGroupCodes = new Set(request.query.groupCodes);

  return {
    endDate,
    slug,
    organizationSlugs,
    eventGroupCodes,
    startDate,
    pagination: paginationOptionRequest(query),
    isSitemap: Boolean(query.isSitemap),
    isPrivate: false,
  };
};
