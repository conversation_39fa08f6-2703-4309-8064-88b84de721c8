import type { FindEventMetadataDto } from '@/events/events/domain/contracts/FindEventMetadataContract';
import type { FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { findEventMetadataSchema } from '@app/http/@types/cli-api/events/findMetadata/schema';

type Request = FastifyRequestTypebox<typeof findEventMetadataSchema>;

export const parseFindEventMetadataRequest = (request: Request): FindEventMetadataDto => {
  return {
    idOrCode: request.params.idOrCode,
    language: request.fingerPrint.fingerPrint.language.short,
  };
};
