import { Maybe } from '@discocil/fv-domain-library/domain';

import type { FindEventDto } from '@/events/events/domain/contracts/FindEventContract';
import type { FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { findEventSchema } from '@app/http/@types/cli-api/events/find/schema';

type Request = FastifyRequestTypebox<typeof findEventSchema>;

export const parseFindEventRequest = (request: Request): FindEventDto => {
  return {
    idOrCode: request.params.idOrCode,
    smsSale: request.query.smsSale ?? false,
    slugChannel: Maybe.fromValue(request.query.slugChannel),
  };
};
