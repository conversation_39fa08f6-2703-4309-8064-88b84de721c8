import type { FindBookingSpaceAvailabilityDto } from '@/events/events/domain/contracts/bookingSpaceAvailability/FindBookingSpaceAvailabilityContract';
import type { FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { FindBookingSpaceAvailabilitySchema } from '@app/http/@types/cli-api/events/findBookingSpaceAvailability/schema';

type Request = FastifyRequestTypebox<FindBookingSpaceAvailabilitySchema>;

export const parseFindBookingSpaceAvailabilityRequest = (request: Request): FindBookingSpaceAvailabilityDto => {
  const { token } = request.query;

  return { token };
};
