import { Maybe } from '@discocil/fv-domain-library/domain';

import type { EventVisitedDto } from '@/events/events/domain/contracts/EventVisitedContract';
import type { FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { eventVisitedSchema } from '@app/http/@types/cli-api/events/visited/schema';
import type {
  ECountryCode,
  EGender,
  ELanguagesCodes,
} from '@discocil/fv-domain-library/domain';

type Request = FastifyRequestTypebox<typeof eventVisitedSchema>;

export const parseEventVisitedRequest = (request: Request): EventVisitedDto => {
  const {
    body, application, fingerPrint: { fingerPrint }, params,
  } = request;

  return {
    eventId: params.id,
    referrerId: body.referrerId ? Maybe.some(body.referrerId) : Maybe.none<string>(),
    applicationId: application.id,
    idx: body.idx,
    customer: {
      language: Maybe.fromValue<ELanguagesCodes>(fingerPrint.language.veryShort as ELanguagesCodes),
      country: body.customer ? Maybe.fromValue(body.customer.country) : Maybe.none<ECountryCode>(),
      gender: body.customer ? Maybe.fromValue(body.customer.gender) : Maybe.none<EGender>(),
      birthDate: body.customer ? Maybe.fromValue(body.customer.birthDate) : Maybe.none<number>(),
    },
    device: {
      browser: fingerPrint.browser.name,
      device: fingerPrint.device.name,
      os: fingerPrint.os.name,
    },
  };
};
