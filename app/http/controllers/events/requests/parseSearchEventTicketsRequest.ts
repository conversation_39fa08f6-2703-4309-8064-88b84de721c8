import { paginationOptionRequest } from '@discocil/fv-criteria-converter-library/infrastructure';

import type { SearchEventTicketTypesDto } from '@/events/events/domain/contracts/ticketType/SearchEventTicketTypesContract';
import type { FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { searchTicketsTypesSchema } from '@app/http/@types/cli-api/events/searchTicketsTypes/schema';

type Request = FastifyRequestTypebox<typeof searchTicketsTypesSchema>;

export const parseSearchEventTicketsTypesRequest = (request: Request): SearchEventTicketTypesDto => {
  const organizationSlugs = new Set(request.query.organizations);

  return {
    idOrCode: request.params.idOrCode,
    slug: request.query.slug,
    organizationSlugs,
    language: request.fingerPrint.fingerPrint.language.short,
    smsSale: request.query.smsSale ?? false,
    pagination: paginationOptionRequest(request.query),
  };
};
