import { UniqueEntityID } from '@discocil/fv-domain-library';

import type { FindEventZonesWebImageDto } from '@/events/events/application/contracts/FindEventZonesWebImageDto';
import type { FindEventZonesWebImageUseCaseRequest } from '@app/http/controllers/events/contracts/FindZonesWebImageContract';

export const FindEventZonesWebImageRequest = (request: FindEventZonesWebImageUseCaseRequest): FindEventZonesWebImageDto => {
  const { eventId } = request.params;
  const { organizationId } = request.query;

  return {
    eventId: UniqueEntityID.build(eventId),
    organizationId: UniqueEntityID.build(organizationId),
  };
};
