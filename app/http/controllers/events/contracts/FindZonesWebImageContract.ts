import type { findZonesWebImageSchema } from '@app/http/@types/cli-api/events/findZonesWebImage/schema';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { FindEventZonesWebImageResponse as Response } from '@app/http/controllers/events/responses/FindEventZonesWebImageResponse';

type Schema = typeof findZonesWebImageSchema;

export type FindEventZonesWebImageUseCaseRequest = FastifyRequestTypebox<Schema>;

export type FindEventZonesWebImageUseCaseReply = FastifyReplyTypebox<Schema>;

export type FindEventZonesWebImageUseCaseResponse = ReturnType<ReturnType<typeof Response>['execute']>;
