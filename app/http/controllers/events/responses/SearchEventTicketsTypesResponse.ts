import { HTTP_CODES } from '@app/http/HttpCodes';
import { paginationResponse } from '@app/http/responses/PaginationResponse';

import type { SearchEventTicketsTypeResponse } from '@/events/events/domain/contracts/ticketType/EventTicketTypeTypes';
import type { DisponibilityTypeResponse } from '@/tickets/ticketsTypes/domain/contracts/TicketsTypesContracts';
import type { successResponseSchema } from '@app/http/@types/cli-api/events/searchTicketsTypes/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

export const SearchEventTicketsTypesResponse = (): IResponse<SearchEventTicketsTypeResponse, Response> => {
  const execute = (dto: SearchEventTicketsTypeResponse): Response['data'] => {
    const { ticketTypes, language } = dto;

    return Array.from(ticketTypes.values()).map((ticketType) => {
      const additionalData = ticketType.additionalData.get();

      return {
        id: ticketType.id,
        name: ticketType.name,
        isComplete: ticketType.isComplete,
        type: ticketType.type,
        order: ticketType.order,
        hideCountdown: ticketType.hasHideCountdown,
        dates: {
          from: ticketType.getFromDateInSeconds(),
          to: ticketType.getToDateInSeconds().fold(() => null, value => value),
        },
        price: ticketType.getTicketTypePriceBasedOnCurrentOption(),
        priceComplete: ticketType.getTicketTypePriceCompleteBasedOnCurrentOption(language),
        isSoldOut: additionalData.isSoldOut,
        areFewLeft: ticketType.areFewLeft(),
        fewLeftSelectedKey: ticketType.fewLeftSelectedKey.fold(() => null, value => value),
        disponibility: {
          isActive: additionalData.disponibility.isActive,
          isPercentageExceed: ticketType.hasExceededPercentageLimit(additionalData.disponibility),
          type: ticketType.disponibilityLabel,
          value: additionalData.disponibility.value,
        },
        customers: {
          max: additionalData.quantitySelector.maximum,
          min: additionalData.quantitySelector.minimum,
          quantity: additionalData.quantitySelector.quantity,
        },
        options: ticketType.getOptions().map((option) => {
          const optionDisponibility = additionalData.disponibilityByOptionId.get(option.id) as DisponibilityTypeResponse;

          return {
            id: option.id,
            name: option.name.fold(() => null, value => value),
            price: option.price,
            content: option.content.fold(() => null, value => value),
            additionalInfo: option.additionalInfo.fold(() => null, value => value),
            max: option.max,
            until: option.getToDateInSeconds(),
            totalSold: option.getTotalSold(),
            disponibility: {
              isActive: optionDisponibility.isActive,
              isPercentageExceed: ticketType.hasExceededPercentageLimit(optionDisponibility),
              type: ticketType.disponibilityLabel,
              value: optionDisponibility.value,
            },
          };
        }),
        availableOptionId: ticketType.getCurrentOption().fold(() => null, value => value.id),
      };
    });
  };

  return {
    execute: paginationResponse(execute),
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
