import { HTTP_CODES } from '@app/http/HttpCodes';
import { paginationResponse } from '@app/http/responses/PaginationResponse';

import type { SearchEventBookingZonesResponse as SearchEventBookingsRequest } from '@/events/events/domain/contracts/bookingZones/EventBookingsZones';
import type { successResponseSchema } from '@app/http/@types/cli-api/events/searchBookingZones/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;
type DataResponse = Response['data'];
type Types = DataResponse[number]['types'];

export const SearchEventBookingZonesResponse = (): IResponse<SearchEventBookingsRequest, Response> => {
  const execute = (dto: SearchEventBookingsRequest): DataResponse => {
    const { zones, typesByZones } = dto;

    return [...zones.values()].map((zone) => {
      const types = typesByZones.find(zone.id);

      const typesResponse: Types = types.map((type) => {
        return {
          id: type.id,
          name: type.name,
          price: type.price.fold(() => null, price => price.amount),
          colors: type.colors,
        };
      });

      return {
        id: zone.id,
        name: zone.name,
        isComplete: zone.isComplete,
        order: zone.order,
        types: typesResponse,
      };
    });
  };

  return {
    execute: paginationResponse(execute),
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
