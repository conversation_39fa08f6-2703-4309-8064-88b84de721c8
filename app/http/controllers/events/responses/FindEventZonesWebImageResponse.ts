import { HTTP_CODES } from '@app/http/HttpCodes';

import type { Maybe } from '@discocil/fv-domain-library';
import type { successResponseSchema } from '@app/http/@types/cli-api/events/findZonesWebImage/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

export const FindEventZonesWebImageResponse = (): IResponse<Maybe<string>, Response> => {
  const execute = (webImageUrl: Maybe<string>): Response => {
    const response = { distributionWebImageUrl: webImageUrl.fold(() => null, url => url) };

    return response;
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
