import { HTTP_CODES } from '@app/http/HttpCodes';

import type { RegisterEventPreregisterUserResponse as UserResponse } from '@/events/events/domain/entities/EventPreregisterUser';
import type { successResponseSchema } from '@app/http/@types/cli-api/events/registerPreregisterUser/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

export const RegisterEventPreregisterUserResponse = (): IResponse<UserResponse, Response> => {
  const execute = (dto: UserResponse): Response => {
    const { id } = dto;

    return { id };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
