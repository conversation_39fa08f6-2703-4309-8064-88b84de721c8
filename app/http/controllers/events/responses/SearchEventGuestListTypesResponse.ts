import { FvDate } from '@discocil/fv-domain-library/domain';

import { HTTP_CODES } from '@app/http/HttpCodes';
import { paginationResponse } from '@app/http/responses/PaginationResponse';

import type { AssignGuestTypeLimits } from '@/events/events/domain/contracts/guestListType/AssignGuestListTypeLimitsContracts';
import type { SearchEventGuestListsResponse as SearchEventGuestListsRequest } from '@/events/events/domain/contracts/guestListType/EventGuestListContracts';
import type { successResponseSchema } from '@app/http/@types/cli-api/events/searchGuestLists/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;
type ItemResponse = Response['data'];
type OptionsResponse = ItemResponse[number]['options'];

export const SearchEventGuestListTypesResponse = (): IResponse<SearchEventGuestListsRequest, Response> => {
  const execute = (dto: SearchEventGuestListsRequest): ItemResponse => {
    const { guestListTypes, limits } = dto;

    return [...guestListTypes.values()].map((guestListType) => {
      const limit = limits.get(guestListType.id) as AssignGuestTypeLimits;

      const options: OptionsResponse = guestListType.options.map((option) => {
        return {
          id: option.id,
          hideCountdown: guestListType.hasHideCountdown,
          dates: {
            from: FvDate.create(option.dateFrom).toSeconds(),
            to: FvDate.create(option.dateTo).toSeconds(),
          },
          male: option.male,
          female: option.female,
          price: option.price,
          age: option.age,
          content: option.content.fold(() => null, item => item),
          additionalInfo: option.additionalInfo.fold(() => null, item => item),
        };
      });

      return {
        id: guestListType.id,
        name: guestListType.name.fold(() => null, item => item),
        isComplete: guestListType.isComplete,
        isActive: guestListType.isActive,
        type: guestListType.type,
        hideCountdown: guestListType.hasHideCountdown,
        dates: {
          from: guestListType.dateFrom.fold(() => null, date => FvDate.create(date).toSeconds()),
          to: guestListType.dateTo.fold(() => null, date => FvDate.create(date).toSeconds()),
        },
        summary: guestListType.getOptionsSummaries().map(summary => ({
          id: summary.id,
          duration: summary.duration,
          until: summary.until,
          content: summary.content.fold(() => null, item => item),
          additionalInfo: summary.additionalInfo.fold(() => null, item => item),
        })),
        maximum: limit.maximum,
        available: limit.available,
        order: guestListType.order,
        options,
      };
    });
  };

  return {
    execute: paginationResponse(execute),
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
