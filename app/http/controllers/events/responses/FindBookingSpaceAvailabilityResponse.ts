import { HTTP_CODES } from '@app/http/HttpCodes';

import type { EventAvailability } from '@/events/events/domain/contracts/bookingSpaceAvailability/FindBookingSpaceAvailabilityContract';
import type { successResponseSchema } from '@app/http/@types/cli-api/events/findBookingSpaceAvailability/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;
type Zones = Response['zones'];
type Spaces = Zones[number]['spaces'];

export const FindBookingSpaceAvailabilityResponse = (): IResponse<EventAvailability, Response> => {
  const execute = (dto: EventAvailability): Response => {
    const {
      event,
      zones,
      user,
      zonesSpacesBookingTypes,
    } = dto;

    const responseZones: Zones = [];

    for (const zone of zones.values()) {
      const responseSpaces: Spaces = [];

      for (const space of zone.getSpacesWithoutCombinations().values()) {
        const spacePrices = zonesSpacesBookingTypes.prices.get(zone.id);
        const spaceBookingTypeCapacities = zonesSpacesBookingTypes.bookingTypeCapacities.get(zone.id);

        if (!spacePrices || !spaceBookingTypeCapacities) {
          continue;
        }

        const price = spacePrices.get(space.id) ?? null;

        const bookingTypeCapacity = spaceBookingTypeCapacities.get(space.id) ?? null;

        responseSpaces.push({
          name: space.name,
          capacity: space.capacity,
          isAvailable: zone.isAvailableSpace(space.id),
          currency: event.currency,
          bookingTypeCapacity,
          price,
        });
      }

      responseZones.push({
        name: zone.name,
        spaces: responseSpaces,
      });
    }

    return {
      collaborator: {
        name: user.profile.fold(
          () => null,
          item => item.name.fold(() => null, item => item),
        ),
        image: user.profile.fold(
          () => null,
          item => item.photo.fold(() => null, item => item),
        ),
        phone: user.phone.fold(() => null, item => item),
      },
      organization: {
        name: event.businessName,
        image: event.businessImage,
        slug: event.getOrganization().fold(() => null, organization => organization.slug.fold(() => null, item => item)),
        floorImage: event.getOrganization().fold(() => null, organization => organization.floorImage.fold(() => null, item => item)),
      },
      name: event.name,
      date: event.startDate.toISOString(),
      zones: responseZones,
    };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};

