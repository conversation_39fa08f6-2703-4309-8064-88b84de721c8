
import { EventGoogleMetadata } from '@/events/events/infrastructure/services/EventGoogleMetadata';
import { HTTP_CODES } from '@app/http/HttpCodes';
import config from '@config/index';

import type { FindEventMetadataResponse as FindEventMetadataRequest } from '@/events/events/domain/entities/EventEntity';
import type { successResponseSchema } from '@app/http/@types/cli-api/events/findMetadata/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

export const FindEventMetadataResponse = (): IResponse<FindEventMetadataRequest, Response> => {
  const execute = (dto: FindEventMetadataRequest): Response => {
    const {
      event, artists, organization, ticketTypes, guestListTypes,
    } = dto;

    const eventMetadata = EventGoogleMetadata.execute({
      event,
      organization,
      artists,
      ticketTypes,
      guestListTypes,
      micrositeUrl: config.fv.apps.microsites.url ?? '',
    });

    return eventMetadata;
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
