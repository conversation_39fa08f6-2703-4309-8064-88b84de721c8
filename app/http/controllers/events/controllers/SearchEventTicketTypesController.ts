import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { SearchEventTicketTypesWithFeesUseCase } from '@/events/events/application/SearchEventTicketTypesWithFeesUseCase';

import { parseSearchEventTicketsTypesRequest } from '../requests/parseSearchEventTicketsRequest';
import { SearchEventTicketsTypesResponse } from '../responses/SearchEventTicketsTypesResponse';

import type {
  SearchEventTicketsTypesReply,
  SearchEventTicketsTypesReplyResponse,
  SearchEventTicketsTypesRequest,
} from '@app/http/@types/cli-api/events/searchTicketsTypes/schema';

@singleton()
export class SearchEventTicketTypesController {
  constructor(private readonly useCase: SearchEventTicketTypesWithFeesUseCase) {}

  @cacheController()
  async handler(request: SearchEventTicketsTypesRequest, reply: SearchEventTicketsTypesReply): Promise<SearchEventTicketsTypesReplyResponse> {
    const dto = parseSearchEventTicketsTypesRequest(request);
    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    const response = SearchEventTicketsTypesResponse();
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
