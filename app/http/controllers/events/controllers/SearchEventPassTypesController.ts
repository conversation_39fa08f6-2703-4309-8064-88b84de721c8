import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { SearchEventPassTypesUseCase } from '@/events/events/application/SearchEventPassTypesUseCase';

import { SearchPassTypesResponse } from '../../passType/responses/SearchPassTypesResponse';
import { parseSearchEventPassesRequest } from '../requests/parseSearchEventPassesRequest';

import type {
  SearchEventPassTypesReply, SearchEventPassTypesReplyResponse, SearchEventPassTypesRequest,
} from '@app/http/@types/cli-api/events/searchPassTypes/schema';

@singleton()
export class SearchEventPassTypesController {
  constructor(private readonly useCase: SearchEventPassTypesUseCase) {}

  @cacheController()
  async handler(request: SearchEventPassTypesRequest, reply: SearchEventPassTypesReply): Promise<SearchEventPassTypesReplyResponse> {
    const dto = parseSearchEventPassesRequest(request);
    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    const response = SearchPassTypesResponse();
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
