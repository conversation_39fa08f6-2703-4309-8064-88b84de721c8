import { singleton } from 'tsyringe';

import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { RegisterPreregisterUseCase } from '@/events/events/application/RegisterPreregisterUseCase';
import { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import { registerEventPreregisterUserSchema } from '@app/http/@types/cli-api/events/registerPreregisterUser/schema';

import { parseRegisterEventPreregisterUser } from '../requests/ParseRegisterEventPreregisterUser';
import { FindEventResponse } from '../responses/FindEventResponse';
import { RegisterEventPreregisterUserResponse } from '../responses/RegisterEventPreregisterUserResponse';

type Response = ReturnType<ReturnType<typeof FindEventResponse>['execute']>;
type Schema = typeof registerEventPreregisterUserSchema;

@singleton()
export class RegisterEventPreregisterUserController {
  constructor(private readonly useCase: RegisterPreregisterUseCase) {}

  async handler(request: FastifyRequestTypebox<Schema>, reply: FastifyReplyTypebox<Schema>): Promise<Response> {
    const dto = parseRegisterEventPreregisterUser(request);

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    const response = RegisterEventPreregisterUserResponse();
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
