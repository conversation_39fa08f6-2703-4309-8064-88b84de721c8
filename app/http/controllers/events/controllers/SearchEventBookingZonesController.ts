import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { SearchEventBookingZonesUseCase } from '@/events/events/application/SearchEventBookingZonesUseCase';

import { parseSearchEventBookingZonesRequest } from '../requests/ParseSearchEventBookingZonesRequest';
import { SearchEventBookingZonesResponse } from '../responses/SearchEventBookingZonesResponse';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { searchBookingsSchema } from '@app/http/@types/cli-api/events/searchBookingZones/schema';

type Response = ReturnType<ReturnType<typeof SearchEventBookingZonesResponse>['execute']>;
type Schema = typeof searchBookingsSchema;

@singleton()
export class SearchEventBookingZonesController {
  constructor(private readonly useCase: SearchEventBookingZonesUseCase) {}

  @cacheController()
  async handler(request: FastifyRequestTypebox<Schema>, reply: FastifyReplyTypebox<Schema>): Promise<Response> {
    const dto = parseSearchEventBookingZonesRequest(request);
    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    const response = SearchEventBookingZonesResponse();
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
