import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { SearchEventGuestListTypesUseCase } from '@/events/events/application/SearchEventGuestListTypesUseCase';

import { parseSearchEventGuestListsRequest } from '../requests/ParseSearchEventGuestListsRequest';
import { SearchEventGuestListTypesResponse } from '../responses/SearchEventGuestListTypesResponse';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { searchGuestListsSchema } from '@app/http/@types/cli-api/events/searchGuestLists/schema';

type Response = ReturnType<ReturnType<typeof SearchEventGuestListTypesResponse>['execute']>;

type Schema = typeof searchGuestListsSchema;

@singleton()
export class SearchEventGuestListTypesController {
  constructor(private readonly useCase: SearchEventGuestListTypesUseCase) {}

  @cacheController()
  async handler(request: FastifyRequestTypebox<Schema>, reply: FastifyReplyTypebox<Schema>): Promise<Response> {
    const dto = parseSearchEventGuestListsRequest(request);
    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    const response = SearchEventGuestListTypesResponse();
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
