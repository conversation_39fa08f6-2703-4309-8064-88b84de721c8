import { singleton } from 'tsyringe';

import { EventVisitedUseCase } from '@/events/visits/application/use-cases/EventVisitedUseCase';
import { EventVisitedReply, EventVisitedRequest } from '@app/http/@types/cli-api/events/visited/schema';
import { HTTP_CODES } from '@app/http/HttpCodes';

import { parseEventVisitedRequest } from '../requests/ParseEventVisitedRequest';

@singleton()
export class EventVisitedController {
  constructor(private readonly useCase: EventVisitedUseCase) {}

  async handler(request: EventVisitedRequest, reply: EventVisitedReply): Promise<void> {
    const dto = parseEventVisitedRequest(request);

    await this.useCase.execute(dto);

    return reply.code(HTTP_CODES.NO_CONTENT_204).send();
  }
}
