import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { FindEventUseCase } from '@/events/events/application/FindEventUseCase';
import { FindReservationsConfigurationUseCase } from '@/reservations/configuration/application/use-cases/FindReservationsConfigurationUseCase';

import { parseFindEventRequest } from '../requests/ParseFindEventRequest';
import { FindEventResponse } from '../responses/FindEventResponse';

import type {
  FindEventReply,
  FindEventRequest,
  FindEventResponse as Response,
} from '@app/http/@types/cli-api/events/find/schema';

@singleton()
export class FindEventController {
  constructor(
    private readonly useCase: FindEventUseCase,
    private readonly findReservationsConfigurationUseCase: FindReservationsConfigurationUseCase,
  ) { }

  @cacheController()
  async handler(request: FindEventRequest, reply: FindEventReply): Promise<Response> {
    const dto = parseFindEventRequest(request);
    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    const useCaseResponse = useCaseResult.value;

    const reservationsConfigurationResult = await this.findReservationsConfigurationUseCase.execute({ event: useCaseResponse.event });

    if (reservationsConfigurationResult.isLeft()) {
      const errorInstance = reservationsConfigurationResult.value.contextualize({
        context: this.constructor.name,
        data: { event: useCaseResponse.event },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    const reservationsConfiguration = reservationsConfigurationResult.value;

    const response = FindEventResponse();
    const responseData = response.execute({
      ...useCaseResponse,
      reservationsConfiguration,
    });

    return reply.code(response.status()).send(responseData);
  }
}
