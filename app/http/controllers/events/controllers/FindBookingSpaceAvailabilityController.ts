import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { FindBookingSpaceAvailabilityUseCase } from '@/events/events/application/FindBookingSpaceAvailabilityUseCase';

import { parseFindBookingSpaceAvailabilityRequest } from '../requests/ParseFindBookingSpaceAvailabilityRequest';
import { FindBookingSpaceAvailabilityResponse } from '../responses/FindBookingSpaceAvailabilityResponse';

import type {
  FindBookingSpaceAvailabilityReply,
  FindBookingSpaceAvailabilityRequest,
  FindBookingSpaceAvailabilityResponse as Response,
} from '@app/http/@types/cli-api/events/findBookingSpaceAvailability/schema';

@singleton()
export class FindBookingSpaceAvailabilityController {
  constructor(private readonly useCase: FindBookingSpaceAvailabilityUseCase) { }

  @cacheController()
  async handler(request: FindBookingSpaceAvailabilityRequest, reply: FindBookingSpaceAvailabilityReply): Promise<Response> {
    const dto = parseFindBookingSpaceAvailabilityRequest(request);
    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    const response = FindBookingSpaceAvailabilityResponse();
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
