import { singleton } from 'tsyringe';

import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { FindEventZonesWebImageUseCase } from '@/events/events/application/FindEventZonesWebImageUseCase';
import { FindEventZonesWebImageRequest } from '@app/http/controllers/events/requests/FindEventZonesWebImageRequest';
import { FindEventZonesWebImageResponse } from '@app/http/controllers/events/responses/FindEventZonesWebImageResponse';
import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';

import type {
  FindEventZonesWebImageUseCaseReply as Reply,
  FindEventZonesWebImageUseCaseRequest as Request,
} from '@app/http/controllers/events/contracts/FindZonesWebImageContract';

type Response = ReturnType<ReturnType<typeof FindEventZonesWebImageResponse>['execute']>;

@singleton()
export class FindEventZonesWebImageController {
  constructor(private readonly useCase: FindEventZonesWebImageUseCase) {}

  @cacheController()
  async handler(request: Request, reply: Reply): Promise<Response> {
    const requestParsed = FindEventZonesWebImageRequest(request);

    const useCaseResult = await this.useCase.execute(requestParsed);

    if (useCaseResult.isLeft()) {
      const { code, message } = getErrorInfo(useCaseResult.value);

      request.error = useCaseResult.value;

      return await reply.code(code).send({ message });
    }

    const response = FindEventZonesWebImageResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
