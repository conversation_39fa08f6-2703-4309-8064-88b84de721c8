import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { FindEventMetadataUseCase } from '@/events/events/application/FindEventMetadataUseCase';

import { parseFindEventMetadataRequest } from '../requests/ParseFindEventMetadataRequest';
import { FindEventMetadataResponse } from '../responses/FindEventMetadataResponse';

import type {
  FindEventMetadataReply,
  FindEventMetadataRequest,
  FindEventMetadataResponse as Response,
} from '@app/http/@types/cli-api/events/findMetadata/schema';

@singleton()
export class FindEventMetadataController {
  constructor(
    private readonly useCase: FindEventMetadataUseCase,
  ) { }

  @cacheController()
  async handler(request: FindEventMetadataRequest, reply: FindEventMetadataReply): Promise<Response> {
    const dto = parseFindEventMetadataRequest(request);
    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    const response = FindEventMetadataResponse();
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
