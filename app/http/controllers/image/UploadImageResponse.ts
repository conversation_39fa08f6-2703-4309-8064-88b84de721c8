import { HTTP_CODES } from '@app/http/HttpCodes';

import type { Image } from '@/image/domain/entities/Image';
import type { uploadImageResponseSchema } from '@app/http/@types/cli-api/image/upload/uploadImageResponeSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof uploadImageResponseSchema>;

export const UploadImageResponse = (): IResponse<Image, Response> => {
  const execute = (uploadImage: Image): Response => {
    return { url: uploadImage.url };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.CREATED_201,
  };
};
