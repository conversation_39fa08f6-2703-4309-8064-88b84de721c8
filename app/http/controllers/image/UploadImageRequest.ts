import config from '@config/index';

import type { UploadImageDto } from '@/image/domain/contracts/UploadImageDto';
import type { MultipartFile } from '@fastify/multipart';

export const parseUploadImageRequest = async (file: MultipartFile): Promise<UploadImageDto> => {
  return {
    name: file.filename,
    mimetype: file.mimetype,
    content: await file.toBuffer(),
    paths: {
      local: `${config.imagePassesLocation}/passes`,
      external: `${config.aws.cloudFront.url}`,
    },
  };
};
