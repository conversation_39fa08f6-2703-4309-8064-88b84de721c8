import { singleton } from 'tsyringe';

import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { UploadImageUseCase } from '@/image/application/UploadImageUseCase';
import { FastifyReplyTypebox, FastifyRequestMultipartTypeBox } from '@app/http/@types/cli-api/cross-cutting/schema';
import { HTTP_CODES } from '@app/http/HttpCodes';

import { parseUploadImageRequest } from './UploadImageRequest';
import { UploadImageResponse } from './UploadImageResponse';

import type { uploadImageSchema } from '@app/http/@types/cli-api/image/upload/uploadImageSchema';

type Schema = typeof uploadImageSchema;
type Response = ReturnType<ReturnType<typeof UploadImageResponse>['execute']>;
type UploadImageSchema = typeof uploadImageSchema;

@singleton()
export class UploadImageController {
  constructor(private readonly useCase: UploadImageUseCase) {}

  async handler(request: FastifyRequestMultipartTypeBox<Schema>, reply: FastifyReplyTypebox<UploadImageSchema>): Promise<Response> {
    const file = await request.file();

    if (!file) {
      return await reply.code(HTTP_CODES.BAD_REQUEST_400).send({ message: 'File is required' });
    }

    const requestBody = await parseUploadImageRequest(file);
    const useCaseResult = await this.useCase.execute(requestBody);

    if (useCaseResult.isLeft()) {
      request.error = useCaseResult.value.contextualize({ context: this.constructor.name });

      const { code, message } = getErrorInfo(request.error);

      return await reply.code(code).send({ message });
    }

    const uploadImage = useCaseResult.value;

    const response = UploadImageResponse();
    const responseData = response.execute(uploadImage);

    return reply.code(response.status()).send(responseData);
  }
}
