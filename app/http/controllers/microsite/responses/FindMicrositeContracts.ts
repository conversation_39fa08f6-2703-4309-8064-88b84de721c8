import type { FindMicrositeResponseType } from '@/microsite/domain/contracts/FindMicrositeResponse';
import type {
  micrositeSuccessResponseSchema,
  organizationDataSchema,
  referrerDataSchema,
} from '@app/http/@types/cli-api/microsite/find/successResponseSchema';
import type { HTTP_CODES } from '@app/http/HttpCodes';
import type { Static } from '@sinclair/typebox';

export type FindMicrositeResponseDto = Static<typeof micrositeSuccessResponseSchema>;

export interface IFindMicrositeResource {
  execute: (findMicrosite: FindMicrositeResponseType) => FindMicrositeResponseDto;
  status: () => HTTP_CODES;
}

export type ReferrerDataSchema = Static<typeof referrerDataSchema>;
export type OrganizationDataSchema = Static<typeof organizationDataSchema>;
