import { EMicrositeChannel } from '@discocil/fv-domain-library/domain';

import { FindMicrositeOrganizationResponse } from './FindMicrositeOrganizationResponse';
import { FindMicrositeReferrerResponse } from './FindMicrositeReferrerResponse';

import type { IFindMicrositeResource } from './FindMicrositeContracts';

export class FindMicrositeResponseFactory {
  static build(channel: EMicrositeChannel): IFindMicrositeResource {
    const match: Record<EMicrositeChannel, () => IFindMicrositeResource> = {
      [EMicrositeChannel.ORGANIZATION]: () => FindMicrositeOrganizationResponse(),
      [EMicrositeChannel.REFERRER]: () => FindMicrositeReferrerResponse(),
    };

    return match[channel]();
  }
}
