import { HTTP_CODES } from '@app/http/HttpCodes';

import type { FindMicrositeResponseType, ReferrerData } from '@/microsite/domain/contracts/FindMicrositeResponse';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type {
  FindMicrositeResponseDto, IFindMicrositeResource, ReferrerDataSchema,
} from './FindMicrositeContracts';

type OrganizationSchema = ReferrerDataSchema['organizations'][number];

export const FindMicrositeReferrerResponse = (): IFindMicrositeResource => {
  const execute = (findMicrosite: FindMicrositeResponseType): FindMicrositeResponseDto => {
    const findMicrositeData = findMicrosite.data as ReferrerData;
    const {
      user, organizations, organizationsWithPassTypes,
    } = findMicrositeData;

    return {
      channel: findMicrosite.channel,
      data: {
        id: user.id,
        name: user.profile.fold(() => null, profile => profile.name.fold(() => null, item => item)),
        slug: user.username.fold(() => null, item => item),
        image: user.profile?.fold(() => null, profile => profile.photo.fold(() => null, item => item)),
        lastname: user.profile.fold(() => null, profile => profile.lastname.fold(() => null, item => item)),
        fullName: user.getFullName().fold(() => null, item => item),
        organizations: [...organizations.values()].map((organization: Organization): OrganizationSchema => {
          return {
            id: organization.id,
            name: organization.name,
            slug: organization.slug.fold(() => null, item => item),
            image: organization.image.fold(() => null, item => item),
            type: organization.type,
            hasPassTypes: organizationsWithPassTypes.has(organization.id),
            currency: organization.currency,
          };
        }),
      },
    };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
