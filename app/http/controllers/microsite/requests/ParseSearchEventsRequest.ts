import type { FindMicrositeDTO } from '@/microsite/domain/contracts/FindMicrositeResponse';
import type { FindMicrositeRequest } from '@app/http/@types/cli-api/microsite/find/schema';

export const parseFindMicrositeRequest = (request: FindMicrositeRequest): FindMicrositeDTO => {
  const organizationSlugs = new Set(request.query.organizations);

  return {
    slug: request.params.slug,
    organizationSlugs,
  };
};
