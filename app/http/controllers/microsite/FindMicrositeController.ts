import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { FindMicrositeUseCase } from '@/microsite/application/FindMicrositeUseCase';

import { parseFindMicrositeRequest } from './requests/ParseSearchEventsRequest';
import { FindMicrositeResponseDto } from './responses/FindMicrositeContracts';
import { FindMicrositeResponseFactory } from './responses/FindMicrositeResponseFactory';

import type { FindMicrositeReply, FindMicrositeRequest } from '@app/http/@types/cli-api/microsite/find/schema';

type Response = FindMicrositeResponseDto;

@singleton()
export class FindMicrositeController {
  constructor(private readonly useCase: FindMicrositeUseCase) {}

  @cacheController()
  async handler(request: FindMicrositeRequest, reply: FindMicrositeReply): Promise<Response> {
    const dto = parseFindMicrositeRequest(request);

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    const response = FindMicrositeResponseFactory.build(useCaseResult.value.channel);
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
