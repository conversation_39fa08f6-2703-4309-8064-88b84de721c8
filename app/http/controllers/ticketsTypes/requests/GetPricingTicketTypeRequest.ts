import type { GetPricingTicketTypeDto } from '@/tickets/ticketsTypes/domain/contracts/GetPricingTicketTypeDto';
import type { PricingTicketTypeRequest } from '@app/http/@types/cli-api/ticketsTypes/pricing/schema';

export const parseGetPricingTicketTypeRequest = (request: PricingTicketTypeRequest): GetPricingTicketTypeDto => {
  return {
    id: request.params.id,
    organizationId: request.query.organizationId,
    eventIdOrCode: request.query.eventIdentifier.toString(),
    amount: request.query.amount,
    organizationAssignedId: request.query.organizationAssignedId ?? null,
  };
};
