import type { FindTicketTypeDto } from '@/tickets/ticketsTypes/domain/contracts/FindTicketTypeDto';
import type { FindTicketTypeRequest } from '@app/http/@types/cli-api/ticketsTypes/find/schema';

export const parseFindTicketTypeRequest = (request: FindTicketTypeRequest): FindTicketTypeDto => {
  return {
    id: request.params.id,
    slug: request.query.slug,
    eventIdOrCode: request.query.eventIdentifier.toString(),
    isActive: request.query.isActive ?? true,
  };
};
