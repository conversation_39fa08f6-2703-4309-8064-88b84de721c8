import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { GetPricingTicketTypeUseCase } from '@/tickets/ticketsTypes/application/use-cases/GetPricingTicketTypeUseCase';

import { parseGetPricingTicketTypeRequest } from '../requests/GetPricingTicketTypeRequest';
import { PricingTicketTypeResponse } from '../responses/PricingTicketTypeResponse';

import type {
  PricingTicketTypeReply,
  PricingTicketTypeReplyResponse,
  PricingTicketTypeRequest,
} from '@app/http/@types/cli-api/ticketsTypes/pricing/schema';

@singleton()
export class GetPricingTicketTypeController {
  constructor(private readonly useCase: GetPricingTicketTypeUseCase) {}

  @cacheController()
  async handler(request: PricingTicketTypeRequest, reply: PricingTicketTypeReply): Promise<PricingTicketTypeReplyResponse> {
    const dto = parseGetPricingTicketTypeRequest(request);
    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return await reply.code(code).send(errorInstance);
    }

    const response = PricingTicketTypeResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
