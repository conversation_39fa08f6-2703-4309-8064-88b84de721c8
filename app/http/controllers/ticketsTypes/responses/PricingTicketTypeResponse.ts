import { HTTP_CODES } from '@app/http/HttpCodes';

import type { TicketTypePricing } from '@/tickets/ticketsTypes/domain/contracts/PricingTicketContract';
import type { successResponseSchema } from '@app/http/@types/cli-api/ticketsTypes/pricing/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

export const PricingTicketTypeResponse = (): IResponse<TicketTypePricing, Response> => {
  const execute = (dto: TicketTypePricing): Response => {
    const {
      samePrice,
      options: optionsIncoming,
    } = dto;

    const ids: string[] = [];
    const prices: number[] = [];

    const options = optionsIncoming.map((option) => {
      const price = option.price;

      ids.push(option.id);
      prices.push(price);

      return {
        id: option.id,
        name: option.name.fold(() => null, value => value),
        price,
        ggdd: {
          type: option.ggddType,
          amount: option.ggddAmount,
        },
      };
    });

    return {
      samePrice,
      ids,
      prices,
      options,
    };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
