import type { AddSupplementsDto } from '@/upselling/tickets/application/contracts/AddSupplementsDto';
import type { FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { addSupplementsSchema } from '@app/http/@types/cli-api/upselling/addSupplements/schema';

export const parseAddSupplementsRequest = (request: FastifyRequestTypebox<typeof addSupplementsSchema>): AddSupplementsDto => {
  const { body } = request;

  return {
    ticketId: body.ticketId,
    supplements: body.supplements.map(supplement => ({
      id: supplement.id,
      purchaseQuantity: supplement.purchaseQuantity,
    })),
    urls: {
      redirect: body.urls.redirect,
      error: body.urls.error,
    },
  };
};
