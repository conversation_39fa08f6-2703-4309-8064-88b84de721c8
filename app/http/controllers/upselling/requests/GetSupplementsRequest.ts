import type { GetSupplementsDto } from '@/upselling/ticketTypes/application/contracts/GetSupplementsDto';
import type { FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { getSupplementsSchema } from '@app/http/@types/cli-api/upselling/getSupplements/schema';

export const parseGetSupplementsRequest = (request: FastifyRequestTypebox<typeof getSupplementsSchema>): GetSupplementsDto => {
  return { ticketTypeId: request.params.ticketTypeId };
};
