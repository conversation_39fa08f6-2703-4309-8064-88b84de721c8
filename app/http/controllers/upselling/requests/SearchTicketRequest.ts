import type { SearchTicketDto } from '@/upselling/tickets/application/contracts/SearchTicketDto';
import type { FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { searchTicketSchema } from '@app/http/@types/cli-api/upselling/search/schema';

type Request = FastifyRequestTypebox<typeof searchTicketSchema>;

export const parseSearchTicketRequest = (request: Request): SearchTicketDto => {
  return {
    code: request.query.code,
    eventDate: request.query.eventDate,
  };
};
