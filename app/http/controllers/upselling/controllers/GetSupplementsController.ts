import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { GetSupplementsUseCase } from '@/upselling/ticketTypes/application/use-cases/GetSupplementsUseCase';
import { getSupplementsSchema } from '@app/http/@types/cli-api/upselling/getSupplements/schema';

import { parseGetSupplementsRequest } from '../requests/GetSupplementsRequest';
import { GetSupplementsResponse } from '../responses/GetSupplementResponse';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';

type Response = ReturnType<ReturnType<typeof GetSupplementsResponse>['execute']>;

type Schema = typeof getSupplementsSchema;

@singleton()
export class GetSupplementsController {
  constructor(private readonly useCase: GetSupplementsUseCase) {}

  @cacheController()
  async handler(request: FastifyRequestTypebox<Schema>, reply: FastifyReplyTypebox<Schema>): Promise<Response> {
    const dto = parseGetSupplementsRequest(request);

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    const response = GetSupplementsResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
