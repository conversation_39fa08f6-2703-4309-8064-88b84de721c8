import { singleton } from 'tsyringe';

import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { AddSupplementsUseCase } from '@/upselling/tickets/application/use-cases/AddSupplementsUseCase';
import { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import { addSupplementsSchema } from '@app/http/@types/cli-api/upselling/addSupplements/schema';

import { parseAddSupplementsRequest } from '../requests/AddSupplementsRequest';
import { AddSupplementsResponse } from '../responses/AddSupplementsResponse';

type AddSupplementSchema = typeof addSupplementsSchema;

@singleton()
export class AddSupplementsController {
  constructor(private readonly useCase: AddSupplementsUseCase) {}

  async handler(request: FastifyRequestTypebox<AddSupplementSchema>, reply: FastifyReplyTypebox<AddSupplementSchema>): Promise<Response> {
    const dto = parseAddSupplementsRequest(request);

    const useCaseResult = await this.useCase.execute(dto);


    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    const response = AddSupplementsResponse();
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
