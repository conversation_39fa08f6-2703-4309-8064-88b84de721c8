import { HTTP_CODES } from '@app/http/HttpCodes';

import type { Supplements } from '@/tickets/tickets/domain/contracts/Supplements';
import type { Ticket } from '@/tickets/tickets/domain/entities/TicketEntity';
import type { successResponseSchema } from '@app/http/@types/cli-api/upselling/search/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

type Assistant = {
  readonly name: string;
  readonly email: string;
  readonly phone: string;
  readonly personalDocument: PersonalDocument | null;
};

type Supplement = {
  readonly id: string;
  readonly label: string;
  readonly price: number;
  readonly purchaseQuantity: number | null;
  readonly description: string | null;
  readonly productQuantity: number | null;
  readonly fakePrice: number | null;
  readonly redemptionDeadlineValue: number | null;
};

type PersonalDocument = {
  readonly number: string | null;
  readonly type: string | null;
};

export const SearchTicketResponse = (): IResponse<Ticket, Response> => {
  const execute = (ticket: Ticket): Response => {
    return {
      id: ticket.id,
      organizationId: ticket.organizationId,
      eventId: ticket.eventId,
      code: ticket.code,
      typeId: ticket.typeId,
      assistant: buildAssistant(ticket),
      supplements: ticket.supplements.map(buildSupplement),
    };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};

const buildSupplement = (supplement: Supplements): Supplement => {
  const purchaseQuantity = supplement.purchaseQuantity.fold(
    () => null,
    purchaseQuantity => purchaseQuantity.quantity,
  );

  const redemptionDeadlineValue = supplement.redemptionDeadlineValue.fold(
    () => null,
    redemptionDeadlineValue => redemptionDeadlineValue.toSeconds(),
  );

  return {
    id: supplement.id,
    label: supplement.label,
    fakePrice: supplement.fakePrice ?? null,
    price: supplement.price,
    description: supplement.description ?? null,
    productQuantity: supplement.productQuantity ?? null,
    redemptionDeadlineValue: redemptionDeadlineValue ?? null,
    purchaseQuantity,
  };
};

export const buildAssistant = (ticket: Ticket): Assistant | null => {
  const {
    name, email, phone,
  } = ticket;
  const personalDocument = buildPersonalDocument(ticket);

  const exists = name.isDefined() && email.isDefined() && phone.isDefined();

  if (!exists) {
    return null;
  }

  return {
    name: name.get(),
    email: email.get(),
    phone: phone.get(),
    personalDocument: personalDocument ?? null,
  };
};

const buildPersonalDocument = (ticket: Ticket): PersonalDocument | null => {
  const {
    personalDocumentNumber: number,
    personalDocumentType: type,
    dni,
  } = ticket;

  const exists = !!(number ?? type ?? dni);

  if (!exists) {
    return null;
  }

  return {
    number: number.fold(() => dni.fold(() => null, item => item), item => item),
    type: type.fold(() => null, item => item),
  };
};
