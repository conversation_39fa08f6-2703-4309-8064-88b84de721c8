import { UrlsPayment } from '@/payments/infrastructure/decorators/UrlsPayment';
import { HTTP_CODES } from '@app/http/HttpCodes';

import type { Payment } from '@/payments/domain/entities/Payment';
import type { successResponseSchema } from '@app/http/@types/cli-api/upselling/addSupplements/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

export const AddSupplementsResponse = (): IResponse<Payment, Response> => {
  const execute = (payment: Payment): Response => {
    const url = new UrlsPayment(payment);

    return { payment: { url: url.getUrl() } };
  };

  return { execute, status: () => HTTP_CODES.CREATED_201 };
};
