import { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import { singleton } from 'tsyringe';

import { SearchZonesDto } from '@/reservations/zones/application/contracts/SearchZonesDto';

import type { SearchZonesUseCaseRequest as Request } from '../contracts/SearchZonesContract';

@singleton()
export class SearchZonesRequest {
  static parser(request: Request): SearchZonesDto {
    return {
      organizationId: UniqueEntityID.build(request.query.organizationId),
      eventId: UniqueEntityID.build(request.query.eventId),
    };
  }
}
