import { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import { singleton } from 'tsyringe';

import { FindZoneDto } from '@/reservations/zones/application/contracts/FindZoneDto';

import type { FindZoneUseCaseRequest as Request } from '../contracts/FindZoneContract';

@singleton()
export class FindZoneRequest {
  static parser(request: Request): FindZoneDto {
    return {
      zoneId: UniqueEntityID.build(request.params.zoneId),
      organizationId: UniqueEntityID.build(request.query.organizationId),
      eventId: UniqueEntityID.build(request.query.eventId),
    };
  }
}
