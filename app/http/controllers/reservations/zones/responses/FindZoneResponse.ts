import { UniqueEntityID, type MoneyProps } from '@discocil/fv-domain-library';

import { HTTP_CODES } from '@app/http/HttpCodes';

import type { RatePrimitivesWithPricePerPerson } from '@/reservations/rates/domain/entities/Rate';
import type { RateDepositPrimitives } from '@/reservations/rates/domain/value-objects/RateDeposit';
import type { successResponseSchema } from '@app/http/@types/cli-api/reservations/zones/find/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';
import type { Zone } from '@/reservations/zones/domain/entities/Zone';

type Response = Static<typeof successResponseSchema>;

export const FindZoneResponse = (): IResponse<Zone, Response> => {
  const execute = (zone: Zone): Response => {
    const rates = getUniqueRates(zone);

    return {
      name: zone.name,
      maximumNumberOfPeople: zone.getMaxPeopleLimit(),
      webImageUrl: zone.webImageUrl.fold(() => null, url => url),
      rates: rates.map((rate) => {
        return ({
          id: rate.id,
          name: rate.name,
          price: rate.amount.amount,
          numberOfPeople: rate.maximumNumberOfPeople,
          extraPeoplePrice: rate.pricePerExtraPerson.amount,
          extraPeopleLimit: rate.extraPeopleAllowed,
          soldOut: zone.rateIsSoldOut(UniqueEntityID.build(rate.id)),
          pricePerPerson: getPricePerPersonResponse(rate.pricePerPerson, zone.getMaxPeopleLimit()),
          availabilityPerPerson: zone.getAvailabilityPerPerson(),
          color: rate.color,
          deposit: {
            type: rate.deposit.type,
            value: buildDepositValueResponse(rate.deposit),
          },
        });
      }),
    };
  };

  const getUniqueRates = (zone: Zone): RatePrimitivesWithPricePerPerson[] => {
    const rates = zone.getSpacesRates().flat();

    const uniqueRatesMap = new Map<string, RatePrimitivesWithPricePerPerson>();

    for (const rate of rates) {
      if (!uniqueRatesMap.has(rate.id)) {
        uniqueRatesMap.set(rate.id, rate);
      }
    }

    const uniqueRates = Array.from(uniqueRatesMap.values());

    return uniqueRates;
  };

  const getPricePerPersonResponse = (pricePerPerson: Record<number, MoneyProps>, maximumNumberOfPeople: number): Record<number, number | null> => {
    const pricePerPersonResponse: Record<number, number | null> = {};

    for (let i = 1; i <= maximumNumberOfPeople; i++) {
      const price = pricePerPerson[i]?.amount ?? null;

      pricePerPersonResponse[i] = price;
    }

    return pricePerPersonResponse;
  };

  const buildDepositValueResponse = (deposit: RateDepositPrimitives): number | MoneyProps => {
    if (deposit.type === 'fixed') {
      return {
        amount: deposit.amount,
        currency: deposit.currency,
      };
    }

    return deposit.amount;
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
