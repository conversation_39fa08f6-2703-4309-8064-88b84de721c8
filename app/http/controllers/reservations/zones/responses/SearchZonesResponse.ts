import { HTTP_CODES } from '@app/http/HttpCodes';

import type { ZoneCollection } from '@/reservations/zones/domain/collections/ZoneCollection';
import type { Zone } from '@/reservations/zones/domain/entities/Zone';
import type { successResponseSchema } from '@app/http/@types/cli-api/reservations/zones/search/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

export const SearchZonesResponse = (): IResponse<ZoneCollection, Response> => {
  const execute = (zonesCollection: ZoneCollection): Response => {
    const zones = zonesCollection.toArray();

    const maximumNumberOfPeople = zones.reduce((acc, zone) => Math.max(acc, zone.getMaxPeopleLimit()), 0);

    const zonesResponse = zones.map(zone => ({
      id: zone.id,
      name: zone.name,
      soldOut: zone.isFull || !zone.hasSpaces() || !zone.someSpaceHasValidRate(),
      maxPeopleLimit: zone.getMaxPeopleLimit(),
      availabilityPerPerson: buildAvailabilityResponse(zone, maximumNumberOfPeople),
      minPricePerPerson: buildMinPriceResponse(zone, maximumNumberOfPeople),
    }));

    return {
      maximumNumberOfPeople,
      zones: zonesResponse,
    };
  };

  const buildMinPriceResponse = (zone: Zone, maximumNumberOfPeople: number): Record<number, number | null> => {
    const minPricePerPersonResponse: Record<number, number | null> = {};
    const minPricePerPerson = zone.getMinPricePerPerson();

    for (let i = 1; i <= maximumNumberOfPeople; i++) {
      const maybePrice = minPricePerPerson[i];

      const minPrice = maybePrice
        ? maybePrice.fold(() => null, value => value.toDecimal())
        : null;

      minPricePerPersonResponse[i] = minPrice;
    }

    return minPricePerPersonResponse;
  };

  const buildAvailabilityResponse = (zone: Zone, maximumNumberOfPeople: number): Record<number, boolean> => {
    const availabilityResponse: Record<number, boolean> = {};

    for (let i = 1; i <= maximumNumberOfPeople; i++) {
      availabilityResponse[i] = zone.isAvailableInMicrosite(i);
    }

    return availabilityResponse;
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
