import type { findZoneSchema } from '@app/http/@types/cli-api/reservations/zones/find/schema';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { SearchZonesResponse as Response } from '../responses/SearchZonesResponse';

type Schema = typeof findZoneSchema;

export type FindZoneUseCaseRequest = FastifyRequestTypebox<Schema>;

export type FindZoneUseCaseReply = FastifyReplyTypebox<Schema>;

export type FindZoneUseCaseResponse = ReturnType<ReturnType<typeof Response>['execute']>;
