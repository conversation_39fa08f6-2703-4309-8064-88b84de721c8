import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { searchZonesSchema } from '@app/http/@types/cli-api/reservations/zones/search/schema';
import type { SearchZonesResponse as Response } from '../responses/SearchZonesResponse';

type Schema = typeof searchZonesSchema;

export type SearchZonesUseCaseRequest = FastifyRequestTypebox<Schema>;

export type SearchZonesUseCaseReply = FastifyReplyTypebox<Schema>;

export type SearchZonesUseCaseResponse = ReturnType<ReturnType<typeof Response>['execute']>;
