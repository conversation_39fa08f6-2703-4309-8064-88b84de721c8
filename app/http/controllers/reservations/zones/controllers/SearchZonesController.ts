import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { SearchZonesUseCase } from '@/reservations/zones/application/use-cases/SearchZonesUseCase';
import { SearchZonesRequest } from '@app/http/controllers/reservations/zones/requests/SearchZonesRequest';
import { SearchZonesResponse } from '@app/http/controllers/reservations/zones/responses/SearchZonesResponse';

import type {
  SearchZonesUseCaseReply as Reply,
  SearchZonesUseCaseRequest as Request,
} from '../contracts/SearchZonesContract';

type Response = ReturnType<ReturnType<typeof SearchZonesResponse>['execute']>;

@singleton()
export class SearchZonesController {
  constructor(private readonly useCase: SearchZonesUseCase) {}

  @cacheController()
  async handler(request: Request, reply: Reply): Promise<Response> {
    const dto = SearchZonesRequest.parser(request);

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return await reply.code(code).send(errorInstance);
    }

    const response = SearchZonesResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
