import { singleton } from 'tsyringe';

import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { PurchaseReservationUseCase } from '@/reservations/reservations/application/use-cases/PurchaseReservationUseCase';
import { PurchaseReservationRequest } from '@app/http/controllers/reservations/reservations/requests/PurchaseReservationRequest';
import { PurchaseReservationResponse } from '@app/http/controllers/reservations/reservations/responses/PurchaseReservationResponseReservationResponse';

import type {
  PurchaseReservationUseCaseReply as Reply,
  PurchaseReservationUseCaseRequest as Request,
} from '../contracts/PurchaseReservationContract';

type Response = ReturnType<ReturnType<typeof PurchaseReservationResponse>['execute']>;

@singleton()
export class PurchaseReservationController {
  constructor(private readonly useCase: PurchaseReservationUseCase) {}

  async handler(request: Request, reply: Reply): Promise<Response> {
    const requestParsed = PurchaseReservationRequest.parser(request);

    const useCaseResult = await this.useCase.execute(requestParsed);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { requestParsed },
      });

      const { code } = getErrorInfo(errorInstance);

      return await reply.code(code).send(errorInstance);
    }

    const response = PurchaseReservationResponse();
    const responseData = response.execute(useCaseResult.value);

    return await reply.code(response.status()).send(responseData);
  }
}
