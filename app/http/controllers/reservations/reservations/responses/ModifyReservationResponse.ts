import { HTTP_CODES } from '@app/http/HttpCodes';

import type { Reservation } from '@/reservations/reservations/domain/entities/Reservation';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';
import type { successResponseSchema } from '@app/http/@types/cli-api/reservations/reservations/modify/successResponseSchema';

type Response = Static<typeof successResponseSchema>;

export const ModifyReservationResponse = (): IResponse<Reservation, Response> => {
  const execute = (response: Reservation): Response => {
    return { paymentUrl: response.paymentUrl.get() };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
