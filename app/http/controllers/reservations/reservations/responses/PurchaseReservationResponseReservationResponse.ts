import { HTTP_CODES } from '@app/http/HttpCodes';

import type { Reservation } from '@/reservations/reservations/domain/entities/Reservation';
import type { successResponseSchema } from '@app/http/@types/cli-api/reservations/reservations/purchase/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

export const PurchaseReservationResponse = (): IResponse<Reservation, Response> => {
  const execute = (reservation: Reservation): Response => {
    return { paymentUrl: reservation.paymentUrl.get() };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.CREATED_201,
  };
};
