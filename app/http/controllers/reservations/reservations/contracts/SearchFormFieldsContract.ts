import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { searchFormFieldsSchema } from '@app/http/@types/cli-api/reservations/form-fields/search/schema';
import type { SearchFormFieldsResponse as Response } from '../responses/SearchFormFieldsResponse';

type Schema = typeof searchFormFieldsSchema;

export type SearchFormFieldsUseCaseRequest = FastifyRequestTypebox<Schema>;

export type SearchFormFieldsUseCaseReply = FastifyReplyTypebox<Schema>;

export type SearchFormFieldsUseCaseResponse = ReturnType<ReturnType<typeof Response>['execute']>;
