import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { findReservationSchema } from '@app/http/@types/cli-api/reservations/reservations/find/schema';
import type { FindReservationResponse as Response } from '../responses/FindReservationResponse';

type Schema = typeof findReservationSchema;

export type FindReservationUseCaseRequest = FastifyRequestTypebox<Schema>;

export type FindReservationUseCaseReply = FastifyReplyTypebox<Schema>;

export type FindReservationUseCaseResponse = ReturnType<ReturnType<typeof Response>['execute']>;
