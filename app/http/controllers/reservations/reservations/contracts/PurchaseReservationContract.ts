import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { purchaseReservationSchema } from '@app/http/@types/cli-api/reservations/reservations/purchase/schema';
import type { PurchaseReservationResponse as Response } from '../responses/PurchaseReservationResponseReservationResponse';

type Schema = typeof purchaseReservationSchema;

export type PurchaseReservationUseCaseRequest = FastifyRequestTypebox<Schema>;

export type PurchaseReservationUseCaseReply = FastifyReplyTypebox<Schema>;

export type PurchaseReservationUseCaseResponse = ReturnType<ReturnType<typeof Response>['execute']>;
