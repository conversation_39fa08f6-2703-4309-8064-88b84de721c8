import type { FastifyRequestTypebox, FastifyReplyTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { ModifyReservationResponse as Response } from '../responses/ModifyReservationResponse';
import type { modifyReservationSchema } from '@app/http/@types/cli-api/reservations/reservations/modify/schema';

type Schema = typeof modifyReservationSchema;

export type ModifyReservationUseCaseRequest = FastifyRequestTypebox<Schema>;

export type ModifyReservationUseCaseReply = FastifyReplyTypebox<Schema>;

export type ModifyReservationUseCaseResponse = ReturnType<ReturnType<typeof Response>['execute']>;
