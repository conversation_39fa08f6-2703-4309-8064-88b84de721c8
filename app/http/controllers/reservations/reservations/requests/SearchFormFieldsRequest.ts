import { singleton } from 'tsyringe';
import { UniqueEntityID } from '@discocil/fv-domain-library/domain';

import { SearchFormFieldsDto } from '@/reservations/reservations/application/contracts/SearchFormFieldsDto';

import type { SearchFormFieldsUseCaseRequest as Request } from '../contracts/SearchFormFieldsContract';

@singleton()
export class SearchFormFieldsRequest {
  static parser(request: Request): SearchFormFieldsDto {
    return { organizationId: UniqueEntityID.build(request.params.organizationId) };
  }
}
