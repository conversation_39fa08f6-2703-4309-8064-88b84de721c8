import { Maybe, UniqueEntityID } from '@discocil/fv-domain-library/domain';
import { singleton } from 'tsyringe';

import { ModifyReservationRequest as ModifyReservationRequestDto } from '@/reservations/reservations/domain/dtos/ModifyReservationRequest';

import type { ModifyReservationUseCaseRequest as Request } from '../contracts/ModifyReservationContract';

@singleton()
export class ModifyReservationRequest {
  static parser(request: Request): ModifyReservationRequestDto {
    return {
      organizationId: UniqueEntityID.build(request.body.organizationId),
      reservationId: UniqueEntityID.build(request.body.reservationId),
      observations: Maybe.fromValue(request.body.observations),
      shouldAddSubscriber: request.body.shouldAddSubscriber,
      isFullPayment: request.body.isFullPayment,
      purchaseId: UniqueEntityID.build(request.body.purchaseId),
      successUrl: request.body.successUrl,
      failureUrl: request.body.failureUrl,
      ip: request.fingerPrint.fingerPrint.ip,
      applicationId: request.application.id,
      totalAmount: request.body.totalAmount,
      formFields: request.body.fields.map(field => ({
        title: field.title,
        value: field.value,
      })),
    };
  }
}
