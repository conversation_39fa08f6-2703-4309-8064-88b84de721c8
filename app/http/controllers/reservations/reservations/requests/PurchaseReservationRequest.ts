import {
  Collection, Maybe, UniqueEntityID,
} from '@discocil/fv-domain-library/domain';
import { singleton } from 'tsyringe';

import { PurchaseReservationRequestDto } from '@/reservations/reservations/domain/dtos/PurchaseReservationRequest';

import type { PurchaseReservationUseCaseRequest as Request } from '../contracts/PurchaseReservationContract';

@singleton()
export class PurchaseReservationRequest {
  static parser(request: Request): PurchaseReservationRequestDto {
    const formFields = request.body.fields.map(field => ({
      title: field.title,
      value: field.value,
    }));

    return {
      organizationId: UniqueEntityID.build(request.body.organizationId),
      observations: Maybe.fromValue(request.body.observations),
      eventId: UniqueEntityID.build(request.body.eventId),
      zoneId: UniqueEntityID.build(request.body.zoneId),
      numberOfPeople: request.body.numberOfPeople,
      rateId: UniqueEntityID.build(request.body.rateId),
      shouldAddSubscriber: request.body.shouldAddSubscriber,
      isFullPayment: request.body.isFullPayment,
      purchaseId: UniqueEntityID.build(request.body.purchaseId),
      successUrl: request.body.successUrl,
      failureUrl: request.body.failureUrl,
      ip: request.fingerPrint.fingerPrint.ip,
      applicationId: request.application.id,
      totalAmount: request.body.totalAmount,
      formFields: Collection.build(formFields, 'title'),
      referentId: Maybe.fromValue(request.body.referentId),
      notificationLanguage: request.fingerPrint.fingerPrint.language.veryShort,
    };
  }
}
