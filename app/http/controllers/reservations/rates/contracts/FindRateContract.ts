import type { findRateSchema } from '@app/http/@types/cli-api/reservations/rates/find/schema';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { FindRateResponse as Response } from '../responses/FindRateResponse';

type Schema = typeof findRateSchema;

export type FindRateUseCaseRequest = FastifyRequestTypebox<Schema>;

export type FindRateUseCaseReply = FastifyReplyTypebox<Schema>;

export type FindRateUseCaseResponse = ReturnType<ReturnType<typeof Response>['execute']>;
