import { UniqueEntityID } from '@discocil/fv-domain-library/domain';
import { singleton } from 'tsyringe';

import { FindRateDto } from '@/reservations/rates/application/contracts/FindRateDto';

import type { FindRateUseCaseRequest as Request } from '../contracts/FindRateContract';

@singleton()
export class FindRateRequest {
  static parser(request: Request): FindRateDto {
    return {
      organizationId: UniqueEntityID.build(request.query.organizationId),
      eventId: UniqueEntityID.build(request.query.eventId),
      zoneId: UniqueEntityID.build(request.query.zoneId),
      rateId: UniqueEntityID.build(request.params.rateId),
      numberOfPeople: request.query.numberOfPeople,
    };
  }
}
