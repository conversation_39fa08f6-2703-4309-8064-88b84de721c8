import { singleton } from 'tsyringe';

import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { TrackingEventUseCase } from '@/tracking/application/use-cases/TrackingEventUseCase';
import { TrackingFvEventUseCase } from '@/tracking/application/use-cases/TrackingFvEventUseCase';
import { EEventChannel } from '@/tracking/domain/value-objects/EventType';
import {
  FvTrackingRequest, TrackingReply, TrackingRequest,
} from '@app/http/@types/cli-api/tracking/track/schema';
import { HTTP_CODES } from '@app/http/HttpCodes';

import { ParseFvTrackingRequest } from './requests/ParseFvTrackingRequest';
import { ParseTrackingRequest } from './requests/ParseTrackingRequest';

@singleton()
export class TrackingController {
  constructor(
    private readonly metaUseCase: TrackingEventUseCase,
    private readonly fvUseCase: TrackingFvEventUseCase,
  ) { }

  async handler(request: TrackingRequest | FvTrackingRequest, reply: TrackingReply): Promise<void> {
    const channel: EEventChannel = request.params.channel;

    if (channel === EEventChannel.fv) {
      const dto = ParseFvTrackingRequest.parser(request as FvTrackingRequest);

      const useCaseResult = await this.fvUseCase.execute(dto);

      if (useCaseResult.isLeft()) {
        request.error = useCaseResult.value.contextualize({
          context: this.constructor.name,
          data: { dto },
        });

        const { code, message } = getErrorInfo(request.error);

        return reply.code(code).send({ message });
      }

      return reply.code(HTTP_CODES.NO_CONTENT_204).send();
    }

    const dto = ParseTrackingRequest.parser(request as TrackingRequest);

    const useCaseResult = await this.metaUseCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    return reply.code(HTTP_CODES.NO_CONTENT_204).send();
  }
}
