import {
  <PERSON>ur<PERSON>cy,
  Maybe,
  Money,
  MoneyProps,
} from '@discocil/fv-domain-library/domain';
import { singleton } from 'tsyringe';

import { hasTruthyProperty } from '@/cross-cutting/domain/helpers/objects';
import { FingerPrint } from '@/cross-cutting/infrastructure/middlewares/fingerPrint/contracts/FingerPrint';
import { EEventChannel, EventType } from '@/tracking/domain/value-objects/EventType';

import type {
  TrackingFb,
  TrackingItemPrimitive,
  TrackingUser,
} from '@/tracking/domain/contracts/EntityContracts';
import type { TrackingEventDto } from '@/tracking/domain/contracts/TrackingEventUseCaseContracts';
import type { TrackingRequest } from '@app/http/@types/cli-api/tracking/track/schema';

export type TrackingEventFingerPrint = {
  readonly remoteAddress: string;
  readonly userAgent: string;
};

@singleton()
export class ParseTrackingRequest {
  static parser(request: TrackingRequest): TrackingEventDto {
    const eventTypeOrError = EventType.build(request.body.event_name);

    if (eventTypeOrError.isLeft()) {
      throw eventTypeOrError.value;
    }

    const eventType = eventTypeOrError.value;

    const { fingerPrint: requestFingerPrint, body: bodyRequest } = request;

    const fingerPrint = this.parseFingerPrintRequest(requestFingerPrint.fingerPrint);

    const trackingEventDto: TrackingEventDto = {
      id: bodyRequest.event_id,
      name: eventType.toPrimitive(),
      channel: 'channel' in bodyRequest ? (bodyRequest.channel as EEventChannel) : EEventChannel.meta,
      eventId: Maybe.fromValue(bodyRequest.event_id),
      externalId: 'external_id' in bodyRequest
        ? Maybe.fromValue(bodyRequest.external_id)
        : Maybe.none<string>(),
      organizationId: bodyRequest.organization_id,
      urlPage: bodyRequest.url_page,
      route: 'route' in bodyRequest
        ? Maybe.fromValue(bodyRequest.route)
        : Maybe.none<string>(),
      user: this.parseUserRequest(bodyRequest),
      fb: this.parseFbRequest(bodyRequest),
      content: Maybe.some({
        id: 'content_id' in bodyRequest && bodyRequest.content_id ? bodyRequest.content_id : undefined,
        name: 'content_name' in bodyRequest && bodyRequest.content_name ? bodyRequest.content_name : undefined,
        type: 'content_type' in bodyRequest && bodyRequest.content_type ? bodyRequest.content_type : undefined,
        ids: 'content_ids' in bodyRequest ? bodyRequest.content_ids : undefined,
      }),
      price: 'price' in bodyRequest
        ? this.parseMoneyRequest(bodyRequest.price, bodyRequest.currency)
        : this.parseMoneyRequest(),
      numItems: 'num_items' in bodyRequest ? Maybe.some(bodyRequest.num_items) : Maybe.none<number>(),
      totalPrice: 'value' in bodyRequest
        ? this.parseMoneyRequest(bodyRequest.value, bodyRequest.currency)
        : this.parseMoneyRequest(),
      items: 'items' in bodyRequest && bodyRequest.items.length
        ? Maybe.some(
          bodyRequest.items.map(item => ({
            id: item.id,
            price: item.price,
            quantity: item.quantity,
            category: item.category,
          })),
        )
        : Maybe.none<TrackingItemPrimitive[]>(),
      sessionId: Maybe.none(),
      serviceType: Maybe.none(),
      containerType: Maybe.none(),
      ...fingerPrint,
    };

    return trackingEventDto;
  };

  private static parseUserRequest(bodyRequest?: TrackingRequest['body']): Maybe<TrackingUser> {
    if (!bodyRequest) {
      return Maybe.none<TrackingUser>();
    }

    const isEmpty = hasTruthyProperty({
      name: 'user_name' in bodyRequest ? bodyRequest.user_name : undefined,
      email: 'user_email' in bodyRequest ? bodyRequest.user_email : undefined,
      phone: 'user_phone' in bodyRequest ? bodyRequest.user_phone : undefined,
    });

    if (isEmpty) {
      return Maybe.none<TrackingUser>();
    }

    return Maybe.some<TrackingUser>({
      name: 'user_name' in bodyRequest ? Maybe.fromValue(bodyRequest.user_name) : Maybe.none<string>(),
      email: 'user_email' in bodyRequest ? Maybe.fromValue(bodyRequest.user_email) : Maybe.none<string>(),
      phone: 'user_phone' in bodyRequest ? Maybe.fromValue(bodyRequest.user_phone) : Maybe.none<string>(),
    });
  }

  private static parseFbRequest(bodyRequest: TrackingRequest['body']): Maybe<TrackingFb> {
    const isEmpty = hasTruthyProperty({
      fbclid: 'fbclid' in bodyRequest ? bodyRequest.fbclid : undefined,
      fbc: 'fbc' in bodyRequest ? bodyRequest.fbc : undefined,
      fbp: 'fbp' in bodyRequest ? bodyRequest.fbp : undefined,
    });

    if (isEmpty) {
      return Maybe.none<TrackingFb>();
    }

    return Maybe.some<TrackingFb>({
      fbclid: 'fbclid' in bodyRequest ? Maybe.fromValue(bodyRequest.fbclid) : Maybe.none<string>(),
      fbc: 'fbc' in bodyRequest ? Maybe.fromValue(bodyRequest.fbc) : Maybe.none<string>(),
      fbp: 'fbp' in bodyRequest ? Maybe.fromValue(bodyRequest.fbp) : Maybe.none<string>(),
    });
  }

  private static parseMoneyRequest(amount?: number, currency?: string): Maybe<MoneyProps> {
    if (!amount || !currency) {
      return Maybe.none<MoneyProps>();
    }

    const moneyOrError = Money.build({ amount, currency: currency as ECurrency });

    if (moneyOrError.isLeft()) {
      return Maybe.none<MoneyProps>();
    }

    return Maybe.some(moneyOrError.value.toPrimitive());
  }

  private static parseFingerPrintRequest(fingerPrint: FingerPrint): TrackingEventFingerPrint {
    return {
      remoteAddress: fingerPrint.ip,
      userAgent: fingerPrint.browser.source,
    };
  }
}
