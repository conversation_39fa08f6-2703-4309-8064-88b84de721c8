import { Maybe } from '@discocil/fv-domain-library/domain';
import { singleton } from 'tsyringe';

import { FingerPrint } from '@/cross-cutting/infrastructure/middlewares/fingerPrint/contracts/FingerPrint';
import { EEventChannel, EventType } from '@/tracking/domain/value-objects/EventType';

import { TrackingEventFingerPrint } from './ParseTrackingRequest';

import type {
  TrackingFb,
  TrackingItemPrimitive,
  TrackingUser,
} from '@/tracking/domain/contracts/EntityContracts';
import type { TrackingEventDto } from '@/tracking/domain/contracts/TrackingEventUseCaseContracts';
import type { FvTrackingRequest } from '@app/http/@types/cli-api/tracking/track/schema';
import type {
  ECurrency,
  MoneyProps,
} from '@discocil/fv-domain-library/domain';

@singleton()
export class ParseFvTrackingRequest {
  static parser(request: FvTrackingRequest): TrackingEventDto {
    const eventTypeOrError = EventType.build(request.body.event_name);

    if (eventTypeOrError.isLeft()) {
      throw eventTypeOrError.value;
    }

    const eventType = eventTypeOrError.value;

    const { fingerPrint: requestFingerprint, body: bodyRequest } = request;

    const fingerPrint = this.parseFingerPrintRequest(requestFingerprint.fingerPrint);

    const trackingEventDto: TrackingEventDto = {
      id: bodyRequest.event_id,
      name: eventType.toPrimitive(),
      channel: 'channel' in bodyRequest ? (bodyRequest.channel as EEventChannel) : EEventChannel.meta,
      eventId: Maybe.fromValue(bodyRequest.event_id),
      externalId: Maybe.none<string>(),
      organizationId: bodyRequest.organization_id,
      urlPage: bodyRequest.url_page,
      route: Maybe.none<string>(),
      user: Maybe.none<TrackingUser>(),
      fb: Maybe.none<TrackingFb>(),
      content: Maybe.some({
        id: 'content_id' in bodyRequest ? bodyRequest.content_id : undefined,
        name: 'content_name' in bodyRequest ? bodyRequest.content_name : undefined,
        type: 'content_type' in bodyRequest ? bodyRequest.content_type : undefined,
        ids: 'content_ids' in bodyRequest ? bodyRequest.content_ids : undefined,
      }),
      price: 'price' in bodyRequest
        ? this.parseMoneyRequest(bodyRequest.price, bodyRequest.currency)
        : this.parseMoneyRequest(),
      numItems: Maybe.none<number>(),
      totalPrice: this.parseMoneyRequest(),
      items: Maybe.none<TrackingItemPrimitive[]>(),
      sessionId: Maybe.fromValue(bodyRequest.session_id),
      serviceType: Maybe.fromValue(bodyRequest.service_type),
      containerType: Maybe.fromValue(bodyRequest.container_type),
      ...fingerPrint,
    };

    return trackingEventDto;
  };

  private static parseMoneyRequest(amount?: number, currency?: string): Maybe<MoneyProps> {
    if (!amount || !currency) {
      return Maybe.none<MoneyProps>();
    }

    return Maybe.some({
      amount,
      currency: currency as ECurrency,
    });
  }

  private static parseFingerPrintRequest(fingerPrint: FingerPrint): TrackingEventFingerPrint {
    return {
      remoteAddress: fingerPrint.ip,
      userAgent: fingerPrint.browser.source,
    };
  }
}
