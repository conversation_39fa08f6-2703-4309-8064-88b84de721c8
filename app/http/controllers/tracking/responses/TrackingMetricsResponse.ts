import { EMicrositeServices } from '@discocil/fv-domain-library';

import { TrackingMetricsTypes } from '@/tracking/domain/entities/EventTracking';
import { HTTP_CODES } from '@app/http/HttpCodes';

import type {
  EventTrackingsMetrics, EventTrackingsMetricsKeys,
  TrackingMetricsFvEventResponse,
} from '@/tracking/domain/entities/EventTracking';
import type { successResponseSchema } from '@app/http/@types/cli-api/tracking/metrics/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

type TrackingMetricsCountByType = Record<TrackingMetricsTypes, number>;

export const TrackingMetricsResponse = (): IResponse<TrackingMetricsFvEventResponse, Response> => {
  const makeEventTrackingMetricsByType = (
    groupEventTrackings: EventTrackingsMetrics,
    serviceType: EventTrackingsMetricsKeys,
  ): TrackingMetricsCountByType => {
    const eventTrackingMetricsByType = groupEventTrackings[serviceType];

    return {
      [TrackingMetricsTypes.InitiateCheckout]: eventTrackingMetricsByType[TrackingMetricsTypes.InitiateCheckout].size,
      [TrackingMetricsTypes.AddToCart]: eventTrackingMetricsByType[TrackingMetricsTypes.AddToCart].size,
      [TrackingMetricsTypes.Purchase]: eventTrackingMetricsByType[TrackingMetricsTypes.Purchase].size,
    };
  };

  const execute = (dto: TrackingMetricsFvEventResponse): Response => {
    const { allEventTrackings, eventTrackingsForTheLast15Minutes } = dto;

    return {
      allEventTrackings: {
        [EMicrositeServices.TICKETS]: makeEventTrackingMetricsByType(allEventTrackings, EMicrositeServices.TICKETS),
        [EMicrositeServices.GUESTLISTS]: makeEventTrackingMetricsByType(allEventTrackings, EMicrositeServices.GUESTLISTS),
        [EMicrositeServices.RESERVATIONS]: makeEventTrackingMetricsByType(allEventTrackings, EMicrositeServices.RESERVATIONS),
        [EMicrositeServices.PASSES]: makeEventTrackingMetricsByType(allEventTrackings, EMicrositeServices.PASSES),
        all: makeEventTrackingMetricsByType(allEventTrackings, 'all'),
      },
      eventTrackingsForTheLast15Minutes: {
        [EMicrositeServices.TICKETS]: makeEventTrackingMetricsByType(eventTrackingsForTheLast15Minutes, EMicrositeServices.TICKETS),
        [EMicrositeServices.GUESTLISTS]: makeEventTrackingMetricsByType(eventTrackingsForTheLast15Minutes, EMicrositeServices.GUESTLISTS),
        [EMicrositeServices.RESERVATIONS]: makeEventTrackingMetricsByType(eventTrackingsForTheLast15Minutes, EMicrositeServices.RESERVATIONS),
        [EMicrositeServices.PASSES]: makeEventTrackingMetricsByType(eventTrackingsForTheLast15Minutes, EMicrositeServices.PASSES),
        all: makeEventTrackingMetricsByType(eventTrackingsForTheLast15Minutes, 'all'),
      },
    };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
