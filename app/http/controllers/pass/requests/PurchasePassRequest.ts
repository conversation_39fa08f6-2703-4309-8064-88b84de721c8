import { UniqueEntityID } from '@discocil/fv-domain-library';

import type { SetPassesDto } from '@/passes/passes/domain/contracts/PurchasePassResponse';
import type { PurchasePassRequest } from '@app/http/@types/cli-api/pass/purchase/schema';

export const parsePurchasePassRequest = (request: PurchasePassRequest): SetPassesDto => {
  const {
    application, body, fingerPrint,
  } = request;

  return {
    organizationId: body.organizationId,
    redirect_url: body.redirect_url,
    error_url: body.error_url,
    idx: body.idx,
    referrerId: body.referrerId,
    passes: body.passes.map((pass) => {
      return {
        passTypeId: pass.passTypeId,
        priceId: pass.priceId,
        customer: {
          id: UniqueEntityID.create().toPrimitive(),
          name: pass.customer.name,
          email: pass.customer.email,
          phone: pass.customer.phone ?? null,
          address: pass.customer.address ?? null,
          dateOfBirth: pass.customer.dateOfBirth ?? null,
          country: pass.customer.country ?? null,
          postalCode: pass.customer.postalCode ?? null,
          gender: pass.customer.gender ?? null,
          typeIdentity: pass.customer.typeIdentity ?? null,
          identity: pass.customer.identity ?? null,
          image: pass.customer.image ?? null,
          customFields: pass.customer.customFields ?? null,
        },
      };
    }),
    sourceUrl: body.sourceUrl,
    remarketing: body.remarketing,
    paylink: body.paylink,
    device: fingerPrint.fingerPrint.device.id,
    os: fingerPrint.fingerPrint.os.name,
    language: fingerPrint.fingerPrint.language.name,
    applicationId: application.id,
    browser: fingerPrint.fingerPrint.browser.name,
  };
};
