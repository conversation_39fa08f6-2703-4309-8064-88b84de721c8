import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { FindPassUseCase } from '@/passes/passes/application/FindPassUseCase';

import { FindPassResponse } from './responses/FindPassResponse';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { getPassSchema } from '@app/http/@types/cli-api/pass/find/schema';

type Response = ReturnType<ReturnType<typeof FindPassResponse>['execute']>;
type Schema = typeof getPassSchema;

@singleton()
export class FindPassController {
  constructor(private readonly useCase: FindPassUseCase) {}

  @cacheController()
  async handler(request: FastifyRequestTypebox<Schema>, reply: FastifyReplyTypebox<Schema>): Promise<Response> {
    const dto = {
      id: request.params.id,
      organizationId: request.query.organizationId,
    };

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    const pass = useCaseResult.value;

    const response = FindPassResponse();
    const responseData = response.execute(pass);

    return reply.code(response.status()).send(responseData);
  }
}
