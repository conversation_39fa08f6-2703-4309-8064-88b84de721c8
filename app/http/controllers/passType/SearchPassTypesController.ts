import { singleton } from 'tsyringe';

import { cacheController } from '@/cross-cutting/infrastructure/decorators/CacheControllerDecorator';
import { getErrorInfo } from '@/cross-cutting/infrastructure/responses/ErrorResponse';
import { SearchPassTypesWithFeesUseCase } from '@/passes/passTypes/application/SearchPassTypesWithFeesUseCase';

import { parseSearchPassTypesRequest } from './requests/SearchPassTypeRequest';
import { SearchPassTypesResponse } from './responses/SearchPassTypesResponse';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { searchPassTypeSchema } from '@app/http/@types/cli-api/passType/search/schema';

type Schema = typeof searchPassTypeSchema;

type Response = ReturnType<ReturnType<typeof SearchPassTypesResponse>['execute']>;

@singleton()
export class SearchPassTypesController {
  constructor(private readonly useCase: SearchPassTypesWithFeesUseCase) {}

  @cacheController()
  async handler(request: FastifyRequestTypebox<Schema>, reply: FastifyReplyTypebox<Schema>): Promise<Response> {
    const dto = parseSearchPassTypesRequest(request);

    const useCaseResult = await this.useCase.execute(dto);

    if (useCaseResult.isLeft()) {
      const errorInstance = useCaseResult.value.contextualize({
        context: this.constructor.name,
        data: { dto },
      });

      const { code } = getErrorInfo(errorInstance);

      return reply.code(code).send(errorInstance);
    }

    const response = SearchPassTypesResponse();
    const responseData = response.execute(useCaseResult.value);

    return reply.code(response.status()).send(responseData);
  }
}
