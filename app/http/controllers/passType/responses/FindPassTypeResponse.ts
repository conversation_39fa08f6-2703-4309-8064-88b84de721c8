import { FvDate } from '@discocil/fv-domain-library/domain';

import { HTTP_CODES } from '@app/http/HttpCodes';

import type { PassType } from '@/passes/passTypes/domain/entities/PassType';
import type { Prices } from '@/passes/prices/domain/entities/Price';
import type { successResponseSchema } from '@app/http/@types/cli-api/passType/find/schema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

export type Response = Static<typeof successResponseSchema>;


export const FindPassTypeResponse = (): IResponse<{ passType: PassType; prices: Prices; }, Response> => {
  const execute = (dto: { passType: PassType; prices: Prices; }): Response => {
    const { passType } = dto;

    const price = passType.getPrice().get();

    return {
      id: passType.id,
      organizationId: passType.organizationId,
      name: passType.name,
      startDate: FvDate.create(passType.startDate).toSeconds(),
      endDate: FvDate.create(passType.endDate).toSeconds(),
      startCountdown: passType.startCountdown,
      color: passType.color,
      tags: passType.tags,
      designId: passType.designId.fold(() => null, item => item),
      status: passType.getStatus(),
      maxAvailable: passType.getMaxAvailable(),
      fields: passType.fields.fold(() => null, (field) => {
        return {
          fullname: field.fullname.fold(() => undefined, item => item),
          email: field.email.fold(() => undefined, item => item),
          phone: field.phone.fold(() => undefined, item => item),
          birthDate: field.birthDate.fold(() => undefined, item => item),
          postalCode: field.postalCode.fold(() => undefined, item => item),
          gender: field.gender.fold(() => undefined, item => item),
          address: field.address.fold(() => undefined, item => item),
          country: field.country.fold(() => undefined, item => item),
          personalDocumentNumber: field.personalDocumentNumber.fold(() => undefined, item => item),
          image: field.image.fold(() => undefined, item => item),
          customerCustomFields: field.customerCustomFields.fold(() => undefined, item => item),
        };
      }),
      price: {
        id: price.id,
        description: price.description.fold(() => null, item => item),
        price: price.price,
        fee: price.serviceFees.quantity,
        feeType: price.serviceFees.type,
        additionalInfo: price.additionalInfo.fold(() => null, item => item),
      },
      description: passType.description.fold(() => null, item => item),
    };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
