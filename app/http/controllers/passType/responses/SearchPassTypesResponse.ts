import { FvDate } from '@discocil/fv-domain-library/domain';

import { HTTP_CODES } from '@app/http/HttpCodes';
import { paginationResponse } from '@app/http/responses/PaginationResponse';

import type { SearchPassTypesType } from '@/passes/passTypes/domain/entities/PassType';
import type { passTypeSuccessSchema, successResponseSchema } from '@app/http/@types/cli-api/passType/search/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;
type PassTypeResponse = Static<typeof passTypeSuccessSchema>;

export const SearchPassTypesResponse = (): IResponse<SearchPassTypesType, Response> => {
  const execute = (passTypesResponse: SearchPassTypesType): Response['data'] => {
    const { passTypes } = passTypesResponse;

    const passTypesResult: Response['data'] = [];

    for (const passType of passTypes.values()) {
      const priceOrEmpty = passType.getPrice();

      if (priceOrEmpty.isEmpty()) {
        continue;
      }

      const price = priceOrEmpty.get();

      const passTypeResult: PassTypeResponse = {
        id: passType.id,
        organizationId: passType.organizationId,
        name: passType.name,
        startDate: FvDate.create(passType.startDate).toSeconds(),
        endDate: FvDate.create(passType.endDate).toSeconds(),
        startCountdown: passType.startCountdown,
        color: passType.color,
        tags: passType.tags,
        designId: passType.designId.fold(() => null, item => item),
        description: passType.description.fold(() => null, item => item),
        status: passType.getStatus(),
        price: {
          id: price.id,
          description: price.description.fold(() => null, item => item),
          price: price.price,
          fee: price.serviceFees.quantity,
          feeType: price.serviceFees.type,
          additionalInfo: price.additionalInfo.fold(() => null, item => item),
        },
      };

      passTypesResult.push(passTypeResult);
    }

    return passTypesResult;
  };

  return {
    execute: paginationResponse(execute),
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
