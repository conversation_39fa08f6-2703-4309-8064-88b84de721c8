import { paginationOptionRequest } from '@discocil/fv-criteria-converter-library/infrastructure';
import { Maybe } from '@discocil/fv-domain-library/domain';

import type { SearchPassTypesDto } from '@/passes/passTypes/domain/contracts/SearchPassTypesContract';
import type { FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';
import type { searchPassTypeSchema } from '@app/http/@types/cli-api/passType/search/schema';

type Request = FastifyRequestTypebox<typeof searchPassTypeSchema>;

export const parseSearchPassTypesRequest = (request: Request): SearchPassTypesDto => {
  return {
    organizationId: request.query.organizationId,
    pagination: paginationOptionRequest(request.query),
    eventId: Maybe.none(),
  };
};
