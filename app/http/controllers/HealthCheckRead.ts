import { FvDate } from '@discocil/fv-domain-library/domain';
import mongoose from 'mongoose';
import { singleton } from 'tsyringe';

import config from '@config/index';

import type { Response } from '../@types/healthCheck/response';

@singleton()
export class HealthCheckRead {
  private readonly dbUri: string;

  constructor() {
    this.dbUri = `${config.mongo.url}`;
  }

  async handler(): Promise<Response> {
    await mongoose.connect(this.dbUri);

    return {
      uptime: process.uptime(),
      timestamp: FvDate.create().toMilliseconds(),
    };
  }
}
