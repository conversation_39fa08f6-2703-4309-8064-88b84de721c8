import { FvError } from '@discocil/fv-domain-library';

import { stringify } from '@/cross-cutting/domain/helpers/stringify';
import { buildMessage } from '@/cross-cutting/infrastructure/responses/ErrorResponse';

import { HTTP_CODES } from './HttpCodes';

import type { Logger } from '@/cross-cutting/infrastructure/loggers/Logger';
import type {
  FastifyBaseLogger,
  FastifyError,
  FastifyReply,
  FastifyRequest,
} from 'fastify';

type ErrorRequest = {
  serverError: FastifyError | FvError;
  request: FastifyRequest;
  reply: FastifyReply;
  fastifyLogger: FastifyBaseLogger;
  logger: Logger;
};

export const errorHandler = (dto: ErrorRequest): unknown => {
  const {
    serverError, request, reply, fastifyLogger, logger,
  } = dto;

  let statusCode: number = reply.statusCode ?? HTTP_CODES.INTERNAL_SERVER_ERROR_500;
  const serverResponse = { message: buildMessage(serverError) };

  if ('statusCode' in serverError && serverError.statusCode) {
    statusCode = serverError.statusCode;
  }

  if ('validation' in serverError && serverError.validation) {
    statusCode ||= HTTP_CODES.BAD_REQUEST_400;

    serverError.validation.forEach((error) => {
      fastifyLogger.warn(error);
    });

    return reply.status(statusCode).send(serverResponse);
  }

  const isServerError = statusCode >= HTTP_CODES.INTERNAL_SERVER_ERROR_500;

  if (isServerError) {
    const { config } = request.routeOptions;
    const context = { config };

    const customError = serverError instanceof FvError
      ? serverError
      : `Untracked error: ${stringify(serverError.message)}`;

    logger.error(customError, context);
  }

  return reply.status(statusCode).send(serverResponse);
};
