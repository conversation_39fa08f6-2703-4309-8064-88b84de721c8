import path from 'node:path';

import {
  FvError, PerformanceMeasurement, UUID,
} from '@discocil/fv-domain-library/domain';
import fastifyCompress from '@fastify/compress';
import fastifyCors from '@fastify/cors';
import helmet from '@fastify/helmet';
import fastifyJwt, { FastifyJwtNamespace } from '@fastify/jwt';
import fastifyStatic from '@fastify/static';
import fastifySwagger from '@fastify/swagger';
import fastifySwaggerUi from '@fastify/swagger-ui';
import { TypeBoxTypeProvider } from '@fastify/type-provider-typebox';
import Fastify from 'fastify';
import { PinoLoggerOptions } from 'fastify/types/logger';
import pino from 'pino';
import { inject, singleton } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { ControllerCache } from '@/cross-cutting/infrastructure/cache/ControllerCache';
import { Logger } from '@/cross-cutting/infrastructure/loggers/Logger';
import { prettyStream } from '@/cross-cutting/infrastructure/loggers/Pino';
import { CustomHeadersMiddleware, ECustomHeaders } from '@/cross-cutting/infrastructure/middlewares/CustomHeadersMiddleware';
import { FingerPrintMiddleware } from '@/cross-cutting/infrastructure/middlewares/fingerPrint/FingerPrintMiddleware';
import { LanguageMiddleware } from '@/cross-cutting/infrastructure/middlewares/LanguageMiddleware';
import config from '@config/index';

import { generateGuestTokenController } from './controllers/GenerateGuestTokenController';
import { HealthCheck } from './controllers/HealthCheck';
import { HealthCheckRead } from './controllers/HealthCheckRead';
import { errorHandler } from './ErrorHandler';
import { handleCacheOnSend } from './hooks/handleCacheOnSend';
import { preHandler } from './hooks/PreHandlerHook';
import { setupCacheOnRequest } from './hooks/setupCacheOnRequest';
import { transformPayloadOnSend } from './hooks/transformPayloadOnSend';
import { VerifyAuthOnRequest } from './hooks/VerifyAuthOnRequest';
import { queryStringParser } from './queryStringParser';
import { ApiManager } from './routes/ApiManager';
import ResponseSchema from './schemas/healthCheck/response.json';

import type { FingerPrint } from '@/cross-cutting/infrastructure/middlewares/fingerPrint/contracts/FingerPrint';
import type {
  FastifyBaseLogger, FastifyError, FastifyInstance, FastifyReply, FastifyRequest, HookHandlerDoneFunction, RawServerBase,
} from 'fastify';

const pinoConfig: PinoLoggerOptions = { name: config.serviceName };

const loggerSystem: Record<string, FastifyBaseLogger> = {
  production: pino({
    ...pinoConfig,
    redact: ['headers.authorization'],
  }),
  develop: pino(pinoConfig, prettyStream),
  test: pino({ ...pinoConfig, enabled: false }),
};

declare module 'fastify' {
  export interface FastifyRequest {
    application: {
      readonly id: string;
    };
    fingerPrint: {
      readonly fingerPrint: FingerPrint;
      readonly cost: number;
    };
    cache: ControllerCache;
    error?: FvError;
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  export interface FastifyInstance
    extends FastifyJwtNamespace<{
      jwtDecode: 'securityJwtDecode';
      jwtSign: 'securityJwtSign';
      jwtVerify: 'securityJwtVerify';
    }> {}
}

@singleton()
export class Server {
  private readonly fastifyServer: FastifyInstance;
  private readonly showApiDocumentation = config.apiDocumentation;

  constructor(
    apiRoutesManager: ApiManager,
    healthCheck: HealthCheck,
    healthCheckRead: HealthCheckRead,
    fingerPrintMiddleware: FingerPrintMiddleware,
    customHeadersMiddleware: CustomHeadersMiddleware,
    onRequestHook: VerifyAuthOnRequest,
    languageMiddleware: LanguageMiddleware,
    @inject(DependencyIdentifier.Logger) private readonly logger: Logger,
  ) {
    const {
      env, secretAuth, origin, disableRequestLogging,
    } = config;

    this.fastifyServer = Fastify({
      loggerInstance: loggerSystem[env],
      disableRequestLogging,
      genReqId() {
        return UUID.create().toPrimitive();
      },
      querystringParser: queryStringParser,
      ajv: {
        customOptions: {
          coerceTypes: false,
          logger: this.logger,
          verbose: true,
        },
      },
    }).withTypeProvider<TypeBoxTypeProvider>();

    void this.fastifyServer.register(helmet);

    void this.fastifyServer.register(fastifyCors, {
      origin,
      preflightContinue: true,
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'Accept',
        'Referer',
        'Sec-Ch-Ua',
        'Sec-Ch-Ua-Mobile',
        'Sec-Ch-Ua-Platform',
        'User-Agent',
        'Content-Length',
        'Accept-Language',
        'Origin',
        ...Object.values(ECustomHeaders),
      ],
    });

    void this.fastifyServer.register(fastifyCompress, { global: false });

    this.fastifyServer.addHook('preHandler', fingerPrintMiddleware.handle.bind(fingerPrintMiddleware));
    this.fastifyServer.addHook('preHandler', languageMiddleware.handle.bind(languageMiddleware));
    this.fastifyServer.addHook('preHandler', customHeadersMiddleware.handle.bind(customHeadersMiddleware));

    this.fastifyServer.addHook('preHandler', (request: FastifyRequest, reply: FastifyReply, done: HookHandlerDoneFunction) => {
      preHandler(request, reply, done, this.logger);
    });

    this.fastifyServer.addHook('onRequest', setupCacheOnRequest);

    this.fastifyServer.addHook('onRequest', onRequestHook.handle.bind(onRequestHook));

    this.fastifyServer.addHook('onSend', transformPayloadOnSend);

    this.fastifyServer.addHook('onSend', handleCacheOnSend);

    this.fastifyServer.setErrorHandler((error: FastifyError | FvError, request: FastifyRequest, reply: FastifyReply) => {
      return errorHandler({
        serverError: error,
        request,
        reply,
        fastifyLogger: this.fastifyServer.log,
        logger: this.logger,
      });
    });

    if (this.showApiDocumentation) {
      void this.fastifyServer.register(fastifySwagger, {
        swagger: {
          info: {
            title: 'Cli Api',
            version: '1.0.0',
          },
          host: 'localhost:3750',
          schemes: ['http', 'https'],
          consumes: ['application/json'],
          produces: ['application/json'],
        },
      });

      void this.fastifyServer.register(fastifySwaggerUi, {
        routePrefix: '/docs',
        uiConfig: {
          docExpansion: 'full',
          deepLinking: false,
        },
        uiHooks: {
          onRequest: function (_request, _reply, next) {
            next();
          },
          preHandler: function (_request, _reply, next) {
            next();
          },
        },
        staticCSP: false,
        transformStaticCSP: header => header,
        transformSpecification: swaggerObject => swaggerObject,
        transformSpecificationClone: true,
        theme: { title: 'Cli API Docs' },
      });

      this.logger.info('API REST documentation available at /docs');

      const asyncApiStaticPath = path.join(__dirname, '../../src/cross-cutting/infrastructure/MessageBroker/asyncapi.json');

      void this.fastifyServer.register(fastifyStatic, {
        root: asyncApiStaticPath,
        prefix: '/docs/events',
      });

      this.logger.info('API Events documentation available at /docs/events');
    }

    void this.fastifyServer.register(apiRoutesManager.run.bind(apiRoutesManager), { prefix: '/api' });

    this.fastifyServer.get('/generateGuestToken', generateGuestTokenController);

    this.fastifyServer.get('/liveness', {
      schema: { response: { 200: ResponseSchema } },
      handler: healthCheck.handler.bind(healthCheck),
    });

    this.fastifyServer.get('/readiness', {
      schema: { response: { 200: ResponseSchema } },
      handler: healthCheckRead.handler.bind(healthCheckRead),
    });

    void this.fastifyServer.register(fastifyJwt, { secret: secretAuth });
  }

  async start(host: string, port: number): Promise<string> {
    const performanceMeasurement = PerformanceMeasurement.create(this.constructor.name);

    const listenResult = await this.fastifyServer.listen({ host, port });

    this.logger.info(`✅ Fastify server ready in ${performanceMeasurement.end()} ms`);

    return listenResult;
  }

  async stop(): Promise<void> {
    await this.fastifyServer.close();

    this.logger.info(`👊🏽 | Fastify server closed successfully`);
  }

  async ready(): Promise<RawServerBase> {
    await this.fastifyServer.ready();

    if (this.showApiDocumentation) {
      this.fastifyServer.swagger();
    }

    return this.fastifyServer.server;
  }
}
