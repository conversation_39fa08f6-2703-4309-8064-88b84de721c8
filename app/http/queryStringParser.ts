import qs from 'qs';

type IQueryStringParser = (queryStringToParse: string) => Record<string, unknown>;

export const queryStringParser: IQueryStringParser = (queryStringToParse: string) =>
  qs.parse(queryStringToParse, {
    decoder(str, _decoder, charset) {
      const strWithoutPlus = str.replace(/\+/g, ' ');

      if (charset === 'iso-8859-1') {
        // unescape never throws, no try...catch needed:
        return strWithoutPlus.replace(/%[0-9a-f]{2}/gi, unescape);
      }

      if (/^(\d+|\d*\.\d+)$/.test(str)) {
        return parseFloat(str);
      }

      const keywords = {
        true: true,
        false: false,
        null: null,
        undefined,
      };

      if (str in keywords) {
        return keywords[str as keyof typeof keywords];
      }

      // utf-8
      try {
        return decodeURIComponent(strWithoutPlus);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        return strWithoutPlus;
      }
    },
  });
