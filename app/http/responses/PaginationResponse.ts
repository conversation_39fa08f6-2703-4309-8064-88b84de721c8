import type { PaginationMetadata, PaginationMetadataResponse } from '@discocil/fv-criteria-converter-library/domain';
import type { Static } from '@sinclair/typebox';
import type { paginationMetadata } from '../@types/cli-api/cross-cutting/PaginationSchemas';

type PaginationResponse = Static<typeof paginationMetadata>;

export const paginationResponse = <Request extends PaginationMetadataResponse, Response, DecoratorResponse>(
  fn: (dto: Request) => Response,
) => {
  return (dto: Request): DecoratorResponse => {
    const buildMetadata = (pagination: PaginationMetadata): PaginationResponse => {
      return {
        page: pagination.page,
        perPage: pagination.perPage,
        total: pagination.total,
        next: pagination.next.fold(() => null, item => item),
        previous: pagination.previous.fold(() => null, item => item),
        totalPages: pagination.totalPages,
        order: pagination.order,
      };
    };

    return {
      data: fn(dto),
      metadata: dto.pagination ? buildMetadata(dto.pagination) : null,
    } as unknown as DecoratorResponse;
  };
};
