import { Type } from '@sinclair/typebox';

import { featureFlagSuccessResponseSchema } from './successResponseSchema';

import type { FindEventResponse as Response } from '@app/http/controllers/events/responses/FindEventResponse';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const querySchema = Type.Object({ slug: Type.String({ minLength: 1 }) });

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': featureFlagSuccessResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const paramsSchema = Type.Object({ key: Type.String({ minLength: 1 }) });

export const findFeatureFlagSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};

type Schema = typeof findFeatureFlagSchema;

export type FindFeatureFlagRequest = FastifyRequestTypebox<Schema>;

export type FindFeatureFlagReply = FastifyReplyTypebox<Schema>;

export type FindFeatureFlagResponse = ReturnType<ReturnType<typeof Response>['execute']>;
