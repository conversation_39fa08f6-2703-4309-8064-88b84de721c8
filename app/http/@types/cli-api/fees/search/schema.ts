import { Type } from '@sinclair/typebox';

import { successResponseSchema } from './successResponseSchema';

import type { SearchFeesResponse } from '@app/http/controllers/fees/responses/SearchFeesResponse';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const paramsSchema = Type.Object({});

export const querySchema = Type.Object({
  applyTo: Type.String(),
  organizationId: Type.String(),
});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const searchFeesSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};

type Schema = typeof searchFeesSchema;

export type SearchFeesRequest = FastifyRequestTypebox<Schema>;
export type SearchFeesReply = FastifyReplyTypebox<Schema>;
export type SearchFeesReplyResponse = ReturnType<ReturnType<typeof SearchFeesResponse>['execute']>;
