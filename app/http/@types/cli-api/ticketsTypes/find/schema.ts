import { Type } from '@sinclair/typebox';

import { successResponseSchema } from './successResponseSchema';

import type { FindTicketTypeResponse } from '@app/http/controllers/ticketsTypes/responses/FindTicketTypeResponse';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const paramsSchema = Type.Object({ id: Type.String() });

export const querySchema = Type.Object({
  slug: Type.String(),
  eventIdentifier: Type.Union([Type.String(), Type.Number()]),
  isActive: Type.Optional(Type.Boolean({ default: true })),
});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const findTicketTypeSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};

type Schema = typeof findTicketTypeSchema;

export type FindTicketTypeRequest = FastifyRequestTypebox<Schema>;
export type FindTicketTypeReply = FastifyReplyTypebox<Schema>;
export type FindTicketTypeReplyResponse = ReturnType<ReturnType<typeof FindTicketTypeResponse>['execute']>;
