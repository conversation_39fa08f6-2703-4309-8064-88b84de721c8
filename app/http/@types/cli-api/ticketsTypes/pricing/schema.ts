import { Type } from '@sinclair/typebox';

import { querySchema } from './querySchema';
import { successResponseSchema } from './successResponseSchema';

import type { PricingTicketTypeResponse } from '@app/http/controllers/ticketsTypes/responses/PricingTicketTypeResponse';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const paramsSchema = Type.Object({ id: Type.String() });

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const getPricingTicketTypeSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};

type Schema = typeof getPricingTicketTypeSchema;

export type PricingTicketTypeRequest = FastifyRequestTypebox<Schema>;
export type PricingTicketTypeReply = FastifyReplyTypebox<Schema>;
export type PricingTicketTypeReplyResponse = ReturnType<ReturnType<typeof PricingTicketTypeResponse>['execute']>;
