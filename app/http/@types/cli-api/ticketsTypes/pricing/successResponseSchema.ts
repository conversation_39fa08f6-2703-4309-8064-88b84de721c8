import { EGGDD } from '@discocil/fv-domain-library/domain';
import { Type } from '@sinclair/typebox';

import { Nullable } from '../../cross-cutting/schema';

export const successResponseSchema = Type.Object({
  samePrice: Type.Boolean(),
  ids: Type.Array(Type.String()),
  prices: Type.Array(Type.Number()),
  options: Type.Array(
    Type.Object({
      id: Type.String(),
      name: Nullable(Type.String()),
      price: Type.Number(),
      ggdd: Type.Object({
        type: Type.Enum(EGGDD),
        amount: Type.Number(),
      }),
    }),
  ),
});
