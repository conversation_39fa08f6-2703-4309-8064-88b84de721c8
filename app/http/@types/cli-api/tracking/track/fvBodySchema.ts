import { EMicrositeServices } from '@discocil/fv-domain-library';
import { Type } from '@sinclair/typebox';

import { EMicrositeContainerType } from '@/tracking/domain/contracts/EntityContracts';
import { EEventTypes } from '@/tracking/domain/value-objects/EventType';

import { Nullable } from '../../cross-cutting/schema';

import type { Static } from '@sinclair/typebox';

const bodyFvAddToCartSchema = Type.Object({
  event_name: Type.Enum(EEventTypes),
  session_id: Nullable(Type.String()),
  event_id: Type.String(),
  organization_id: Type.String(),
  url_page: Type.String({ format: 'uri' }),
  price: Type.Number(),
  currency: Type.String(),
  service_type: Type.Enum(EMicrositeServices),
  container_type: Type.Enum(EMicrositeContainerType),
});

const bodyInitiateCheckoutSchema = Type.Object({
  event_name: Type.Enum(EEventTypes),
  session_id: Nullable(Type.String()),
  event_id: Type.String(),
  organization_id: Type.String(),
  url_page: Type.String({ format: 'uri' }),
  price: Type.Number(),
  currency: Type.String(),
  service_type: Type.Enum(EMicrositeServices),
  container_type: Type.Enum(EMicrositeContainerType),
});

const bodyFvPurchaseSchema = Type.Object({
  event_name: Type.Enum(EEventTypes),
  session_id: Nullable(Type.String()),
  event_id: Type.String(),
  organization_id: Type.String(),
  url_page: Type.String({ format: 'uri' }),
  price: Type.Number(),
  currency: Type.String(),
  service_type: Type.Enum(EMicrositeServices),
  container_type: Type.Enum(EMicrositeContainerType),
});

export const fvBodySchema = Type.Union([
  bodyFvAddToCartSchema,
  bodyInitiateCheckoutSchema,
  bodyFvPurchaseSchema,
]);

export type FvAddToCartRequest = Static<typeof bodyFvAddToCartSchema>;
export type FvInitiateCheckoutRequest = Static<typeof bodyInitiateCheckoutSchema>;
export type FvPurchaseRequest = Static<typeof bodyFvPurchaseSchema>;
