import { Type } from '@fastify/type-provider-typebox';

const trackingGroupByEventName = {
  InitiateCheckout: Type.Number(),
  AddToCart: Type.Number(),
  Purchase: Type.Number(),
};

const trackingGroupByService = {
  tickets: Type.Object(trackingGroupByEventName),
  guestlists: Type.Object(trackingGroupByEventName),
  reservations: Type.Object(trackingGroupByEventName),
  passes: Type.Object(trackingGroupByEventName),
  all: Type.Object(trackingGroupByEventName),
};

export const successResponseSchema = Type.Object({
  allEventTrackings: Type.Object(trackingGroupByService),
  eventTrackingsForTheLast15Minutes: Type.Object(trackingGroupByService),
});
