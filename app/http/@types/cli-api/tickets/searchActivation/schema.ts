import { Type } from '@sinclair/typebox';

import { successResponseSchema } from './successResponseSchema';

import type { FindTicketResponse as Response } from '@app/http/controllers/tickets/responses/FindTicketResponse';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const paramsSchema = Type.Object({ ticketOrPurchaseId: Type.String() });

const querySchema = Type.Object({});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const searchActivationTicketsSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};

type Schema = typeof searchActivationTicketsSchema;

export type SearchActivationTicketsRequest = FastifyRequestTypebox<Schema>;

export type SearchActivationTicketsReply = FastifyReplyTypebox<Schema>;

export type SearchActivationTicketsResponse = ReturnType<ReturnType<typeof Response>['execute']>;
