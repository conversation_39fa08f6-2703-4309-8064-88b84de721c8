import { Type } from '@sinclair/typebox';

import { Nullable } from '../../cross-cutting/schema';

const ticketType = {
  id: Type.String(),
  type: Type.String(),
};

const personalDocument = {
  number: Nullable(Type.String()),
  type: Nullable(Type.String()),
};

const payment = { id: Type.String() };

const assistant = {
  name: Nullable(Type.String()),
  email: Nullable(Type.String()),
  phone: Nullable(Type.String()),
  personalDocument: Nullable(Type.Object(personalDocument)),
};

export const urlsSchema = Type.Object({
  download: Type.String(),
  export: Type.String(),
  payment: Nullable(Type.String()),
  source: Nullable(Type.String()),
});

export const customerSchema = Nullable(Type.Object({
  fullname: Nullable(Type.String()),
  email: Nullable(Type.String()),
  phone: Nullable(Type.String()),
}));

const warrantySchema = Nullable(Type.Object({ total: Type.Number() }));

const supplementSchema = Type.Object({
  id: Type.String(),
  label: Type.String(),
  price: Type.Number(),
  fakePrice: Nullable(Type.Number()),
  purchaseQuantity: Nullable(Type.Number()),
  description: Nullable(Type.String()),
});

const commissionsSchema = Nullable(Type.Object({ business: Type.Number() }));

const optionSchema = Type.Object({
  name: Nullable(Type.String()),
  price: Type.Number(),
});

export const successResponseSchema = Type.Object({
  id: Type.String(),
  organization: Type.Object({
    id: Type.String(),
    slug: Nullable(Type.String()),
  }),
  eventId: Type.String(),
  nominative: Type.Boolean(),
  organizationAssignedId: Type.String(),
  code: Type.String(),
  type: Type.Object(ticketType),
  referrerId: Nullable(Type.String()),
  purchaseId: Nullable(Type.String()),
  paymentId: Nullable(Type.String()),
  payment: Nullable(Type.Object(payment)),
  assistant: Nullable(Type.Object(assistant)),
  urls: urlsSchema,
  customer: customerSchema,
  price: Type.Number(),
  totalPrice: Type.Number(),
  remarketing: Type.Boolean(),
  warranty: warrantySchema,
  supplements: Type.Array(supplementSchema),
  commissions: commissionsSchema,
  option: optionSchema,
  state: Type.String(),
});
