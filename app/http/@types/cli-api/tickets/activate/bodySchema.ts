import { Type } from '@sinclair/typebox';

import { assistant } from '../purchase/bodySchema';

import type { Static } from '@sinclair/typebox';

export const activateAssistant = Type.Intersect([
  Type.Object({ ticketId: Type.String() }),
  Type.Omit(assistant, ['supplements']),
]);

export const bodySchema = Type.Object({
  idx: Type.String({ maxLength: 32 }),
  remarketing: Type.Boolean(),
  assistants: Type.Array(activateAssistant, { minItems: 1 }),
});

export type ActivateTicketBody = Static<typeof bodySchema>;
