import { Type } from '@sinclair/typebox';

import { bodySchema } from './bodySchema';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const paramsSchema = Type.Object({ ticketOrPurchaseId: Type.String() });

const querySchema = Type.Object({});

const successResponseSchema = Type.Object({});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const activateTicketSchema = {
  params: paramsSchema,
  querystring: querySchema,
  body: bodySchema,
  response: responseSchema,
};

type Schema = typeof activateTicketSchema;

export type ActivateTicketRequest = FastifyRequestTypebox<Schema>;

export type ActivateTicketReply = FastifyReplyTypebox<Schema>;
