import { Type } from '@sinclair/typebox';

import { bodySchema } from './bodySchema';
import { successResponseSchema } from './successResponseSchema';

import type { PurchaseTicketsResponse as Response } from '@app/http/controllers/tickets/responses/PurchaseTicketsResponse';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const paramsSchema = Type.Object({});

const querySchema = Type.Object({});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const purchaseTicketsSchema = {
  params: paramsSchema,
  querystring: querySchema,
  body: bodySchema,
  response: responseSchema,
};

type Schema = typeof purchaseTicketsSchema;

export type PurchaseTicketsRequest = FastifyRequestTypebox<Schema>;

export type PurchaseTicketsReply = FastifyReplyTypebox<Schema>;

export type PurchaseTicketsResponse = ReturnType<ReturnType<typeof Response>['execute']>;
