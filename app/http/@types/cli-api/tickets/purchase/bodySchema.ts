import {
  ECountryCode,
  EDocumentType,
  EGender,
  ESaleTypes,
} from '@discocil/fv-domain-library/domain';
import { Type } from '@sinclair/typebox';

import { Nullable } from '../../cross-cutting/schema';

import type { Static } from '@sinclair/typebox';

const personalDocument = {
  type: Type.Enum(EDocumentType),
  number: Type.String(),
};

const answers = {
  id: Type.String(),
  answer: Type.String(),
};

const purchaseSupplements = {
  id: Type.String(),
  purchaseQuantity: Type.Number(),
};

export const assistant = Type.Object({
  email: Type.String(),
  emailConfirmation: Type.Optional(Type.String()),
  supplements: Type.Optional(Type.Array(Type.Object(purchaseSupplements))),
  fullname: Type.Optional(Type.String()),
  phone: Type.Optional(Type.String()),
  birthDate: Type.Optional(Type.Number()),
  gender: Type.Optional(Type.Enum(EGender)),
  personalDocument: Type.Optional(Type.Object(personalDocument)),
  address: Type.Optional(Type.String()),
  country: Type.Optional(Type.Enum(ECountryCode)),
  zipCode: Type.Optional(Type.String()),
  answers: Type.Optional(Type.Array(Type.Object(answers))),
});

const assistantsForOrder = Type.Intersect([Type.Object({ optionId: Type.String() }), assistant]);

const urls = Type.Object({
  redirect: Type.String(),
  error: Type.String(),
  page: Nullable(Type.String()),
});

const fb = Nullable(
  Type.Object({
    fbp: Nullable(Type.String()),
    fbc: Nullable(Type.String()),
    fbclid: Nullable(Type.String()),
    capiPurchaseId: Nullable(Type.String()),
    fvCookies: Type.Boolean(),
  }),
);

const sitting = Nullable(
  Type.Object({
    holdToken: Nullable(Type.String()),
    seats: Type.String(),
    encodingVersion: Type.Number(),
  }),
);

const spotify = Nullable(
  Type.Object({ pzClickref: Type.String() }),
);

export const oneTypePurchaseBodySchema = Type.Object({
  typeId: Type.String(),
  assistants: Type.Array(assistant, { minItems: 1 }),
  eventId: Type.String(),
  amount: Type.Number({ minimum: 1, maximum: 100 }),
  purchaseId: Nullable(Type.String()),
  saleType: Type.Enum(ESaleTypes),
  organizationAssignedId: Nullable(Type.String()),
  referrerId: Nullable(Type.String()),
  linkId: Nullable(Type.String()),
  subscriberId: Nullable(Type.String()),
  idx: Type.String({ maxLength: 32 }),
  discountCode: Nullable(Type.String()),
  warrantySelected: Type.Boolean(),
  remarketing: Type.Boolean(),
  paylinkId: Nullable(Type.String()),
  urls,
  fb,
  sitting,
  spotify,
});

export const orderPurchaseBodySchema = Type.Intersect([
  Type.Omit(oneTypePurchaseBodySchema, ['typeId', 'assistants']),
  Type.Object({
    types: Type.Array(
      Type.Object({
        typeId: Type.String(),
        assistants: Type.Array(assistantsForOrder, { minItems: 1 }),
      }),
    ),
  }),
]);

export const bodySchema = Type.Union([oneTypePurchaseBodySchema, orderPurchaseBodySchema]);

export type OneTypePurchaseTickets = Static<typeof oneTypePurchaseBodySchema>;
export type OrderPurchaseTickets = Static<typeof orderPurchaseBodySchema>;
export type OneTypePurchaseAssistants = Static<typeof orderPurchaseBodySchema>['types'][number]['assistants'][number];
export type OrderPurchaseAssistants = Static<typeof oneTypePurchaseBodySchema>['assistants'][number];
