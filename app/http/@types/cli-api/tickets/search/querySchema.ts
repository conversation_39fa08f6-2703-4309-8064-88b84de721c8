import { Type } from '@sinclair/typebox';

import { paginationSchema } from '../../cross-cutting/PaginationSchemas';

export const querySchemaSearchTickets = Type.Object({
  ids: Type.Optional(Type.Array(Type.String())),
  idx: Type.Optional(Type.String({ maxLength: 32 })),
  purchaseId: Type.Optional(Type.String()),
});

export const querySchema = Type.Intersect([querySchemaSearchTickets, paginationSchema]);
