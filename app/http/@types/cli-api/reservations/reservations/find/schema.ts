import { Type } from '@sinclair/typebox';

import { successResponseSchema } from './successResponseSchema';

const paramsSchema = Type.Object({});

const querySchema = Type.Object({
  activationCode: Type.Optional(Type.String()),
  purchaseId: Type.Optional(Type.String()),
});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const findReservationSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};
