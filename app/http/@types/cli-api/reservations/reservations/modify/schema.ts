import { Type } from '@sinclair/typebox';

import { successResponseSchema } from './successResponseSchema';

const paramsSchema = Type.Object({});

const querySchema = Type.Object({});

const bodySchema = Type.Object({
  organizationId: Type.String(),
  reservationId: Type.String(),
  observations: Type.Union([
    Type.String(),
    Type.Null(),
  ]),
  shouldAddSubscriber: Type.Boolean(),
  isFullPayment: Type.Boolean(),
  purchaseId: Type.String(),
  successUrl: Type.String(),
  failureUrl: Type.String(),
  totalAmount: Type.Number(),
  fields: Type.Array(Type.Object({
    title: Type.String(),
    value: Type.Unknown(),
  })),
});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const modifyReservationSchema = {
  params: paramsSchema,
  querystring: querySchema,
  body: bodySchema,
  response: responseSchema,
};
