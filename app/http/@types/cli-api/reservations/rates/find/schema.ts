import { Type } from '@sinclair/typebox';

import { successResponseSchema } from './successResponseSchema';

const paramsSchema = Type.Object({ rateId: Type.String() });

const querySchema = Type.Object({
  organizationId: Type.String(),
  eventId: Type.String(),
  zoneId: Type.String(),
  numberOfPeople: Type.Number(),
});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const findRateSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};
