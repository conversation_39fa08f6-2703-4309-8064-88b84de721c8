import { Type } from '@sinclair/typebox';

export const successResponseSchema = Type.Object({
  rateId: Type.String(),
  price: Type.Number(),
  currency: Type.String(),
  allowCompletePayment: Type.Boolean(),
  pendingFullPayment: Type.Object({
    baseAmount: Type.Number(),
    feeAmount: Type.Number(),
    totalAmount: Type.Number(),
  }),
  pendingDepositPayment: Type.Object({
    baseAmount: Type.Number(),
    feeAmount: Type.Number(),
    totalAmount: Type.Number(),
  }),
  color: Type.String(),
  includes: Type.Union([Type.String(), Type.Null()]),
  conditions: Type.Union([Type.String(), Type.Null()]),
});
