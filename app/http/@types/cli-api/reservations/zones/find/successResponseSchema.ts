import { Type } from '@sinclair/typebox';

export const successResponseSchema = Type.Object({
  name: Type.String(),
  maximumNumberOfPeople: Type.Number(),
  webImageUrl: Type.Union([Type.String(), Type.Null()]),
  rates: Type.Array(
    Type.Object({
      id: Type.String(),
      name: Type.String(),
      price: Type.Number(),
      numberOfPeople: Type.Number(),
      extraPeoplePrice: Type.Number(),
      extraPeopleLimit: Type.Number(),
      soldOut: Type.Boolean(),
      color: Type.String(),
      pricePerPerson: Type.Record(Type.Number(), Type.Union([Type.Number(), Type.Null()])),
      availabilityPerPerson: Type.Record(Type.Number(), Type.Boolean()),
      deposit: Type.Object({
        type: Type.String(),
        value: Type.Union([
          Type.Number(),
          Type.Object({
            amount: Type.Number(),
            currency: Type.String(),
          }),
        ]),
      }),
    }),
  ),
});

