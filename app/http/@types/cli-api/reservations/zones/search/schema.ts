import { Type } from '@sinclair/typebox';

import { successResponseSchema } from './successResponseSchema';

const paramsSchema = Type.Object({});

const querySchema = Type.Object({
  organizationId: Type.String(),
  eventId: Type.String(),
});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const searchZonesSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};
