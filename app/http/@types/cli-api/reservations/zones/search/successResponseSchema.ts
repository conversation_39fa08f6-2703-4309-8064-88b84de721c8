import { Type } from '@sinclair/typebox';

export const successResponseSchema = Type.Object({
  maximumNumberOfPeople: Type.Number(),
  zones: Type.Array(
    Type.Object({
      id: Type.String(),
      name: Type.String(),
      soldOut: Type.Boolean(),
      maxPeopleLimit: Type.Number(),
      availabilityPerPerson: Type.Record(Type.Number(), Type.Boolean()),
      minPricePerPerson: Type.Record(Type.Number(), Type.Union([Type.Number(), Type.Null()])),
    }),
  ),
});
