/* eslint-disable @typescript-eslint/explicit-function-return-type */

import { OrderTypes } from '@discocil/fv-criteria-converter-library/domain';
import { Type } from '@sinclair/typebox';

import { Nullable } from './schema';

import type { TSchema } from '@sinclair/typebox';

export const paginationMetadata = Nullable(
  Type.Object({
    page: Type.Number(),
    perPage: Type.Number(),
    total: Type.Number(),
    next: Nullable(Type.Number()),
    previous: Nullable(Type.Number()),
    totalPages: Type.Number(),
    order: Type.Object({
      type: Type.String(),
      by: Type.String(),
    }),
  }),
);

export const paginationSchema = Type.Object({
  page: Type.Optional(Type.Integer()),
  perPage: Type.Optional(Type.Integer()),
  orderBy: Type.Optional(Type.String()),
  orderType: Type.Optional(Type.Enum(OrderTypes)),
});

export const PaginationResponse = <T extends TSchema>(data: T) => Type.Object({ data, metadata: paginationMetadata });
