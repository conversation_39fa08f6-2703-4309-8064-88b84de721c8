/* eslint-disable @typescript-eslint/explicit-function-return-type */

import { Type } from '@sinclair/typebox';

import type { MultipartFile } from '@fastify/multipart';
import type { TypeBoxTypeProvider } from '@fastify/type-provider-typebox';
import type { TSchema } from '@sinclair/typebox';
import type {
  ContextConfigDefault,
  FastifyBaseLogger,
  FastifyInstance,
  FastifyReply,
  FastifyRequest,
  RawReplyDefaultExpression,
  RawRequestDefaultExpression,
  RawServerDefault,
} from 'fastify';
import type { RouteGenericInterface } from 'fastify/types/route';
import type { FastifySchema } from 'fastify/types/schema';

export const Nullable = <T extends TSchema>(schema: T) => Type.Union([schema, Type.Null()]);

export const Undefined = <T extends TSchema>(schema: T) => Type.Union([schema, Type.Undefined()]);

export const NullableOrUndefined = <T extends TSchema>(schema: T) => Type.Union([schema, Type.Null(), Type.Undefined()]);

export const Empty = <T extends TSchema>(schema: T) => Type.Union([schema, Type.Object({})]);

export type FastifyTypebox = FastifyInstance<
  RawServerDefault,
  RawRequestDefaultExpression,
  RawReplyDefaultExpression,
  FastifyBaseLogger,
  TypeBoxTypeProvider
>;

export type FastifyRequestTypebox<TSchema extends FastifySchema> = FastifyRequest<
  RouteGenericInterface,
  RawServerDefault,
  RawRequestDefaultExpression,
  TSchema,
  TypeBoxTypeProvider
>;

export type FastifyReplyTypebox<TSchema extends FastifySchema> = FastifyReply<
  RouteGenericInterface,
  RawServerDefault,
  RawRequestDefaultExpression,
  RawReplyDefaultExpression,
  ContextConfigDefault,
  TSchema,
  TypeBoxTypeProvider
>;

export type FastifyRequestMultipartTypeBox<TSchema extends FastifySchema> = FastifyRequestTypebox<TSchema> & {
  file: () => Promise<MultipartFile | undefined>;
};
