import { Type } from '@sinclair/typebox';

import { uploadImageRequest } from './uploadImageRequest';
import { uploadImageResponseSchema } from './uploadImageResponeSchema';

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': uploadImageResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const uploadImageSchema = {
  body: uploadImageRequest,
  response: responseSchema,
};
