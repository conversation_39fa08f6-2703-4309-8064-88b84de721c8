import { Type } from '@sinclair/typebox';

import { successResponseSchema } from '../../passType/search/successResponseSchema';

import { querySchema } from './querySchema';

import type { SearchPassTypesResponse } from '@app/http/controllers/passType/responses/SearchPassTypesResponse';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const paramsSchema = Type.Object({ idOrCode: Type.String() });

export const searchPassTypesSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};

type Schema = typeof searchPassTypesSchema;

export type SearchEventPassTypesRequest = FastifyRequestTypebox<Schema>;
export type SearchEventPassTypesReply = FastifyReplyTypebox<Schema>;
export type SearchEventPassTypesReplyResponse = ReturnType<ReturnType<typeof SearchPassTypesResponse>['execute']>;
