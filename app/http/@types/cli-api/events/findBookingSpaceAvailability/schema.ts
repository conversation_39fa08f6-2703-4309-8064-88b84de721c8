import { Type } from '@sinclair/typebox';

import { findBookingSpaceAvailabilityQuerySchema } from './querySchema';
import { successResponseSchema } from './successResponseSchema';

import type { FindBookingSpaceAvailabilityResponse as Response } from '@app/http/controllers/events/responses/FindBookingSpaceAvailabilityResponse';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const findBookingSpaceAvailabilitySchema = {
  querystring: findBookingSpaceAvailabilityQuerySchema,
  response: responseSchema,
};

export type FindBookingSpaceAvailabilitySchema = typeof findBookingSpaceAvailabilitySchema;

export type FindBookingSpaceAvailabilityRequest = FastifyRequestTypebox<FindBookingSpaceAvailabilitySchema>;
export type FindBookingSpaceAvailabilityReply = FastifyReplyTypebox<FindBookingSpaceAvailabilitySchema>;
export type FindBookingSpaceAvailabilityResponse = ReturnType<ReturnType<typeof Response>['execute']>;
