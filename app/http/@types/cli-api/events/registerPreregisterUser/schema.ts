import { Type } from '@sinclair/typebox';

import { successResponseSchema } from './successResponseSchema';

const querySchema = Type.Object({});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

const bodySchema = Type.Object({
  fields: Type.Object({}),
  remarketing: Type.Boolean({ default: false }),
  newsletter: Type.Boolean({ default: false }),
});

export const paramsSchema = Type.Object({ id: Type.String() });

export const registerEventPreregisterUserSchema = {
  params: paramsSchema,
  querystring: querySchema,
  body: bodySchema,
  response: responseSchema,
};
