import { Type } from '@sinclair/typebox';

import { querySchema } from './querySchema';
import { successResponseSchema } from './successResponseSchema';

import type { SearchEventTicketsTypesResponse } from '@app/http/controllers/events/responses/SearchEventTicketsTypesResponse';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const paramsSchema = Type.Object({ idOrCode: Type.String() });

export const searchTicketsTypesSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};

type Schema = typeof searchTicketsTypesSchema;

export type SearchEventTicketsTypesRequest = FastifyRequestTypebox<Schema>;
export type SearchEventTicketsTypesReply = FastifyReplyTypebox<Schema>;
export type SearchEventTicketsTypesReplyResponse = ReturnType<ReturnType<typeof SearchEventTicketsTypesResponse>['execute']>;
