import { Type } from '@sinclair/typebox';

import { PaginationResponse } from '../../cross-cutting/PaginationSchemas';
import { Nullable } from '../../cross-cutting/schema';
import { customersSchema } from '../../ticketsTypes/find/successResponseSchema';

const disponibilitySchema = {
  isActive: Type.Boolean(),
  isPercentageExceed: Type.Boolean(),
  type: Type.String(),
  value: Type.Number(),
};

const optionSchema = {
  id: Type.String(),
  name: Nullable(Type.String()),
  price: Type.Number(),
  content: Nullable(Type.String()),
  additionalInfo: Nullable(Type.String()),
  max: Type.Number(),
  until: Type.Number(),
  totalSold: Type.Number(),
  disponibility: Type.Object(disponibilitySchema),
};

const dates = {
  from: Type.Number(),
  to: Nullable(Type.Number()),
};

const successSchema = Type.Array(
  Type.Object({
    id: Type.String(),
    name: Type.String(),
    isComplete: Type.Boolean(),
    type: Type.String(),
    order: Type.Number(),
    hideCountdown: Type.Boolean(),
    dates: Type.Object(dates),
    price: Type.Number(),
    priceComplete: Type.String(),
    isSoldOut: Type.Boolean(),
    areFewLeft: Type.Boolean(),
    fewLeftSelectedKey: Nullable(Type.String()),
    customers: Type.Object(customersSchema),
    options: Type.Array(Type.Object(optionSchema)),
    availableOptionId: Nullable(Type.String()),
    disponibility: Type.Object(disponibilitySchema),
  }),
);

export const successResponseSchema = PaginationResponse(successSchema);
