import { Type } from '@sinclair/typebox';

import { paginationSchema } from '../../cross-cutting/PaginationSchemas';

export const querySchemaSearchTicketTypes = Type.Object({
  slug: Type.String(),
  organizations: Type.Array(
    Type.String(),
    { default: [] },
  ),
  smsSale: Type.Optional(Type.Boolean({ default: false })),
});

export const querySchema = Type.Intersect([querySchemaSearchTicketTypes, paginationSchema]);
