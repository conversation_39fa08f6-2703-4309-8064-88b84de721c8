import { Type } from '@sinclair/typebox';

import { bodySchema } from './bodySchema';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const querySchema = Type.Object({});

const errorResponseSchema = Type.Object({ message: Type.String() });

const successResponseSchema = Type.Object({});

const responseSchema = {
  '2xx': successResponseSchema,
  '5xx': errorResponseSchema,
};

const paramsSchema = Type.Object({ id: Type.String() });

export const eventVisitedSchema = {
  params: paramsSchema,
  querystring: querySchema,
  body: bodySchema,
  response: responseSchema,
};

type Schema = typeof eventVisitedSchema;

export type EventVisitedRequest = FastifyRequestTypebox<Schema>;
export type EventVisitedReply = FastifyReplyTypebox<Schema>;
