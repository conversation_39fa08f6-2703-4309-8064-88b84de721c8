import { Type } from '@sinclair/typebox';

import { paginationSchema } from '../../cross-cutting/PaginationSchemas';

const querySearchBookingZonesSchema = Type.Object({
  slug: Type.String(),
  organizations: Type.Array(
    Type.String(),
    { default: [] },
  ),
  smsSale: Type.Optional(Type.Boolean({ default: false })),
});

export const querySchema = Type.Intersect([querySearchBookingZonesSchema, paginationSchema]);
