import { Type } from '@sinclair/typebox';

import { PaginationResponse } from '../../cross-cutting/PaginationSchemas';
import { Nullable } from '../../cross-cutting/schema';

const typesSchema = {
  id: Type.String(),
  name: Type.String(),
  price: Nullable(Type.Number()),
  colors: Type.Array(Type.Number()),
};

const successSchema = Type.Array(
  Type.Object({
    id: Type.String(),
    name: Type.String(),
    isComplete: Type.Boolean(),
    order: Type.Number(),
    types: Type.Array(Type.Object(typesSchema)),
  }),
);

export const successResponseSchema = PaginationResponse(successSchema);
