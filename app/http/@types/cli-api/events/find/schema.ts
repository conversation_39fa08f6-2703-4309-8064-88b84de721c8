import { Type } from '@sinclair/typebox';

import { successResponseSchema } from './successResponseSchema';

import type { FindEventResponse as Response } from '@app/http/controllers/events/responses/FindEventResponse';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const querySchema = Type.Object({
  smsSale: Type.Optional(Type.Boolean({ default: false })),
  slugChannel: Type.Optional(Type.String()),
});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const paramsSchema = Type.Object({ idOrCode: Type.String() });

export const findEventSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};

type Schema = typeof findEventSchema;

export type FindEventRequest = FastifyRequestTypebox<Schema>;

export type FindEventReply = FastifyReplyTypebox<Schema>;

export type FindEventResponse = ReturnType<ReturnType<typeof Response>['execute']>;
