import { Type } from '@sinclair/typebox';

import { successResponseSchema } from '@app/http/@types/cli-api/events/findZonesWebImage/successResponseSchema';

const paramsSchema = Type.Object({ eventId: Type.String() });

const querySchema = Type.Object({ organizationId: Type.String() });

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const findZonesWebImageSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};
