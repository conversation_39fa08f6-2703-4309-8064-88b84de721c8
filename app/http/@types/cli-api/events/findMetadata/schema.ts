import { Type } from '@sinclair/typebox';

import { successResponseSchema } from './successResponseSchema';

import type { FindEventMetadataResponse as Response } from '@app/http/controllers/events/responses/FindEventMetadataResponse';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const querySchema = Type.Object({});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const paramsSchema = Type.Object({ idOrCode: Type.String() });

export const findEventMetadataSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};

type Schema = typeof findEventMetadataSchema;

export type FindEventMetadataRequest = FastifyRequestTypebox<Schema>;

export type FindEventMetadataReply = FastifyReplyTypebox<Schema>;

export type FindEventMetadataResponse = ReturnType<ReturnType<typeof Response>['execute']>;
