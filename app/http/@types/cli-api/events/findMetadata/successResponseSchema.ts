import { Type } from '@sinclair/typebox';

export const successResponseSchema = Type.Object({
  '@type': Type.String(),
  '@context': Type.String(),
  'name': Type.String(),
  'eventStatus': Type.String(),
  'eventAttendanceMode': Type.String(),
  'typicalAgeRange': Type.String(),
  'startDate': Type.String(),
  'endDate': Type.String(),
  'doorDate': Type.String(),
  'description': Type.String(),
  'image': Type.String(),
  'duration': Type.String(),
  'organizer': Type.Object({
    '@type': Type.String(),
    'name': Type.String(),
    'url': Type.String(),
    'logo': Type.String(),
    'description': Type.String(),
  }),
  'location': Type.Optional(
    Type.Object({
      '@type': Type.String(),
      'name': Type.String(),
      'address': Type.Object({
        '@type': Type.String(),
        'streetAddress': Type.String(),
        'postalCode': Type.String(),
        'addressLocality': Type.String(),
        'addressCountry': Type.String(),
        'addressRegion': Type.String(),
      }),
      'geo': Type.Object({
        '@type': Type.String(),
        'latitude': Type.Number(),
        'longitude': Type.Number(),
      }),
    }),
  ),
  'performers': Type.Optional(
    Type.Array(
      Type.Object({
        '@type': Type.String(),
        'name': Type.String(),
        'image': Type.String(),
      }),
    ),
  ),
  'offers': Type.Array(Type.Object({
    '@type': Type.String(),
    'priceCurrency': Type.String(),
    'price': Type.Number(),
    'name': Type.String(),
    'validFrom': Type.String(),
    'validThrough': Type.String(),
    'availabilityStarts': Type.Optional(Type.String()),
    'availabilityEnds': Type.Optional(Type.String()),
    'availability': Type.String(),
    'acceptedPaymentMethod': Type.Array(Type.String()),
    'url': Type.Optional(Type.String()),
  })),
});

