import { Type } from '@sinclair/typebox';

import { paginationSchema } from '../../cross-cutting/PaginationSchemas';

const querySchema = Type.Object({
  slug: Type.String(),
  organizations: Type.Array(
    Type.String(),
    { default: [] },
  ),
  smsSale: Type.Optional(Type.Boolean({ default: false })),
});

export const querySearchGuestListSchema = Type.Intersect([querySchema, paginationSchema]);
