import { Type } from '@sinclair/typebox';

import { PaginationResponse } from '../../cross-cutting/PaginationSchemas';
import { Nullable } from '../../cross-cutting/schema';

const optionSchema = {
  id: Type.String(),
  dates: Type.Object({
    from: Type.Number(),
    to: Type.Number(),
  }),
  male: Type.Boolean(),
  female: Type.Boolean(),
  price: Type.Number(),
  age: Type.Number(),
  content: Nullable(Type.String()),
  additionalInfo: Nullable(Type.String()),
};

const summarySchema = {
  id: Type.String(),
  duration: Type.String(),
  until: Type.String(),
  content: Nullable(Type.String()),
  additionalInfo: Nullable(Type.String()),
};

export const successSchema = Type.Array(
  Type.Object({
    id: Type.String(),
    type: Type.String(),
    isComplete: Type.Boolean(),
    isActive: Type.Boolean(),
    name: Nullable(Type.String()),
    hideCountdown: Type.Boolean(),
    dates: Type.Object({
      from: Nullable(Type.Number()),
      to: Nullable(Type.Number()),
    }),
    summary: Type.Array(Type.Object(summarySchema)),
    maximum: Type.Number(),
    available: Type.Number(),
    options: Type.Array(Type.Object(optionSchema)),
  }),
);

export const successResponseSchema = PaginationResponse(successSchema);
