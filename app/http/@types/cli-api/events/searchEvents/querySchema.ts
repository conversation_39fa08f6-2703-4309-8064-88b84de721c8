import { Type } from '@sinclair/typebox';

import { paginationSchema } from '@app/http/@types/cli-api/cross-cutting/PaginationSchemas';

const querySchema = Type.Object({
  startDate: Type.Optional(Type.Number()),
  endDate: Type.Optional(Type.Number()),
  slug: Type.Optional(Type.String()),
  organizations: Type.Array(
    Type.String(),
    { default: [] },
  ),
  groupCodes: Type.Array(
    Type.String(),
    { default: [] },
  ),
  isSitemap: Type.Boolean({ default: false }),
});

export const querySearchEventsSchema = Type.Intersect([querySchema, paginationSchema]);
