import { Type } from '@sinclair/typebox';

import { PaginationResponse } from '../../cross-cutting/PaginationSchemas';
import { Nullable } from '../../cross-cutting/schema';

export const passTypeSuccessSchema = Type.Object({
  id: Type.String(),
  organizationId: Type.String(),
  name: Type.String(),
  startDate: Type.Number(),
  endDate: Type.Number(),
  startCountdown: Type.Boolean(),
  description: Nullable(Type.String()),
  status: Type.String(),
  price: Type.Object({
    id: Type.String(),
    description: Nullable(Type.String()),
    price: Type.Number(),
    fee: Type.Number(),
    feeType: Type.String(),
    additionalInfo: Nullable(Type.String()),
  }),
  color: Type.String(),
  tags: Type.Array(Type.String()),
  designId: Nullable(Type.String()),
});

export const successSchema = Type.Array(passTypeSuccessSchema);

export const successResponseSchema = PaginationResponse(successSchema);
