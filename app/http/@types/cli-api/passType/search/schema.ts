import { Type } from '@sinclair/typebox';

import { querySchema } from './querySchema';
import { successResponseSchema } from './successResponseSchema';

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const paramsSchema = Type.Object({});

export const searchPassTypeSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};
