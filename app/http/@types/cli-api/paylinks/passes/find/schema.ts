import { Type } from '@sinclair/typebox';

import { successResponseSchema } from './successResponseSchema';

import type { FindPassPaylinkResponse as Response } from '@app/http/controllers/paylink/responses/passes/FindPassPaylinkResponse';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../../cross-cutting/schema';

const paramsSchema = Type.Object({ activateCode: Type.String() });

const querySchema = Type.Object({});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const findPaylinkSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};

type Schema = typeof findPaylinkSchema;

export type FindPaylinkRequest = FastifyRequestTypebox<Schema>;

export type FindPaylinkReply = FastifyReplyTypebox<Schema>;

export type FindPaylinkReplyResponse = ReturnType<ReturnType<typeof Response>['execute']>;
