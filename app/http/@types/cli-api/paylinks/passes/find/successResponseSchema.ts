import { Type } from '@sinclair/typebox';

import { Nullable } from '../../../cross-cutting/schema';

const organizationSchema = {
  id: Type.String(),
  name: Type.String(),
  slug: Nullable(Type.String()),
  image: Nullable(Type.String()),
};

const resourceSchema = {
  ids: Type.Array(Type.String()),
  type: Type.String(),
};

const referrerSchema = { id: Type.String() };

const rateSchema = { id: Type.String() };

export const successResponseSchema = Type.Object({
  id: Type.String(),
  organization: Type.Object(organizationSchema),
  resource: Type.Object(resourceSchema),
  referrer: Type.Object(referrerSchema),
  rate: Type.Object(rateSchema),
  phone: Nullable(Type.String()),
  email: Nullable(Type.String()),
  activateCode: Type.String(),
  status: Type.String(),
  language: Type.String(),
});
