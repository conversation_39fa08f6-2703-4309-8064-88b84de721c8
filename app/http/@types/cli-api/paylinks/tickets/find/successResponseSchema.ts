import { Type } from '@sinclair/typebox';

import { Nullable } from '@app/http/@types/cli-api/cross-cutting/schema';

import { customersSchema } from '../../../ticketsTypes/find/successResponseSchema';

const organizationSchema = {
  id: Type.String(),
  slug: Nullable(Type.String()),
};

const eventSchema = { id: Type.String() };

const referrerSchema = { id: Type.String() };

const ticketTypeSchema = {
  id: Type.String(),
  customers: Type.Object(customersSchema),
};

export const successResponseSchema = Type.Object({
  id: Type.String(),
  organization: Type.Object(organizationSchema),
  event: Type.Object(eventSchema),
  referrer: Type.Object(referrerSchema),
  ticketType: Type.Object(ticketTypeSchema),
  phone: Nullable(Type.String()),
  email: Nullable(Type.String()),
  activateCode: Type.String(),
  status: Type.String(),
  language: Type.String(),
  nCompleted: Type.Number(),
  nRequested: Type.Number(),
});
