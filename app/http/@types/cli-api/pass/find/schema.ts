import { Type } from '@sinclair/typebox';

import { successResponseSchema } from './response';

export const paramsSchema = Type.Object({ id: Type.String() });

export const querySchema = Type.Object({ organizationId: Type.String() });

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const getPassSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};
