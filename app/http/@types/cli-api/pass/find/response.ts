import { Type } from '@sinclair/typebox';

import { Nullable } from '../../cross-cutting/schema';

export const successResponseSchema = Type.Object({
  id: Type.String(),
  organizationId: Type.String(),
  applicationId: Type.String(),
  organizationAssignedId: Nullable(Type.String()),
  code: Type.String(),
  sourceUrl: Nullable(Type.String()),
  customer: Type.Object({
    fullname: Type.String(),
    image: Nullable(Type.String()),
    email: Nullable(Type.String()),
    phone: Nullable(Type.String()),
    gender: Nullable(Type.String()),
    personalDocumentNumber: Nullable(Type.String()),
    personalDocumentType: Nullable(Type.String()),
    address: Nullable(Type.String()),
    birthday: Nullable(Type.Number()),
    country: Nullable(Type.String()),
    customFields: Nullable(Type.Array(Type.Object({
      question: Type.String(),
      answer: Type.String(),
    }))),
  }),
  typeId: Nullable(Type.String()),
  typeName: Nullable(Type.String()),
  typeColor: Nullable(Type.String()),
  priceId: Nullable(Type.String()),
  purchaseDate: Nullable(Type.Number()),
  purchasePrice: Type.Number(),
  serviceFees: Type.Number(),
  revenue: Type.Number(),
  paymentId: Nullable(Type.String()),
  state: Type.String(),
  idx: Type.String(),
  language: Type.String(),
  browser: Nullable(Type.String()),
  device: Nullable(Type.String()),
  os: Nullable(Type.String()),
  archived: Type.Boolean(),
  saleType: Type.String(),
  urlDownload: Nullable(Type.String()),
  urlExport: Nullable(Type.String()),
});
