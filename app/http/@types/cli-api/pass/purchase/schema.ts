import { Type } from '@sinclair/typebox';

import { purchasePassBodySchema } from './bodySchema';
import { purchasePassSuccessResponseSchema } from './successResponseSchema';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const paramsSchema = Type.Object({});

const querySchema = Type.Object({});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': purchasePassSuccessResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const purchasePassSchema = {
  params: paramsSchema,
  querystring: querySchema,
  body: purchasePassBodySchema,
  response: responseSchema,
};

type Schema = typeof purchasePassSchema;

export type PurchasePassRequest = FastifyRequestTypebox<Schema>;
export type PurchasePassReply = FastifyReplyTypebox<Schema>;
