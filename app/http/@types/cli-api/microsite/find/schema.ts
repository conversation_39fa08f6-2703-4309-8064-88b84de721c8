import { Type } from '@sinclair/typebox';

import { micrositeSuccessResponseSchema } from './successResponseSchema';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const paramsSchema = Type.Object({ slug: Type.String() });

const querySchema = Type.Object({
  organizations: Type.Array(
    Type.String(),
    { default: [] },
  ),
});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': micrositeSuccessResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const findMicrositeTypeSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};

type Schema = typeof findMicrositeTypeSchema;

export type FindMicrositeRequest = FastifyRequestTypebox<Schema>;
export type FindMicrositeReply = FastifyReplyTypebox<Schema>;
