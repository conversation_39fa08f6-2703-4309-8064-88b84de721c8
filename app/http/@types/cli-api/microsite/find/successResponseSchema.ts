import { Type } from '@sinclair/typebox';

import { Nullable } from '../../cross-cutting/schema';

const trackingSchema = Type.Object({
  google: Type.Object({ propertyId: Nullable(Type.String()) }),
  facebook: Type.Object({
    pixelId: Nullable(Type.String()),
    accessToken: Nullable(Type.String()),
  }),
});

const seoSchema = Type.Object({
  title: Nullable(Type.String()),
  keywords: Nullable(Type.String()),
  place: Nullable(Type.String()),
  descriptions: Type.Object({
    short: Nullable(Type.String()),
    lead: Type.Object({
      relatedToPlace: Nullable(Type.String()),
      local: Nullable(Type.String()),
    }),
    body: Type.Object({
      relatedToPlace: Nullable(Type.String()),
      local: Nullable(Type.String()),
    }),
  }),
});

const iframeSchema = Type.Object({
  pages: Type.Object({
    cancel: Nullable(Type.String()),
    theme: Nullable(Type.String()),
    thankYou: Nullable(Type.String()),
  }),
});

const organizationReferrerSchema = Type.Object({
  id: Type.String(),
  name: Type.String(),
  slug: Nullable(Type.String()),
  image: Nullable(Type.String()),
  type: Type.String(),
  hasPassTypes: Type.Boolean(),
  currency: Nullable(Type.String()),
});

const imageSchema = Type.Object({
  image: Nullable(Type.String()),
  imageLogo: Nullable(Type.String()),
});

const coverImagesSchema = Type.Object({
  original: Nullable(Type.String()),
  versioned: Nullable(Type.String()),
  sized: Nullable(Type.String()),
  isDefault: Nullable(Type.Boolean()),
});

const coversSchema = Type.Object({
  cover: Nullable(Type.String()),
  coverImages: Nullable(
    Type.Object({
      mini: Nullable(coverImagesSchema),
      small: Nullable(coverImagesSchema),
      medium: Nullable(coverImagesSchema),
    }),
  ),
});

const urlSchema = Type.Object({
  website: Nullable(Type.String()),
  facebook: Nullable(Type.String()),
  instagram: Nullable(Type.String()),
  menuUrl: Nullable(Type.String()),
});

const locationSchema = Type.Object({
  id: Type.String(),
  fullAddress: Nullable(Type.String()),
  latitude: Nullable(Type.Number()),
  longitude: Nullable(Type.Number()),
  alias: Nullable(Type.String()),
});

export const referrerDataSchema = Type.Object({
  id: Type.String(),
  slug: Nullable(Type.String()),
  name: Nullable(Type.String()),
  lastname: Nullable(Type.String()),
  fullName: Nullable(Type.String()),
  image: Nullable(Type.String()),
  organizations: Type.Array(organizationReferrerSchema),
});

export const organizationDataSchema = Type.Object({
  id: Type.String(),
  name: Type.String(),
  type: Type.String(),
  slug: Nullable(Type.String()),
  currency: Type.String(),
  countryCode: Nullable(Type.String()),
  hosts: Type.Array(Type.String()),
  images: Nullable(imageSchema),
  covers: Nullable(coversSchema),
  usesDays: Nullable(Type.Boolean()),
  urls: Nullable(urlSchema),
  tracking: Nullable(trackingSchema),
  iframe: Nullable(iframeSchema),
  parentalAuthorization: Nullable(Type.String()),
  termsAndConditions: Nullable(Type.String()),
  bookingExtraPeoplePricing: Nullable(Type.String()),
  ticketCancellationTime: Nullable(Type.Number()),
  areFeesShownUpfront: Type.Boolean(),
  hasPassTypes: Type.Boolean(),
  photos: Type.Array(Type.String()),
  defaultLocation: Nullable(locationSchema),
  seo: Nullable(seoSchema),
});

export const micrositeSuccessResponseSchema = Type.Object({
  channel: Type.String(),
  data: Type.Union([organizationDataSchema, referrerDataSchema]),
});
