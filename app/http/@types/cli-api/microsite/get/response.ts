import { Type } from '@sinclair/typebox';

import { Nullable } from '../../cross-cutting/schema';

export const getMicrositeResponseSchema = Type.Object({
  iframe: Nullable(
    Type.Object({
      cancelPage: Nullable(Type.String()),
      pageTheme: Nullable(Type.String()),
      thankYouPage: Nullable(Type.String()),
    }),
  ),
  tracking: Nullable(
    Type.Object({
      propertyIdGoogle: Nullable(Type.String()),
      pixelIdFacebook: Nullable(Type.String()),
      accessTokenFacebook: Nullable(Type.String()),
    }),
  ),
  seo: Nullable(
    Type.Object({
      title: Nullable(Type.String()),
      shortDescription: Nullable(Type.String()),
      keyWords: Nullable(Type.String()),
      place: Nullable(Type.String()),
      leadDescriptionRelatedToPlace: Nullable(Type.String()),
      bodyDescriptionRelatedToPlace: Nullable(Type.String()),
      leadLocalDescription: Nullable(Type.String()),
      bodyLocalDescription: Nullable(Type.String()),
      image: Nullable(Type.String()),
    }),
  ),
  data: Nullable(
    Type.Object({
      id: Nullable(Type.String()),
      name: Nullable(Type.String()),
      referrerId: Nullable(Type.String()),
      canal: Nullable(Type.String()),
    }),
  ),
  referrerData: Nullable(
    Type.Object({
      id: Nullable(Type.String()),
      name: Nullable(Type.String()),
      lastname: Nullable(Type.String()),
      photo: Nullable(Type.String()),
      organizations: Type.Array(
        Type.Object({
          id: Nullable(Type.String()),
          name: Nullable(Type.String()),
          slug: Nullable(Type.String()),
          type: Nullable(Type.String()),
          image: Nullable(Type.String()),
        }),
      ),
    }),
  ),
  currency: Nullable(Type.String()),
  countryCode: Nullable(Type.String()),
  termsAndConditions: Nullable(Type.String()),
});
