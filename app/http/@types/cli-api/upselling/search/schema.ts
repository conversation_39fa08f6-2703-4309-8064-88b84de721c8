import { Type } from '@sinclair/typebox';

import { querySchemaSearchUpsellingTickets } from './querySchema';
import { successResponseSchema } from './successResponseSchema';

const paramsSchema = Type.Object({});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const searchTicketSchema = {
  params: paramsSchema,
  querystring: querySchemaSearchUpsellingTickets,
  response: responseSchema,
};
