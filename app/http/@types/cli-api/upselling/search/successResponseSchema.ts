import { Type } from '@sinclair/typebox';

import { Nullable } from '../../cross-cutting/schema';

const personalDocument = {
  number: Nullable(Type.String()),
  type: Nullable(Type.String()),
};


const supplementSchema = Type.Object({
  id: Type.String(),
  label: Type.String(),
  price: Type.Number(),
  description: Nullable(Type.String()),
  productQuantity: Nullable(Type.Number()),
  fakePrice: Nullable(Type.Number()),
  purchaseQuantity: Nullable(Type.Number()),
  redemptionDeadlineValue: Nullable(Type.Number()),
});

const assistant = {
  name: Nullable(Type.String()),
  email: Nullable(Type.String()),
  phone: Nullable(Type.String()),
  personalDocument: Nullable(Type.Object(personalDocument)),
};

export const successResponseSchema = Type.Object({
  id: Type.String(),
  organizationId: Type.String(),
  eventId: Type.String(),
  code: Type.String(),
  typeId: Type.String(),
  assistant: Nullable(Type.Object(assistant)),
  supplements: Type.Array(supplementSchema),
});
