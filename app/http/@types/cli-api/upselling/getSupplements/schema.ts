import { Type } from '@sinclair/typebox';

import { successResponseSchema } from './successResponseSchema';

const paramsSchema = Type.Object({ ticketTypeId: Type.String() });

const querySchema = Type.Object({});

const errorResponseSchema = Type.Object({ message: Type.String() });


const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const getSupplementsSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};
