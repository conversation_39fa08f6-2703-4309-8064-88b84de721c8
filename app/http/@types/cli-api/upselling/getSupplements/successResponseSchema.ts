import { Type } from '@sinclair/typebox';

import { Nullable } from '../../cross-cutting/schema';

export const successResponseSchema = Type.Object({
  supplements: Type.Array(
    Type.Object({
      id: Type.String(),
      label: Type.String(),
      price: Type.Number(),
      productQuantity: Nullable(Type.Number()),
      description: Nullable(Type.String()),
      fakePrice: Nullable(Type.Number()),
      purchaseLimit: Nullable(Type.Object({
        minQuantity: Type.Number(),
        maxQuantity: Nullable(Type.Number()),
        isUnlimited: Type.Boolean(),
      })),
      redemptionDeadline: Nullable(Type.Number()),
    }),
  ),
});
