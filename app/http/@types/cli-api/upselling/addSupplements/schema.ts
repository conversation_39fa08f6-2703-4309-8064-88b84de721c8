import { Type } from '@sinclair/typebox';

import { bodySchema } from './bodySchema';
import { successResponseSchema } from './successResponseSchema';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '@app/http/@types/cli-api/cross-cutting/schema';

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const addSupplementsSchema = {
  body: bodySchema,
  response: responseSchema,
};

type Schema = typeof addSupplementsSchema;

export type AddSupplementsRequest = FastifyRequestTypebox<Schema>;
export type AddSupplementsReply = FastifyReplyTypebox<Schema>;
